{"RootName": "DataModels", "CTVER": "43543334", "TableCount": 13, "Count": 1, "items": [{"ID": 1, "Name": "班组记事", "CreateDate": "2023/11/23 15:25:52", "OrderNo": 1, "Tables": {"Count": 13, "items": [{"ID": 1, "Name": "BZJS_TPL", "Caption": "班组记事模版设置表", "CreateDate": "2023/11/23 15:27:23", "OrderNo": 1, "GraphDesc": "Left=54.00\r\nTop=62.00", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "TPL_NAME", "OrderNo": 2, "DisplayName": "模版名称", "DataType": 1, "DataLength": 200}, {"ID": 3, "Name": "REMARK", "OrderNo": 3, "DisplayName": "备注", "DataType": 1, "DataLength": 4000}, {"ID": 4, "Name": "ENABLED", "Memo": "1=使用，0=停用", "OrderNo": 4, "DisplayName": "使用状态", "DataType": 2}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 5, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 2, "Name": "BZJS_TPL_DETAIL", "Caption": "班组记事模版详细设置表（栏目设置）", "CreateDate": "2023/11/23 17:00:37", "OrderNo": 2, "GraphDesc": "Left=318.00\r\nTop=64.00", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 10, "Name": "TPL_ID", "Memo": "BZJS_TPL 表 ID 字段", "OrderNo": 2, "DisplayName": "班组记事模版ID", "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "TITLE", "OrderNo": 3, "DisplayName": "栏目标题", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "DATA_SOURCE", "OrderNo": 4, "DisplayName": "数据来源", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "DATA_TYPE", "OrderNo": 5, "DisplayName": "数据类型", "DataType": 1, "DataLength": 100}, {"ID": 6, "Name": "DATA_RANGE", "OrderNo": 6, "DisplayName": "数据范围", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "MAX_COL_NUM", "OrderNo": 7, "DisplayName": "表格最大列数", "DataType": 2}, {"ID": 8, "Name": "EDITABLE", "Memo": "1=可以编辑，0=不可编辑", "OrderNo": 8, "DisplayName": "是否可编辑", "DataType": 2}, {"ID": 4, "Name": "ENABLED", "Memo": "1=使用，0=停用", "OrderNo": 9, "DisplayName": "使用状态", "DataType": 2}, {"ID": 9, "Name": "SN", "OrderNo": 10, "DisplayName": "序号", "DataType": 2}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 11, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 3, "Name": "BZJS_INPUT_DATAMX", "Caption": "班组记事录入明细数据表", "CreateDate": "2023/11/24 8:18:48", "OrderNo": 3, "GraphDesc": "Left=385.00\r\nTop=293.00", "MetaFields": {"Count": 14, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 17, "Name": "IPT_DATA_ID", "Memo": "BZJS_INPUT_DATA 表 ID 字段", "OrderNo": 2, "DisplayName": "录入主数据ID", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "FORM_DATA_ID", "Memo": "表单每填写一次都会生成新的ID", "OrderNo": 3, "DisplayName": "行云流表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "TPL_ID", "Memo": "BZJS_TPL 表 ID 字段", "OrderNo": 4, "DisplayName": "班组记事模版ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "TPL_DETAIL_ID", "Memo": "BZJS_TPL_DETAIL 表 ID 字段", "OrderNo": 5, "DisplayName": "班组记事模版栏目ID", "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "TITLE", "OrderNo": 6, "DisplayName": "栏目标题", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "DATA_SOURCE", "OrderNo": 7, "DisplayName": "数据来源", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "DATA_TYPE", "OrderNo": 8, "DisplayName": "数据类型", "DataType": 1, "DataLength": 100}, {"ID": 6, "Name": "DATA_RANGE", "OrderNo": 9, "DisplayName": "数据范围", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "MAX_COL_NUM", "OrderNo": 10, "DisplayName": "表格最大列数", "DataType": 2}, {"ID": 8, "Name": "EDITABLE", "Memo": "1=可以编辑，0=不可编辑", "OrderNo": 11, "DisplayName": "是否可编辑", "DataType": 2}, {"ID": 4, "Name": "ENABLED", "Memo": "1=使用，0=停用", "OrderNo": 12, "DisplayName": "使用状态", "DataType": 2}, {"ID": 9, "Name": "SN", "OrderNo": 13, "DisplayName": "序号", "DataType": 2}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 14, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 4, "Name": "BZJS_INPUT_DATA", "Caption": "班组记事录入数据表", "CreateDate": "2023/11/24 9:21:51", "OrderNo": 4, "GraphDesc": "Left=70.00\r\nTop=291.00", "MetaFields": {"Count": 18, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 18, "Name": "FORM_ID", "OrderNo": 2, "DisplayName": "行云流表单模版ID", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "FORM_DATA_ID", "Memo": "表单每填写一次都会生成新的ID", "OrderNo": 3, "DisplayName": "行云流表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "TPL_ID", "Memo": "BZJS_TPL 表 ID 字段", "OrderNo": 4, "DisplayName": "班组记事模版ID", "DataType": 1, "DataLength": 50}, {"ID": 2, "Name": "TPL_NAME", "OrderNo": 5, "DisplayName": "模版名称", "DataType": 1, "DataLength": 200}, {"ID": 3, "Name": "INPUT_TIME", "OrderNo": 6, "DisplayName": "录入时间", "DataType": 4}, {"ID": 11, "Name": "ACCNT_OBJ_IDS", "Memo": "逗号分隔，例如：xxx,xxx,xxx", "OrderNo": 7, "DisplayName": "多个核算对象ID", "DataType": 1, "DataLength": 4000}, {"ID": 13, "Name": "ACCNT_OBJ_NAMES", "Memo": "逗号分隔，例如：xxx,xxx,xxx", "OrderNo": 8, "DisplayName": "多个核算对象名称", "DataType": 1, "DataLength": 4000}, {"ID": 17, "Name": "ACCOUNT_ID", "OrderNo": 9, "DisplayName": "台账ID", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "BCDM", "OrderNo": 10, "DisplayName": "班次代码", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "BCMC", "OrderNo": 11, "DisplayName": "班次名称", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "SBSJ", "OrderNo": 12, "DisplayName": "上班时间", "DataType": 4}, {"ID": 6, "Name": "XBSJ", "OrderNo": 13, "DisplayName": "下班时间", "DataType": 4}, {"ID": 8, "Name": "GWMC", "OrderNo": 14, "DisplayName": "岗位名称", "DataType": 1, "DataLength": 200}, {"ID": 9, "Name": "GWDM", "OrderNo": 15, "DisplayName": "岗位代码", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "TEAM_ID", "OrderNo": 16, "DisplayName": "班组ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "TEAM_NAME", "OrderNo": 17, "DisplayName": "班组名称", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 18, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 5, "Name": "BZJS_INPUT_TEXT", "Caption": "班组记事录入文本数据表", "Memo": "数据来源：手工录入", "CreateDate": "2023/11/24 9:37:00", "OrderNo": 5, "GraphDesc": "Left=750.00\r\nTop=283.00", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "FORM_DATA_ID", "Memo": "表单每填写一次都会生成新的ID", "OrderNo": 2, "DisplayName": "行云流表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "IPT_DATA_ID", "Memo": "BZJS_INPUT_DATA 表 ID 字段", "OrderNo": 3, "DisplayName": "录入主数据ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "IPT_DATAMX_ID", "Memo": "BZJS_INPUT_DATAMX 表 ID 字段", "OrderNo": 4, "DisplayName": "录入明细数据ID", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "INPUT_CONTENT", "Memo": "通用：本班生产调节、生产指令", "OrderNo": 5, "DisplayName": "手动录入内容", "DataType": 1, "DataLength": 4000}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 6, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 6, "Name": "BZJS_INPUT_HSZB", "Caption": "班组记事录入核算指标数据表", "Memo": "数据来源：核算指标", "CreateDate": "2023/11/24 10:51:53", "OrderNo": 6, "GraphDesc": "Left=749.00\r\nTop=410.00", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "FORM_DATA_ID", "Memo": "表单每填写一次都会生成新的ID", "OrderNo": 2, "DisplayName": "行云流表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "IPT_DATA_ID", "Memo": "BZJS_INPUT_DATA 表 ID 字段", "OrderNo": 3, "DisplayName": "录入主数据ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "IPT_DATAMX_ID", "Memo": "BZJS_INPUT_DATAMX 表 ID 字段", "OrderNo": 4, "DisplayName": "录入明细数据ID", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "ZBMC", "OrderNo": 5, "DisplayName": "指标名称", "DataType": 1, "DataLength": 200}, {"ID": 7, "Name": "ZBBM", "OrderNo": 6, "DisplayName": "指标编码", "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "VALUE", "OrderNo": 7, "DisplayName": "录入值", "DataType": 1, "DataLength": 1000}, {"ID": 10, "Name": "ITEM_UNIT", "OrderNo": 8, "DisplayName": "计量单位", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "SN", "OrderNo": 9, "DisplayName": "序号", "DataType": 2}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 10, "DisplayName": "记录标识", "DataType": 2}, {"ID": 11, "Name": "CAN_MODIFIED", "Memo": "t 允许修改；f 不允许修改，默认是f", "OrderNo": 11, "DisplayName": "是否允许被修改标识", "DataType": 1, "DataLength": 50}]}}, {"ID": 7, "Name": "BZJS_INPUT_GJSB", "Caption": "班组记事录入关键设备数据表", "Memo": "数据来源：关键设备", "CreateDate": "2023/11/24 10:58:17", "OrderNo": 7, "GraphDesc": "Left=748.00\r\nTop=621.00", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "FORM_DATA_ID", "Memo": "表单每填写一次都会生成新的ID", "OrderNo": 2, "DisplayName": "行云流表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "IPT_DATA_ID", "Memo": "BZJS_INPUT_DATA 表 ID 字段", "OrderNo": 3, "DisplayName": "录入主数据ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "IPT_DATAMX_ID", "Memo": "BZJS_INPUT_DATAMX 表 ID 字段", "OrderNo": 4, "DisplayName": "录入明细数据ID", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "SBMC", "OrderNo": 5, "DisplayName": "设备名称", "DataType": 1, "DataLength": 200}, {"ID": 7, "Name": "SBBM", "OrderNo": 6, "DisplayName": "设备编码", "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "SBZT", "Memo": "开、停", "OrderNo": 7, "DisplayName": "设备状态", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "ITEM_UNIT", "OrderNo": 8, "DisplayName": "计量单位", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "SB_OPTIONS", "Memo": "JSON数组格式字符串", "OrderNo": 9, "DisplayName": "设备选择项", "DataType": 1, "DataLength": 4000}, {"ID": 9, "Name": "SN", "OrderNo": 10, "DisplayName": "序号", "DataType": 2}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 11, "DisplayName": "记录标识", "DataType": 2}, {"ID": 12, "Name": "ALTERED", "Memo": "1=已修改，0=未修改", "OrderNo": 12, "DisplayName": "修改标识", "DataType": 2}]}}, {"ID": 8, "Name": "BZJS_INPUT_HANDOVER", "Caption": "班组记事录入交班信息", "CreateDate": "2023/11/24 11:21:38", "OrderNo": 8, "GraphDesc": "Left=432.00\r\nTop=851.00", "MetaFields": {"Count": 13, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "FORM_DATA_ID", "Memo": "表单每填写一次都会生成新的ID", "OrderNo": 2, "DisplayName": "行云流表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "IPT_DATA_ID", "Memo": "BZJS_INPUT_DATA 表 ID 字段", "OrderNo": 3, "DisplayName": "录入主数据ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "IPT_DATAMX_ID", "Memo": "BZJS_INPUT_DATAMX 表 ID 字段", "OrderNo": 4, "DisplayName": "录入明细数据ID", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "BCMC", "OrderNo": 5, "DisplayName": "班次名称", "DataType": 1, "DataLength": 200}, {"ID": 6, "Name": "BCDM", "OrderNo": 6, "DisplayName": "班次代码", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "BZMC", "OrderNo": 7, "DisplayName": "班组名称", "DataType": 1, "DataLength": 200}, {"ID": 8, "Name": "BZDM", "OrderNo": 8, "DisplayName": "班组代码", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "PERSON_ID", "OrderNo": 9, "DisplayName": "人员ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "PERSON_NAME", "OrderNo": 10, "DisplayName": "人员姓名", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "OPER_TIME", "OrderNo": 11, "DisplayName": "操作时间", "DataType": 4}, {"ID": 13, "Name": "HANDOVER_TIME", "OrderNo": 12, "DisplayName": "交班时间", "DataType": 4}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 13, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 8, "Name": "BZJS_INPUT_TAKEOVER", "Caption": "班组记事录入接班信息", "CreateDate": "2023/11/24 11:21:38", "OrderNo": 9, "GraphDesc": "Left=752.00\r\nTop=852.00", "MetaFields": {"Count": 13, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "FORM_DATA_ID", "Memo": "表单每填写一次都会生成新的ID", "OrderNo": 2, "DisplayName": "行云流表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "IPT_DATA_ID", "Memo": "BZJS_INPUT_DATA 表 ID 字段", "OrderNo": 3, "DisplayName": "录入主数据ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "IPT_DATAMX_ID", "Memo": "BZJS_INPUT_DATAMX 表 ID 字段", "OrderNo": 4, "DisplayName": "录入明细数据ID", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "BCMC", "OrderNo": 5, "DisplayName": "班次名称", "DataType": 1, "DataLength": 200}, {"ID": 6, "Name": "BCDM", "OrderNo": 6, "DisplayName": "班次代码", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "BZMC", "OrderNo": 7, "DisplayName": "班组名称", "DataType": 1, "DataLength": 200}, {"ID": 8, "Name": "BZDM", "OrderNo": 8, "DisplayName": "班组代码", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "PERSON_ID", "OrderNo": 9, "DisplayName": "人员ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "PERSON_NAME", "OrderNo": 10, "DisplayName": "人员姓名", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "OPER_TIME", "OrderNo": 11, "DisplayName": "操作时间", "DataType": 4}, {"ID": 13, "Name": "TAKEOVER_TIME", "OrderNo": 12, "DisplayName": "接班时间", "DataType": 4}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 13, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 8, "Name": "BZJS_INPUT_CONFIRM", "Caption": "班组记事录入确认签字", "CreateDate": "2023/11/24 11:21:38", "OrderNo": 10, "GraphDesc": "Left=1078.00\r\nTop=874.00", "MetaFields": {"Count": 10, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "FORM_DATA_ID", "Memo": "表单每填写一次都会生成新的ID", "OrderNo": 2, "DisplayName": "行云流表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "IPT_DATA_ID", "Memo": "BZJS_INPUT_DATA 表 ID 字段", "OrderNo": 3, "DisplayName": "录入主数据ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "IPT_DATAMX_ID", "Memo": "BZJS_INPUT_DATAMX 表 ID 字段", "OrderNo": 4, "DisplayName": "录入明细数据ID", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "GWDM", "OrderNo": 5, "DisplayName": "岗位代码", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "GWMC", "OrderNo": 6, "DisplayName": "岗位名称", "DataType": 1, "DataLength": 200}, {"ID": 16, "Name": "PERSON_ID", "OrderNo": 7, "DisplayName": "人员ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "PERSON_NAME", "OrderNo": 8, "DisplayName": "人员姓名", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "OPER_TIME", "OrderNo": 9, "DisplayName": "确认时间", "DataType": 4}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 10, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 11, "Name": "BZJS_GJSB_STATUS", "Caption": "班组记事关键设备状态配置数据表", "CreateDate": "2023/11/28 16:56:02", "OrderNo": 11, "GraphDesc": "Left=1059.00\r\nTop=71.00", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 3, "Name": "ST_NAME", "OrderNo": 2, "DisplayName": "状态名称", "DataType": 1, "DataLength": 200}, {"ID": 4, "Name": "ST_FONT_COLOR", "Memo": "十六进制颜色值，例如：#000000", "OrderNo": 3, "DisplayName": "状态名称文字颜色", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "SN", "OrderNo": 4, "DisplayName": "序号", "DataType": 2}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 5, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 12, "Name": "BZJS_PERMISSION", "Caption": "班组记事权限设置表", "CreateDate": "2023/12/26 8:10:11", "OrderNo": 12, "GraphDesc": "Left=741.00\r\nTop=69.00", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 10, "Name": "TPL_ID", "Memo": "BZJS_TPL 表 ID 字段", "OrderNo": 2, "DisplayName": "班组记事模版ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "TPL_DETAIL_ID", "Memo": "BZJS_TPL_DETAIL 表 ID 字段", "OrderNo": 3, "DisplayName": "班组记事模版栏目ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "PERM_TYPE", "Memo": "role=角色，post=岗位，staff=员工", "OrderNo": 4, "DisplayName": "权限类型", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "PERM_ID", "Memo": "一个ID为一条记录，存储角色、岗位、员工的ID", "OrderNo": 5, "DisplayName": "权限ID", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "PERM_NAME", "OrderNo": 6, "DisplayName": "权限名称", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 7, "DisplayName": "记录标识", "DataType": 2}]}}, {"ID": 12, "Name": "BZJS_INPUT_PERMISSION", "Caption": "班组记事权限历史数据表", "CreateDate": "2023/12/26 8:10:11", "OrderNo": 13, "GraphDesc": "Left=373.00\r\nTop=558.00", "MetaFields": {"Count": 10, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "记录唯一ID", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 10, "Name": "TPL_ID", "Memo": "BZJS_TPL 表 ID 字段", "OrderNo": 2, "DisplayName": "班组记事模版ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "TPL_DETAIL_ID", "Memo": "BZJS_TPL_DETAIL 表 ID 字段", "OrderNo": 3, "DisplayName": "班组记事模版栏目ID", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "FORM_DATA_ID", "Memo": "表单每填写一次都会生成新的ID", "OrderNo": 4, "DisplayName": "行云流表单数据ID", "DataType": 1, "DataLength": 50}, {"ID": 17, "Name": "IPT_DATA_ID", "Memo": "BZJS_INPUT_DATA 表 ID 字段", "OrderNo": 5, "DisplayName": "录入主数据ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "IPT_DATAMX_ID", "Memo": "BZJS_INPUT_DATAMX 表 ID 字段", "OrderNo": 6, "DisplayName": "录入明细数据ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "PERM_TYPE", "Memo": "role=角色，post=岗位，staff=员工", "OrderNo": 7, "DisplayName": "权限类型", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "PERM_ID", "Memo": "一个ID为一条记录，存储角色、岗位、员工的ID", "OrderNo": 8, "DisplayName": "权限ID", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "PERM_NAME", "OrderNo": 9, "DisplayName": "权限名称", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "TMUSED", "Memo": "1=可用，0=已删除", "OrderNo": 10, "DisplayName": "记录标识", "DataType": 2}]}}]}}]}