{
		    "columnCfg": [
		    {
		        "insideColumn": "NODE_ID",
		        "outsideColumn": "NODE_ID",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "NODE_NUM",
		        "outsideColumn": "NODE_NUM",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "NODE_NAME",
		        "outsideColumn": "NODE_NAME",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "NODE_ALIAS",
		        "outsideColumn": "NODE_ALIAS",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "FCTR_ID",
		        "outsideColumn": "FCTR_ID",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "NODE_TYPE",
		        "outsideColumn": "NODE_TYPE",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "AREA_ID",
		        "outsideColumn": "AREA_ID",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "TAG",
		        "outsideColumn": "TAG",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "COEFFICIENT",
		        "outsideColumn": "COEFFICIENT",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "DIMENSION_ID",
		        "outsideColumn": "DIMENSION_ID",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "REAL_FLAG",
		        "outsideColumn": "REAL_FLAG",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "ACCU_INSTR_FLAG",
		        "outsideColumn": "ACCU_INSTR_FLAG",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "UP_SPAN",
		        "outsideColumn": "UP_SPAN",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "DOWM_SPAN",
		        "outsideColumn": "DOWM_SPAN",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "USE_FLAG",
		        "outsideColumn": "USE_FLAG",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "DES",
		        "outsideColumn": "DES",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "DISPLAY_ORDER",
		        "outsideColumn": "DISPLAY_ORDER",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "CREATE_USER",
		        "outsideColumn": "CREATE_USER",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "CREATE_TIME",
		        "outsideColumn": "CREATE_TIME",
		        "synDataType": "date",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "MODIFY_TIME",
		        "outsideColumn": "MODIFY_TIME",
		        "synDataType": "date",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "MODIFY_USER",
		        "outsideColumn": "MODIFY_USER",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "IS_DELETE",
		        "outsideColumn": "IS_DELETE",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "AREA_TYPE",
		        "outsideColumn": "AREA_TYPE",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      }
		    ],
		  "pullTemplate": "",
		  "synCode": "sql",
		  "synDataJson": ["
{
    \"code\": \"200\",
    \"msg\": \"查询成功\",
    \"version\": \"1.0.0\",
    \"startPage\": \"2\",
    \"hasMore\": \"1\",
    \"total\": \"2148\",
    \"rows\": [
        {
            \"NODE_ID\": \"378\",
            \"NODE_NUM\": \"10378\",
            \"NODE_NAME\": \"苯乙烯装置苯乙烯FIQ40471\",
            \"NODE_ALIAS\": \"3700FIQ40471\",
            \"FCTR_ID\": \"208\",
            \"NODE_TYPE\": \"1\",
            \"AREA_ID\": \"140\",
            \"TAG\": \"STU1_3700FIQ40471.OUT\",
            \"COEFFICIENT\": \"1\",
            \"DIMENSION_ID\": \"7\",
            \"REAL_FLAG\": \"0\",
            \"ACCU_INSTR_FLAG\": \"1\",
            \"UP_SPAN\": \"999999999.0000\",
            \"DOWM_SPAN\": \"0\",
            \"USE_FLAG\": \"1\",
            \"DES\": \"\",
            \"DISPLAY_ORDER\": \"3780.0000\",
            \"CREATE_USER\": \"\",
            \"CREATE_TIME\": \"\",
            \"MODIFY_TIME\": \"\",
            \"MODIFY_USER\": \"\",
            \"IS_DELETE\": \"\",
            \"AREA_TYPE\": \"30\"
        }
    ]
}
                                   "],
		  "synDataPath": "/rows",
		  "synDesc": "",
		  "synParam": "{\"batchSize\":1000}",
		  "synVersionPath": "/version",
		  "tableName": "PM_MEASURE_NODE_T",
		  "whereSql": "@all"
		}