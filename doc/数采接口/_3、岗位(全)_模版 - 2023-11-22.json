{"columnCfg": [{"insideColumn": "empTmuid", "outsideColumn": "userId", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "changeDt", "outsideColumn": "modifyTime", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "postid", "outsideColumn": "postId", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "postname", "outsideColumn": "postName", "synDataType": "", "synDesc": "", "synParam": ""}], "pullTemplate": "", "synCode": "emp", "synDataJson": "{{rawData}}", "synDataPath": "/rows", "synDesc": "", "synParam": "", "synVersionPath": "/version", "tableName": "", "whereSql": ""}