{
		    "columnCfg": [
		    {
		        "insideColumn": "NODE_ID",
		        "outsideColumn": "NODE_ID",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "NODE_NUM",
		        "outsideColumn": "NODE_NUM",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "NODE_CODE",
		        "outsideColumn": "NODE_CODE",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "NODE_NAME",
		        "outsideColumn": "NODE_NAME",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "NODE_ALIAS",
		        "outsideColumn": "NODE_ALIAS",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "UNIT_ID",
		        "outsideColumn": "UNIT_ID",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "SLINE_MTRL_TYPE_ID",
		        "outsideColumn": "SLINE_MTRL_TYPE_ID",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "SLINE_INOUT_TYPE_ID",
		        "outsideColumn": "SLINE_INOUT_TYPE_ID",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "AREA_FORM",
		        "outsideColumn": "AREA_FORM",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "ACCURACY",
		        "outsideColumn": "ACCURACY",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "DES",
		        "outsideColumn": "DES",
		        "synDataType": "",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "DISPLAY_ORDER",
		        "outsideColumn": "DISPLAY_ORDER",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "CREATE_USER",
		        "outsideColumn": "CREATE_USER",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "CREATE_TIME",
		        "outsideColumn": "CREATE_TIME",
		        "synDataType": "date",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "MODIFY_TIME",
		        "outsideColumn": "MODIFY_TIME",
		        "synDataType": "date",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "MODIFY_USER",
		        "outsideColumn": "MODIFY_USER",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "IS_DELETE",
		        "outsideColumn": "IS_DELETE",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "USE_FLAG",
		        "outsideColumn": "USE_FLAG",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      },{
		        "insideColumn": "IS_CHECKED",
		        "outsideColumn": "IS_CHECKED",
		        "synDataType": "number",
		        "synDesc": "",
		        "synParam": ""
		      }
		    ],
		  "pullTemplate": "",
		  "synCode": "sql",
		  "synDataJson": ["
{
    \"code\": \"200\",
    \"msg\": \"查询成功\",
    \"version\": \"1.0.0\",
    \"startPage\": \"2\",
    \"hasMore\": \"1\",
    \"total\": \"2148\",
    \"rows\": [
        {
            \"NODE_ID\": \"279\",
            \"NODE_NUM\": \"200001\",
            \"NODE_CODE\": \"CDU1_001\",
            \"NODE_NAME\": \"常减压Ⅰ原油进料线\",
            \"NODE_ALIAS\": \"常减压Ⅰ原油进料线\",
            \"UNIT_ID\": \"138\",
            \"SLINE_MTRL_TYPE_ID\": \"1\",
            \"SLINE_INOUT_TYPE_ID\": \"0\",
            \"AREA_FORM\": \"{2101FIQ1001}\",
            \"ACCURACY\": \"100\",
            \"DES\": \"ABC\",
            \"DISPLAY_ORDER\": \"20\",
            \"CREATE_USER\": \"30\",
            \"CREATE_TIME\": \"\",
            \"MODIFY_TIME\": \"\",
            \"MODIFY_USER\": \"40\",
            \"IS_DELETE\": \"0\",
            \"IS_CHECKED\": \"1\",
            \"USE_FLAG\": \"1\"
        }
    ]
}
                                   "],
		  "synDataPath": "/rows",
		  "synDesc": "",
		  "synParam": "{\"batchSize\":1000}",
		  "synVersionPath": "/version",
		  "tableName": "PM_SIDELINE_T",
		  "whereSql": "@all"
		}