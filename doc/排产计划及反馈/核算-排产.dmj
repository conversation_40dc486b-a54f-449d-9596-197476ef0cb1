{"RootName": "DataModels", "CTVER": "43543332", "TableCount": 5, "Count": 1, "items": [{"ID": 4, "Name": "核算-排产", "CreateDate": "2023-08-02 11:03:26", "OrderNo": 1, "Tables": {"Count": 5, "items": [{"ID": 1, "Name": "PRODUCTSCHEDU_PLAN", "Caption": "排产计划", "CreateDate": "2023-08-02 11:03:43", "OrderNo": 1, "GraphDesc": "Left=10.00\r\nTop=10.00\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 22, "items": [{"ID": 1, "Name": "Id", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "INDENTID", "OrderNo": 2, "DisplayName": "订单ID", "DataType": 1, "DataLength": 100}, {"ID": 19, "Name": "PRODUCTNAME", "OrderNo": 3, "DisplayName": "产品名称", "DataType": 1, "DataLength": 100}, {"ID": 13, "Name": "PRODUCTID", "OrderNo": 4, "DisplayName": "产品ID", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "UNITID", "OrderNo": 5, "DisplayName": "核算对象ID", "DataType": 1, "DataLength": 100}, {"ID": 20, "Name": "UNITNAME", "OrderNo": 6, "DisplayName": "核算对象名称", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "PROGRAMID", "OrderNo": 7, "DisplayName": "方案ID", "DataType": 1, "DataLength": 100}, {"ID": 21, "Name": "PROGRAMNAME", "OrderNo": 8, "DisplayName": "方案名称", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "STARTDAY", "OrderNo": 9, "DisplayName": "开始日期", "DataType": 1}, {"ID": 16, "Name": "ENDDAY", "OrderNo": 10, "DisplayName": "结束日期", "DataType": 1}, {"ID": 18, "Name": "COMPLETEAMOUNT", "OrderNo": 11, "DisplayName": "完成量 ", "DataType": 3}, {"ID": 19, "Name": "SHSTATE", "OrderNo": 12, "DisplayName": "审核状态（0，未审核，1：已审核）", "DataType": 2}, {"ID": 20, "Name": "SHUSERID", "OrderNo": 13, "DisplayName": "审核人员编码", "DataType": 1, "DataLength": 100}, {"ID": 24, "Name": "SHUSERNAME", "OrderNo": 14, "DisplayName": "审核人员姓名", "DataType": 1, "DataLength": 100}, {"ID": 21, "Name": "SHDATE", "OrderNo": 15, "DisplayName": "审核时间", "DataType": 1, "DataLength": 20}, {"ID": 22, "Name": "SPSTATE", "OrderNo": 16, "DisplayName": "审批状态（0：未审核，1：已审批）", "DataType": 2}, {"ID": 23, "Name": "SPUSERID", "OrderNo": 17, "DisplayName": "审批人员编码", "DataType": 1, "DataLength": 100}, {"ID": 25, "Name": "SPUSERNAME", "OrderNo": 18, "DisplayName": "审批人员姓名", "DataType": 1, "DataLength": 100}, {"ID": 26, "Name": "SPDATE", "OrderNo": 19, "DisplayName": "审批时间 ", "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "ISCOMPLETE", "OrderNo": 20, "DisplayName": "是否完成（0：未完成，1：已完成）", "DataType": 2}, {"ID": 28, "Name": "TMUSED", "OrderNo": 21, "DisplayName": "删除标识（0：已删除，1：未删除）", "DataType": 2}, {"ID": 22, "Name": "CLIENTNAME", "OrderNo": 22, "DisplayName": "客户单位名称", "DataType": 1, "DataLength": 100}]}}, {"ID": 2, "Name": "PRODUCTSCHEDU_PLAN_FEED", "Caption": "排产计划反馈 ", "CreateDate": "2023-08-07 13:26:32", "OrderNo": 2, "GraphDesc": "Left=10.00\r\nTop=468.00\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 19, "items": [{"ID": 1, "Name": "Id", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 36, "Name": "PLANID", "OrderNo": 2, "DisplayName": "计划编码", "DataType": 1, "DataLength": 100}, {"ID": 29, "Name": "INDENTID", "OrderNo": 3, "DisplayName": "订单ID ", "DataType": 1, "DataLength": 100}, {"ID": 30, "Name": "PRODUCTNAME", "OrderNo": 4, "DisplayName": "产品名称 ", "DataType": 1, "DataLength": 100}, {"ID": 31, "Name": "PRODUCTID", "OrderNo": 5, "DisplayName": "产品ID ", "DataType": 1, "DataLength": 100}, {"ID": 32, "Name": "UNITID", "OrderNo": 6, "DisplayName": "核算对象ID ", "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "UNITNAME", "OrderNo": 7, "DisplayName": "核算对象名称 ", "DataType": 1, "DataLength": 100}, {"ID": 34, "Name": "PROGRAMID", "OrderNo": 8, "DisplayName": "方案ID ", "DataType": 1, "DataLength": 100}, {"ID": 35, "Name": "PROGRAMNAME", "OrderNo": 9, "DisplayName": "方案名称 ", "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "STARTDATETIME", "OrderNo": 10, "DisplayName": "开始日期和时间", "DataType": 1, "DataLength": 19}, {"ID": 11, "Name": "ENDDATETIME", "OrderNo": 11, "DisplayName": "结束日期和时间", "DataType": 1, "DataLength": 19}, {"ID": 12, "Name": "FEEDORGCODE", "OrderNo": 12, "DisplayName": "反馈机构代码", "DataType": 1, "DataLength": 100}, {"ID": 13, "Name": "FEEDORGNAME", "OrderNo": 13, "DisplayName": "反馈机构名称", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "FEEDUSERID", "OrderNo": 14, "DisplayName": "反馈人员编码", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "FEEDUSERNAME", "OrderNo": 15, "DisplayName": "反馈人员姓名", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "FEEDVALUE", "OrderNo": 16, "DisplayName": "反馈完成量", "DataType": 3}, {"ID": 17, "Name": "BATCHNO", "OrderNo": 17, "DisplayName": "批次号", "DataType": 1, "DataLength": 100}, {"ID": 37, "Name": "FEEDDATE", "OrderNo": 18, "DisplayName": "反馈日期", "DataType": 1, "DataLength": 10}, {"ID": 38, "Name": "ISCOMPLETE", "OrderNo": 19, "DisplayName": "是否完成（0：未完成，1：：已完成 ） ", "DataType": 2}]}}, {"ID": 3, "Name": "PRODUCTSCHEDU_TITLE", "Caption": "排产计划表头", "CreateDate": "2023-09-11 15:22:12", "OrderNo": 3, "GraphDesc": "Left=10.00\r\nTop=894.00\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "Id", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 32, "Name": "UNITID", "OrderNo": 2, "DisplayName": "核算对象ID ", "DataType": 1, "DataLength": 100}, {"ID": 34, "Name": "COLUMNCODE", "OrderNo": 3, "DisplayName": "字段别名", "DataType": 1, "DataLength": 100}, {"ID": 35, "Name": "COLUMNNAME", "OrderNo": 4, "DisplayName": "字段名称", "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "SHOWNAME", "OrderNo": 5, "DisplayName": "显示名称", "DataType": 1, "DataLength": 19}, {"ID": 11, "Name": "COLUMNTYPE", "OrderNo": 6, "DisplayName": "字段类型（0：固定字段，1：备用字段）", "DataType": 2, "DataLength": 19}, {"ID": 12, "Name": "COLUMNUSETYPE", "OrderNo": 7, "DisplayName": "字段使用类型（1：计划，2：反馈）", "DataType": 2, "DataLength": 100}, {"ID": 13, "Name": "COMMENTTYPE", "OrderNo": 8, "DisplayName": "组件类型（textFiled：文本框，numberFiled：数值框，dateTimeFiled：日期时间选择框", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "ISMUST", "OrderNo": 9, "DisplayName": "计划是否必须填写（0：不需要，1：必需填写）", "DataType": 2, "DataLength": 100}, {"ID": 16, "Name": "ISSHOW", "OrderNo": 10, "DisplayName": "计划中显示（0：不显示，1：显示）", "DataType": 2}, {"ID": 37, "Name": "TMSORT", "OrderNo": 11, "DisplayName": "排序字段", "DataType": 2, "DataLength": 10}]}}, {"ID": 6, "Name": "PRODUCTSCHEDU_PLAN_START", "Caption": "排产计划开始结束日期表", "CreateDate": "2023-09-18 10:14:46", "OrderNo": 4, "GraphDesc": "Left=10.00\r\nTop=1192.00\r\nBLeft=0.00\r\nBTop=0.00", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "Id", "OrderNo": 1, "DisplayName": "编号", "DataType": 2, "KeyFieldType": 1}, {"ID": 36, "Name": "PLANID", "OrderNo": 2, "DisplayName": "计划编码", "DataType": 1, "DataLength": 100}, {"ID": 32, "Name": "UNITID", "OrderNo": 3, "DisplayName": "核算对象ID ", "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "UNITNAME", "OrderNo": 4, "DisplayName": "核算对象名称 ", "DataType": 1, "DataLength": 100}, {"ID": 34, "Name": "PROGRAMID", "OrderNo": 5, "DisplayName": "方案ID ", "DataType": 1, "DataLength": 100}, {"ID": 35, "Name": "PROGRAMNAME", "OrderNo": 6, "DisplayName": "方案名称 ", "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "STARTDATETIME", "OrderNo": 7, "DisplayName": "开始时间", "DataType": 1, "DataLength": 20}, {"ID": 34, "Name": "ENDDATETIME", "OrderNo": 8, "DisplayName": "结束时间", "DataType": 1, "DataLength": 20}]}}, {"ID": 5, "Name": "UNITRUNSTATUS", "Caption": "核算对象当前运行状态", "CreateDate": "2023-09-21 11:12:36", "OrderNo": 5, "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "Id", "OrderNo": 1, "DisplayName": "数据唯一编码", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "UNITID", "OrderNo": 2, "DisplayName": "父编号", "DataType": 1, "DataLength": 100}, {"ID": 3, "Name": "RUNSTATUSNAME", "OrderNo": 3, "DisplayName": "运行状态名称", "DataType": 1, "DataLength": 200}, {"ID": 4, "Name": "RUNSTATUSID", "OrderNo": 4, "DisplayName": "运行状态编码", "DataType": 1, "DataLength": 100}, {"ID": 5, "Name": "RUNTYPECOLOR", "OrderNo": 5, "DisplayName": "运行状态颜色", "DataType": 1, "DataLength": 200}]}}]}}]}