{"RootName": "DataModels", "CTVER": "43543338", "TableCount": 74, "Count": 1, "items": [{"ID": 1, "Name": "模型", "CreateDate": "2024-06-06 09:30:12", "OrderNo": 1, "CustomAttr1": "DVS:558.00,941.00,1.25,0,", "DefDbEngine": "SQLSERVER", "DbConnectStr": "TCtMetaSqlsvrDb", "ConfigStr": "DrawerWidth=4009\r\nDrawerHeight=2560\r\nWorkAreaColor=********\r\nSelectedColor=16711680\r\nDefaultObjectColor=15921906\r\nDefaultTitleColor=255\r\nDefaultPKColor=16711935\r\nDefaultFKColor=16711680\r\nDefaultBorderColor=12632256\r\nDefaultLineColor=16711680\r\nDefaultGroupEdgeColor=128\r\nShowFieldType=1\r\nShowFieldIcon=1\r\nShowPhyFieldName=2\r\nDatabaseEngine=\r\nGenFKIndexesSQL=0\r\nIndependPosForOverviewMode=0\r\n", "Tables": {"Count": 74, "items": [{"ID": 1, "Name": "ACCTOBJ_INPUT", "Caption": "核算对象录入表（主表）", "CreateDate": "2024-01-31 15:24:46", "OrderNo": 1, "GraphDesc": "Left=1428.66\r\nTop=1655.68", "BgColor": 15267306, "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_INPUT_MAPPING", "RelateField": "SLAVE_ID", "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1794.68\r\nP3=1343.00,1794.68\r\nP4=1428.66,1794.68\r\nHookP1=285.61,75.00\r\nHookP2=148.00,139.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 15, "Name": "BA_ID", "Memo": "BA：Business Activity", "OrderNo": 2, "DisplayName": "业务活动ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 16, "Name": "BA_NAME", "Memo": "BA：Business Activity", "OrderNo": 3, "DisplayName": "业务活动名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "ACCTOBJ_ID", "OrderNo": 4, "DisplayName": "核算对象ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 13, "Name": "ACCTOBJ_NAME", "OrderNo": 5, "DisplayName": "核算对象名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 4, "Name": "INPUT_TIME", "OrderNo": 6, "DisplayName": "录入时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 5, "Name": "INPUT_LONGITUDE", "OrderNo": 7, "DisplayName": "录入时所在位置经度", "DataType": 3, "RelateTable": "任务督办", "DataLength": 18, "DataScale": 6}, {"ID": 6, "Name": "INPUT_LATITUDE", "OrderNo": 8, "DisplayName": "录入时所在位置纬度", "DataType": 3, "RelateTable": "任务督办", "DataLength": 18, "DataScale": 6}, {"ID": 14, "Name": "OPERATING_RADIUS", "Memo": "录入人不在作业半径以内，不允许录入", "OrderNo": 9, "DisplayName": "作业半径", "DataType": 3, "RelateTable": "任务督办", "DataLength": 18, "DataScale": 6}, {"ID": 7, "Name": "BCDM", "OrderNo": 10, "DisplayName": "班次代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 4, "Name": "BCMC", "OrderNo": 11, "DisplayName": "班次名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 5, "Name": "SBSJ", "OrderNo": 12, "DisplayName": "上班时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 6, "Name": "XBSJ", "OrderNo": 13, "DisplayName": "下班时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 14, "Name": "TEAM_ID", "OrderNo": 14, "DisplayName": "班组ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 15, "Name": "TEAM_NAME", "OrderNo": 15, "DisplayName": "班组名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 16, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 3, "Name": "ACCTOBJ_INPUTMX", "Caption": "核算对象采集点录入表（明细）", "CreateDate": "2024-01-31 15:46:14", "OrderNo": 2, "GraphDesc": "Left=2321.66\r\nTop=1647.68", "BgColor": 15789301, "MetaFields": {"Count": 17, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 15, "Name": "BA_ID", "Memo": "BA：Business Activity", "OrderNo": 2, "DisplayName": "业务活动ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "ACCTOBJ_ID", "OrderNo": 3, "DisplayName": "核算对象ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "IPT_ID", "Memo": "ACCTOBJ_INPUT 表的 ID", "OrderNo": 4, "DisplayName": "录入主表ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "ACCTOBJ_INPUT", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1724.66,1793.72\r\nP2=1815.00,1793.72\r\nP3=1815.00,1699.00\r\nP4=2321.66,1699.00\r\nHookP1=132.00,138.04\r\nHookP2=20.34,51.32\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 18, "Name": "IPT_FL_ID", "OrderNo": 5, "DisplayName": "录入采集点分类表ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "ACCTOBJ_INPUT_FL", "RelateField": "ID", "GraphDesc": "P1=2149.31,1823.00\r\nP2=2235.00,1823.00\r\nP3=2235.00,1823.00\r\nP4=2321.66,1823.00\r\nHookP1=282.69,91.28\r\nHookP2=28.34,175.32\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "INPUT_TIME", "OrderNo": 6, "DisplayName": "录入时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 8, "Name": "TAG_NO", "OrderNo": 7, "DisplayName": "仪表位号", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 13, "Name": "COLLECT_POINT_ID", "OrderNo": 8, "DisplayName": "采集点ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 5, "Name": "COLLECT_POINT", "OrderNo": 9, "DisplayName": "采集点", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 7, "Name": "COLLECT_POINT_VAL", "OrderNo": 10, "DisplayName": "采集点录入值", "DataType": 1, "RelateTable": "任务督办", "DataLength": 4000}, {"ID": 11, "Name": "COLLECT_POINT_TEXT", "OrderNo": 11, "DisplayName": "下拉框选择值对应的显示值", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 9, "Name": "SN", "OrderNo": 12, "DisplayName": "序号", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 13, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 12, "Name": "INPUT_COMP_TYPE", "Memo": "空=文本输入框，textfield=文本输入框，numberfield=数值输入框，datetimefield=日期时间选择框，combobox=下拉选择框，checkbox=复选框", "OrderNo": 14, "DisplayName": "录入组件类型", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 13, "Name": "INPUT_OPTIONS", "Memo": "下拉选择框的备选数据，格式为JSON数组：[{value:\"\",text:\"\"}]", "OrderNo": 15, "DisplayName": "录入组件备选数据", "DataType": 1, "RelateTable": "任务督办", "DataLength": 4000}, {"ID": 14, "Name": "EQUIP_MT_REMARK", "Memo": "对应设备维护保养表格中的记录列", "OrderNo": 16, "DisplayName": "设备维保备注信息", "DataType": 1, "RelateTable": "任务督办", "DataLength": 4000}, {"ID": 15, "Name": "IS_EQUIP_MAINTENANCE", "Memo": "1=是设备维保，0=不是", "OrderNo": 17, "DisplayName": "设备维保业务标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 3, "Name": "采集点录入", "TypeName": "GROUP", "Memo": "采集点录入（复用：辽兴项目移动端录入表结构）", "CreateDate": "2024-06-06 09:40:51", "OrderNo": 3, "GraphDesc": "Left=1377.23\r\nTop=1596.66\r\nWidth=1373.00\r\nHeight=367.00\r\nAutoSize=0\r\nBWidth=686.50\r\nBHeight=183.50", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 4, "Name": "JOBLIST_INPUT_DATA", "Caption": "工作清单录入数据表", "CreateDate": "2024-06-06 09:44:35", "OrderNo": 4, "GraphDesc": "Left=2210.60\r\nTop=959.06\r\nWidth=299.00\r\nHeight=149.00\r\nAutoSize=0", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1962.61,1161.00\r\nP2=1912.00,1161.00\r\nP3=1912.00,1161.00\r\nP4=1861.35,1161.00\r\nHookP1=36.39,107.34\r\nHookP2=278.65,58.04\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 15, "Name": "JOB_ID", "OrderNo": 2, "DisplayName": "作业活动ID", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=1279.07,663.00\r\nP2=1681.00,663.00\r\nP3=1681.00,663.00\r\nP4=1681.00,1102.96\r\nHookP1=178.00,144.80\r\nHookP2=118.65,36.04\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=0"}, {"ID": 16, "Name": "JOB_TYPE", "Memo": "1=例行工作，2=例外工作", "OrderNo": 3, "DisplayName": "作业活动类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 14, "Name": "INPUT_TIME", "OrderNo": 4, "DisplayName": "录入时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 6, "Name": "INPUT_PERSON_ID", "OrderNo": 5, "DisplayName": "录入人ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 7, "Name": "INPUT_PERSON_NAME", "OrderNo": 6, "DisplayName": "录入人", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 7, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 5, "Name": "作业活动录入", "TypeName": "GROUP", "Memo": "作业活动录入", "CreateDate": "2024-06-06 09:49:47", "OrderNo": 5, "GraphDesc": "Left=2165.41\r\nTop=901.70\r\nWidth=397.65\r\nHeight=237.65\r\nAutoSize=0\r\nBWidth=200.00\r\nBHeight=150.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 7, "Name": "JOBLIST_INPUT_MAPPING", "Caption": "作业活动录入关系表", "Memo": "用于连接内部功能模块、外部数据表结构", "CreateDate": "2024-06-06 09:58:46", "OrderNo": 6, "GraphDesc": "Left=1057.39\r\nTop=937.52", "BgColor": ********, "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 15, "Name": "MAP_TYPE", "Memo": "temp_work=临时工作，ledger=电子台账，account=核算报表，collection_point=采集点，general=常规录入", "OrderNo": 2, "DisplayName": "关系类型（根据实际业务约定）", "DataType": 1, "RelateTable": "任务督办"}, {"ID": 4, "Name": "MASTER_ID", "Memo": "逻辑相当于主表记录ID作为从表的外键（主表 : 从表 = 1 : n）", "OrderNo": 3, "DisplayName": "主表记录ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_ACTIVITYEXAMPLE", "RelateField": "ID", "GraphDesc": "P1=1231.85,803.40\r\nP2=1231.85,870.00\r\nP3=1109.66,870.00\r\nP4=1109.66,937.52\r\nHookP1=195.00,432.80\r\nHookP2=52.27,57.65\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 5, "Name": "SLAVE_ID", "Memo": "逻辑相当于主表记录ID作为从表的外键（主表 : 从表 = 1 : n）", "OrderNo": 4, "DisplayName": "从表记录ID", "DataType": 1, "RelateTable": "任务督办", "GraphDesc": "P1=1065.00,200.00\r\nP2=998.00,200.00\r\nP3=998.00,200.00\r\nP4=930.86,200.00\r\nHookP1=20.00,85.00\r\nHookP2=416.14,58.73\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 5, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 6, "Name": "INPUT_PERSON_ID", "OrderNo": 6, "DisplayName": "录入人ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 7, "Name": "INPUT_PERSON_NAME", "OrderNo": 7, "DisplayName": "录入人", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 14, "Name": "INPUT_TIME", "OrderNo": 8, "DisplayName": "录入时间", "DataType": 4, "RelateTable": "任务督办"}]}}, {"ID": 2, "Name": "工作清单设置", "TypeName": "GROUP", "Memo": "工作清单设置", "CreateDate": "2024-06-05 10:22:04", "OrderNo": 7, "GraphDesc": "Left=35.11\r\nTop=35.00\r\nWidth=926.04\r\nHeight=1105.50\r\nAutoSize=0\r\nBWidth=687.39\r\nBHeight=564.59", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 1, "Name": "JOBLIST_UNIT", "Caption": "作业单元", "Memo": "作业单元，作业活动依附在作业单元上", "CreateDate": "2024-06-05 10:21:44", "OrderNo": 8, "GraphDesc": "Left=219.81\r\nTop=332.99", "BgColor": 16116976, "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "Memo": "作业单元的子单元、作业活动要使用", "OrderNo": 1, "DisplayName": "作业单元ID", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "PID", "Memo": "父单元ID，根分类ID是root", "OrderNo": 2, "DisplayName": "父单元ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 3, "Name": "ORGID", "Memo": "作业单元所属的机构ID", "OrderNo": 3, "DisplayName": "机构ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 4, "Name": "UNITNAME", "OrderNo": 4, "DisplayName": "作业单元名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 12, "Name": "TYPEID", "Memo": "作业单元的类型ID，作业单元是管理还是操作", "OrderNo": 5, "DisplayName": "类型ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_CLASS", "RelateField": "TYPEID", "DataLength": 100, "GraphDesc": "P1=279.00,232.77\r\nP2=279.00,283.00\r\nP3=279.00,283.00\r\nP4=279.00,332.99\r\nHookP1=140.01,114.23\r\nHookP2=59.19,28.01\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 5, "Name": "MEMO", "OrderNo": 6, "DisplayName": "描述", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 13, "Name": "TMUSED", "Memo": "1 使用，0 已删除，默认使用", "OrderNo": 7, "DisplayName": "使用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 4, "Name": "JOBLIST_PERSONBIND", "Caption": "作业单元的人员绑定", "Memo": "作业单元上绑定的岗位和工区", "CreateDate": "2024-06-05 10:23:36", "OrderNo": 9, "GraphDesc": "Left=629.89\r\nTop=357.61", "BgColor": 16117492, "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "Memo": "唯一键", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "PID", "Memo": "绑定关联的作业单元ID,关联的分类ID，关联的活动ID", "OrderNo": 2, "DisplayName": "关联ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_UNIT", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=448.81,417.00\r\nP2=539.00,417.00\r\nP3=539.00,417.00\r\nP4=629.89,417.00\r\nHookP1=209.19,84.01\r\nHookP2=20.11,59.39\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 12, "Name": "ORGID", "Memo": "作业单元的所属机构ID", "OrderNo": 3, "DisplayName": "机构ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_CLASS", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=338.99,205.00\r\nP2=484.00,205.00\r\nP3=484.00,386.00\r\nP4=629.89,386.00\r\nHookP1=172.01,122.23\r\nHookP2=28.11,28.39\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 13, "Name": "BINDTYPE", "Memo": "类型：0 人员ID，1 岗位ID，2 工区ID，3 专业ID", "OrderNo": 4, "DisplayName": "绑定类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 14, "Name": "BINDID", "Memo": "BINDTYPE=0时是人员ID，BINDTYPE=1时是岗位ID，BINDTYPE=2时是工区ID，BINDTYPE=3时是专业ID", "OrderNo": 5, "DisplayName": "绑定ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_EXTPROPERTIESCONFIG", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=713.00,247.49\r\nP2=713.00,303.00\r\nP3=713.00,303.00\r\nP4=713.00,357.61\r\nHookP1=175.10,145.51\r\nHookP2=83.11,36.39\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 15, "Name": "TMUSED", "Memo": "1 使用，0 已删除，默认使用", "OrderNo": 6, "DisplayName": "使用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 6, "Name": "JOBLIST_CLASS", "Caption": "作业分类", "CreateDate": "2024-06-05 13:34:35", "OrderNo": 10, "GraphDesc": "Left=138.99\r\nTop=82.77", "BgColor": 15332070, "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "Memo": "唯一键", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 3, "Name": "TYPEID", "Memo": "作业单元的类型ID，分类是管理还是操作的。与作业单元的typeid对应", "OrderNo": 2, "DisplayName": "类型ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "PID", "Memo": "父分类ID，根分类ID是root", "OrderNo": 3, "DisplayName": "父分类ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 12, "Name": "ORGID", "Memo": "作业单元所属的机构ID", "OrderNo": 4, "DisplayName": "机构ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 4, "Name": "CNAME", "OrderNo": 5, "DisplayName": "名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 5, "Name": "MEMO", "Memo": "关键活动", "OrderNo": 6, "DisplayName": "描述", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 13, "Name": "TMUSED", "Memo": "1 使用，0 已删除，默认使用", "OrderNo": 7, "DisplayName": "使用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 9, "Name": "JOBLIST_EXTPROPERTIESCONFIG", "Caption": "专业表", "CreateDate": "2024-06-06 09:05:30", "OrderNo": 11, "GraphDesc": "Left=537.90\r\nTop=81.49", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "专业ID", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "PID", "Memo": "根是root", "OrderNo": 2, "DisplayName": "父专业ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 3, "Name": "ORGID", "Memo": "专业所属的机构ID，system是租户内通用的默认分类 ，如果机构有自己的专业就用自己的", "OrderNo": 3, "DisplayName": "机构ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 8, "Name": "PTYPE", "Memo": "0 专业，1 优先级，2频次，3 规程", "OrderNo": 4, "DisplayName": "类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 4, "Name": "PNAME", "OrderNo": 5, "DisplayName": "名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 5, "Name": "MEMO", "OrderNo": 6, "DisplayName": "描述", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 9, "Name": "FREQUENCYTYPE", "Memo": "0 单次 1 重复", "OrderNo": 7, "DisplayName": "频次类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 13, "Name": "TMUSED", "Memo": "1 使用，0 已删除，默认使用", "OrderNo": 8, "DisplayName": "使用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 9, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 12, "Name": "JOBLIST_ACTIVITYPROPERTIES", "Caption": "活动属性", "CreateDate": "2024-06-06 15:05:43", "OrderNo": 12, "GraphDesc": "Left=68.39\r\nTop=556.02\r\nWidth=328.00\r\nHeight=530.00\r\nAutoSize=0", "BgColor": 15397584, "MetaFields": {"Count": 32, "items": [{"ID": 1, "Name": "ID", "Memo": "这个ID也是核算对象的ID", "OrderNo": 1, "DisplayName": "活动ID", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 100, "GraphDesc": "P1=609.52,744.00\r\nP2=524.00,744.00\r\nP3=524.00,744.00\r\nP4=438.66,744.00\r\nHookP1=20.48,146.94\r\nHookP2=261.34,168.17\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "ORGID", "Memo": "作业活动所属的机构ID", "OrderNo": 2, "DisplayName": "机构ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 14, "Name": "ACTIVITYTYPE", "Memo": "目前仅有固定工作和日周作业，来源于数据字典", "OrderNo": 3, "DisplayName": "活动类型", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "CLASSID", "Memo": "活动所属分类的ID", "OrderNo": 4, "DisplayName": "分类ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_CLASS", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=191.00,232.77\r\nP2=191.00,394.00\r\nP3=208.39,394.00\r\nP4=208.39,556.02\r\nHookP1=52.01,130.23\r\nHookP2=140.00,235.00\r\nMod_OP1=0\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 12, "Name": "STATUSID", "OrderNo": 5, "DisplayName": "工况ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 4, "Name": "PRIORITYID", "OrderNo": 6, "DisplayName": "优先级ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 5, "Name": "RESPONSIBILITYTYPE", "Memo": "0 班组，1 岗位，默认班组", "OrderNo": 7, "DisplayName": "责任对象类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 6, "Name": "AVAILABLECOUNT", "Memo": "默认是0", "OrderNo": 8, "DisplayName": "领取次数", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 19, "Name": "FREQUENCYID", "Memo": "绑定的频次ID", "OrderNo": 9, "DisplayName": "频次ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "CYCLE_SCHEME", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=586.46,956.00\r\nP2=491.00,956.00\r\nP3=491.00,956.00\r\nP4=396.39,956.00\r\nHookP1=19.54,83.16\r\nHookP2=283.61,399.98\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 7, "Name": "ISFIXTIME", "Memo": "0 不是固定时间，1 是固定时间，默认是时间", "OrderNo": 10, "DisplayName": "固定时间", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 8, "Name": "FIXTIME", "Memo": "文本形式的时间，格式是hh:mm:ss", "OrderNo": 11, "DisplayName": "固定时间点", "DataType": 1, "RelateTable": "任务督办", "DataLength": 10}, {"ID": 9, "Name": "RELATIVETYPE", "Memo": "0 上班之前 1 上班之后 2下班之前 3 下班之后，默认是1", "OrderNo": 12, "DisplayName": "相对时间类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 10, "Name": "RELATIVETIME", "Memo": "单位是分钟，默认是0", "OrderNo": 13, "DisplayName": "相对时间", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "BEGINDATE", "Memo": "文本形式的日期，格式是yyyy-mm-dd", "OrderNo": 14, "DisplayName": "活动开始日期", "DataType": 1, "RelateTable": "任务督办", "DataLength": 10}, {"ID": 12, "Name": "ENDDATE", "Memo": "文本形式的日期，格式是yyyy-mm-dd", "OrderNo": 15, "DisplayName": "活动结束日期", "DataType": 1, "RelateTable": "任务督办", "DataLength": 10}, {"ID": 13, "Name": "NOENDDATE", "Memo": "0 活动有结束期限，1 活动永久有效，默认是1，此时不能修改结束日期", "OrderNo": 16, "DisplayName": "无限期", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 15, "Name": "JOBREQUIREMENT", "OrderNo": 17, "DisplayName": "工作要求", "DataType": 1, "RelateTable": "任务督办", "DataLength": 4000}, {"ID": 16, "Name": "CONFIRMTYPE", "Memo": "0 自动确认 1 手工确认，默认是自动确认", "OrderNo": 18, "DisplayName": "确认方式", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 18, "Name": "CALCTPYE", "Memo": "0 给定分值 1给定权重，默认是给定分值", "OrderNo": 19, "DisplayName": "分值计算方式", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 28, "Name": "CALCSCHEMEID", "OrderNo": 20, "DisplayName": "分解方案ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 17, "Name": "BASICSCORE", "Memo": "先给定2位小数，给定分值时不可填写", "OrderNo": 21, "DisplayName": "基本分", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 20, "Name": "STANDARDDURATION", "Memo": "标准时长，2位小数", "OrderNo": 22, "DisplayName": "标准时长", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 21, "Name": "TIMEUNIT", "Memo": "0 秒 1 分 2 小时 3 天", "OrderNo": 23, "DisplayName": "标准时长单位", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 22, "Name": "RECORDTYPE", "Memo": "从数据字典读取来源", "OrderNo": 24, "DisplayName": "记录来源", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 23, "Name": "RECORDID", "OrderNo": 25, "DisplayName": "绑定的记录ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 25, "Name": "STARTINGCONDITIONID", "OrderNo": 26, "DisplayName": "开始条件ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 24, "Name": "ENDCONDITIONID", "OrderNo": 27, "DisplayName": "结束条件ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 26, "Name": "PREPLANID", "OrderNo": 28, "DisplayName": "预案ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 27, "Name": "REGULATIONID", "OrderNo": 29, "DisplayName": "操作规程ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 30, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 31, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 32, "Name": "UNITID", "OrderNo": 32, "DisplayName": "作业单元ID", "DataType": 1, "DataLength": 100}]}}, {"ID": 8, "Name": "TMTASK_INFO_MAIN", "Caption": "督办信息主表", "Memo": "督办基础信息", "OrderNo": 13, "GraphDesc": "Left=89.64\r\nTop=1261.98", "MetaFields": {"Count": 71, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1562.35,1191.78\r\nP2=724.00,1191.78\r\nP3=724.00,1290.00\r\nP4=622.64,1290.00\r\nHookP1=124.00,88.82\r\nHookP2=505.36,28.02\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 49, "Name": "FLOW_ID", "OrderNo": 2, "DisplayName": "流程标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 12, "Name": "FLOW_VERSION", "OrderNo": 3, "DisplayName": "流程版本ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 50, "Name": "STEP_NUM", "OrderNo": 4, "DisplayName": "步骤数", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 51, "Name": "STEP_CURR", "OrderNo": 5, "DisplayName": "步骤当前位置", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 14, "Name": "TEMPLATE_MARK", "OrderNo": 6, "DisplayName": "是否模板", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 52, "Name": "TEMPLATE_ID", "OrderNo": 7, "DisplayName": "模板id", "DataType": 1, "RelateTable": "任务督办", "DataLength": 500}, {"ID": 53, "Name": "PARENT_ID", "OrderNo": 8, "DisplayName": "父id(分解来源)", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 51, "Name": "EID", "OrderNo": 9, "DisplayName": "延期数据ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 45, "Name": "NODE_MARK", "OrderNo": 10, "DisplayName": "是否阶段反馈任务1是0否", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 47, "Name": "STEP_INFO", "OrderNo": 11, "DisplayName": "所有步骤信息字符串", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 48, "Name": "APPLY_MARK", "OrderNo": 12, "DisplayName": "应用状态标识0无1应用中", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 16, "Name": "TITLE", "OrderNo": 13, "DisplayName": "标题", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 17, "Name": "CONTENT", "OrderNo": 14, "DisplayName": "内容", "DataType": 1, "RelateTable": "任务督办"}, {"ID": 18, "Name": "COMBO1", "OrderNo": 15, "DisplayName": "任务类型", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 13, "Name": "COMBO2", "OrderNo": 16, "DisplayName": "任务级别", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 14, "Name": "COMBO3", "OrderNo": 17, "DisplayName": "重要程度", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 15, "Name": "COMBO4", "OrderNo": 18, "DisplayName": "任务出处", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 49, "Name": "COMBO5", "OrderNo": 19, "DisplayName": "选择1", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 50, "Name": "COMBO6", "OrderNo": 20, "DisplayName": "选择2", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 16, "Name": "CTIME", "OrderNo": 21, "DisplayName": "创建时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 17, "Name": "PUBLISH_TIME", "OrderNo": 22, "DisplayName": "发布时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 18, "Name": "START_TIME", "OrderNo": 23, "DisplayName": "开始时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 19, "Name": "END_TIME", "OrderNo": 24, "DisplayName": "截止时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 52, "Name": "RESOLVE_END_TIME", "OrderNo": 25, "DisplayName": "分解截至日期", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 46, "Name": "PERIOD_MARK", "OrderNo": 26, "DisplayName": "周期类型1日2周3旬4月5季6半年7年0自定义", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 44, "Name": "TASK_PERIOD", "OrderNo": 27, "DisplayName": "任务周期", "DataType": 1, "RelateTable": "任务督办", "DataLength": 300}, {"ID": 36, "Name": "COMPLETE_MARK", "OrderNo": 28, "DisplayName": "完成标识2完成1未完成", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 47, "Name": "END_MARK", "OrderNo": 29, "DisplayName": "任务结束标识0未结束1销项2结束", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 37, "Name": "OVERTIME_MARK", "OrderNo": 30, "DisplayName": "超期标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 38, "Name": "ASSESS_TYPE", "OrderNo": 31, "DisplayName": "考核类型1分2奖", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 46, "Name": "ASSESS_MODE", "OrderNo": 32, "DisplayName": "考核模式1单2共同", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 39, "Name": "BASIC_ASSESS_VAL", "OrderNo": 33, "DisplayName": "基础考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 40, "Name": "ASSESS_VAL", "OrderNo": 34, "DisplayName": "考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 42, "Name": "OVERTIME_VAL", "OrderNo": 35, "DisplayName": "超期考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 41, "Name": "EVALUATE_VAL", "OrderNo": 36, "DisplayName": "评价值（平均）", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 43, "Name": "CREATE_ORG_CODE", "OrderNo": 37, "DisplayName": "创建机构", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 44, "Name": "CREATE_JOB_CODE", "OrderNo": 38, "DisplayName": "创建岗位", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 45, "Name": "CREATE_USER_CODE", "OrderNo": 39, "DisplayName": "创建人员", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 43, "Name": "CREATE_USER_NAME", "OrderNo": 40, "DisplayName": "创建人", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 32, "Name": "EXT_INFO1", "OrderNo": 41, "DisplayName": "扩展内容1", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 33, "Name": "EXT_INFO2", "OrderNo": 42, "DisplayName": "扩展内容2", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 34, "Name": "EXT_INFO3", "OrderNo": 43, "DisplayName": "扩展内容3", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 35, "Name": "EXT_INFO4", "OrderNo": 44, "DisplayName": "扩展内容4", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 49, "Name": "EXT_INFO5", "OrderNo": 45, "DisplayName": "扩展内容5", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 50, "Name": "EXT_INFO6", "OrderNo": 46, "DisplayName": "扩展内容6", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 51, "Name": "EXT_INFO7", "OrderNo": 47, "DisplayName": "扩展内容7", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 52, "Name": "EXT_INFO8", "OrderNo": 48, "DisplayName": "扩展内容8", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 53, "Name": "EXT_INFO9", "OrderNo": 49, "DisplayName": "扩展内容9", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 56, "Name": "EXT_INFO10", "OrderNo": 50, "DisplayName": "扩展内容10", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 19, "Name": "TMSORT", "OrderNo": 51, "DisplayName": "排序号", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 20, "Name": "TMUSED", "OrderNo": 52, "DisplayName": "是否使用", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 53, "Name": "ACTUAL_END_TIME", "OrderNo": 53, "DisplayName": "任务结束时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 54, "Name": "OVER_DAYS", "OrderNo": 54, "DisplayName": "超期天数，默认0", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 55, "Name": "STEP_PUBLISH_MARK", "OrderNo": 55, "DisplayName": "发布标识，1待发布2已发布", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 56, "Name": "STEP_PUBLISH_USER_CODE", "OrderNo": 56, "DisplayName": "发布人代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 57, "Name": "STEP_PUBLISH_TIME", "OrderNo": 57, "DisplayName": "发布时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 58, "Name": "THEME_ID", "OrderNo": 58, "DisplayName": "关联主题", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 60, "Name": "PERIOD_TYPE", "OrderNo": 59, "DisplayName": "周期类型1日2周3月4季度5半年6年", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 61, "Name": "PERIOD_START", "OrderNo": 60, "DisplayName": "周期开始", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 59, "Name": "PERIOD_END", "OrderNo": 61, "DisplayName": "周期截止", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 63, "Name": "LIMIT_MARK", "OrderNo": 62, "DisplayName": "有效标识1永久0自定义", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 64, "Name": "LIMIT_START_DAY", "OrderNo": 63, "DisplayName": "有效开始日期", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 65, "Name": "LIMIT_END_DAY", "OrderNo": 64, "DisplayName": "有效截至日期", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 62, "Name": "PERIOD_USED", "OrderNo": 65, "DisplayName": "周期启用0未启用1启用", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 66, "Name": "PERIOD_PUBLISH_TIME", "OrderNo": 66, "DisplayName": "周期任务最后发布时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 67, "Name": "PERIOD_UPDATE_TIME", "OrderNo": 67, "DisplayName": "周期任务最后修改时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 69, "Name": "INDEX_ID", "OrderNo": 68, "DisplayName": "关联指标ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 68, "Name": "INDEX_NAME", "OrderNo": 69, "DisplayName": "关联指标名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 70, "Name": "HAVE_FEED_WEEK", "OrderNo": 70, "DisplayName": "是否有周反馈", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 71, "Name": "ORG_ACC_MARK", "OrderNo": 71, "DisplayName": "机构任务接收标识1任务下达到机构", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 10, "Name": "TMTASK_INFO_DEGREE", "Caption": "督办信息处理表", "Memo": "审核、审批、反馈、确认、评价等", "OrderNo": 14, "GraphDesc": "Left=746.48\r\nTop=1264.11", "BgColor": 16117236, "MetaFields": {"Count": 56, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_INPUT_MAPPING", "RelateField": "SLAVE_ID", "DataLength": 50, "GraphDesc": "P1=1101.78,1087.52\r\nP2=1101.78,1176.00\r\nP3=990.48,1176.00\r\nP4=990.48,1264.11\r\nHookP1=44.39,59.34\r\nHookP2=244.00,244.32\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 36, "Name": "FLOW_ID", "OrderNo": 2, "DisplayName": "流程标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 34, "Name": "FLOW_VERSION", "OrderNo": 3, "DisplayName": "流程版本标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 37, "Name": "STEP_ID", "OrderNo": 4, "DisplayName": "步骤标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 35, "Name": "STEP_VERSION", "OrderNo": 5, "DisplayName": "步骤版本标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 14, "Name": "STEP_TYPE", "OrderNo": 6, "DisplayName": "步骤类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 26, "Name": "STEP_NO", "OrderNo": 7, "DisplayName": "步骤序号", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 41, "Name": "STEP_NAME", "OrderNo": 8, "DisplayName": "步骤名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 38, "Name": "INFO_ID", "OrderNo": 9, "DisplayName": "主信息标识", "DataType": 1, "KeyFieldType": 3, "RelateTable": "TMTASK_INFO_MAIN", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=681.64,1547.38\r\nP2=714.00,1547.38\r\nP3=714.00,1674.00\r\nP4=746.48,1674.00\r\nHookP1=288.80,285.40\r\nHookP2=27.52,409.89\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 44, "Name": "HAVE_EXT", "OrderNo": 10, "DisplayName": "是否进行过延期0无1有", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 51, "Name": "EXT_NUM", "OrderNo": 11, "DisplayName": "延期申请次数，默认0", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 46, "Name": "OVER_EXT_MARK", "OrderNo": 12, "DisplayName": "延期超期标识0无1是", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 47, "Name": "OVER_EXT_VAL", "OrderNo": 13, "DisplayName": "延期超期考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 45, "Name": "HAVE_RESOLVE", "OrderNo": 14, "DisplayName": "是否进行过分解0无1有", "DataType": 1, "RelateTable": "任务督办"}, {"ID": 43, "Name": "HAVE_CALLBACK", "OrderNo": 15, "DisplayName": "是否分解回调0无1有", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 48, "Name": "SHOW_STATUS", "OrderNo": 16, "DisplayName": "是否显示处理状态1显示0不显示", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 40, "Name": "DEGREE_MODE", "OrderNo": 17, "DisplayName": "处理方式1需要处理0不需要处理", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 15, "Name": "DEGREE_STATUS", "OrderNo": 18, "DisplayName": "处理标识状态-1被否决0未处理1通过", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 21, "Name": "DEGREE_ID", "OrderNo": 19, "DisplayName": "处理标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 16, "Name": "DEGREE_NAME", "OrderNo": 20, "DisplayName": "处理状态名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 31, "Name": "DEGREE_TYPE", "OrderNo": 21, "DisplayName": "处理类型1人3机构", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 28, "Name": "DEGREE_MARK", "OrderNo": 22, "DisplayName": "处理标识1主办2协办3协助", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 17, "Name": "DEGREE_ORG_CODE", "OrderNo": 23, "DisplayName": "机构代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 18, "Name": "DEGREE_ORG_NAME", "OrderNo": 24, "DisplayName": "机构名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 22, "Name": "DEGREE_JOB_CODE", "OrderNo": 25, "DisplayName": "岗位代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 23, "Name": "DEGREE_JOB_NAME", "OrderNo": 26, "DisplayName": "岗位名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 24, "Name": "DEGREE_USER_CODE", "OrderNo": 27, "DisplayName": "人员代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 25, "Name": "DEGREE_USER_NAME", "OrderNo": 28, "DisplayName": "人员名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 26, "Name": "DEGREE_TIME", "OrderNo": 29, "DisplayName": "最后处理时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 27, "Name": "DEGREE_INFO", "OrderNo": 30, "DisplayName": "处理说明（文本编辑器）", "DataType": 1, "RelateTable": "任务督办", "DataLength": 8000}, {"ID": 28, "Name": "DEGREE_CONTENT", "OrderNo": 31, "DisplayName": "处理说明（文本域）", "DataType": 1, "RelateTable": "任务督办", "DataLength": 8000}, {"ID": 32, "Name": "EVALUATE_SELECT", "OrderNo": 32, "DisplayName": "评价选择", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 35, "Name": "EVALUATE_SELECT_NAME", "OrderNo": 33, "DisplayName": "评价选择名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 33, "Name": "EVALUATE_SCORE", "OrderNo": 34, "DisplayName": "评价分", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 34, "Name": "EVALUATE_BONUS", "OrderNo": 35, "DisplayName": "评价奖", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 29, "Name": "OVER_MARK", "OrderNo": 36, "DisplayName": "超期标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 32, "Name": "ASSESS_MODE", "OrderNo": 37, "DisplayName": "考核模式1分2奖", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 33, "Name": "ASSESS_VAL", "OrderNo": 38, "DisplayName": "工作考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 42, "Name": "OVER_CATCH_DAYS", "OrderNo": 39, "DisplayName": "超期反馈延期天数", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 52, "Name": "OVER_FEEDBACK_MARK", "OrderNo": 40, "DisplayName": "超期反馈标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 34, "Name": "OVERTIME_VAL", "OrderNo": 41, "DisplayName": "超期反馈考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 49, "Name": "OVER_RESOLVE_MARK", "OrderNo": 42, "DisplayName": "超期分解标识1超0无", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 50, "Name": "OVER_RESOLVE_VAL", "OrderNo": 43, "DisplayName": "超期分解考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 35, "Name": "EVALUATE_VAL", "OrderNo": 44, "DisplayName": "评价值（平均）", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 29, "Name": "EXT_INFO1", "OrderNo": 45, "DisplayName": "备用说明1", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 30, "Name": "EXT_INFO2", "OrderNo": 46, "DisplayName": "备用说明2", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 36, "Name": "EXT_INFO3", "OrderNo": 47, "DisplayName": "备用说明3", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 37, "Name": "EXT_INFO4", "OrderNo": 48, "DisplayName": "备用说明4", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 38, "Name": "EXT_INFO5", "OrderNo": 49, "DisplayName": "备用说明5", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 39, "Name": "EXT_INFO6", "OrderNo": 50, "DisplayName": "备用说明6", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 53, "Name": "FEEDBACK_ASSESS_ID", "OrderNo": 51, "DisplayName": "超期反馈考核库ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 55, "Name": "RESOLVE_ASSESS_ID", "OrderNo": 52, "DisplayName": "超期分解考核库ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 54, "Name": "ASSESS_ID", "OrderNo": 53, "DisplayName": "考核库ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 19, "Name": "TMSORT", "OrderNo": 54, "DisplayName": "排序号", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 20, "Name": "TMUSED", "OrderNo": 55, "DisplayName": "是否使用", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 56, "Name": "DEGREE_ACC_MARK", "OrderNo": 56, "DisplayName": "只下达到机构标识1是", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 15, "Name": "任务督办", "TypeName": "GROUP", "Memo": "任务督办", "CreateDate": "2024-06-27 08:14:38", "OrderNo": 15, "GraphDesc": "Left=5.13\r\nTop=1215.68\r\nWidth=1261.59\r\nHeight=901.02\r\nAutoSize=0\r\nBWidth=630.80\r\nBHeight=450.51", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 13, "Name": "CYCLE_SCHEME", "Caption": "周期方案", "CreateDate": "2024-06-24 09:49:39", "OrderNo": 16, "GraphDesc": "Left=586.46\r\nTop=872.84", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 100}, {"ID": 13, "Name": "ORGID", "Memo": "方案的机构ID，system为系统内置方案，所有机构可见", "OrderNo": 2, "DisplayName": "机构ID", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "SNAME", "OrderNo": 3, "DisplayName": "方案名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "MEMO", "OrderNo": 4, "DisplayName": "描述", "DataType": 1, "DataLength": 2000}, {"ID": 7, "Name": "ISCRON", "Memo": "0 不是，1 是", "OrderNo": 5, "DisplayName": "cyclestr是cron表达式", "DataType": 2}, {"ID": 8, "Name": "CYCLESTR", "Memo": "cron表达式或周期设置的json", "OrderNo": 6, "DisplayName": "表达式", "DataType": 1, "DataLength": 4000}, {"ID": 15, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 7, "DisplayName": "可用标识", "DataType": 2}, {"ID": 16, "Name": "TMSORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2}, {"ID": 9, "Name": "BINDCYCLE", "Memo": "1、班；2、日；3、周；4、月；5、季；6、年", "OrderNo": 9, "DisplayName": "周期类型", "DataType": 2}]}}, {"ID": 5, "Name": "常规活动反馈", "TypeName": "GROUP", "Memo": "常规活动反馈", "CreateDate": "2024-06-06 09:49:47", "OrderNo": 17, "GraphDesc": "Left=1511.51\r\nTop=1186.77\r\nWidth=408.24\r\nHeight=234.12\r\nAutoSize=0\r\nBWidth=200.00\r\nBHeight=150.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 19, "Name": "JOBLIST_GENERAL_FEEDBACK", "Caption": "常规活动反馈", "CreateDate": "2024-07-10 14:45:24", "OrderNo": 18, "GraphDesc": "Left=1557.51\r\nTop=1241.77\r\nWidth=294.00\r\nHeight=152.00\r\nAutoSize=0", "BgColor": 15660524, "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_INPUT_MAPPING", "RelateField": "SLAVE_ID", "DataLength": 50, "GraphDesc": "P1=1441.15,1087.52\r\nP2=1441.15,1262.00\r\nP3=1441.15,1262.00\r\nP4=1557.51,1262.00\r\nHookP1=383.76,83.00\r\nHookP2=19.88,20.23\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 15, "Name": "JOB_ID", "OrderNo": 2, "DisplayName": "作业活动ID", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=1248.52,800.00\r\nP2=1522.00,800.00\r\nP3=1522.00,1600.00\r\nP4=2148.00,1600.00\r\nHookP1=305.48,233.94\r\nHookP2=28.00,28.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "INPUT_TIME", "OrderNo": 3, "DisplayName": "反馈时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 6, "Name": "INPUT_PERSON_ID", "OrderNo": 4, "DisplayName": "反馈人ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 7, "Name": "INPUT_PERSON_NAME", "OrderNo": 5, "DisplayName": "反馈人", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 7, "Name": "ACTIVITY_STATUS", "Memo": "0=未开始，1=进行中，2=已完成", "OrderNo": 6, "DisplayName": "活动完成状态", "DataType": 2}, {"ID": 8, "Name": "FEEDBACK_CONTENT", "OrderNo": 7, "DisplayName": "反馈内容", "DataType": 1, "DataLength": 4000}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 8, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 20, "Name": "ACCTOBJ_INPUT_FL", "Caption": "核算对象录入采集点分类表", "CreateDate": "2024-07-22 16:11:13", "OrderNo": 19, "GraphDesc": "Left=1838.31\r\nTop=1731.72", "BgColor": 16116977, "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=2428.00,1224.66\r\nP2=2428.00,1499.00\r\nP3=2428.00,1499.00\r\nP4=3083.00,1499.00\r\nHookP1=416.39,74.34\r\nHookP2=20.00,20.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=1"}, {"ID": 15, "Name": "BA_ID", "Memo": "BA：Business Activity", "OrderNo": 2, "DisplayName": "业务活动ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "ACCTOBJ_ID", "OrderNo": 3, "DisplayName": "核算对象ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "IPT_ID", "Memo": "ACCTOBJ_INPUT 表的 ID", "OrderNo": 4, "DisplayName": "录入主表ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "ACCTOBJ_INPUT", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1724.66,1802.68\r\nP2=1781.00,1802.68\r\nP3=1781.00,1815.00\r\nP4=1838.31,1815.00\r\nHookP1=156.00,147.00\r\nHookP2=19.69,83.28\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "INPUT_TIME", "OrderNo": 5, "DisplayName": "录入时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 13, "Name": "FL_ID", "OrderNo": 6, "DisplayName": "分类ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 5, "Name": "FL_NAME", "OrderNo": 7, "DisplayName": "分类名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 8, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 9, "Name": "SN", "OrderNo": 9, "DisplayName": "序号", "DataType": 2, "RelateTable": "任务督办"}]}}, {"Name": "JOBLIST_ACTIVITYEXAMPLE", "Caption": "活动实例表", "OrderNo": 20, "GraphDesc": "Left=1036.85\r\nTop=342.48\r\nWidth=373.44\r\nHeight=460.92\r\nAutoSize=0", "BgColor": 13427923, "MetaFields": {"Count": 27, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "活动实例ID", "DataType": 1, "KeyFieldType": 1, "RelateTable": "OPERCARD_CATALOG_EXEC", "RelateField": "WORKID", "DataLength": 50, "GraphDesc": "P1=4364.00,585.00\r\nP2=4364.00,23.95\r\nP3=1191.85,23.95\r\nP4=1191.85,342.48\r\nHookP1=180.00,59.00\r\nHookP2=155.00,198.00\r\nMod_OP1=0\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}, {"ID": 19, "Name": "PID", "Memo": "当此id为空时代表当前任务是主任务，反之为子任务", "OrderNo": 2, "DisplayName": "父任务ID", "DataType": 1, "DataLength": 100}, {"ID": 9, "Name": "ACTIVITY_ID", "Memo": "活动属性表ID", "OrderNo": 3, "DisplayName": "活动配置ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_ACTIVITYPROPERTIES", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=396.39,680.00\r\nP2=717.00,680.00\r\nP3=717.00,680.00\r\nP4=1036.85,680.00\r\nHookP1=299.61,123.98\r\nHookP2=28.15,337.52\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 8, "Name": "ACTIVITY_DATE", "OrderNo": 4, "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "ACTIVITY_STATUS", "Memo": "活动状态：0=未开始，1=进行中，2=已完成，-1=未完成", "OrderNo": 5, "DisplayName": "活动实例状态", "DataType": 2}, {"ID": 11, "Name": "BEGIN_DATE", "Memo": "查询接口需要提供的时间符合此时间范围", "OrderNo": 6, "DisplayName": "活动实例的开始时间", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "DELIVER_DATE", "OrderNo": 7, "DisplayName": "分配时间", "DataType": 1, "DataLength": 100}, {"ID": 13, "Name": "END_DATE", "Memo": "查询接口需要提供的时间符合此时间范围", "OrderNo": 8, "DisplayName": "活动实例的结束时间", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "FINISH_DATE", "OrderNo": 9, "DisplayName": "完成时间", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "IS_PARENT", "Memo": "1 父记录 0子记录", "OrderNo": 10, "DisplayName": "是否为父活动记录", "DataType": 2}, {"ID": 16, "Name": "MEMO", "OrderNo": 11, "DisplayName": "任务备注", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "ORG_CODE", "OrderNo": 12, "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "P_ORG_CODE", "OrderNo": 13, "DataType": 1, "DataLength": 100}, {"ID": 20, "Name": "RECORD_ID", "OrderNo": 14, "DataType": 1, "DataLength": 100}, {"ID": 21, "Name": "RESPONSIBLE_TYPE", "OrderNo": 15, "DataType": 2}, {"ID": 22, "Name": "RESPONSIBLE_PERSON_ID", "OrderNo": 16, "DisplayName": "活动负责人id", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "RESPONSIBLE_PERSON_NAME", "OrderNo": 17, "DisplayName": "活动负责人姓名", "DataType": 1, "DataLength": 100}, {"ID": 24, "Name": "SBSJ", "OrderNo": 18, "DataType": 1, "DataLength": 100}, {"ID": 29, "Name": "XBSJ", "OrderNo": 19, "DataType": 1, "DataLength": 100}, {"ID": 25, "Name": "SHIFT_CLASS_CODE", "OrderNo": 20, "DataType": 1, "DataLength": 100}, {"ID": 26, "Name": "STANDARD_DURATION", "Memo": "与活动属性表一致", "OrderNo": 21, "DisplayName": "标准时长", "DataType": 3}, {"ID": 30, "Name": "JOBREQUIREMENT", "OrderNo": 22, "DataType": 1, "DataLength": 4000}, {"ID": 31, "Name": "RECORDTYPE", "OrderNo": 23, "DataType": 1, "DataLength": 100}, {"ID": 32, "Name": "FREQUENCY_TYPE", "OrderNo": 24, "DataType": 2}, {"ID": 33, "Name": "JOB_NO", "OrderNo": 25, "DataType": 2}, {"ID": 28, "Name": "TMUSED", "OrderNo": 26, "DisplayName": "是否启用", "DataType": 2}, {"ID": 27, "Name": "TMSORT", "OrderNo": 27, "DisplayName": "排序", "DataType": 2}]}}, {"Name": "JOBLIST_CONFIRM", "Caption": "活动确认数据表", "OrderNo": 21, "GraphDesc": "Left=1511.21\r\nTop=360.40", "MetaFields": {"Count": 14, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_BY_ORG", "OrderNo": 3, "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "CREATE_BY_POST", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "CREATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "UPDATE_BY", "OrderNo": 6, "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "UPDATE_TIME", "OrderNo": 7, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 8, "Name": "ACTIVITY_FEED_CONTENT", "OrderNo": 8, "DataType": 1, "DataLength": 100}, {"ID": 9, "Name": "ACTIVITY_INSTANCE_ID", "OrderNo": 9, "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_ACTIVITYEXAMPLE", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=1410.29,498.48\r\nP2=1461.00,498.48\r\nP3=1461.00,520.60\r\nP4=1511.21,520.60\r\nHookP1=337.37,156.00\r\nHookP2=152.00,160.20\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 10, "Name": "ACTIVITY_NAME", "OrderNo": 10, "DataType": 1, "DataLength": 100}, {"ID": 11, "Name": "ACTIVITY_PROPERTIES_ID", "OrderNo": 11, "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "ACTIVITY_STATUS", "OrderNo": 12, "DataType": 2}, {"ID": 13, "Name": "IS_GET_SCORE", "OrderNo": 13, "DataType": 2}, {"ID": 14, "Name": "UPDATA_TYPE", "OrderNo": 14, "DataType": 2}]}}, {"Name": "JOBLIST_EXAMPLE_DUTYPERSON", "Caption": "活动责任人、反馈人数据表", "OrderNo": 22, "GraphDesc": "Left=1484.65\r\nTop=642.09", "MetaFields": {"Count": 15, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_BY_ORG", "OrderNo": 3, "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "CREATE_BY_POST", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "CREATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "UPDATE_BY", "OrderNo": 6, "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "UPDATE_TIME", "OrderNo": 7, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 8, "Name": "ACTIVITY_EXAMPLE_ID", "OrderNo": 8, "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_ACTIVITYEXAMPLE", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=1410.29,723.00\r\nP2=1447.00,723.00\r\nP3=1447.00,723.00\r\nP4=1484.65,723.00\r\nHookP1=321.15,380.52\r\nHookP2=20.35,80.91\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 9, "Name": "ACTIVITY_PROPERTIES_ID", "OrderNo": 9, "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "PERSON_ID", "OrderNo": 10, "DataType": 1, "DataLength": 100}, {"ID": 11, "Name": "PERSON_NAME", "OrderNo": 11, "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "POST_ID", "OrderNo": 12, "DataType": 1, "DataLength": 100}, {"ID": 13, "Name": "SCORE", "OrderNo": 13, "DataType": 3}, {"ID": 14, "Name": "TMSORT", "OrderNo": 14, "DataType": 2}, {"ID": 15, "Name": "PERSON_TYPE", "Memo": "0=责任人，1=反馈人", "OrderNo": 15, "DataType": 2}]}}, {"ID": 21, "Name": "活动实例数据", "TypeName": "GROUP", "Memo": "活动实例数据", "CreateDate": "2024-08-05 09:49:40", "OrderNo": 23, "GraphDesc": "Left=993.66\r\nTop=285.75\r\nWidth=1101.85\r\nHeight=848.76\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 22, "Name": "JOBLIST_UPLOAD", "Caption": "活动图片上传", "CreateDate": "2024-08-21 16:00:32", "OrderNo": 24, "GraphDesc": "Left=1995.00\r\nTop=1293.00", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1297.00\r\nP3=1343.00,1297.00\r\nP4=1952.00,1297.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 2, "Name": "MASTERID", "Memo": "任意主数据ID", "OrderNo": 2, "DisplayName": "父编号", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_GENERAL_FEEDBACK", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1851.51,1343.00\r\nP2=1923.00,1343.00\r\nP3=1923.00,1343.00\r\nP4=1995.00,1343.00\r\nHookP1=266.49,101.23\r\nHookP2=20.00,50.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 15, "Name": "DATA_TYPE", "Memo": "general=常规活动反馈", "OrderNo": 3, "DisplayName": "主数据类型", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "IMG_ID", "OrderNo": 4, "DisplayName": "图片ID", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 5, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 6, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 23, "Name": "WI_DATA", "Caption": "工作指令数据表", "Memo": "Work Instruction：工作指令", "CreateDate": "2025-01-21 09:25:33", "OrderNo": 25, "GraphDesc": "Left=2860.32\r\nTop=558.62", "BgColor": 15918813, "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1256.95\r\nP3=1343.00,1256.95\r\nP4=2674.35,1256.95\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 12, "Name": "WI_LIB_ID", "OrderNo": 2, "DisplayName": "指令库ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_LIB", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3008.00,293.53\r\nP2=3008.00,426.00\r\nP3=2928.00,426.00\r\nP4=2928.00,558.62\r\nHookP1=89.70,114.47\r\nHookP2=67.68,36.38\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 5, "Name": "WI_NAME", "OrderNo": 3, "DisplayName": "工作指令名称", "DataType": 1, "DataLength": 200}, {"ID": 6, "Name": "WI_CONTENT", "OrderNo": 4, "DisplayName": "工作指令内容", "DataType": 1, "DataLength": 4000}, {"ID": 7, "Name": "WI_TYPE", "Memo": "prod=生产指令，oper=操作指令", "OrderNo": 5, "DisplayName": "指令类型", "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "START_TIME", "OrderNo": 6, "DisplayName": "开始时间", "DataType": 4}, {"ID": 10, "Name": "ENDT_TIME", "OrderNo": 7, "DisplayName": "截止时间", "DataType": 4}, {"ID": 10, "Name": "OPCARD_ID", "OrderNo": 8, "DisplayName": "操作卡ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPCARD_SETTING", "RelateField": "ID", "GraphDesc": "P1=3845.53,584.00\r\nP2=3471.00,584.00\r\nP3=3471.00,587.00\r\nP4=3097.32,587.00\r\nHookP1=20.47,28.36\r\nHookP2=208.68,28.38\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 11, "Name": "IS_AUDIT", "Memo": "1=需要审核，其它=不审核", "OrderNo": 9, "DisplayName": "是否审核", "DataType": 2}, {"ID": 6, "Name": "AUDIT_STATUS", "Memo": "0=未审核，1=审核通过，-1=审核否决", "OrderNo": 10, "DisplayName": "审核状态", "DataType": 2}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 11, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 12, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 25, "Name": "WI_ACCEPT_DATA", "Caption": "工作指令接收机构", "CreateDate": "2025-01-21 11:12:38", "OrderNo": 26, "GraphDesc": "Left=3340.47\r\nTop=629.67", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1482.00\r\nP3=1343.00,1482.00\r\nP4=3137.00,1482.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 7, "Name": "PROCESS_DATA_ID", "OrderNo": 2, "DisplayName": "流程数据ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_PROCESS_DATA", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3072.97,1038.60\r\nP2=3249.47,1038.60\r\nP3=3249.47,714.39\r\nP4=3340.47,714.39\r\nHookP1=118.00,51.00\r\nHookP2=20.16,84.72\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "WI_DATA_ID", "OrderNo": 3, "DisplayName": "工作指令数据ID", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=3097.32,705.00\r\nP2=3219.00,705.00\r\nP3=3219.00,705.00\r\nP4=3340.47,705.00\r\nHookP1=216.68,146.38\r\nHookP2=27.53,75.33\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 6, "Name": "WI_ACCEPT_OBJ", "OrderNo": 4, "DisplayName": "指令接收对象", "DataType": 1, "DataLength": 100}, {"ID": 7, "Name": "WI_ACCEPT_OBJ_ID", "OrderNo": 5, "DisplayName": "指令接收对象ID", "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "OBJ_TYPE", "Memo": "org=机构，person=人员", "OrderNo": 6, "DisplayName": "接收对象类型", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 7, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 26, "Name": "WI_AUDIT_DATA", "Caption": "指令审核人数据表", "CreateDate": "2025-01-21 13:15:05", "OrderNo": 27, "GraphDesc": "Left=3340.57\r\nTop=791.56\r\nWidth=270.22\r\nHeight=150.56\r\nAutoSize=0", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1540.00\r\nP3=1343.00,1540.00\r\nP4=3118.00,1540.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 7, "Name": "PROCESS_DATA_ID", "OrderNo": 2, "DisplayName": "流程数据ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_PROCESS_DATA", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3072.97,1038.10\r\nP2=3249.00,1038.10\r\nP3=3249.00,922.39\r\nP4=3340.57,922.39\r\nHookP1=126.00,50.50\r\nHookP2=20.03,130.83\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 14, "Name": "AUDIT_PERSON", "OrderNo": 3, "DisplayName": "审核人姓名", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "AUDIT_PERSON_ID", "OrderNo": 4, "DisplayName": "审核人ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "WI_DATA_ID", "OrderNo": 5, "DisplayName": "工作指令数据ID", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=3069.00,772.62\r\nP2=3069.00,817.00\r\nP3=3069.00,817.00\r\nP4=3339.97,817.00\r\nHookP1=208.68,186.38\r\nHookP2=28.03,27.83\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=1"}, {"ID": 6, "Name": "AUDIT_STATUS", "Memo": "0=未审核，1=审核通过，-1=审核否决", "OrderNo": 6, "DisplayName": "审核状态", "DataType": 2}, {"ID": 7, "Name": "AUDIT_TIME", "OrderNo": 7, "DisplayName": "审核时间", "DataType": 4}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 8, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 27, "Name": "工作指令下达数据", "TypeName": "GROUP", "Memo": "工作指令下达数据", "CreateDate": "2025-01-21 13:18:19", "OrderNo": 28, "GraphDesc": "Left=2749.81\r\nTop=498.67\r\nWidth=978.52\r\nHeight=846.43\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 28, "Name": "WI_PROCESS_DATA", "Caption": "指令流程数据表", "CreateDate": "2025-01-21 13:29:12", "OrderNo": 29, "GraphDesc": "Left=2835.97\r\nTop=987.60", "BgColor": 15261915, "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1453.00\r\nP3=1343.00,1453.00\r\nP4=3010.00,1453.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 4, "Name": "WI_DATA_ID", "OrderNo": 2, "DisplayName": "工作指令数据ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_DATA", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3063.00,772.62\r\nP2=3063.00,914.82\r\nP3=2967.00,914.82\r\nP4=2967.00,987.60\r\nHookP1=202.68,131.00\r\nHookP2=131.03,43.40\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}, {"ID": 5, "Name": "PROCESS_ID", "OrderNo": 3, "DisplayName": "流程ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_PROCESS_SETTING", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3311.03,248.37\r\nP2=3203.74,248.37\r\nP3=3203.74,1004.95\r\nP4=3072.97,1004.95\r\nHookP1=133.00,87.50\r\nHookP2=214.88,17.35\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "PROCESS_NAME", "OrderNo": 4, "DisplayName": "流程名称", "DataType": 1, "DataLength": 200}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 5, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 29, "Name": "WI_COPYTO_DATA", "Caption": "指令抄送人数据表", "CreateDate": "2025-01-21 13:37:16", "OrderNo": 30, "GraphDesc": "Left=3340.55\r\nTop=971.26\r\nWidth=280.57\r\nHeight=137.35\r\nAutoSize=0", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1399.62\r\nP3=1343.00,1399.62\r\nP4=3039.58,1399.62\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 4, "Name": "WI_DATA_ID", "OrderNo": 2, "DisplayName": "工作指令数据ID", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=3097.32,737.00\r\nP2=3219.00,737.00\r\nP3=3219.00,991.00\r\nP4=3340.55,991.00\r\nHookP1=200.68,178.38\r\nHookP2=20.45,19.74\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 7, "Name": "PROCESS_DATA_ID", "OrderNo": 3, "DisplayName": "流程数据ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_PROCESS_DATA", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3072.97,1038.60\r\nP2=3250.00,1038.60\r\nP3=3250.00,991.00\r\nP4=3340.55,991.00\r\nHookP1=102.00,51.00\r\nHookP2=20.45,19.74\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "COPYTO_PERSON", "OrderNo": 4, "DisplayName": "抄送接收人", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "COPYTO_PERSON_ID", "OrderNo": 5, "DisplayName": "抄送接收人ID", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "READED", "Memo": "0=未读，1=已读", "OrderNo": 6, "DisplayName": "是否已读", "DataType": 2}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 7, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 28, "Name": "WI_PROCESS_SETTING", "Caption": "指令流程设置表", "CreateDate": "2025-01-21 13:29:12", "OrderNo": 31, "GraphDesc": "Left=3311.03\r\nTop=160.87", "BgColor": 14675952, "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1453.00\r\nP3=1343.00,1453.00\r\nP4=3010.00,1453.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 6, "Name": "WI_LIB_ID", "OrderNo": 2, "DisplayName": "指令库ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_LIB", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3155.30,192.50\r\nP2=3233.00,192.50\r\nP3=3233.00,228.00\r\nP4=3311.03,228.00\r\nHookP1=126.00,32.97\r\nHookP2=27.97,67.13\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "ORG_CODE", "OrderNo": 3, "DisplayName": "指令下达机构编码", "DataType": 1, "RelateTable": "WI_DATA", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=2881.00,1190.23\r\nP2=2881.00,1389.00\r\nP3=3073.00,1389.00\r\nP4=3073.00,1748.30\r\nHookP1=177.31,137.77\r\nHookP2=20.31,19.70\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 5, "Name": "ORG_NAME", "OrderNo": 4, "DisplayName": "指令下达机构名称", "DataType": 1, "DataLength": 200}, {"ID": 4, "Name": "PROCESS_DESC", "OrderNo": 5, "DisplayName": "流程名称", "DataType": 1, "DataLength": 4000}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 6, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 28, "Name": "WI_PROCESS_SETTING_ACC", "Caption": "指令流程接收对象设置表", "CreateDate": "2025-01-21 13:29:12", "OrderNo": 32, "GraphDesc": "Left=3681.26\r\nTop=159.08", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1453.00\r\nP3=1343.00,1453.00\r\nP4=3010.00,1453.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 5, "Name": "PROCESS_ID", "OrderNo": 2, "DisplayName": "流程ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_PROCESS_SETTING", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3577.03,211.00\r\nP2=3629.00,211.00\r\nP3=3629.00,211.00\r\nP4=3681.26,211.00\r\nHookP1=229.97,50.13\r\nHookP2=19.74,51.92\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 6, "Name": "WI_ACCEPT_OBJ", "OrderNo": 3, "DisplayName": "指令接收对象", "DataType": 1, "DataLength": 100}, {"ID": 7, "Name": "WI_ACCEPT_OBJ_ID", "OrderNo": 4, "DisplayName": "指令接收对象ID", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 5, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 34, "Name": "工作指令设置数据", "TypeName": "GROUP", "Memo": "工作指令设置数据", "CreateDate": "2025-01-21 14:22:17", "OrderNo": 33, "GraphDesc": "Left=2852.95\r\nTop=59.36\r\nWidth=1301.98\r\nHeight=434.85\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 29, "Name": "WI_PROCESS_SETTING_COPYTO", "Caption": "指令抄送人设置表", "CreateDate": "2025-01-21 13:37:16", "OrderNo": 34, "GraphDesc": "Left=3704.03\r\nTop=312.87", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1399.62\r\nP3=1343.00,1399.62\r\nP4=3039.58,1399.62\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 5, "Name": "PROCESS_ID", "OrderNo": 2, "DisplayName": "流程ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_PROCESS_SETTING", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3533.00,278.87\r\nP2=3533.00,333.00\r\nP3=3533.00,333.00\r\nP4=3704.03,333.00\r\nHookP1=221.97,74.13\r\nHookP2=19.97,20.13\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=1"}, {"ID": 4, "Name": "COPYTO_PERSON", "OrderNo": 3, "DisplayName": "抄送接收人", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "COPYTO_PERSON_ID", "OrderNo": 4, "DisplayName": "抄送接收人ID", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 5, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 37, "Name": "WI_LIB", "Caption": "指令库", "CreateDate": "2025-01-21 15:00:31", "OrderNo": 35, "GraphDesc": "Left=2918.30\r\nTop=159.53", "BgColor": 15985906, "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,807.00\r\nP3=1343.00,807.00\r\nP4=3043.00,807.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,102.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 7, "Name": "BASE_ID", "OrderNo": 2, "DisplayName": "原始ID", "DataType": 1, "DataLength": 50}, {"ID": 6, "Name": "WI_CODE", "OrderNo": 3, "DisplayName": "指令编码", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "WI_NAME", "OrderNo": 4, "DisplayName": "工作指令名称", "DataType": 1, "DataLength": 200}, {"ID": 6, "Name": "WI_CONTENT", "OrderNo": 5, "DisplayName": "工作指令内容", "DataType": 1, "DataLength": 4000}, {"ID": 12, "Name": "IS_CURRENT", "OrderNo": 6, "DisplayName": "是否为当前使用", "DataType": 2}, {"ID": 13, "Name": "VERSION", "OrderNo": 7, "DisplayName": "版本号", "DataType": 1, "DataLength": 50}]}}, {"ID": 38, "Name": "操作卡数据", "TypeName": "GROUP", "Memo": "操作卡数据", "CreateDate": "2025-01-21 15:22:26", "OrderNo": 36, "GraphDesc": "Left=3802.46\r\nTop=515.83\r\nWidth=1299.93\r\nHeight=1025.60\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 39, "Name": "WI_FEEDBACK_DATA", "Caption": "指令反馈数据表", "CreateDate": "2025-01-21 15:27:27", "OrderNo": 37, "GraphDesc": "Left=3349.46\r\nTop=1159.04", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1130.00\r\nP3=1343.00,1130.00\r\nP4=3083.50,1130.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 13, "Name": "FB_CONTENT", "OrderNo": 2, "DisplayName": "反馈内容", "DataType": 1, "DataLength": 4000}, {"ID": 4, "Name": "WI_DATA_ID", "OrderNo": 3, "DisplayName": "工作指令数据ID", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=3037.00,772.62\r\nP2=3037.00,1126.00\r\nP3=3369.00,1126.00\r\nP4=3369.00,1159.04\r\nHookP1=176.68,154.38\r\nHookP2=19.54,19.96\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 7, "Name": "PROCESS_DATA_ID", "OrderNo": 4, "DisplayName": "流程数据ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_PROCESS_DATA", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3072.97,1038.60\r\nP2=3250.50,1038.60\r\nP3=3250.50,1241.26\r\nP4=3349.46,1241.26\r\nHookP1=237.00,51.00\r\nHookP2=20.16,82.22\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 5, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 40, "Name": "WI_FILE_DATA", "Caption": "指令附件表", "CreateDate": "2025-01-21 15:30:26", "OrderNo": 38, "GraphDesc": "Left=2771.02\r\nTop=806.96", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1256.95\r\nP3=1343.00,1256.95\r\nP4=2674.35,1256.95\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 4, "Name": "WI_DATA_ID", "OrderNo": 2, "DisplayName": "工作指令数据ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_DATA", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=2934.00,772.62\r\nP2=2934.00,790.00\r\nP3=2934.00,790.00\r\nP4=2934.00,806.96\r\nHookP1=73.68,170.38\r\nHookP2=162.98,20.04\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 4, "Name": "FILE_ID", "OrderNo": 3, "DisplayName": "文件ID", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 4, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 41, "Name": "ELEM_LIB", "Caption": "要素库", "CreateDate": "2025-01-23 09:22:44", "OrderNo": 39, "GraphDesc": "Left=3591.27\r\nTop=2226.39\r\nWidth=213.98\r\nHeight=117.58\r\nAutoSize=0", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,2624.00\r\nP3=1343.00,2624.00\r\nP4=2298.00,2624.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 2, "Name": "ELEM_NAME", "OrderNo": 2, "DisplayName": "要素名称", "DataType": 1, "DataLength": 100}, {"ID": 3, "Name": "PID", "OrderNo": 3, "DisplayName": "父ID", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "NODE_TYPE", "Memo": "folder=分类节点，leaf=叶子节点", "OrderNo": 4, "DisplayName": "节点类型", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 5, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 6, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 42, "Name": "要素", "TypeName": "GROUP", "Memo": "要素", "CreateDate": "2025-01-23 10:51:13", "OrderNo": 40, "GraphDesc": "Left=3561.20\r\nTop=2190.68\r\nWidth=258.42\r\nHeight=178.97\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 43, "Name": "DIGITAL_LEDGER", "Caption": "活动台账表单模型关系表", "Memo": "行云流表单与台账模型关系表", "CreateDate": "2025-01-23 10:51:37", "OrderNo": 41, "GraphDesc": "Left=1737.13\r\nTop=2365.11", "BgColor": 13488368, "MetaFields": {"Count": 10, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,2237.00\r\nP3=1343.00,2237.00\r\nP4=2490.00,2237.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 8, "Name": "FORM_ID", "OrderNo": 2, "DisplayName": "行云流表单ID", "DataType": 1, "DataLength": 50}, {"ID": 9, "Name": "FORM_NAME", "OrderNo": 3, "DisplayName": "行云流表单名称", "DataType": 1, "DataLength": 100}, {"ID": 6, "Name": "LEDGER_MODULE_ID", "OrderNo": 4, "DisplayName": "台账模型ID", "DataType": 1, "RelateTable": "DIGITAL_LEDGER_MODULE", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=2114.79,2603.00\r\nP2=2005.00,2603.00\r\nP3=2005.00,2603.00\r\nP4=2005.00,2499.11\r\nHookP1=20.21,20.34\r\nHookP2=267.87,96.89\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=0"}, {"ID": 16, "Name": "LEDGER_NAME", "OrderNo": 5, "DisplayName": "台账名称", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "COMPONENT_ID", "OrderNo": 6, "DisplayName": "组件标识", "DataType": 1, "DataLength": 100}, {"ID": 11, "Name": "VERSION_NUM", "OrderNo": 7, "DisplayName": "版本暂无用", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "INIT_TYPE", "Memo": "time=时间批次数据填充模式，extendedRow=扩展行数据填充模式，fixedRow=固定行数据填充模式", "OrderNo": 8, "DisplayName": "台账数据初始化类型", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 9, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 10, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 45, "Name": "数字化台账", "TypeName": "GROUP", "Memo": "数字化台账", "CreateDate": "2025-01-23 11:20:39", "OrderNo": 42, "GraphDesc": "Left=1585.48\r\nTop=2341.25\r\nWidth=1019.67\r\nHeight=796.12\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 46, "Name": "QUALITY_INDEX", "Caption": "质量指标设置", "CreateDate": "2025-01-23 13:21:13", "OrderNo": 43, "GraphDesc": "Left=748.58\r\nTop=2680.30", "BgColor": 16051441, "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,2694.00\r\nP3=1343.00,2694.00\r\nP4=2073.00,2694.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 9, "Name": "OBJECT_ID", "Memo": "对象id", "OrderNo": 2, "DisplayName": "对象ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "DIGITAL_LEDGER", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1737.13,2503.00\r\nP2=995.00,2503.00\r\nP3=995.00,2503.00\r\nP4=995.00,2680.30\r\nHookP1=43.87,137.89\r\nHookP2=246.42,27.70\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=0"}, {"ID": 8, "Name": "OBJECT_FLAG", "Memo": "用于指定是什么模块的质量指标", "OrderNo": 3, "DisplayName": "对象标识", "DataType": 1}, {"ID": 13, "Name": "INDEX_NAME", "OrderNo": 4, "DisplayName": "指标名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "INDEX_DESC", "OrderNo": 5, "DisplayName": "指标描述", "DataType": 1, "DataLength": 4000}, {"ID": 15, "Name": "SPEC_CODE", "OrderNo": 6, "DisplayName": "专业", "DataType": 1, "DataLength": 100}, {"ID": 6, "Name": "DATA_SOURCE_FORMULA", "OrderNo": 7, "DisplayName": "数据来源公式", "DataType": 1, "DataLength": 4000}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 8, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 48, "Name": "质量指标", "TypeName": "GROUP", "Memo": "质量指标", "CreateDate": "2025-01-23 13:42:52", "OrderNo": 44, "GraphDesc": "Left=701.00\r\nTop=2646.57\r\nWidth=817.26\r\nHeight=470.83\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 49, "Name": "QUALITY_EVAL_RULE", "Caption": "质量指标考核细则", "CreateDate": "2025-01-23 13:44:18", "OrderNo": 45, "GraphDesc": "Left=1188.42\r\nTop=2676.83", "BgColor": 15529452, "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,1130.00\r\nP3=1335.57,1130.00\r\nP4=1335.57,2675.98\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 2, "Name": "QI_ID", "OrderNo": 2, "DisplayName": "质量指标ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "QUALITY_INDEX", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1022.58,2746.00\r\nP2=1106.00,2746.00\r\nP3=1106.00,2746.00\r\nP4=1188.42,2746.00\r\nHookP1=254.42,65.70\r\nHookP2=19.58,69.17\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "CONDITION", "Memo": "满足此条件将获取对应分数", "OrderNo": 3, "DisplayName": "条件", "DataType": 1, "DataLength": 4000}, {"ID": 7, "Name": "OBJECT_ID", "Memo": "对象id", "OrderNo": 4, "DisplayName": "对象id", "DataType": 1, "DataLength": 50}, {"ID": 8, "Name": "OBJECT_FLAG", "Memo": "用于区分什么模块的质量指标", "OrderNo": 5, "DisplayName": "对象标识", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "SCORE", "OrderNo": 6, "DisplayName": "得分", "DataType": 3}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 7, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 52, "Name": "DIGITAL_LEDGER_MODULE", "Caption": "数字化台账模型", "CreateDate": "2025-01-23 14:03:52", "OrderNo": 46, "GraphDesc": "Left=2114.79\r\nTop=2582.66", "BgColor": 15986164, "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,2638.00\r\nP3=1343.00,2638.00\r\nP4=2180.00,2638.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 2, "Name": "MODULE_NAME", "OrderNo": 2, "DisplayName": "模型名称", "DataType": 1, "DataLength": 100}, {"ID": 3, "Name": "MODULE_DESC", "OrderNo": 3, "DisplayName": "模型说明", "DataType": 1, "DataLength": 4000}, {"ID": 6, "Name": "FILE_ID", "Memo": "上传的模版文件，通过其解析出采集点", "OrderNo": 4, "DisplayName": "文件ID", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "ACCOUNT_OBJ_ID", "OrderNo": 5, "DisplayName": "核算对象ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "COSTUINT", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=2660.62,2650.00\r\nP2=2525.00,2650.00\r\nP3=2525.00,2650.00\r\nP4=2388.79,2650.00\r\nHookP1=36.38,426.53\r\nHookP2=254.21,67.34\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 6, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 7, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 57, "Name": "DIGITAL_LEDGER_TIME", "Caption": "台账时间批次初始化表", "CreateDate": "2025-01-23 14:55:52", "OrderNo": 47, "GraphDesc": "Left=1666.56\r\nTop=2631.51", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,2761.95\r\nP3=1343.00,2761.95\r\nP4=1742.22,2761.95\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 2, "Name": "LEDGER_ID", "OrderNo": 2, "DisplayName": "台账ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "DIGITAL_LEDGER", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1746.27,2547.11\r\nP2=1746.27,2573.00\r\nP3=1674.14,2573.00\r\nP4=1674.14,2631.51\r\nHookP1=9.14,99.00\r\nHookP2=7.58,83.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 4, "Name": "START_TIME_TYPE", "Memo": "shift=当班开始时间，custom=自定义", "OrderNo": 3, "DisplayName": "开始时间类型", "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "START_TIME", "OrderNo": 4, "DisplayName": "开始时间", "DataType": 4}, {"ID": 6, "Name": "END_TIME_TYPE", "Memo": "shift=当班开始时间，custom=自定义", "OrderNo": 5, "DisplayName": "截止时间类型", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "END_TIME", "OrderNo": 6, "DisplayName": "截止时间", "DataType": 4}, {"ID": 8, "Name": "TIME_INTERVAL", "OrderNo": 7, "DisplayName": "时间间隔", "DataType": 2}, {"ID": 9, "Name": "TIME_INTERVAL_TYPE", "Memo": "minute=分钟，hour=小时，day=天，month=月，year=年", "OrderNo": 8, "DisplayName": "时间间隔类型", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 9, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 57, "Name": "DIGITAL_LEDGER_EXTEND_ROW", "Caption": "台账扩展行初始化表", "CreateDate": "2025-01-23 14:55:52", "OrderNo": 48, "GraphDesc": "Left=1634.75\r\nTop=2839.52", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,2761.95\r\nP3=1343.00,2761.95\r\nP4=1742.22,2761.95\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 2, "Name": "LEDGER_ID", "OrderNo": 2, "DisplayName": "台账ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "DIGITAL_LEDGER", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1746.27,2547.11\r\nP2=1746.27,2581.00\r\nP3=1642.33,2581.00\r\nP4=1642.33,2839.52\r\nHookP1=9.14,99.00\r\nHookP2=7.58,83.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 10, "Name": "COLLECTION_POINT_ID", "OrderNo": 3, "DisplayName": "采集点ID", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "DATA_SOURCE_ALIAS", "OrderNo": 4, "DisplayName": "数据源别名", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "DATA_SOURCE_FIELD", "OrderNo": 5, "DisplayName": "数据源输出字段", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 6, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 57, "Name": "DIGITAL_LEDGER_FIXED_ROW", "Caption": "台账固定行初始化表", "CreateDate": "2025-01-23 14:55:52", "OrderNo": 49, "GraphDesc": "Left=1610.55\r\nTop=2979.77", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,2761.95\r\nP3=1343.00,2761.95\r\nP4=1742.22,2761.95\r\nHookP1=285.61,75.00\r\nHookP2=148.00,1.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 2, "Name": "LEDGER_ID", "OrderNo": 2, "DisplayName": "台账ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "DIGITAL_LEDGER", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1748.29,2547.11\r\nP2=1748.29,2619.00\r\nP3=1618.13,2619.00\r\nP4=1618.13,2979.77\r\nHookP1=11.16,99.00\r\nHookP2=7.58,83.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 10, "Name": "TPL_JSON", "Memo": "[{\"采集点ID1\":100,\"采集点ID2\":200,\"采集点ID3\":300,\"采集点ID4\":\"$数据源别名.gets(0,'输出字段n')\"}]", "OrderNo": 3, "DisplayName": "模版json字符串", "DataType": 1, "DataLength": 4000}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 4, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 1, "Name": "OPERCARD_CLASSIFY", "Caption": "操作卡分类", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 50, "GraphDesc": "Left=10.00\r\nTop=3391.37", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "PID", "OrderNo": 2, "DisplayName": "父编号", "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "NAME", "OrderNo": 3, "DisplayName": "分类名称", "DataType": 1, "DataLength": 255}, {"ID": 6, "Name": "ORGCODE", "Memo": "车间代码", "OrderNo": 4, "DisplayName": "机构编码", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "TMUSED", "Memo": "1：使用；0：不使用", "OrderNo": 5, "DisplayName": "是否使用", "DataType": 2}, {"ID": 7, "Name": "TMSORT", "OrderNo": 6, "DisplayName": "排序", "DataType": 2}, {"ID": 5, "Name": "MEMO", "OrderNo": 7, "DisplayName": "注释", "DataType": 1, "DataLength": 500}]}}, {"ID": 1, "Name": "OPERCARD_INFO", "Caption": "操作卡信息表", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 51, "GraphDesc": "Left=290.00\r\nTop=3391.37", "BgColor": 15332335, "MetaFields": {"Count": 20, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CLASSID", "Memo": "OPERCARD_CLASSIFY.ID", "OrderNo": 2, "DisplayName": "分类id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_CLASSIFY", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=225.00,3458.00\r\nP2=258.00,3458.00\r\nP3=258.00,3458.00\r\nP4=290.00,3458.00\r\nHookP1=195.00,66.63\r\nHookP2=44.00,66.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "NAME", "OrderNo": 3, "DisplayName": "操作卡名称", "DataType": 1, "DataLength": 255}, {"ID": 6, "Name": "ORGCODE", "Memo": "车间代码", "OrderNo": 4, "DisplayName": "机构编码", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "TMUSED", "Memo": "1：使用；0：不使用", "OrderNo": 5, "DisplayName": "是否使用", "DataType": 2}, {"ID": 7, "Name": "TMSORT", "OrderNo": 6, "DisplayName": "排序", "DataType": 2}, {"ID": 5, "Name": "MEMO", "OrderNo": 7, "DisplayName": "注释", "DataType": 1, "DataLength": 500}, {"ID": 8, "Name": "CARDNO", "OrderNo": 8, "DisplayName": "操作卡编号", "DataType": 1, "DataLength": 255}, {"ID": 9, "Name": "CARDLEVEL", "Memo": "数据字典：company:公司级；department:部门级;shiftteam:班组级", "OrderNo": 9, "DisplayName": "操作卡级别", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "CARDSTATUS", "Memo": "1：已发布；0：未发布；2:审核中", "OrderNo": 10, "DisplayName": "状态", "DataType": 2}, {"ID": 18, "Name": "EXECTYPE", "Memo": "1串行执行 2并行执行", "OrderNo": 11, "DisplayName": "执行方式", "DataType": 2}, {"ID": 19, "Name": "INPUTTYPE", "Memo": "1逐步录入 2关键点录入", "OrderNo": 12, "DisplayName": "录入方式", "DataType": 2}, {"ID": 20, "Name": "FIXEDABLE", "Memo": "1允许 0不允许", "OrderNo": 13, "DisplayName": "是否允许固化", "DataType": 2}, {"ID": 17, "Name": "AUDITSTATUS", "Memo": "1：审核通过；0:未提交；2：审核中；-1：否决", "OrderNo": 14, "DisplayName": "审核状态", "DataType": 2}, {"ID": 11, "Name": "CARDUSING", "Memo": "1：使用中；0：未使用", "OrderNo": 15, "DisplayName": "是否为使用中", "DataType": 2}, {"ID": 13, "Name": "CARDVER", "OrderNo": 16, "DisplayName": "操作卡最新版本", "DataType": 1}, {"ID": 12, "Name": "PUBLISHDT", "OrderNo": 17, "DisplayName": "发布时间", "DataType": 4}, {"ID": 14, "Name": "PUBLISHPERSON", "OrderNo": 18, "DisplayName": "发布人", "DataType": 1, "DataLength": 200}, {"ID": 15, "Name": "PUBLISHPERSONID", "OrderNo": 19, "DisplayName": "发布人id", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "CURRENTVERID", "OrderNo": 20, "DisplayName": "操作卡当前生效版本ID", "DataType": 1, "DataLength": 50}]}}, {"ID": 1, "Name": "OPERCARD_INFO_VERSION", "Caption": "操作卡版本", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 52, "GraphDesc": "Left=675.00\r\nTop=3391.37", "BgColor": 15332335, "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 17, "Name": "CARDID", "Memo": "OPERCARD_INFO.ID", "OrderNo": 2, "DisplayName": "操作卡ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 13, "Name": "CARDVER", "OrderNo": 3, "DisplayName": "操作卡版本", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "TMUSED", "Memo": "1：使用；0：不使用", "OrderNo": 4, "DisplayName": "是否使用", "DataType": 2}, {"ID": 12, "Name": "PUBLISHDT", "OrderNo": 5, "DisplayName": "发布时间", "DataType": 4}, {"ID": 14, "Name": "PUBLISHPERSON", "OrderNo": 6, "DisplayName": "发布人", "DataType": 1, "DataLength": 200}, {"ID": 15, "Name": "PUBLISHPERSONID", "OrderNo": 7, "DisplayName": "发布人id", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "CURRENTSTATUS", "Memo": "0不生效 1生效 全部版本中仅一个版本生效，其他都为不生效", "OrderNo": 8, "DisplayName": "当前生效状态", "DataType": 2}]}}, {"ID": 1, "Name": "OPERCARD_CATALOG", "Caption": "操作卡目录", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 53, "GraphDesc": "Left=955.00\r\nTop=3391.37", "BgColor": 15463143, "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CARDID", "Memo": "OPERCARD_INFO.ID/OPERCARD_INFO_VERSION.ID", "OrderNo": 2, "DisplayName": "操作卡id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_INFO", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=615.00,3554.00\r\nP2=785.00,3554.00\r\nP3=785.00,3554.00\r\nP4=955.00,3554.00\r\nHookP1=305.00,162.63\r\nHookP2=20.00,162.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 11, "Name": "CARDVERID", "Memo": "OPERCARD_INFO_VERSION.ID", "OrderNo": 3, "DisplayName": "操作卡版本ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "PID", "OrderNo": 4, "DisplayName": "父目录", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "CATALOGALIAS", "Memo": "用于可执行子操作卡版本继承等操作", "OrderNo": 5, "DisplayName": "目录别名", "DataType": 1, "DataLength": 50}, {"ID": 10, "Name": "CATALOGTYPE", "Memo": "1:目录 2:可执行子操作卡", "OrderNo": 6, "DisplayName": "目录类型", "DataType": 2}, {"ID": 4, "Name": "NAME", "OrderNo": 7, "DisplayName": "目录名称", "DataType": 1, "DataLength": 500}, {"ID": 18, "Name": "TMUSED", "Memo": "1：使用；0：不使用", "OrderNo": 8, "DisplayName": "是否使用", "DataType": 2}, {"ID": 7, "Name": "TMSORT", "OrderNo": 9, "DisplayName": "排序", "DataType": 2}, {"ID": 5, "Name": "MEMO", "OrderNo": 10, "DisplayName": "注释", "DataType": 1, "DataLength": 500}, {"ID": 10, "Name": "CARDCONTENT", "Memo": "大文本clob", "OrderNo": 11, "DisplayName": "操作卡内容", "DataType": 1}]}}, {"ID": 1, "Name": "OPERCARD_OPERSTEP", "Caption": "操作卡操作步骤", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 54, "GraphDesc": "Left=1235.00\r\nTop=3391.37", "BgColor": 14807012, "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CARDID", "Memo": "OPERCARD_INFO.ID/OPERCARD_INFO_VERSION.ID", "OrderNo": 2, "DisplayName": "操作卡id", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 11, "Name": "CARDVERID", "Memo": "OPERCARD_INFO_VERSION.ID", "OrderNo": 3, "DisplayName": "操作卡版本ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "CATALOGALIAS", "Memo": "OPERCARD_CATALOG.CATALOGALIAS", "OrderNo": 4, "DisplayName": "操作卡目录别名", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_CATALOG", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1177.00,3479.00\r\nP2=1206.00,3479.00\r\nP3=1206.00,3479.00\r\nP4=1235.00,3479.00\r\nHookP1=186.00,87.63\r\nHookP2=114.00,87.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "NAME", "OrderNo": 5, "DisplayName": "步骤名称", "DataType": 1, "DataLength": 255}, {"ID": 18, "Name": "TMUSED", "Memo": "1：使用；0：不使用", "OrderNo": 6, "DisplayName": "是否使用", "DataType": 2}, {"ID": 7, "Name": "TMSORT", "OrderNo": 7, "DisplayName": "排序", "DataType": 2}, {"ID": 5, "Name": "MEMO", "OrderNo": 8, "DisplayName": "注释", "DataType": 1, "DataLength": 500}, {"ID": 8, "Name": "OPTYPE", "Memo": "1：文本；2：警示；3：操作步骤", "OrderNo": 9, "DisplayName": "步骤类型", "DataType": 2}, {"ID": 10, "Name": "OPCONTENT", "OrderNo": 10, "DisplayName": "操作内容", "DataType": 1, "DataLength": 4000}, {"ID": 10, "Name": "OPSIGN", "Memo": "[P] [I]", "OrderNo": 11, "DisplayName": "操作标识", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "OPSIGNTYPE", "Memo": "[]:操作 ():确认 <>:安全", "OrderNo": 12, "DisplayName": "操作标识类型", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "OPSIGNPOST", "Memo": "I:内操 P:外操 M班长", "OrderNo": 13, "DisplayName": "操作标识岗位", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "REQUIREDSTEP", "Memo": "1：必填；0：非必填 默认1", "OrderNo": 14, "DisplayName": "必填步骤", "DataType": 2}, {"ID": 14, "Name": "GROUPID", "Memo": "操作卡的段落（提示卡用提示用）", "OrderNo": 15, "DisplayName": "段落ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "CONFIRMTYPE", "Memo": "1手动确认 2身份证 3人脸识别", "OrderNo": 16, "DisplayName": "确认方式", "DataType": 2}]}}, {"ID": 1, "Name": "OPERCARD_STEPPOST", "Caption": "操作卡步骤执行岗位", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 55, "GraphDesc": "Left=1515.00\r\nTop=3391.37", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CARDID", "Memo": "OPERCARD_INFO.ID", "OrderNo": 2, "DisplayName": "操作卡id", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 11, "Name": "CARDVERID", "Memo": "OPERCARD_INFO_VERSION.ID", "OrderNo": 3, "DisplayName": "操作卡版本ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 6, "Name": "CATALOGALIAS", "Memo": "OPERCARD_CATALOG.CATALOGALIAS", "OrderNo": 4, "DisplayName": "操作卡目录别名", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "STEPID", "Memo": "OPERCARD_OPERSTEP.ID", "OrderNo": 5, "DisplayName": "步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3465.00\r\nP2=1497.00,3465.00\r\nP3=1497.00,3465.00\r\nP4=1515.00,3465.00\r\nHookP1=130.00,73.63\r\nHookP2=20.00,73.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "POSTID", "Memo": "执行岗位id", "OrderNo": 6, "DisplayName": "岗位id", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "POSTNAME", "OrderNo": 7, "DisplayName": "执行岗位", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "MEMO", "OrderNo": 8, "DisplayName": "注释", "DataType": 1, "DataLength": 500}, {"ID": 18, "Name": "TMUSED", "Memo": "1：使用；0：不使用", "OrderNo": 9, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 1, "Name": "OPERCARD_INSTRUMENT", "Caption": "操作卡操作步骤仪表信息", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 56, "GraphDesc": "Left=1830.00\r\nTop=3391.37", "MetaFields": {"Count": 14, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CARDID", "Memo": "OPERCARD_INFO.ID", "OrderNo": 2, "DisplayName": "操作卡id", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 11, "Name": "CARDVERID", "Memo": "OPERCARD_INFO_VERSION.ID", "OrderNo": 3, "DisplayName": "操作卡版本ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 12, "Name": "CATALOGALIAS", "Memo": "OPERCARD_CATALOG.CATALOGALIAS", "OrderNo": 4, "DisplayName": "操作卡目录别名", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "STEPID", "Memo": "OPERCARD_OPERSTEP.ID", "OrderNo": 5, "DisplayName": "步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3520.00\r\nP2=1510.00,3520.00\r\nP3=1510.00,3520.00\r\nP4=1830.00,3520.00\r\nHookP1=130.00,128.63\r\nHookP2=20.00,128.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 5, "Name": "MEMO", "OrderNo": 6, "DisplayName": "注释", "DataType": 1, "DataLength": 500}, {"ID": 6, "Name": "TAGNAME", "OrderNo": 7, "DisplayName": "仪表名称", "DataType": 1, "DataLength": 255}, {"ID": 7, "Name": "TAGCODE", "OrderNo": 8, "DisplayName": "仪表位号", "DataType": 1, "DataLength": 255}, {"ID": 8, "Name": "UPLIMIT", "OrderNo": 9, "DisplayName": "上限值", "DataType": 3}, {"ID": 9, "Name": "LOWLIMIT", "OrderNo": 10, "DisplayName": "下限值", "DataType": 3}, {"ID": 10, "Name": "COMTYPE", "Memo": "raw：实时仪表；number：数字框；combo:下拉框；img:图片；tip：风险提示；textfield：文本框", "OrderNo": 11, "DisplayName": "控件类型", "DataType": 1, "DataLength": 50}, {"ID": 11, "Name": "TMUSED", "OrderNo": 12, "DisplayName": "是否使用", "DataType": 2}, {"ID": 7, "Name": "TMSORT", "OrderNo": 13, "DisplayName": "排序", "DataType": 2}, {"ID": 12, "Name": "PROPERTYCODE", "Memo": "下拉框内容等信息", "OrderNo": 14, "DisplayName": "扩展内容", "DataType": 1, "DataLength": 4000}]}}, {"ID": 1, "Name": "OPERCARD_EXEC", "Caption": "操作卡执行_主表", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 57, "GraphDesc": "Left=2180.00\r\nTop=3391.37", "BgColor": 14141423, "MetaFields": {"Count": 20, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CARDID", "Memo": "OPERCARD_INFO.ID", "OrderNo": 2, "DisplayName": "操作卡id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_INFO", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=615.00,3578.00\r\nP2=934.00,3578.00\r\nP3=934.00,3674.00\r\nP4=2180.00,3674.00\r\nHookP1=297.00,186.63\r\nHookP2=28.00,282.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 14, "Name": "CARDNAME", "OrderNo": 3, "DisplayName": "操作卡名称", "DataType": 1, "DataLength": 255}, {"ID": 10, "Name": "CATALOGALIAS", "Memo": "OPERCARD_CATALOG.CATALOGALIAS", "OrderNo": 4, "DisplayName": "操作卡目录别名", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 15, "Name": "CATALOGNAME", "OrderNo": 5, "DisplayName": "目录名称", "DataType": 1, "DataLength": 500}, {"ID": 11, "Name": "CARDVERID", "Memo": "OPERCARD_INFO_VERSION.ID", "OrderNo": 6, "DisplayName": "操作卡版本ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 12, "Name": "ORGCODE", "OrderNo": 7, "DisplayName": "机构代码", "DataType": 1, "DataLength": 50}, {"ID": 14, "Name": "STARTDT", "OrderNo": 8, "DisplayName": "开始时间", "DataType": 4}, {"ID": 15, "Name": "ENDDT", "OrderNo": 9, "DisplayName": "结束时间", "DataType": 4}, {"ID": 16, "Name": "EXECUTIVETIME", "Memo": "分钟", "OrderNo": 10, "DisplayName": "执行持续时间", "DataType": 3}, {"ID": 16, "Name": "WORKID", "Memo": "指令/活动id", "OrderNo": 11, "DisplayName": "工作记录id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_DATA", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3080.00,772.62\r\nP2=3080.00,2082.00\r\nP3=2381.00,2082.00\r\nP4=2381.00,3391.37\r\nHookP1=219.68,186.38\r\nHookP2=201.00,35.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 17, "Name": "WORKTYPE", "Memo": "1=指令，2=活动", "OrderNo": 12, "DisplayName": "工作类型", "DataType": 2}, {"ID": 12, "Name": "EXECTYPE", "Memo": "1串行执行 2并行执行", "OrderNo": 13, "DisplayName": "执行方式", "DataType": 2}, {"ID": 19, "Name": "INPUTTYPE", "Memo": "1逐步录入 2关键点录入", "OrderNo": 14, "DisplayName": "录入方式", "DataType": 2}, {"ID": 12, "Name": "EXECSTATUS", "Memo": "-1废止 0未开始 1执行中 2已完成", "OrderNo": 15, "DisplayName": "执行状态", "DataType": 2}, {"ID": 12, "Name": "FIXEDSTATUS", "Memo": "0未固化 1已固化", "OrderNo": 16, "DisplayName": "固化状态", "DataType": 2}, {"ID": 16, "Name": "PCOUNT", "OrderNo": 17, "DisplayName": "外操执行次数", "DataType": 2}, {"ID": 16, "Name": "ICOUNT", "OrderNo": 18, "DisplayName": "内操执行次数", "DataType": 2}, {"ID": 16, "Name": "MCOUNT", "OrderNo": 19, "DisplayName": "班长执行次数", "DataType": 2}, {"ID": 12, "Name": "INPUTUSERNAME", "OrderNo": 20, "DisplayName": "操作卡录入人员", "DataType": 1, "DataLength": 4000}]}}, {"ID": 1, "Name": "OPERCARD_EXEC_OPERSTEP", "Caption": "操作卡执行步骤", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 58, "GraphDesc": "Left=2460.00\r\nTop=3391.37", "BgColor": 14807012, "MetaFields": {"Count": 20, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 16, "Name": "EXECID", "Memo": "OPERCARD_EXEC.ID", "OrderNo": 2, "DisplayName": "实例ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "STEPID", "Memo": "OPERCARD_OPERSTEP.ID", "OrderNo": 3, "DisplayName": "步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3578.00\r\nP2=1826.00,3578.00\r\nP3=1826.00,3578.00\r\nP4=2460.00,3578.00\r\nHookP1=176.00,186.63\r\nHookP2=20.00,186.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "NAME", "OrderNo": 4, "DisplayName": "步骤名称", "DataType": 1, "DataLength": 255}, {"ID": 7, "Name": "TMSORT", "OrderNo": 5, "DisplayName": "排序", "DataType": 2}, {"ID": 5, "Name": "MEMO", "OrderNo": 6, "DisplayName": "注释", "DataType": 1, "DataLength": 500}, {"ID": 8, "Name": "OPTYPE", "Memo": "1：文本；2：警示；3：操作步骤", "OrderNo": 7, "DisplayName": "步骤类型", "DataType": 2}, {"ID": 10, "Name": "OPCONTENT", "OrderNo": 8, "DisplayName": "操作内容", "DataType": 1, "DataLength": 4000}, {"ID": 10, "Name": "OPSIGN", "Memo": "[P] [I]", "OrderNo": 9, "DisplayName": "操作标识", "DataType": 1, "DataLength": 50}, {"ID": 12, "Name": "OPSIGNTYPE", "Memo": "[]:操作 ():确认 <>:安全", "OrderNo": 10, "DisplayName": "操作标识类型", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "OPSIGNPOST", "Memo": "I:内操 P:外操 M班长", "OrderNo": 11, "DisplayName": "操作标识岗位", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "REQUIREDSTEP", "Memo": "1：必填；0：非必填 默认1", "OrderNo": 12, "DisplayName": "必填步骤", "DataType": 2}, {"ID": 14, "Name": "GROUPID", "Memo": "操作卡的段落（提示卡提示用）", "OrderNo": 13, "DisplayName": "段落ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "CONFIRMTYPE", "Memo": "1手动确认 2身份证 3人脸识别", "OrderNo": 14, "DisplayName": "确认方式", "DataType": 2}, {"ID": 18, "Name": "REPEATSIGN", "Memo": "是否为重复记录 0：不是 1：是", "OrderNo": 15, "DisplayName": "重复记录标识", "DataType": 2}, {"ID": 17, "Name": "REPEATTIME", "Memo": "重复记录的次数，每次重复加1，多次重复累次加1", "OrderNo": 16, "DisplayName": "重复记录次数", "DataType": 2}, {"ID": 17, "Name": "REPEATSORT", "Memo": "存储被复制记录的排序值", "OrderNo": 17, "DisplayName": "重复记录排序", "DataType": 2}, {"ID": 17, "Name": "REPEATDT", "OrderNo": 18, "DisplayName": "重复记录时间", "DataType": 4}, {"ID": 19, "Name": "REPEATPERSON", "OrderNo": 19, "DisplayName": "重复记录人员", "DataType": 1, "DataLength": 200}, {"ID": 18, "Name": "REPEATPERSONID", "OrderNo": 20, "DisplayName": "重复记录人员id", "DataType": 1, "DataLength": 50}]}}, {"ID": 1, "Name": "OPERCARD_EXEC_STEPPOST", "Caption": "操作卡执行_岗位信息", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 59, "GraphDesc": "Left=2775.00\r\nTop=3391.37", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 16, "Name": "EXECID", "Memo": "OPERCARD_EXEC.ID", "OrderNo": 2, "DisplayName": "实例ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "STEPID", "Memo": "OPERCARD_OPERSTEP.ID", "OrderNo": 3, "DisplayName": "步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3650.00\r\nP2=2127.00,3650.00\r\nP3=2127.00,3458.00\r\nP4=2775.00,3458.00\r\nHookP1=152.00,258.63\r\nHookP2=20.00,66.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 9, "Name": "EXECSTEPID", "Memo": "OPERCARD_EXEC_OPERSTEP.ID", "OrderNo": 4, "DisplayName": "执行实例步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3472.00\r\nP2=2159.00,3472.00\r\nP3=2159.00,3472.00\r\nP4=2775.00,3472.00\r\nHookP1=144.00,80.63\r\nHookP2=28.00,80.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 20, "Name": "POSTID", "OrderNo": 5, "DisplayName": "执行岗位ID", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "POSTNAME", "OrderNo": 6, "DisplayName": "执行岗位", "DataType": 1, "DataLength": 200}, {"ID": 5, "Name": "MEMO", "OrderNo": 7, "DisplayName": "注释", "DataType": 1, "DataLength": 500}]}}, {"ID": 1, "Name": "OPERCARD_EXEC_STEPDETAILS", "Caption": "操作卡执行_步骤执行信息", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 60, "GraphDesc": "Left=3125.00\r\nTop=3391.37", "MetaFields": {"Count": 14, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 16, "Name": "EXECID", "Memo": "OPERCARD_EXEC.ID", "OrderNo": 2, "DisplayName": "实例ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "STEPID", "Memo": "OPERCARD_OPERSTEP.ID", "OrderNo": 3, "DisplayName": "步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3511.00\r\nP2=2430.00,3511.00\r\nP3=2430.00,3511.00\r\nP4=3125.00,3511.00\r\nHookP1=168.00,119.63\r\nHookP2=20.00,119.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 9, "Name": "EXECSTEPID", "Memo": "OPERCARD_EXEC_OPERSTEP.ID", "OrderNo": 4, "DisplayName": "执行实例步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3528.00\r\nP2=2302.00,3528.00\r\nP3=2302.00,3480.00\r\nP4=3125.00,3480.00\r\nHookP1=160.00,136.63\r\nHookP2=28.00,88.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 13, "Name": "OPSIGNPOST", "Memo": "I:内操 P:外操 M班长", "OrderNo": 5, "DisplayName": "操作标识岗位", "DataType": 1, "DataLength": 50}, {"ID": 26, "Name": "EXECSTATUS", "Memo": "0：未确认 1：已确认； 2：跳过步骤", "OrderNo": 6, "DisplayName": "执行状态", "DataType": 2}, {"ID": 14, "Name": "STARTDT", "OrderNo": 7, "DisplayName": "开始时间", "DataType": 4}, {"ID": 15, "Name": "ENDDT", "OrderNo": 8, "DisplayName": "结束时间", "DataType": 4}, {"ID": 16, "Name": "EXECUTIVETIME", "Memo": "分钟", "OrderNo": 9, "DisplayName": "执行持续时间", "DataType": 3}, {"ID": 20, "Name": "POSTID", "OrderNo": 10, "DisplayName": "执行岗位", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "POSTNAME", "OrderNo": 11, "DisplayName": "岗位名称", "DataType": 1, "DataLength": 255}, {"ID": 22, "Name": "EXECPERSON", "OrderNo": 12, "DisplayName": "执行人", "DataType": 1, "DataLength": 200}, {"ID": 23, "Name": "EXECPERSONID", "OrderNo": 13, "DisplayName": "执行人id", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "INPUTDT", "OrderNo": 14, "DisplayName": "录入时间", "DataType": 4}]}}, {"ID": 1, "Name": "OPERCARD_EXEC_STEPREAD", "Caption": "操作卡执行_提示卡阅读记录", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 61, "GraphDesc": "Left=3545.00\r\nTop=3391.37", "MetaFields": {"Count": 11, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 16, "Name": "EXECID", "Memo": "OPERCARD_EXEC.ID", "OrderNo": 2, "DisplayName": "实例ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "STEPID", "Memo": "OPERCARD_OPERSTEP.ID", "OrderNo": 3, "DisplayName": "步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3490.00\r\nP2=2432.00,3490.00\r\nP3=2432.00,3490.00\r\nP4=3545.00,3490.00\r\nHookP1=136.00,98.63\r\nHookP2=20.00,98.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 9, "Name": "EXECSTEPID", "Memo": "OPERCARD_EXEC_OPERSTEP.ID", "OrderNo": 4, "DisplayName": "执行实例步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3504.00\r\nP2=2432.00,3504.00\r\nP3=2432.00,3504.00\r\nP4=3545.00,3504.00\r\nHookP1=130.00,112.63\r\nHookP2=28.00,112.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 14, "Name": "GROUPID", "Memo": "操作卡的段落（提示卡提示用）", "OrderNo": 5, "DisplayName": "段落ID", "DataType": 1, "DataLength": 50}, {"ID": 26, "Name": "READSIGN", "Memo": "0：未阅读 1：已阅读", "OrderNo": 6, "DisplayName": "阅读标识", "DataType": 2}, {"ID": 20, "Name": "POSTID", "OrderNo": 7, "DisplayName": "阅读岗位", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "POSTNAME", "OrderNo": 8, "DisplayName": "岗位名称", "DataType": 1, "DataLength": 255}, {"ID": 22, "Name": "READPERSON", "OrderNo": 9, "DisplayName": "阅读人", "DataType": 1, "DataLength": 200}, {"ID": 23, "Name": "READPERSONID", "OrderNo": 10, "DisplayName": "阅读人id", "DataType": 1, "DataLength": 50}, {"ID": 16, "Name": "READDT", "OrderNo": 11, "DisplayName": "阅读时间", "DataType": 4}]}}, {"ID": 1, "Name": "OPERCARD_EXEC_INSTRUMENT", "Caption": "操作卡执行_仪表数据", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 62, "GraphDesc": "Left=3965.00\r\nTop=3391.37", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 16, "Name": "EXECID", "Memo": "OPERCARD_EXEC.ID", "OrderNo": 2, "DisplayName": "实例ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "STEPID", "Memo": "OPERCARD_OPERSTEP.ID", "OrderNo": 3, "DisplayName": "步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3598.00\r\nP2=2722.00,3598.00\r\nP3=2722.00,3534.00\r\nP4=3965.00,3534.00\r\nHookP1=208.00,206.63\r\nHookP2=20.00,142.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 9, "Name": "EXECSTEPID", "Memo": "OPERCARD_EXEC_OPERSTEP.ID", "OrderNo": 4, "DisplayName": "执行实例步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3497.00\r\nP2=2754.00,3497.00\r\nP3=2754.00,3497.00\r\nP4=3965.00,3497.00\r\nHookP1=200.00,105.63\r\nHookP2=28.00,105.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 5, "Name": "MEMO", "OrderNo": 5, "DisplayName": "注释", "DataType": 1, "DataLength": 500}, {"ID": 6, "Name": "TAGNAME", "OrderNo": 6, "DisplayName": "仪表名称", "DataType": 1, "DataLength": 255}, {"ID": 7, "Name": "TAGCODE", "OrderNo": 7, "DisplayName": "仪表位号", "DataType": 1, "DataLength": 255}, {"ID": 8, "Name": "UPLIMIT", "OrderNo": 8, "DisplayName": "上限值", "DataType": 3}, {"ID": 9, "Name": "LOWLIMIT", "OrderNo": 9, "DisplayName": "下限值", "DataType": 3}, {"ID": 10, "Name": "COMTYPE", "Memo": "raw：实时仪表；number：数字框；combo:下拉框；img:图片；tip：风险提示；textfield：文本框", "OrderNo": 10, "DisplayName": "控件类型", "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "TMSORT", "OrderNo": 11, "DisplayName": "排序", "DataType": 2}, {"ID": 12, "Name": "PROPERTYCODE", "Memo": "下拉框内容等信息", "OrderNo": 12, "DisplayName": "扩展内容", "DataType": 1, "DataLength": 4000}]}}, {"ID": 1, "Name": "OPERCARD_EXEC_INSDETAILS", "Caption": "操作卡执行_仪表数据_仪表值", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 63, "GraphDesc": "Left=10.00\r\nTop=3817.37", "MetaFields": {"Count": 15, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 16, "Name": "EXECID", "Memo": "OPERCARD_EXEC.ID", "OrderNo": 2, "DisplayName": "实例ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "STEPID", "Memo": "OPERCARD_OPERSTEP.ID", "OrderNo": 3, "DisplayName": "步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1255.00,3669.37\r\nP2=1255.00,3837.00\r\nP3=1255.00,3837.00\r\nP4=395.00,3837.00\r\nHookP1=20.00,248.63\r\nHookP2=365.00,19.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=1"}, {"ID": 9, "Name": "EXECSTEPID", "Memo": "OPERCARD_EXEC_OPERSTEP.ID", "OrderNo": 4, "DisplayName": "执行实例步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1263.00,3669.37\r\nP2=1263.00,3845.00\r\nP3=1263.00,3845.00\r\nP4=395.00,3845.00\r\nHookP1=28.00,249.63\r\nHookP2=357.00,27.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=1"}, {"ID": 5, "Name": "INSTRUMENTID", "Memo": "OPERCARD_EXEC_INSTRUMENT.ID", "OrderNo": 5, "DisplayName": "仪表ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 6, "Name": "TAGNAME", "OrderNo": 6, "DisplayName": "仪表名称", "DataType": 1, "DataLength": 255}, {"ID": 7, "Name": "TAGCODE", "OrderNo": 7, "DisplayName": "仪表位号", "DataType": 1, "DataLength": 255}, {"ID": 8, "Name": "UPLIMIT", "OrderNo": 8, "DisplayName": "上限值", "DataType": 3}, {"ID": 9, "Name": "LOWLIMIT", "OrderNo": 9, "DisplayName": "下限值", "DataType": 3}, {"ID": 16, "Name": "TAGVALUE", "OrderNo": 10, "DisplayName": "仪表值", "DataType": 1, "DataLength": 4000}, {"ID": 17, "Name": "TAGSTATUS", "Memo": "1：超限；0:正常", "OrderNo": 11, "DisplayName": "仪表状态", "DataType": 2}, {"ID": 15, "Name": "INPUTTYPE", "Memo": "1自动采集 2手动录入", "OrderNo": 12, "DisplayName": "录入方式", "DataType": 2}, {"ID": 16, "Name": "INPUTDT", "OrderNo": 13, "DisplayName": "录入时间", "DataType": 4}, {"ID": 18, "Name": "INPUTPERSON", "OrderNo": 14, "DisplayName": "录入人", "DataType": 1, "DataLength": 200}, {"ID": 17, "Name": "INPUTPERSONID", "OrderNo": 15, "DisplayName": "录入人id", "DataType": 1, "DataLength": 50}]}}, {"ID": 1, "Name": "OPERCARD_EXEC_JUMP", "Caption": "操作卡执行_跳步记录", "Memo": "OPERCARD_EXECDETAIL", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 64, "GraphDesc": "Left=4350.00\r\nTop=3391.37", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 16, "Name": "EXECID", "Memo": "OPERCARD_EXEC.ID", "OrderNo": 2, "DisplayName": "实例ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 9, "Name": "STEPID", "Memo": "OPERCARD_OPERSTEP.ID", "OrderNo": 3, "DisplayName": "步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3562.00\r\nP2=2914.00,3562.00\r\nP3=2914.00,3626.00\r\nP4=4350.00,3626.00\r\nHookP1=192.00,170.63\r\nHookP2=20.00,234.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 9, "Name": "EXECSTEPID", "Memo": "OPERCARD_EXEC_OPERSTEP.ID", "OrderNo": 4, "DisplayName": "执行实例步骤ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_OPERSTEP", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1479.00,3570.00\r\nP2=2914.00,3570.00\r\nP3=2914.00,3634.00\r\nP4=4350.00,3634.00\r\nHookP1=184.00,178.63\r\nHookP2=28.00,242.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 27, "Name": "APPPERSON", "OrderNo": 5, "DisplayName": "申请人", "DataType": 1, "DataLength": 255}, {"ID": 28, "Name": "APPPERSONID", "OrderNo": 6, "DisplayName": "申请人id", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "APPPOSTID", "OrderNo": 7, "DisplayName": "申请人岗位id", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "APPPOSTNAME", "OrderNo": 8, "DisplayName": "申请人岗位名称", "DataType": 1, "DataLength": 255}, {"ID": 30, "Name": "APPREASON", "OrderNo": 9, "DisplayName": "申请原因", "DataType": 1, "DataLength": 2000}, {"ID": 17, "Name": "APPDT", "OrderNo": 10, "DisplayName": "申请时间", "DataType": 4}, {"ID": 8, "Name": "AUDITPERSON", "OrderNo": 11, "DisplayName": "审核人", "DataType": 1, "DataLength": 255}, {"ID": 9, "Name": "AUDITPERSONID", "OrderNo": 12, "DisplayName": "审核人id", "DataType": 1, "DataLength": 50}, {"ID": 29, "Name": "AUDITSTATUS", "Memo": "1：审核通过；0:未提交；2：审核中；-1：否决", "OrderNo": 13, "DisplayName": "审核状态", "DataType": 2}, {"ID": 30, "Name": "AUDITDESC", "OrderNo": 14, "DisplayName": "审核说明", "DataType": 1, "DataLength": 2000}, {"ID": 17, "Name": "AUDITDT", "OrderNo": 15, "DisplayName": "审核时间", "DataType": 4}, {"ID": 18, "Name": "TMUSED", "Memo": "1：使用；0：不使用", "OrderNo": 16, "DisplayName": "是否使用", "DataType": 2}]}}, {"ID": 1, "Name": "OPERCARD_EXEC_FIXED", "Caption": "操作卡执行_操作卡固化表", "CreateDate": "2025-02-02 13:41:48", "OrderNo": 65, "GraphDesc": "Left=4700.00\r\nTop=3391.37", "BgColor": 14141423, "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 2, "Name": "CARDID", "Memo": "OPERCARD_INFO.ID", "OrderNo": 2, "DisplayName": "操作卡id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_INFO", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=615.00,3666.00\r\nP2=2658.00,3666.00\r\nP3=2658.00,3442.00\r\nP4=4700.00,3442.00\r\nHookP1=289.00,274.63\r\nHookP2=20.00,50.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 10, "Name": "CATALOGALIAS", "Memo": "OPERCARD_CATALOG.CATALOGALIAS", "OrderNo": 3, "DisplayName": "操作卡目录别名", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 11, "Name": "CARDVERID", "Memo": "OPERCARD_INFO_VERSION.ID", "OrderNo": 4, "DisplayName": "操作卡版本ID", "DataType": 1, "KeyFieldType": 3, "DataLength": 50}, {"ID": 17, "Name": "EXECCOUNT", "OrderNo": 5, "DisplayName": "执行次数", "DataType": 2}]}}, {"ID": 68, "Name": "OPERCARD_CATALOG_EXEC", "Caption": "操作卡执行目录关系表（暂不使用）", "CreateDate": "2025-02-06 09:29:46", "OrderNo": 66, "GraphDesc": "Left=4184.00\r\nTop=585.00", "BgColor": 16115698, "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=1343.00,1087.52\r\nP2=1343.00,568.00\r\nP3=1343.00,568.00\r\nP4=4249.00,568.00\r\nHookP1=285.61,75.00\r\nHookP2=148.00,118.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 2, "Name": "CARDID", "Memo": "OPERCARD_INFO.ID", "OrderNo": 2, "DisplayName": "操作卡id", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=4090.00,799.97\r\nP2=4090.00,716.00\r\nP3=4277.00,716.00\r\nP4=4277.00,568.00\r\nHookP1=201.12,28.03\r\nHookP2=28.00,90.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 12, "Name": "CATALOGID", "Memo": "OPERCARD_CATALOG.ID", "OrderNo": 3, "DisplayName": "操作卡目录id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_CATALOG", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1177.00,3419.00\r\nP2=1512.00,3419.00\r\nP3=1512.00,627.00\r\nP4=4184.00,627.00\r\nHookP1=194.00,27.63\r\nHookP2=28.00,42.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 16, "Name": "WORKID", "Memo": "指令/活动", "OrderNo": 4, "DisplayName": "工作记录id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "WI_DATA", "RelateField": "ID", "GraphDesc": "P1=2946.32,558.62\r\nP2=2946.32,537.33\r\nP3=4351.00,537.33\r\nP4=4351.00,585.00\r\nHookP1=86.00,75.00\r\nHookP2=167.00,67.00\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=0"}, {"ID": 17, "Name": "WORKTYPE", "Memo": "1=指令，2=活动", "OrderNo": 5, "DisplayName": "工作类型", "DataType": 1}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 6, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 69, "Name": "OPERCARD_EXEC_JOBLIST_ACTIVITYEXAMPLE", "Caption": "操作卡执行_主表_活动实例表（暂不使用）", "CreateDate": "2025-02-06 09:42:26", "OrderNo": 67, "GraphDesc": "Left=10.00\r\nTop=3237.37", "BgColor": 15529196, "MetaFields": {"Count": 2, "items": [{"ID": 1, "Name": "OPERCARD_EXEC_WORKID", "OrderNo": 1, "DisplayName": "操作卡执行_主表_工作记录id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_EXEC", "RelateField": "WORKID", "GraphDesc": "P1=2180.00,3435.00\r\nP2=1380.00,3435.00\r\nP3=1380.00,3256.00\r\nP4=580.00,3256.00\r\nHookP1=44.00,43.63\r\nHookP2=550.00,18.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 2, "Name": "JOBLIST_ACTIVITYEXAMPLE_ID", "OrderNo": 2, "DisplayName": "活动实例表_活动实例ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_ACTIVITYEXAMPLE", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1105.00,803.40\r\nP2=1105.00,852.00\r\nP3=552.00,852.00\r\nP4=552.00,3237.37\r\nHookP1=68.15,392.52\r\nHookP2=542.00,24.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}]}}, {"ID": 70, "Name": "JOBLIST_ACTIVITYEXAMPLE_OPERCARD_EXEC", "Caption": "活动实例表_操作卡执行_主表", "CreateDate": "2025-02-06 09:43:11", "OrderNo": 68, "GraphDesc": "Left=535.00\r\nTop=3237.37", "BgColor": 15660268, "MetaFields": {"Count": 2, "items": [{"ID": 1, "Name": "JOBLIST_ACTIVITYEXAMPLE_ID", "OrderNo": 1, "DisplayName": "活动实例表_活动实例ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_ACTIVITYEXAMPLE", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1057.00,803.40\r\nP2=1057.00,1140.00\r\nP3=724.00,1140.00\r\nP4=724.00,3237.37\r\nHookP1=20.15,424.52\r\nHookP2=189.00,18.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 2, "Name": "OPERCARD_EXEC_WORKID", "OrderNo": 2, "DisplayName": "操作卡执行_主表_工作记录id", "DataType": 1, "KeyFieldType": 3, "RelateTable": "OPERCARD_EXEC", "RelateField": "WORKID", "GraphDesc": "P1=2200.00,3391.37\r\nP2=2200.00,3272.00\r\nP3=2200.00,3272.00\r\nP4=1016.00,3272.00\r\nHookP1=20.00,19.63\r\nHookP2=453.00,34.63\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=1"}]}}, {"Name": "COSTUNITSAMPLEDOT", "Caption": "采集点配置表", "OrderNo": 69, "GraphDesc": "Left=2991.69\r\nTop=2224.32", "MetaFields": {"Count": 44, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_BY_ORG", "OrderNo": 3, "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "CREATE_BY_POST", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "CREATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "UPDATE_BY", "OrderNo": 6, "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "UPDATE_TIME", "OrderNo": 7, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 8, "Name": "ANALYSISNAME", "OrderNo": 8, "DataType": 1, "DataLength": 200}, {"ID": 9, "Name": "ANALYSISSUBNAME", "OrderNo": 9, "DataType": 1, "DataLength": 200}, {"ID": 10, "Name": "BEGINTIME", "OrderNo": 10, "DataType": 1, "DataLength": 10}, {"ID": 11, "Name": "COMBINITKEY", "OrderNo": 11, "DataType": 1, "DataLength": 2000}, {"ID": 12, "Name": "COMBINITVAL", "OrderNo": 12, "DataType": 1, "DataLength": 2000}, {"ID": 13, "Name": "CONTROLTYPE", "OrderNo": 13, "DataType": 2}, {"ID": 14, "Name": "CTYPE", "OrderNo": 14, "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "DATASOURCE", "OrderNo": 15, "DataType": 1, "DataLength": 1000}, {"ID": 16, "Name": "DEFAULTVAL", "OrderNo": 16, "DataType": 2}, {"ID": 17, "Name": "DEVICETYPELIBSAMPLEDOTID", "OrderNo": 17, "DataType": 1, "DataLength": 200}, {"ID": 18, "Name": "INDEXRANGELOWER", "OrderNo": 18, "DataType": 3}, {"ID": 19, "Name": "INDEXRANGEUPPER", "OrderNo": 19, "DataType": 3}, {"ID": 20, "Name": "ISSHOWLEDGER", "OrderNo": 20, "DataType": 2}, {"ID": 21, "Name": "ISUSETORECORDEVENT", "OrderNo": 21, "DataType": 2}, {"ID": 22, "Name": "ISWRITEINPUT", "OrderNo": 22, "DataType": 2}, {"ID": 23, "Name": "MDMCODE", "OrderNo": 23, "DataType": 1, "DataLength": 50}, {"ID": 24, "Name": "NAME", "OrderNo": 24, "DataType": 1, "DataLength": 200}, {"ID": 25, "Name": "OUTSYSDOTID", "OrderNo": 25, "DataType": 1, "DataLength": 50}, {"ID": 26, "Name": "PID", "OrderNo": 26, "DataType": 1, "DataLength": 100}, {"ID": 27, "Name": "POINTCOUNTLEDGER", "OrderNo": 27, "DataType": 2}, {"ID": 28, "Name": "PROCESSUNITNAME", "OrderNo": 28, "DataType": 1, "DataLength": 200}, {"ID": 29, "Name": "PRODUCTNAME", "OrderNo": 29, "DataType": 1, "DataLength": 200}, {"ID": 30, "Name": "SAMPLINTERVAL", "OrderNo": 30, "DataType": 2}, {"ID": 31, "Name": "SAMPLINGPOINT", "OrderNo": 31, "DataType": 1, "DataLength": 200}, {"ID": 32, "Name": "SDUNIT", "OrderNo": 32, "DataType": 1, "DataLength": 100}, {"ID": 33, "Name": "SOURCEYPE", "OrderNo": 33, "DataType": 1, "DataLength": 50}, {"ID": 34, "Name": "TAGNUMBER", "OrderNo": 34, "DataType": 1, "DataLength": 200}, {"ID": 35, "Name": "TARGETVALUE", "OrderNo": 35, "DataType": 3}, {"ID": 36, "Name": "TIMETYPE", "OrderNo": 36, "DataType": 2}, {"ID": 37, "Name": "TMSORT", "OrderNo": 37, "DataType": 2}, {"ID": 38, "Name": "TMUSED", "OrderNo": 38, "DataType": 2}, {"ID": 39, "Name": "UNITID", "OrderNo": 39, "DataType": 1, "KeyFieldType": 3, "RelateTable": "COSTUINT", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=2911.62,2507.00\r\nP2=2952.00,2507.00\r\nP3=2952.00,2507.00\r\nP4=2991.69,2507.00\r\nHookP1=223.38,283.53\r\nHookP2=20.31,282.68\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 40, "Name": "USETO", "OrderNo": 40, "DataType": 2}, {"ID": 41, "Name": "VALRANGE", "OrderNo": 41, "DataType": 1, "DataLength": 100}, {"ID": 42, "Name": "ISWRITEBACKINFLUXDB", "OrderNo": 42, "DataType": 2}, {"ID": 43, "Name": "TENANT_ID", "OrderNo": 43, "Params": "SQLSERVER_DF_CONSTRAINT_NAME=DF__COSTUNITS__TENAN__0E4736E9", "DataType": 1, "IndexType": 2, "DefaultValue": "0", "Not_Nullable": true, "DataLength": 50}, {"ID": 5, "Name": "ELEM_ID", "OrderNo": 44, "DisplayName": "要素属性ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "ELEM_LIB", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3591.27,2285.00\r\nP2=3450.00,2285.00\r\nP3=3450.00,2285.00\r\nP4=3309.69,2285.00\r\nHookP1=19.73,58.61\r\nHookP2=290.31,60.68\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}]}}, {"Name": "COSTUINT", "Caption": "核算对象配置表", "OrderNo": 70, "GraphDesc": "Left=2660.62\r\nTop=2223.47", "MetaFields": {"Count": 34, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_ACTIVITYPROPERTIES", "RelateField": "ID", "Not_Nullable": true, "DataLength": 50, "GraphDesc": "P1=68.39,821.02\r\nP2=-99.19,821.02\r\nP3=-99.19,2243.00\r\nP4=2660.62,2243.00\r\nHookP1=164.00,265.00\r\nHookP2=20.38,19.53\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=1"}, {"ID": 2, "Name": "CREATE_BY", "OrderNo": 2, "DataType": 1, "DataLength": 50}, {"ID": 3, "Name": "CREATE_BY_ORG", "OrderNo": 3, "DataType": 1, "DataLength": 50}, {"ID": 4, "Name": "CREATE_BY_POST", "OrderNo": 4, "DataType": 1, "DataLength": 50}, {"ID": 5, "Name": "CREATE_TIME", "OrderNo": 5, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 6, "Name": "UPDATE_BY", "OrderNo": 6, "DataType": 1, "DataLength": 50}, {"ID": 7, "Name": "UPDATE_TIME", "OrderNo": 7, "DataType": 4, "DataTypeName": "datetime2"}, {"ID": 8, "Name": "ANALYSISTEMPLATE", "OrderNo": 8, "DataType": 1, "DataLength": 255}, {"ID": 9, "Name": "COORDINATEX", "OrderNo": 9, "DataType": 3}, {"ID": 10, "Name": "coordinateY", "OrderNo": 10, "DataType": 3}, {"ID": 11, "Name": "DEVICEALIAS", "OrderNo": 11, "DataType": 1, "DataLength": 200}, {"ID": 12, "Name": "DEVICEIDS", "OrderNo": 12, "DataType": 1, "DataLength": 3000}, {"ID": 13, "Name": "DIGIT", "OrderNo": 13, "DataType": 2}, {"ID": 14, "Name": "DISPATCHDESK", "OrderNo": 14, "DataType": 2}, {"ID": 15, "Name": "FORMID", "OrderNo": 15, "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "LEDGERENTRY", "OrderNo": 16, "DataType": 2}, {"ID": 17, "Name": "MDMCODE", "OrderNo": 17, "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "MEMO", "OrderNo": 18, "DataType": 1, "DataLength": 1000}, {"ID": 19, "Name": "MOBILEINPUT", "OrderNo": 19, "DataType": 2}, {"ID": 20, "Name": "NAME", "OrderNo": 20, "DataType": 1, "DataLength": 200}, {"ID": 21, "Name": "operatingRadius", "OrderNo": 21, "DataType": 3}, {"ID": 22, "Name": "ORGID", "OrderNo": 22, "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "PRODUCTIVE", "Memo": "1是作业活动，其余是普通核算对象", "OrderNo": 23, "DisplayName": "是活动", "DataType": 2}, {"ID": 25, "Name": "PRODUCTIVETYPE", "OrderNo": 24, "DataType": 2}, {"ID": 26, "Name": "TMSORT", "OrderNo": 25, "DataType": 1, "DataLength": 1000}, {"ID": 27, "Name": "TMUSED", "OrderNo": 26, "DataType": 2}, {"ID": 28, "Name": "TYPE", "OrderNo": 27, "DataType": 2}, {"ID": 29, "Name": "UNITNO", "OrderNo": 28, "DataType": 1, "DataLength": 50}, {"ID": 30, "Name": "UNITTYPE", "OrderNo": 29, "DataType": 1, "DataLength": 100}, {"ID": 31, "Name": "USSTATUS", "OrderNo": 30, "DataType": 2}, {"ID": 32, "Name": "USEACCOUNTING", "OrderNo": 31, "DataType": 2}, {"ID": 33, "Name": "isOntime", "OrderNo": 32, "DataType": 2}, {"ID": 34, "Name": "TENANT_ID", "OrderNo": 33, "Params": "SQLSERVER_DF_CONSTRAINT_NAME=DF__COSTUINT__TENANT__098281CC", "DataType": 1, "IndexType": 2, "DefaultValue": "0", "Not_Nullable": true, "DataLength": 50}, {"ID": 13, "Name": "PID", "Memo": "对于作业活动应该是活动的PID", "OrderNo": 34, "DisplayName": "父ID", "DataType": 1, "DataLength": 100}]}}, {"ID": 71, "Name": "核算对象", "TypeName": "GROUP", "Memo": "核算对象", "CreateDate": "2025-02-19 08:58:19", "OrderNo": 71, "GraphDesc": "Left=2637.05\r\nTop=2180.46\r\nWidth=840.91\r\nHeight=856.57\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 73, "Name": "分组73", "TypeName": "GROUP", "Memo": "由Excel解析组件解析出的信息，保存结构，因整体Excel表信息较大，采用文件模式保存及获取\r\n其他区域信息及结构信息采用普通关系数据表结构存储", "CreateDate": "2025-04-02 16:28:09", "OrderNo": 72, "GraphDesc": "Left=2627.40\r\nTop=3072.40", "BgColor": ********, "MetaFields": {"items": []}}, {"ID": 74, "Name": "DIGITAL_LEDGER_EXCEL", "Caption": "数字化台账Excel信息", "CreateDate": "2025-04-02 16:28:53", "OrderNo": 73, "GraphDesc": "Left=2633.20\r\nTop=3133.20", "MetaFields": {"Count": 12, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "BELONG_ID", "Memo": "目前关联表单，excel所在表单ID", "OrderNo": 2, "DisplayName": "所属ID", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "SHOW_NAME", "OrderNo": 3, "DisplayName": "名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "RULES_INFO", "OrderNo": 4, "DisplayName": "解析策略", "DataType": 1, "DataLength": 1000}, {"ID": 15, "Name": "STRUCTS_INFO", "Memo": "结构信息json，长度问题弃用", "OrderNo": 5, "DisplayName": "结构信息", "DataType": 1}, {"ID": 16, "Name": "QUERYS_INFO", "Memo": "结构信息json，长度问题弃用", "OrderNo": 6, "DisplayName": "查询信息", "DataType": 1}, {"ID": 17, "Name": "RULES_FILE_ID", "OrderNo": 7, "DisplayName": "解析策略文件ID", "DataType": 1, "DataLength": 50}, {"ID": 18, "Name": "STRUCTS_FILE_ID", "OrderNo": 8, "DisplayName": "结构信息文件ID", "DataType": 1, "DataLength": 50}, {"ID": 19, "Name": "PARSE_FILE_ID", "Memo": "上传的模版文件，通过其解析出Excel", "OrderNo": 9, "DisplayName": "解析文件ID", "DataType": 1, "DataLength": 50}, {"ID": 20, "Name": "QUERY_FILE_ID", "OrderNo": 10, "DisplayName": "查询文件ID", "DataType": 1, "DataLength": 50}, {"ID": 21, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 11, "DisplayName": "可用标识", "DataType": 2}, {"ID": 22, "Name": "TMSORT", "OrderNo": 12, "DisplayName": "排序", "DataType": 2}]}}, {"ID": 75, "Name": "DIGITAL_LEDGER_EXCEL_AREA", "Caption": "数字化台账Excel区域信息", "CreateDate": "2025-04-02 16:31:03", "OrderNo": 74, "GraphDesc": "Left=2988.40\r\nTop=3125.20", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50}, {"ID": 12, "Name": "BELONG_ID", "Memo": "目前关联表单，excel所在表单ID", "OrderNo": 2, "DisplayName": "所属ID", "DataType": 1, "DataLength": 50}, {"ID": 13, "Name": "EXCEL_ID", "OrderNo": 3, "DisplayName": "父数据ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "DIGITAL_LEDGER_EXCEL", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=2936.20,3204.00\r\nP2=2962.00,3204.00\r\nP3=2962.00,3204.00\r\nP4=2988.40,3204.00\r\nHookP1=282.80,70.80\r\nHookP2=19.60,78.80\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 14, "Name": "AREA_ID", "OrderNo": 4, "DisplayName": "区域ID", "DataType": 1, "DataLength": 50}, {"ID": 15, "Name": "AREA_NAME", "OrderNo": 5, "DisplayName": "区域名称", "DataType": 1, "DataLength": 100}, {"ID": 16, "Name": "STRUCTS_INFO", "Memo": "接收json信息", "OrderNo": 6, "DisplayName": "结构信息", "DataType": 1}, {"ID": 17, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 7, "DisplayName": "可用标识", "DataType": 2}, {"ID": 18, "Name": "TMSORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2}]}}]}}]}