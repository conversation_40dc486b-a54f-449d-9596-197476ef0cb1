-- TDSSQL数据源查询语句：获取上个班次采集点数据（行转列）
-- 直接在前端数据源配置的"查询语句"中使用此SQL
-- 数据源类型：TDSSQL

-- 参数说明：
-- @objid: 当前班次的机构编码（必填）
-- @acctobj_id: 核算对象ID（必填）

-- 方案1：返回键值对格式（推荐用于动态列）
-- 前端可以根据这些数据动态构建表格
SELECT
    input_time,
    collect_point_name,
    collect_point_value
FROM get_previous_shift_collect_data_final('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121')
ORDER BY input_time, collect_point_name;

-- 方案2：如果采集点固定，可以直接进行行转列
-- 注意：需要根据实际的采集点名称调整CASE语句
/*
WITH raw_data AS (
    SELECT * FROM get_previous_shift_collect_data_final(@objid, @acctobj_id)
)
SELECT
    input_time as "时间",
    device_name as "设备",
    MAX(CASE WHEN collect_point_name = '采集点1' THEN collect_point_value END) as "采集点1",
    MAX(CASE WHEN collect_point_name = '采集点2' THEN collect_point_value END) as "采集点2",
    MAX(CASE WHEN collect_point_name = '采集点3' THEN collect_point_value END) as "采集点3",
    MAX(CASE WHEN collect_point_name = '采集点4' THEN collect_point_value END) as "采集点4",
    MAX(CASE WHEN collect_point_name = '采集点5' THEN collect_point_value END) as "采集点5",
    MAX(CASE WHEN collect_point_name = '采集点6' THEN collect_point_value END) as "采集点6",
    MAX(CASE WHEN collect_point_name = '采集点7' THEN collect_point_value END) as "采集点7"
FROM raw_data
GROUP BY input_time, device_name, ipt_id
ORDER BY input_time;
*/

-- 使用步骤：
-- 1. 在前端数据源管理中新建数据源
-- 2. 数据源类型选择：TDSSQL
-- 3. 在"查询语句"中粘贴上面的SQL（选择方案1或方案2）
-- 4. 添加输入参数：objid 和 acctobj_id
-- 5. 保存并测试数据源
