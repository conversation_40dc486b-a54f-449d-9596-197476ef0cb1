-- PostgreSQL 存储过程：获取上个班次的采集点数据（优化版）
-- 解决 "一个字段定义列表需要返回 'record' 的函数" 错误
-- 提供动态列和固定列两种方案

-- 方案1：视图方式（推荐 - 避免函数调用问题）
CREATE OR REPLACE VIEW v_previous_shift_collect_data AS
WITH current_shift AS (
    -- 获取当前班次信息
    SELECT
        sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj,
        COALESCE(
            (SELECT porgcode FROM sys_org_relation WHERE orgcode = sd.objid AND used = 1),
            sd.orgcode
        ) as parent_orgcode
    FROM shift_data sd
    WHERE sd.sbsj <= CURRENT_TIMESTAMP
      AND sd.xbsj > CURRENT_TIMESTAMP
),
previous_shift AS (
    -- 根据当前班次查找上个班次
    SELECT
        s.shiftclassid,
        s.sbsj,
        s.objid,
        cs.objid as current_objid,
        cs.parent_orgcode
    FROM shift_data s
    INNER JOIN current_shift cs ON s.orgcode = cs.parent_orgcode
    WHERE s.xbsj = cs.sbsj
    ORDER BY s.dbrq DESC, s.sbsj DESC
)
-- 查询采集点数据，返回键值对格式
SELECT
    TO_CHAR(mx.input_time, 'YYYY-MM-DD HH24:MI:SS') as input_time,
    mx.collect_point as collect_point_name,
    mx.collect_point_val as collect_point_value,
    mx.collect_point_text,
    ps.current_objid,
    ai.acctobj_id
FROM acctobj_inputmx mx
INNER JOIN acctobj_input ai ON ai.id = mx.ipt_id AND ai.tmused = 1
INNER JOIN previous_shift ps ON (
    ai.bcdm = ps.shiftclassid
    AND ai.sbsj = ps.sbsj
    AND ai.team_id = ps.objid
)
WHERE mx.tmused = 1;


-- 方案2：改进的函数（解决返回类型问题）
CREATE OR REPLACE FUNCTION get_previous_shift_collect_data_v2(
    p_objid VARCHAR(200),
    p_acctobj_id VARCHAR(200)
)
RETURNS SETOF RECORD AS $$
DECLARE
    rec RECORD;
    now_dt TIMESTAMP;
    parent_orgcode VARCHAR(200);
BEGIN
    now_dt := CURRENT_TIMESTAMP;

    -- 获取父级机构代码
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = p_objid AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = p_objid LIMIT 1)
    ) INTO parent_orgcode;

    -- 使用游标方式返回数据
    FOR rec IN
        WITH current_shift AS (
            SELECT
                sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj
            FROM shift_data sd
            WHERE sd.orgcode = parent_orgcode
              AND sd.objid = p_objid
              AND sd.sbsj <= now_dt
              AND sd.xbsj > now_dt
            ORDER BY sd.dbrq DESC, sd.sbsj DESC
            LIMIT 1
        ),
        previous_shift AS (
            SELECT
                s.shiftclassid,
                s.sbsj,
                s.objid
            FROM shift_data s
            INNER JOIN current_shift cs ON s.orgcode = cs.orgcode
            WHERE s.xbsj = cs.sbsj
            ORDER BY s.dbrq DESC, s.sbsj DESC
            LIMIT 1
        )
        SELECT
            TO_CHAR(mx.input_time, 'YYYY-MM-DD HH24:MI:SS') as input_time,
            mx.collect_point as collect_point_name,
            mx.collect_point_val as collect_point_value,
        FROM acctobj_inputmx mx
        WHERE mx.tmused = 1
          AND EXISTS (
              SELECT 1
              FROM acctobj_input ai
              INNER JOIN previous_shift ps ON (
                  ai.acctobj_id = p_acctobj_id
                  AND ai.bcdm = ps.shiftclassid
                  AND ai.sbsj = ps.sbsj
                  AND ai.team_id = ps.objid
              )
              WHERE ai.tmused = 1
                AND ai.id = mx.ipt_id
          )
        ORDER BY mx.input_time, mx.collect_point
    LOOP
        RETURN NEXT rec;
    END LOOP;

    RETURN;
END;
$$ LANGUAGE plpgsql;

-- 使用示例：

-- 1. 使用视图（推荐方式）
-- 动态列查询
SELECT * FROM v_previous_shift_collect_data
WHERE current_objid = 'ZQW9LIB5301C4LN5JG0207'
  AND acctobj_id = 'ZR880Q91V07EDEWGGZ1121'
ORDER BY input_time, collect_point_name;

-- 2. 使用改进的函数
SELECT * FROM get_previous_shift_collect_data_v2('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121')
AS t(input_time TEXT,
     collect_point_name TEXT, collect_point_value TEXT);

-- 3. 原函数的正确调用方式（如果仍要使用）
SELECT * FROM get_previous_shift_collect_data_final('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121')
AS t(input_time VARCHAR,
     collect_point_name VARCHAR, collect_point_value VARCHAR);
