-- 前端直接查询上个班次采集点数据的解决方案
-- 提供动态列和固定列两种实现方式

-- ========================================
-- 方案A：动态列查询（推荐）
-- ========================================
-- 适用场景：采集点数量和名称不固定，前端需要动态构建表格
-- 返回格式：每行一个采集点数据

-- A1. 使用视图查询（最简单，推荐）
SELECT
    input_time as "录入时间",
    device_name as "设备名称",
    collect_point_name as "采集点名称",
    collect_point_value as "采集点数值"
FROM v_previous_shift_collect_data
WHERE current_objid = @objid
  AND acctobj_id = @acctobj_id
ORDER BY input_time, collect_point_name;

-- A2. 直接SQL查询（不依赖函数和视图）
WITH current_shift AS (
    SELECT
        sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj,
        COALESCE(
            (SELECT porgcode FROM sys_org_relation WHERE orgcode = @objid AND used = 1),
            (SELECT orgcode FROM shift_data WHERE objid = @objid LIMIT 1)
        ) as parent_orgcode
    FROM shift_data sd
    WHERE sd.objid = @objid
      AND sd.sbsj <= CURRENT_TIMESTAMP
      AND sd.xbsj > CURRENT_TIMESTAMP
    ORDER BY sd.dbrq DESC, sd.sbsj DESC
    LIMIT 1
),
previous_shift AS (
    SELECT s.shiftclassid, s.sbsj, s.objid
    FROM shift_data s
    INNER JOIN current_shift cs ON s.orgcode = cs.parent_orgcode
    WHERE s.xbsj = cs.sbsj
    ORDER BY s.dbrq DESC, s.sbsj DESC
    LIMIT 1
)
SELECT
    TO_CHAR(mx.input_time, 'YYYY-MM-DD HH24:MI:SS') as "录入时间",
    TO_CHAR(mx.job_input_time, 'YYYY-MM-DD HH24:MI:SS') as "作业时间",
    mx.collect_point_text as "设备名称",
    mx.collect_point as "采集点名称",
    mx.collect_point_val as "采集点数值"
FROM acctobj_inputmx mx
INNER JOIN acctobj_input ai ON ai.id = mx.ipt_id AND ai.tmused = 1
INNER JOIN previous_shift ps ON (
    ai.acctobj_id = @acctobj_id
    AND ai.bcdm = ps.shiftclassid
    AND ai.sbsj = ps.sbsj
    AND ai.team_id = ps.objid
)
WHERE mx.tmused = 1
ORDER BY mx.input_time, mx.collect_point;

-- ========================================
-- 方案B：固定列查询（行转列）
-- ========================================
-- 适用场景：采集点固定，前端需要表格形式展示
-- 返回格式：每行一个时间点，列为各个采集点

-- B1. 动态生成行转列SQL的函数
CREATE OR REPLACE FUNCTION generate_pivot_sql(
    p_objid VARCHAR(200),
    p_acctobj_id VARCHAR(200)
)
RETURNS TEXT AS $$
DECLARE
    collect_points TEXT[];
    pivot_columns TEXT := '';
    sql_text TEXT;
    point_name TEXT;
BEGIN
    -- 获取所有采集点名称
    SELECT ARRAY_AGG(DISTINCT collect_point_name ORDER BY collect_point_name)
    INTO collect_points
    FROM v_previous_shift_collect_data
    WHERE current_objid = p_objid AND acctobj_id = p_acctobj_id;

    -- 构建CASE语句
    FOREACH point_name IN ARRAY collect_points
    LOOP
        IF pivot_columns != '' THEN
            pivot_columns := pivot_columns || ',
    ';
        END IF;
        pivot_columns := pivot_columns ||
            'MAX(CASE WHEN collect_point_name = ''' || point_name ||
            ''' THEN collect_point_value END) as "' || point_name || '"';
    END LOOP;

    -- 构建完整SQL
    sql_text := 'SELECT
    input_time as "录入时间",
    device_name as "设备名称",
    ' || pivot_columns || '
FROM v_previous_shift_collect_data
WHERE current_objid = ''' || p_objid || '''
  AND acctobj_id = ''' || p_acctobj_id || '''
GROUP BY input_time, device_name
ORDER BY input_time';

    RETURN sql_text;
END;
$$ LANGUAGE plpgsql;

-- B2. 手动指定采集点的行转列查询（示例）
-- 注意：需要根据实际采集点名称修改
WITH raw_data AS (
    SELECT * FROM v_previous_shift_collect_data
    WHERE current_objid = 'ZQW9LIB5301C4LN5JG0207' AND acctobj_id = 'ZR880Q91V07EDEWGGZ1121'
)
SELECT
    input_time as "录入时间",
    MAX(CASE WHEN collect_point_name = '采集点1' THEN collect_point_value END) as "采集点1",
    MAX(CASE WHEN collect_point_name = '采集点2' THEN collect_point_value END) as "采集点2",
    MAX(CASE WHEN collect_point_name = '采集点3' THEN collect_point_value END) as "采集点3",
    MAX(CASE WHEN collect_point_name = '采集点4' THEN collect_point_value END) as "采集点4",
    MAX(CASE WHEN collect_point_name = '采集点5' THEN collect_point_value END) as "采集点5",
    MAX(CASE WHEN collect_point_name = '采集点6' THEN collect_point_value END) as "采集点6",
    MAX(CASE WHEN collect_point_name = '采集点7' THEN collect_point_value END) as "采集点7"
FROM raw_data
GROUP BY input_time
ORDER BY input_time;

-- ========================================
-- 使用说明
-- ========================================

-- 1. 前端数据源配置：
--    - 数据源类型：TDSSQL
--    - 参数：@objid (当前登录人机构编码), @acctobj_id (核算对象ID)
--    - 查询语句：选择上面的方案A1、A2、B1或B2

-- 2. 推荐使用顺序：
--    第一选择：方案A1（使用视图）
--    第二选择：方案A2（直接SQL）
--    第三选择：方案B2（固定列）

-- 3. 参数示例：
--    @objid = 'ZQW9LIB5301C4LN5JG0207'
--    @acctobj_id = 'ZR880Q91V07EDEWGGZ1121'

-- 4. 获取动态行转列SQL：
SELECT generate_pivot_sql('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121');
