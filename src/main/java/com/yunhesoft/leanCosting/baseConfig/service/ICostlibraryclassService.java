package com.yunhesoft.leanCosting.baseConfig.service;

import java.util.List;

import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryclassQueryDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryclassSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costlibraryclass;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryclassVo;

public interface ICostlibraryclassService {
	 /**
	 * 查询数据
	 * 
	 * @param dto
	 * @return
	 */
	public List<CostlibraryclassVo> getData(CostlibraryclassQueryDto dto);
	 /**
	 * 通过ID查询
	 * 
	 * @param id
	 * @return
	 */
	public CostlibraryclassVo getBean(String id);
	 /**
	 * 保存数据
	 * 
	 * @param bean
	 * @return
	 */
	public String save(List<CostlibraryclassSaveDto> bean);
	 /**
	 * 更新数据
	 * 
	 * @param bean
	 * @return
	 */
	public int update(List<CostlibraryclassSaveDto> bean);
	 /**
	 * 删除数据
	 * 
	 * @param bean
	 * @return
	 */
	public int delete(List<CostlibraryclassSaveDto> bean);
	/**
	 *初始化默认分类
	 * @return
	 */
	public CostlibraryclassVo initDefault();
	
	/**
	 * 排序
	 * @param updateList
	 * @param oldTmsort
	 * @param i
	 */
	public void getClassLists(List<Costlibraryclass> updateList, Integer oldTmsort, int i);
	
	public List<Costlibraryclass> getClassPidData(String pid);
	
	public Integer getClassSort(String pid);

	
	/**
	 *	保存项目库分类数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveLibraryClassData(List<Costlibraryclass> addList,List<Costlibraryclass> updList,List<Costlibraryclass> delList);
	
	
}
