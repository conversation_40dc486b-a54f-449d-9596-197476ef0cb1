package com.yunhesoft.leanCosting.baseConfig.service.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostclassQueryDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostclassSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostclassVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostclassService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class CostclassServiceImpl implements ICostclassService {
	
	@Autowired
	private EntityService entityService;
	
	@Autowired
	private ICostToolService costToolService;

	@Override
	public List<CostclassVo> getData(CostclassQueryDto dto) {
		Where where = Where.create();
		where.eq(Costclass::getTmused, 1);
		if (!StringUtils.isEmpty(dto.getId())) {
			where.eq(Costclass::getId, dto.getId());
		}
		if (!StringUtils.isEmpty(dto.getUnitcode())) {
			where.eq(Costclass::getUnitid, dto.getUnitcode());
		}
		if (!StringUtils.isEmpty(dto.getUnitcode())) {
			where.eq(Costclass::getBegintime, dto.getVersion());
		}
		Order order = Order.create();
		List<Costclass> configs = entityService.queryList(Costclass.class, where, order);
		List<CostclassVo> _list = new ArrayList<CostclassVo>();
		for (Costclass saveDto : configs) {
			// 数据
			CostclassVo saveObj = new CostclassVo();
			// 复制
			ObjUtils.copyTo(saveDto, saveObj);
			_list.add(saveObj);
		}
		return _list;
	}

	@Override
	public CostclassVo getBean(String id) {
		Costclass obj = entityService.queryObjectById(Costclass.class, id);
		// 数据
		CostclassVo saveObj = new CostclassVo();
		// 复制
		ObjUtils.copyTo(obj, saveObj);
		return saveObj;
	}

	@Override
	public String save(List<CostclassSaveDto> bean) {
		String Unitcode = "";
		Map<String, String> idMap = new LinkedHashMap<String, String>();
		for (CostclassSaveDto saveDto : bean) {
			Unitcode = saveDto.getUnitcode();
			String ccname = saveDto.getCcname();
			String id = saveDto.getId();
			if (idMap.containsKey(ccname)) {
				return "[" + ccname + "]名称重复";
			} else {
				idMap.put(ccname, id);
			}
		}
		CostclassQueryDto dto = new CostclassQueryDto();
		dto.setUnitcode(Unitcode);
		List<CostclassVo> list2 = getData(dto);
		for (CostclassVo bean1 : list2) {
			String ccname = bean1.getCcname();
			String id = bean1.getId();
			if (idMap.containsKey(ccname)) {
				String _id = idMap.get(ccname);
				if (!_id.equals(id)) {// id不等
					return "[" + ccname + "]名称重复";
				}
			} else {
				idMap.put(ccname, id);
			}
		}

		String str = "";
		List<Costclass> infos = new ArrayList<Costclass>();
		List<Costclass> _infos = new ArrayList<Costclass>();
		int tmsort = costToolService.maxSprt(Costclass.class);
		for (CostclassSaveDto saveBean : bean) {
			Costclass saveObj = new Costclass();
			ObjUtils.copyTo(saveBean, saveObj);
			String id = saveObj.getId();
			if (!StringUtils.isEmpty(id)) {
				_infos.add(saveObj);
			} else {
				saveObj.setTmsort(tmsort);
				saveObj.setId(TMUID.getUID());
				infos.add(saveObj);
				tmsort++;
			}
		}
		if (infos.size() > 0)
			entityService.insertBatch(infos);
		if (_infos.size() > 0)
			entityService.updateByIdBatch(_infos);
		return str;
	}

	@Override
	public int update(List<CostclassSaveDto> bean) {
		List<Costclass> infos = new ArrayList<Costclass>();
		for (CostclassSaveDto saveBean : bean) {
			// 数据
			Costclass saveObj = new Costclass();
			ObjUtils.copyTo(saveBean, saveObj);
			infos.add(saveObj);
		}
		return entityService.updateByIdBatch(infos);
	}

	@Override
	public int delete(List<CostclassSaveDto> bean) {
		List<Costclass> infos = new ArrayList<Costclass>();
		for (CostclassSaveDto saveBean : bean) {
			// 数据
			Costclass saveObj = new Costclass();
			ObjUtils.copyTo(saveBean, saveObj);
			infos.add(saveObj);
		}
		return entityService.deleteByIdBatch(infos);
	}

	@Override
	public List<CostclassVo> getDataPermCfgTree() {
		// TODO Auto-generated method stub
		return null;
	}
}
