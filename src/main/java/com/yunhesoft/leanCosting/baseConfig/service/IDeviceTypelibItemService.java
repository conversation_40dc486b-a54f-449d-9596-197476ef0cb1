package com.yunhesoft.leanCosting.baseConfig.service;

import java.util.List;

import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibItem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;

public interface IDeviceTypelibItemService {

	/**
	 * 通过PID查询
	 * @return
	 */
	public List<DeviceTypelibItem> getItemData(DeviceTypelibDto dto);

	/**
	 * 通过PID 查询树形
	 * @param dto
	 * @return
	 */
	public List<Costuint> getItemDataTreeAll(DeviceTypelibDto dto);
	/**
	 * 保存数据
	 * 
	 * @param inserts
	 * @param updatas
	 * @param dels
	 * @return
	 */
	public String saveData(List<DeviceTypelibItem> addList, List<DeviceTypelibItem> updList,
			List<DeviceTypelibItem> delList);

	
}
