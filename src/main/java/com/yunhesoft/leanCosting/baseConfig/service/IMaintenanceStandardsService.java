package com.yunhesoft.leanCosting.baseConfig.service;

import java.util.List;

import com.yunhesoft.leanCosting.baseConfig.entity.dto.MaintenanceStandardsDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.MaintenanceStandards;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.MaintenanceStandardsComboVo;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.MaintenanceStandardsVo;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;

public interface IMaintenanceStandardsService {

	
	/**
	 * TODO:接口
	 * 根据传入的核算对象ID，得到单元类型，从而得到核算对象的保养类型。
	 * @param dto
	 * @return	核算对象的保养类型
	 */
	 public List<Costuint> getMaintenanceType(Costuint costuint);
	
	/**
	 * TODO:接口
	 * 据传入的保养类型名称和单元类型ID得到类型下的保养标准。
	 * @param dto
	 * @return	List<MaintenanceStandards>
	 */
	public List<MaintenanceStandards> getItemData(MaintenanceStandardsDto dto);

	
	
	
	/**
	 * 查询
	 * @param dto
	 * @return
	 */

	public List<MaintenanceStandards> getData(MaintenanceStandardsDto dto);

	public String saveAndUpdate(List<MaintenanceStandards> bean);
	
	public int save(List<MaintenanceStandards> bean);

	public int update(List<MaintenanceStandards> bean);
	
	public int delete(List<MaintenanceStandards> bean);

	
	/**
	 * 导出
	 * @param list
	 * @return
	 */
	public List<MaintenanceStandardsVo> excel(List<MaintenanceStandards> list);

	/**
	 * 导入
	 * @param teList
	 * @param devicetypelibraryId 
	 * @return
	 */
	public String importExcel(List<MaintenanceStandardsVo> teList, String devicetypelibraryId);




}
