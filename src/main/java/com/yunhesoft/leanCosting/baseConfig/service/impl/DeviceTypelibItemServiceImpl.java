package com.yunhesoft.leanCosting.baseConfig.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibItem;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.DevicetypelibraryVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryitemService;
import com.yunhesoft.leanCosting.baseConfig.service.IDeviceTypelibItemService;
import com.yunhesoft.leanCosting.baseConfig.service.IDevicetypelibraryService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class DeviceTypelibItemServiceImpl implements IDeviceTypelibItemService {

	@Autowired
	private EntityService entityService;
	
	@Autowired
	private IDevicetypelibraryService devicetypelibraryService;
	
	@Autowired
	private ICostuintService costuintService;

	@Autowired
    private ICostlibraryitemService costlibraryitemService;
	
	@Override
	public List<DeviceTypelibItem> getItemData(DeviceTypelibDto dto) {
		Where where = Where.create();
		if(dto!=null) {
			if (!StringUtils.isEmpty(dto.getPid())) {
				where.eq(DeviceTypelibItem::getPid, dto.getPid());
			}
		}
		where.eq(DeviceTypelibItem::getTmused, 1);
		Order order = Order.create();
		return entityService.queryList(DeviceTypelibItem.class, where, order);
	}
	
	@Override
	public List<Costuint> getItemDataTreeAll(DeviceTypelibDto dto) {
		List<DevicetypelibraryVo> lists = new ArrayList<DevicetypelibraryVo>();
		List<Devicetypelibrary> items = devicetypelibraryService.getData(null);
		List<DevicetypelibraryVo> list = ObjUtils.copyToList(items, DevicetypelibraryVo.class);
		
		for (DevicetypelibraryVo item : list) {
			if(item.getPid()==null) {
				item.setPid("");
			}
		}
		String pid = dto.getPid();//PID
		Map<String, DevicetypelibraryVo> deviceTypeMap = list.stream().collect(Collectors.toMap(DevicetypelibraryVo::getId, Function.identity()));
		
		Map<String, List<DevicetypelibraryVo>> deviceTypePidMap = list.stream().collect(Collectors.groupingBy(DevicetypelibraryVo::getPid));
		DevicetypelibraryVo bean = deviceTypeMap.get(pid);
		lists.add(bean);
		
		List<DevicetypelibraryVo> list_s = new ArrayList<DevicetypelibraryVo>();
		
		List<DevicetypelibraryVo> listNews = new ArrayList<DevicetypelibraryVo>();
		if(deviceTypePidMap.containsKey(pid)) {//如果没有PID，那么就只有一个节点
//			List<DevicetypelibraryVo> item_s = deviceTypePidMap.get(pid);
			for (DevicetypelibraryVo treeVo : lists) {
				String id = treeVo.getId();
				if(deviceTypePidMap.containsKey(id)) {
					List<DevicetypelibraryVo> children = deviceTypePidMap.get(id);
					if(children!=null) {
						treeVo.setChildren(children);
						listNews.addAll(children);
					}
				}
				list_s.add(treeVo);
				listNews.add(treeVo);
			}
		}
		classTreeVo(list_s);
		//核算单元ID
		List<String> ids = new ArrayList<String>();
		for (DevicetypelibraryVo vo : listNews) {
			String id = vo.getId();
			if(!ids.contains(id)) {
				ids.add(id);
			}
		}
		CostuintQueryDto dto1 = new CostuintQueryDto();
		dto1.setUnittypeIds(ids);
		List<Costuint> costuints = costuintService.getDatas(dto1);
		return costuints;
	}
	
	
	/**
	 * 迭代数据
	 * @param treeVos
	 */
	private void classTreeVo(List<DevicetypelibraryVo> treeVos){
		for (DevicetypelibraryVo treeVo : treeVos) {
			List<DevicetypelibraryVo> list = treeVo.getChildren();
			if(list!=null) {
				classTreeVo(list);
			}
		}
	}
	

	@Override
	public String saveData(List<DeviceTypelibItem> addList, List<DeviceTypelibItem> updList,
			List<DeviceTypelibItem> delList) {
//		ProductSaveDto productSaveDto = new ProductSaveDto();
		String result = "";
//		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
//			addList = costlibraryitemService.saveDeviceTypelibItem(productSaveDto,addList);
//			List<String> list = new ArrayList<String>();
//			for (DeviceTypelibItem deviceTypelibItem : addList) {
//				String itemId = deviceTypelibItem.getCostlibraryitemId();
//				if(!list.contains(itemId)) {
//					list.add(itemId);
//				}else {
//					result = "同步项目库失败！有重名";
//				}
//			}
//		}
		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {	
			if (entityService.insertBatch(addList) == 0) {
				result = "添加失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
//			updList = costlibraryitemService.saveDeviceTypelibItem(productSaveDto,updList);
			if (entityService.updateByIdBatch(updList) == 0) {
				result = "更新失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
			if (entityService.deleteByIdBatch(delList, 500) == 0) {
				result = "删除失败！";
			}
		}
		return result;
	}

	

}
