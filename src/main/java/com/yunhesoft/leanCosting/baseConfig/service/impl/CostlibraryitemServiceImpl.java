package com.yunhesoft.leanCosting.baseConfig.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryclassQueryDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryitemQueryDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryitemSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.ProductSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.CostEnvironmentConfig;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costlibraryclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costlibraryitem;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibItem;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryclassVo;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryitemExcleVo;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryitemVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryclassService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryitemService;
import com.yunhesoft.leanCosting.baseConfig.service.IToolService;
import com.yunhesoft.leanCosting.order.entity.po.ProductControl;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostMeteringUnit;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.service.ISysDictDataService;

@Service
public class CostlibraryitemServiceImpl implements ICostlibraryitemService {
	@Autowired
	private EntityService entityService;
	@Autowired
	private ICostlibraryclassService costlibraryclassService;
	@Autowired
	private ISysDictDataService sysDictDataService;
//	@Autowired
//	private IUnitMethodService unitMethodService;
	@Autowired
	private IUnitMethodService methodService;
	@Autowired
	private IToolService toolService;

	@Override
	public List<CostlibraryitemVo> getData(CostlibraryitemQueryDto dto) {
		Where where = Where.create();
		if (dto != null) {
			if (!StringUtils.isEmpty(dto.getId())) {
				where.eq(Costlibraryitem::getId, dto.getId());
			}
			if (!StringUtils.isEmpty(dto.getIdList())) {
				where.in(Costlibraryitem::getId, dto.getIdList().toArray());
			}
			String pId = dto.getPid();
			if (!StringUtils.isEmpty(pId)) {
				String[] pIdArr = pId.split(",");
				if (pIdArr.length == 1) {
					where.eq(Costlibraryitem::getPid, pId);
				} else {
					List<String> pIdList = Arrays.asList(pIdArr);
					where.in(Costlibraryitem::getPid, pIdList.toArray());
				}
			}
			boolean isNullErpCode = dto.isNullErpCode();
			if (isNullErpCode) {
				where.notNull(Costlibraryitem::getErpcode);
			}
			if (!StringUtils.isEmpty(dto.getCcname())) {
				where.like(Costlibraryitem::getCiname, dto.getCcname());
			}
		}
		where.eq(Costlibraryitem::getTmused, 1);// 是否使用
		Order order = Order.create();
		order.orderByAsc(Costlibraryitem::getTmsort);
		List<Costlibraryitem> configs = entityService.queryList(Costlibraryitem.class, where, order);
		List<CostlibraryitemVo> _list = new ArrayList<CostlibraryitemVo>();
		for (Costlibraryitem saveDto : configs) {
			// 数据
			CostlibraryitemVo saveObj = new CostlibraryitemVo();
			// 复制
			ObjUtils.copyTo(saveDto, saveObj);
			saveObj.setCcname(saveDto.getCiname());
			_list.add(saveObj);
		}
		SysDictData dictData = new SysDictData();
		// 服务类型读取数据字典
		dictData.setDictType("sum_type");
		List<SysDictData> dictDatas = sysDictDataService.selectDictDataList(dictData);
		Map<String, String> map = new HashMap<String, String>();
		for (SysDictData sysDictData : dictDatas) {
			if ("0".equals(sysDictData.getStatus()))
				map.put(sysDictData.getDictValue(), sysDictData.getDictLabel());
		}
		for (CostlibraryitemVo costlibraryitemVo : _list) {
			if (costlibraryitemVo.getPid() == null) {
				costlibraryitemVo.setPid("");
			}
			String sumtype = String.valueOf(costlibraryitemVo.getSumtype());
			if (map.containsKey(sumtype)) {
				costlibraryitemVo.setSumtypeName(map.get(sumtype));
			}
		}

		return _list;
	}
	
	/**
	 * COSTLIBRARYITEM,id,COSTLIBRARYCLASS,ccname
	 * @return
	 */
	@Override
	public Map<String,String> getItemClassMap(List<String> items){
		Map<String,String> map = new LinkedHashMap<String, String>();
		CostlibraryitemQueryDto dto = new CostlibraryitemQueryDto();
		dto.setIdList(items);
		List<CostlibraryitemVo> costlibraryitemVos = getData(dto);//查询内容
		List<String> classPid = new ArrayList<String>();
		for (CostlibraryitemVo costlibraryitemVo : costlibraryitemVos) {
			String pid = costlibraryitemVo.getPid();
			String id = costlibraryitemVo.getId();
			if(!classPid.contains(pid)) {
				classPid.add(pid);
			}
			map.put(id, pid);
		}
		CostlibraryclassQueryDto dto_ = new CostlibraryclassQueryDto();
		dto_.setIdList(classPid);
		List<CostlibraryclassVo> costlibraryclassVos = costlibraryclassService.getData(dto_);
		Map<String, CostlibraryclassVo> deviceTypeMap = costlibraryclassVos.stream()
				.collect(Collectors.toMap(CostlibraryclassVo::getId, Function.identity()));
		for (Entry<String, String> entry : map.entrySet()) {
			String classId = entry.getValue();
			String className = "";
			if(deviceTypeMap.containsKey(classId)) {
				className = deviceTypeMap.get(classId).getCcname();
			}
			map.put(entry.getKey(), className);
		}
		return map;
		
	}

	@Override
	public CostlibraryitemVo getBean(String id) {
		Costlibraryitem obj = entityService.queryObjectById(Costlibraryitem.class, id);
		// 数据
		CostlibraryitemVo saveObj = new CostlibraryitemVo();
		// 复制
		ObjUtils.copyTo(obj, saveObj);
		return saveObj;
	}

	private String repeat(List<CostlibraryitemSaveDto> beans) {
		Map<String, List<CostlibraryitemSaveDto>> pidMap = new LinkedHashMap<String, List<CostlibraryitemSaveDto>>();
		Map<String, String> idMap = new LinkedHashMap<String, String>();
		Map<String, String> erpMap = new LinkedHashMap<String, String>();
		// 整理归纳数据<Pid,List<数据>>
		for (CostlibraryitemSaveDto saveBean : beans) {
			String pid = saveBean.getPid();
			String erpcode = saveBean.getErpcode();
			String id = saveBean.getId();
			if (id == null) {
				id = "";
			}
			// REP编码整体租户内唯一
			if (!StringUtils.isEmpty(erpcode)) {
				if (erpMap.containsKey(erpcode)) {
					return "[" + erpcode + "]Erp编码重复";
				} else {
					erpMap.put(erpcode, id);
				}
			}
			List<CostlibraryitemSaveDto> costlibraryitemSaveDtos = new ArrayList<CostlibraryitemSaveDto>();
			if (pidMap.containsKey(pid)) {
				costlibraryitemSaveDtos = pidMap.get(pid);
			}
			costlibraryitemSaveDtos.add(saveBean);
			// 数据
			pidMap.put(pid, costlibraryitemSaveDtos);
		}

		for (Entry<String, List<CostlibraryitemSaveDto>> entry : pidMap.entrySet()) {
			// 清空内容
			idMap.clear();
			erpMap.clear();

			String pid = entry.getKey();
			List<CostlibraryitemSaveDto> costlibraryitemSaveDtos = entry.getValue();
			// 1.数据中同一分类下不可名称重复
			for (CostlibraryitemSaveDto saveBean : costlibraryitemSaveDtos) {
				String ccname = saveBean.getCcname();
				String erpcode = saveBean.getErpcode();
				String id = saveBean.getId();
				if (id == null) {
					id = "";
				}
				if (idMap.containsKey(ccname)) {
					return "[" + ccname + "]名称重复";
				} else {
					idMap.put(ccname, id);
				}

				if (!StringUtils.isEmpty(erpcode)) {
					if (erpMap.containsKey(erpcode)) {
						return "[" + erpcode + "]Erp编码重复";
					} else {
						erpMap.put(erpcode, id);
					}
				}
			}
			// 判断每一个PID下判断名称
			CostlibraryitemQueryDto dto = new CostlibraryitemQueryDto();
			if (pid != null) {
				dto.setPid(pid);
			}
			List<CostlibraryitemVo> list2 = getData(dto);
			for (CostlibraryitemVo bean1 : list2) {
				String ccname = bean1.getCcname();
				String erpcode = bean1.getErpcode();
				String id = bean1.getId();
				if (idMap.containsKey(ccname)) {
					String _id = idMap.get(ccname);
					if (!_id.equals(id)) {// id不等
						return "[" + ccname + "]名称重复";
					}
				} else {
					idMap.put(ccname, id);
				}

				if (erpcode != null && erpcode.trim().length() > 0) {
					if (erpMap.containsKey(erpcode)) {
						String _id = erpMap.get(erpcode);
						if (!_id.equals(id)) {// id不等
							return "[" + erpcode + "]Erp编码重复";
						}
					} else {
						erpMap.put(erpcode, id);
					}
				}
			}
		}
		return "";
	}

	@Override
	public String save(List<CostlibraryitemSaveDto> bean) {
		String str = this.repeat(bean);
		if (str.length() > 0) {
			return str;
		}
		// 新增排序
		String pid = bean.get(0).getPid();
		Integer maxSort = getItemSort(pid);

		List<Costlibraryitem> infos = new ArrayList<Costlibraryitem>();
		List<Costlibraryitem> _infos = new ArrayList<Costlibraryitem>();
		for (CostlibraryitemSaveDto saveBean : bean) {
			Costlibraryitem saveObj = new Costlibraryitem();
			ObjUtils.copyTo(saveBean, saveObj);
			saveObj.setCiname(saveBean.getCcname());
			String id = saveObj.getId();
			if (!StringUtils.isEmpty(id)) {
				_infos.add(saveObj);
			} else {
				saveObj.setId(TMUID.getUID());
				saveObj.setTmsort(maxSort);
				saveObj.setTmused(1);
				maxSort++;
				infos.add(saveObj);
			}
		}

		if (infos.size() > 0)
			entityService.insertBatch(infos);
		if (_infos.size() > 0) {
			String[] ids = new String[_infos.size()];
			for (int i = 0; i < _infos.size(); i++) {
				ids[i] = _infos.get(i).getId();
			}
			Where where = Where.create();
			Order order = Order.create();
			order.orderByAsc(Costlibraryitem::getTmsort);
			List<String> pIdList = Arrays.asList(ids);
			where.in(Costlibraryitem::getId, pIdList.toArray());
			List<Costlibraryitem> list = entityService.queryList(Costlibraryitem.class, where, order);
			Map<String, Costlibraryitem> deviceTypeMap = list.stream()
					.collect(Collectors.toMap(Costlibraryitem::getId, Function.identity()));
			List<Costlibraryitem> updateObj = new ArrayList<Costlibraryitem>();
			for (Costlibraryitem costlibraryitem : _infos) {
				String ciName = costlibraryitem.getCiname();
				if (deviceTypeMap.containsKey(costlibraryitem.getId())) {
					Costlibraryitem beanItem = deviceTypeMap.get(costlibraryitem.getId());
					String ciName_1 = beanItem.getCiname();
					if (!ciName.equals(ciName_1)) {
						// 名称不相等,需要更新名称
						updateObj.add(costlibraryitem);
					}
				}
			}
			if (entityService.updateByIdBatch(_infos) > 0) {
				for (Costlibraryitem costlibraryitem : _infos) {
					String id = costlibraryitem.getId();
					Where where_ = Where.create();
					where_.eq(DeviceTypelibItem :: getCostlibraryitemId, id);
					Order order_ = Order.create();
					List<DeviceTypelibItem> _list = entityService.queryList(DeviceTypelibItem.class, where_, order_);
					for (DeviceTypelibItem item : _list) {
						item.setItemname(costlibraryitem.getCiname());
						item.setItemunit(costlibraryitem.getUnit());
						item.setErpcode(costlibraryitem.getErpcode());
					}
					entityService.updateByIdBatch(_list);
				}
				
				//unitMethodService.updateCostItemName(updateObj); //广东分支不同步名称了
			}
		}
		return str;
	}

	@Override
	public List<DeviceTypelibItem> saveDeviceTypelibItem(ProductSaveDto dto,
			List<DeviceTypelibItem> insertList) {
		List<CostMeteringUnit> list_1 = methodService.getCostMeteringUnitList(null);
		Map<String, CostMeteringUnit> dataMap_ = list_1.stream()
				.collect(Collectors.toMap(CostMeteringUnit::getId, Function.identity()));
		CostlibraryclassQueryDto dto_ = new CostlibraryclassQueryDto();
		String name_ = "";
		if(dto!=null) {
			String id = dto.getId();
			name_ = dto.getName();
			if (!StringUtils.isEmpty(id)) {
				dto_.setCcname(id);
			}
			if (!StringUtils.isEmpty(name_)) {
				dto_.setCcname(name_);
			}
		}else {
			CostEnvironmentConfig config = toolService.getItemsyncFyItem();
			name_ = config.getParamName();
			if (!StringUtils.isEmpty(name_)) {
				dto_.setCcname(name_);
			}
		}
		List<CostlibraryclassVo> list = costlibraryclassService.getData(dto_);
		String classId = null;
		if (list.size() > 0) {
			CostlibraryclassVo classVo = list.get(0);
			classId = classVo.getId();// 分类ID
		} else {
			List<CostlibraryclassVo> list_ = costlibraryclassService.getData(null);
			// 新增分类
			Costlibraryclass saveVo = new Costlibraryclass();
			saveVo.setId(TMUID.getUID());
			saveVo.setTmsort(list_.size() + 1);
			saveVo.setCcname(name_);
			saveVo.setTmused(1);
			entityService.insert(saveVo);
			classId = saveVo.getId();// 分类ID
		}

		/**
		 * 来源订单的数据进行保存 1.判断是名称是否重复 如果重复则只保存第一条 TODO:去重复
		 */
		if (insertList.size() > 0) {
			CostlibraryitemQueryDto dto1 = new CostlibraryitemQueryDto();
			dto1.setPid(classId);
			List<CostlibraryitemVo> list_ = getData(dto1);
			Map<String, CostlibraryitemVo> libMap = list_.stream()
					.collect(Collectors.toMap(CostlibraryitemVo::getCcname, Function.identity()));
			List<CostlibraryitemSaveDto> beans = new ArrayList<CostlibraryitemSaveDto>();
			for (DeviceTypelibItem bean : insertList) {
				String ccName = bean.getItemname();
				if (libMap.containsKey(ccName)) {
					String id = libMap.get(ccName).getId();
					bean.setCostlibraryitemId(id);
				} else {
					CostlibraryitemSaveDto vo = new CostlibraryitemSaveDto();
					vo.setDataSources("来源费用单元");
					vo.setCcname(bean.getItemname());// 名称
					vo.setUnit(bean.getItemunit());// 单位
					vo.setTmused(1);
					vo.setPid(classId);
					if (dataMap_.containsKey(vo.getUnit())) {
						vo.setUnit(dataMap_.get(vo.getUnit()).getMuName());
					}
					beans.add(vo);
				}
			}
			if (beans.size() > 0) {
				Map<String, Costlibraryitem> map = saveProductControl(beans);
				// 返回ID
				for (DeviceTypelibItem bean : insertList) {
					String name = bean.getItemname();
					if (map.containsKey(name)) {// 如果存在
						Costlibraryitem _bean = map.get(name);
						String id = _bean.getId();
						bean.setCostlibraryitemId(id);
						map.remove(name);
					}
				}
			}
		}
		return insertList;
	}

	@Override
	public List<ProductControl> saveProductVo(ProductSaveDto dto, List<ProductControl> insertList) {
		List<String> idList = new ArrayList<String>(); 
		for (ProductControl productControl : insertList) {
			String itemClassId = productControl.getItemClassId();
			if(idList.contains(itemClassId)) {
				idList.add(itemClassId);
			}
		}
		
		
		String classId = null;
		CostlibraryclassQueryDto dto_ = new CostlibraryclassQueryDto();
		if(idList.size()==0) {//没有默认值
			CostEnvironmentConfig config = toolService.getItemsync();
			String name_ = config.getParamName();
			dto_.setNeCcname(name_);
			List<CostlibraryclassVo> list = costlibraryclassService.getData(dto_);
			if(list.size()==0) {//如果数据库不存在，则新增
				List<CostlibraryclassVo> list_ = costlibraryclassService.getData(null);
				// 新增分类
				Costlibraryclass saveVo = new Costlibraryclass();
				saveVo.setId(TMUID.getUID());
				saveVo.setTmsort(list_.size() + 1);
				saveVo.setCcname(name_);
				saveVo.setTmused(1);
				entityService.insert(saveVo);
				classId = saveVo.getId();// 分类ID
				if(idList.contains(classId)) {
					idList.add(classId);
				}
			}else {
				classId = list.get(0).getId();// 分类ID
			}
		}else {
			List<CostlibraryclassVo> list = costlibraryclassService.getData(dto_);
			CostlibraryclassVo classVo = list.get(0);
			classId = classVo.getId();// 分类ID
		}
		/**
		 * 来源订单的数据进行保存 1.判断是名称是否重复 如果重复则只保存第一条 TODO:去重复
		 */
		if (insertList.size() > 0) {
			List<CostlibraryitemSaveDto> beans = new ArrayList<CostlibraryitemSaveDto>();
			for (ProductControl bean : insertList) {
				String itemClassId = bean.getItemClassId();
//				String itemClassName = bean.getItemClassName();
				String ccName = bean.getProduct();
				
				CostlibraryitemQueryDto dto1 = new CostlibraryitemQueryDto();
				if(itemClassId!=null&&itemClassId.length()>0) {
					dto1.setPid(itemClassId);
				}else {
					dto1.setPid(classId);
				}
				List<CostlibraryitemVo> list_ = getData(dto1);
				Map<String, CostlibraryitemVo> libMap = list_.stream()
						.collect(Collectors.toMap(CostlibraryitemVo::getCcname, Function.identity()));
				
				if (libMap.containsKey(ccName)) {
					String id = libMap.get(ccName).getId();
					bean.setCostlibraryitemId(id);
				} else {
					CostlibraryitemSaveDto vo = new CostlibraryitemSaveDto();
					vo.setDataSources("来源订单");
					vo.setCcname(bean.getProduct());// 名称
					vo.setUnit(bean.getUnit());// 单位
					vo.setTmused(1);
					if(itemClassId!=null&&itemClassId.length()>0) {
						vo.setPid(itemClassId);
					}else {
						vo.setPid(classId);
					}
					beans.add(vo);
				}
			}
			if (beans.size() > 0) {
				Map<String, Costlibraryitem> map = saveProductControl(beans);
				// 返回ID
				for (ProductControl bean : insertList) {
					String name = bean.getProduct();
					if (map.containsKey(name)) {// 如果存在
						Costlibraryitem _bean = map.get(name);
						String id = _bean.getId();
						bean.setCostlibraryitemId(id);
						map.remove(name);
					}
				}
			}

		}
		return insertList;
	}

	/**
	 * 保存来源订单数据
	 * 
	 * @param beans
	 * @return
	 */
	private Map<String, Costlibraryitem> saveProductControl(List<CostlibraryitemSaveDto> beans) {
		// 新增排序
		String pid = beans.get(0).getPid();
		Integer maxSort = getItemSort(pid);
		List<Costlibraryitem> infos = new ArrayList<Costlibraryitem>();
//		List<Costlibraryitem> _infos = new ArrayList<Costlibraryitem>();
		Map<String, Costlibraryitem> ciNames = new LinkedHashMap<String, Costlibraryitem>();
		for (CostlibraryitemSaveDto saveBean : beans) {
			String ciName = saveBean.getCcname();
			if (!ciNames.containsKey(ciName)) {
				Costlibraryitem saveObj = new Costlibraryitem();
				ObjUtils.copyTo(saveBean, saveObj);
				saveObj.setCiname(saveBean.getCcname());
				saveObj.setId(TMUID.getUID());
				saveObj.setTmsort(maxSort);
				saveObj.setTmused(1);
				maxSort++;
				infos.add(saveObj);
				ciNames.put(saveObj.getCiname(), saveObj);
			}
		}
		if (infos.size() > 0)
			entityService.insertBatch(infos);

		return ciNames;
	}

	@Override
	public int update(List<CostlibraryitemSaveDto> bean) {
		List<Costlibraryitem> infos = new ArrayList<Costlibraryitem>();
		for (CostlibraryitemSaveDto saveBean : bean) {
			// 数据
			Costlibraryitem saveObj = new Costlibraryitem();
			ObjUtils.copyTo(saveBean, saveObj);
			infos.add(saveObj);
		}
		return entityService.updateByIdBatch(infos);
	}

	@Override
	public int delete(List<CostlibraryitemSaveDto> bean) {
		List<Costlibraryitem> infos = new ArrayList<Costlibraryitem>();
		for (CostlibraryitemSaveDto saveBean : bean) {
			// 数据
			Costlibraryitem saveObj = new Costlibraryitem();
			ObjUtils.copyTo(saveBean, saveObj);
			infos.add(saveObj);
		}
		return entityService.deleteByIdBatch(infos);
	}

	@Override
	public List<CostlibraryitemExcleVo> excel(List<CostlibraryitemVo> list) {
		List<CostlibraryclassVo> datas = costlibraryclassService.getData(null);
		int i = 0;
		for (CostlibraryclassVo costlibraryclassVo : datas) {
			costlibraryclassVo.setTmsort(i);
			i++;
		}
		Map<String, CostlibraryclassVo> deviceTypeMap = datas.stream()
				.collect(Collectors.toMap(CostlibraryclassVo::getId, Function.identity()));
		List<CostlibraryitemExcleVo> _list = new ArrayList<CostlibraryitemExcleVo>();
		for (CostlibraryitemVo beanVo : list) {
			String pid = beanVo.getPid();
			boolean key = true;
			String ccNames = "";
			String pid_ = new String(pid);
			int sort = 0;
			while (key) {
				if (deviceTypeMap.containsKey(pid_)) {
					CostlibraryclassVo bean = deviceTypeMap.get(pid_);
					String ccName = bean.getCcname();
					ccNames = ccName + "->" + ccNames;
					pid_ = bean.getPid();
					sort = bean.getTmsort();
					if (pid_ == null) {
						key = false;
					}
				} else {
					key = false;
				}
			}
			ccNames = ccNames.substring(0, ccNames.length() - 2);
			CostlibraryitemExcleVo vo = ObjUtils.copyTo(beanVo, CostlibraryitemExcleVo.class);
			vo.setSort(sort);
			vo.setPcname(ccNames);
			_list.add(vo);
		}

		// 升序
		_list = _list.stream().sorted(Comparator.comparing(CostlibraryitemExcleVo::getSort))
				.collect(Collectors.toList());
		return _list;
	}

	@Override
	public String importExcel(List<CostlibraryitemExcleVo> teList) {
		List<CostlibraryclassVo> classList = costlibraryclassService.getData(null);
		List<CostlibraryitemVo> itemList = getData(null);

		// 服务类型读取数据字典
		SysDictData dictData = new SysDictData();
		dictData.setDictType("sum_type");
		List<SysDictData> dictDatas = sysDictDataService.selectDictDataList(dictData);
		Map<String, Integer> map = new HashMap<String, Integer>();
		for (SysDictData sysDictData : dictDatas) {
			if ("0".equals(sysDictData.getStatus()))
				try {
					map.put(sysDictData.getDictLabel(), Integer.parseInt(sysDictData.getDictValue()));
				} catch (Exception e) {
				}
		}
		Map<String, Costlibraryclass> saveClassMap = new LinkedHashMap<String, Costlibraryclass>();
		List<CostlibraryitemSaveDto> saveItemList = new ArrayList<CostlibraryitemSaveDto>();
		if (classList.size() == 0 && itemList.size() == 0) {
			// 没有数据进行初始化
			int count = 0;
			for (CostlibraryitemExcleVo excelVo : teList) {
				String pcName = excelVo.getPcname();
				String ccname = excelVo.getCcname();
				String sumtypeName = excelVo.getSumtypeName();
				String[] flmcs = pcName.split("->");
				// 项目分类
				String mapKey = "";// 分类名称
				for (String flmc : flmcs) {
					if (flmc.trim().length() == 0) {
						continue;
					}
					// 第一列根分类
					Costlibraryclass saveVo = new Costlibraryclass();
					saveVo.setId(TMUID.getUID());
					saveVo.setTmsort(count);
					saveVo.setTmused(1);
					saveVo.setCcname(flmc);
					count++;

					String PmapKey = mapKey;// 父级Key
					// 分类Map保存
					mapKey += "->" + flmc;
					if (!saveClassMap.containsKey(mapKey)) {
						String pid = null;
						if (saveClassMap.containsKey(PmapKey)) {
							Costlibraryclass pBean = saveClassMap.get(PmapKey);
							pid = pBean.getId();
						}
						saveVo.setPid(pid);
						saveClassMap.put(mapKey, saveVo);
					}
				}
				if (saveClassMap.containsKey(mapKey)) {
					if (ccname == null || ccname.trim().length() == 0) {
						continue;
					}
					Costlibraryclass pBean = saveClassMap.get(mapKey);
					String pid = pBean.getId();
					CostlibraryitemSaveDto vo = ObjUtils.copyTo(excelVo, CostlibraryitemSaveDto.class);
					vo.setPid(pid);
					vo.setTmused(1);
					if (map.containsKey(sumtypeName)) {
						vo.setSumtype(map.get(sumtypeName));
					}
					saveItemList.add(vo);
				}
			}

			List<Costlibraryclass> _list = new ArrayList<Costlibraryclass>();
			for (Entry<String, Costlibraryclass> entry : saveClassMap.entrySet()) {
				_list.add(entry.getValue());
			}
			entityService.insertBatch(_list);
			String str = save(saveItemList);
			if (str.trim().length() > 0) {
				entityService.deleteByIdBatch(_list);
				return str;
			}
			return "";
		} else {
			CostlibraryitemQueryDto dto = new CostlibraryitemQueryDto();
			dto.setNullErpCode(true);
			itemList = getData(dto);
			// Key 分类名称
			Map<String, CostlibraryitemVo> deviceTypeMap = itemList.stream()
					.collect(Collectors.toMap(CostlibraryitemVo::getErpcode, Function.identity()));
			for (CostlibraryitemExcleVo excelVo : teList) {
				String erpbm = excelVo.getErpcode();
				String sumtypeName = excelVo.getSumtypeName();
				if (deviceTypeMap.containsKey(erpbm)) {
					CostlibraryitemVo itemVo = deviceTypeMap.get(erpbm);
					CostlibraryitemSaveDto vo = ObjUtils.copyTo(itemVo, CostlibraryitemSaveDto.class);
					vo.setCcname(excelVo.getCcname());
					vo.setUnit(excelVo.getUnit());
					vo.setMemo(excelVo.getMemo());
					if (map.containsKey(sumtypeName)) {
						vo.setSumtype(map.get(sumtypeName));
					}
					saveItemList.add(vo);
				}
			}
			return save(saveItemList);
		}
	}

	@Override
	public Costlibraryitem initAdd(CostlibraryitemSaveDto dto) {
		CostlibraryclassVo classVo = costlibraryclassService.initDefault();
		while (true) {
			List<CostlibraryitemSaveDto> beans = new ArrayList<CostlibraryitemSaveDto>();
			beans.add(dto);
			String str = repeat(beans);
			if (str.length() > 0) {// 保存失败
				if (str.indexOf("名称") > -1) {
					dto.setCcname(dto.getCcname() + "1");
				} else if (str.indexOf("Erp编码") > -1) {
//					dto.setErpcode(dto.getErpcode()+"1");
					return null;
				}
			} else {
				Costlibraryitem saveObj = new Costlibraryitem();
				ObjUtils.copyTo(dto, saveObj);
				saveObj.setCiname(dto.getCcname());
				String pid = classVo.getId();
				saveObj.setPid(pid);
				saveObj.setId(TMUID.getUID());
				if (entityService.insert(saveObj) > 0) {
					return saveObj;
				} else {
					return null;
				}
			}
		}
	}

	@Override
	public Integer getItemSort(String pid) {
		// 查询PID类型
		Where where = Where.create();
		if (!StringUtils.isEmpty(pid)) {
			where.eq(Costlibraryitem::getPid, pid);
		} else {
			where.isNull(Costlibraryitem::getPid);
		}
		where.eq(Costlibraryitem::getTmused, 1);
		Order order = Order.create();
		order.orderByDesc(Costlibraryitem::getTmsort);
		List<Costlibraryitem> list = entityService.queryList(Costlibraryitem.class, where, order);
		List<Integer> tmsorts = new ArrayList<Integer>();
		for (Costlibraryitem costlibraryitem : list) {
			Integer tmsort = costlibraryitem.getTmsort();
			if (tmsort == null)
				tmsort = 0;
			tmsorts.add(tmsort);
		}
		if (tmsorts.size() > 0) {
			Optional<Integer> max = tmsorts.stream().max(Integer::compareTo);
			if (max.isPresent()) {
				return max.get() + 1;
			}
		}
		return 1;
	}

	@Override
	public List<Costlibraryitem> getItemPidData(String pid) {
		// 查询PID类型
		Where where = Where.create();
		if (pid.length() > 0) {
			where.eq(Costlibraryitem::getPid, pid);
		} else {
			where.eq(Costlibraryitem::getPid, pid);
			where.or();
			where.isNull(Costlibraryitem::getPid);
		}
		where.eq(Costlibraryitem::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costlibraryitem::getTmsort);
		return entityService.queryList(Costlibraryitem.class, where, order);
	}

	@Override
	public void getItemLists(List<Costlibraryitem> updateList, Integer oldTmsort, int cot) {
		int cons = 0;
		for (Costlibraryitem costuint : updateList) {
			Integer sort = oldTmsort + cons;
			costuint.setTmsort(sort);
			cons = cons + cot;
		}
	}

	/**
	 * 保存项目库项目数据
	 * 
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	@Override
	public String saveLibraryItemData(List<Costlibraryitem> addList, List<Costlibraryitem> updList,
			List<Costlibraryitem> delList) {
		String result = "";
		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
			if (entityService.insertBatch(addList) == 0) {
				result = "添加失败（项目库项目）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
			if (entityService.updateByIdBatch(updList) == 0) {
				result = "更新失败（项目库项目）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
			if (entityService.deleteByIdBatch(delList, 500) == 0) {
				result = "删除失败（项目库项目）！";
			}
		}
		return result;
	}

}
