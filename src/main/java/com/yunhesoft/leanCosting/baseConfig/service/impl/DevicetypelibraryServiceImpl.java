package com.yunhesoft.leanCosting.baseConfig.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.service.IDevicetypelibraryService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;


@Service
public class DevicetypelibraryServiceImpl implements IDevicetypelibraryService {

	@Autowired
	private EntityService entityService;
	
	
	@Override
	public List<Devicetypelibrary> getData(DeviceTypelibDto dto) {
		Where where = Where.create();
		if(dto!=null) {
			if (!StringUtils.isEmpty(dto.getPid())) {
				where.eq(Devicetypelibrary::getPid, dto.getPid());
			}
		}
		where.eq(Devicetypelibrary::getTmused, 1);
		Order order = Order.create();
		return entityService.queryList(Devicetypelibrary.class, where, order);
	}

}
