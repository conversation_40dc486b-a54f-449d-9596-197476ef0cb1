package com.yunhesoft.leanCosting.baseConfig.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.leanCosting.baseConfig.entity.po.ProjectType;
import com.yunhesoft.leanCosting.baseConfig.service.ICostProjectTypeService;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class CostProjectTypeSeriveceIpml implements ICostProjectTypeService {
	@Autowired
	private EntityService entityService;

	@Autowired
	private IProgramService programService;

	private List<ProjectType> init() {
		List<ProjectType> list = new ArrayList<ProjectType>();
		ProjectType projectType1 = new ProjectType();
		projectType1.setId(TMUID.getUID());
		projectType1.setDsname("开");
		projectType1.setTmused(1);
		projectType1.setOrderno("1");
		projectType1.setTypecolor("#FFFF00");//黄色
		list.add(projectType1);
		ProjectType projectType2 = new ProjectType();
		projectType2.setId(TMUID.getUID());
		projectType2.setDsname("停");
		projectType2.setTmused(1);
		projectType2.setOrderno("2");
		projectType2.setTypecolor("#FF0000");//红色
		list.add(projectType2);
		ProjectType projectType3 = new ProjectType();
		projectType3.setId(TMUID.getUID());
		projectType3.setDsname("运");
		projectType3.setTmused(1);
		projectType3.setOrderno("3");
		projectType3.setTypecolor("#00FF00");//绿色
		projectType3.setISDefault(1);
		list.add(projectType3);
		ProjectType projectType4 = new ProjectType();
		projectType4.setId(TMUID.getUID());
		projectType4.setDsname("检");
		projectType4.setTmused(1);
		projectType4.setOrderno("4");
		projectType4.setTypecolor("#0000FF");//蓝色
		list.add(projectType4);
		entityService.insertBatch(list);
		return list;
	}

	@Override
	public String delete(List<ProjectType> bean) {
		List<ProgramItem> programItems = programService.getProgramItemList(null);
		List<String> deviceStatus_ = new ArrayList<String>();
		for (ProgramItem programItem : programItems) {
			String deviceStatus = programItem.getDeviceStatus();
			if (!deviceStatus_.contains(deviceStatus)) {
				deviceStatus_.add(deviceStatus);
			}
		}

		String ret = "";
		List<ProjectType> list = new ArrayList<ProjectType>();
		for (ProjectType saveBean : bean) {
			String id = saveBean.getId();
			if (deviceStatus_.contains(id)) {
				return "数据删除失败，项目名称：[" + saveBean.getDsname() + "],正在使用！";
			}

			saveBean.setTmused(0);
			list.add(saveBean);
		}
		if (entityService.updateBatch(list) <= 0) {
			ret = "数据删除失败";
		}
		return ret;
	}

	@Override
	public List<ProjectType> getData() {
		List<ProjectType> list = new ArrayList<ProjectType>();
		Where where = Where.create();
		where.eq(ProjectType::getTmused, 1);
		Order order = Order.create("id");
		list = entityService.queryList(ProjectType.class, where, order);
		if (list.size() == 0) {
			list = this.init();
		}
		return list;
	}

	@Override
	public ProjectType getBean(String id) { //
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String save(List<ProjectType> bean) {
		String ret = "";
		Map<String, String> idMap = new LinkedHashMap<String, String>();
		for (ProjectType obj : bean) {
			if (idMap.containsKey(obj.getDsname())) {
				return "[" + obj.getDsname() + "]名称重复";
			} else {
				idMap.put(obj.getDsname(), obj.getId());
			}
		}

		List<ProjectType> list2 = getData();
		for (ProjectType bean1 : list2) {
			String ccname = bean1.getDsname();
			String id = bean1.getId();
			if (idMap.containsKey(ccname)) {
				String _id = idMap.get(ccname);
				if (!_id.equals(id)) {// id不等
					return "[" + ccname + "]名称重复";
				}
			} else {
				idMap.put(ccname, id);
			}
		}

		List<ProjectType> upList = new ArrayList<ProjectType>();
		List<ProjectType> addList = new ArrayList<ProjectType>();
		for (ProjectType obj : bean) {
			if (!StringUtils.isEmpty(obj.getId())) {
				upList.add(obj);
			} else {
				obj.setId(TMUID.getUID());
				addList.add(obj);
			}
		}

		entityService.insertBatch(addList);
		entityService.updateByIdBatch(upList);
		return ret;
	}

	@Override
	public int update(List<ProjectType> bean) {
		// List<ProjectType> list=new ArrayList<ProjectType>();
		return entityService.updateBatch(bean);
	}

	@Override
	public HashMap<String, ProjectType> getProjectTypeM() {
		HashMap<String, ProjectType> rtn = new HashMap<String, ProjectType>();
		List<ProjectType> list = entityService.rawQueryListAll(ProjectType.class);
		if (list != null) {
			for (ProjectType x : list) {
				rtn.put(x.getId(), x);
			}
		}
		return rtn;
	}

}
