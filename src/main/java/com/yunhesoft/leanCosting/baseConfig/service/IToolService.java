package com.yunhesoft.leanCosting.baseConfig.service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibParamSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibSampledotSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.CostEnvironmentConfig;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibItem;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibParam;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibSampledot;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamParamData;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostSumMaryVo;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostMeteringUnit;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.system.tools.dict.entity.SysDictData;

/**
 * 工具类
 * 
 * <AUTHOR>
 *
 */
public interface IToolService {

	/**
	 * 通过dictType字段，查询数据字典
	 * 
	 * @param dictType
	 * @return
	 */
	public List<SysDictData> getDataList(String dictType);

	/**
	 * 周期
	 * 
	 * @return
	 */
	public Map<Integer, String> getWeep();

	/**
	 * 周报-核算指标
	 * 
	 * @param costSumMaryVo
	 * @param vo
	 * @param id
	 * @param type
	 * @param digit
	 */
	public void weekCostSumMaryVo(CostSumMaryVo costSumMaryVo, CostSummaryParamData vo, String id, String type,
			Integer digit);

	/**
	 * 交接班-核算指标
	 * 
	 * @param costSumMaryVo
	 * @param vo
	 * @param id
	 * @param type
	 * @param digit
	 */
	public void jjbCostParamVo(CostSumMaryVo costSumMaryVo, CostTeamParamData vo, String id, String type,
			Integer digit);

	/**
	 * 交接班-仪表
	 * 
	 * @param costSumMaryVo
	 * @param vo
	 * @param id
	 * @param type
	 */
	public void jjbCostInstrumentVo(CostSumMaryVo costSumMaryVo, CostTeamInstrumentData vo, String id, String type,Integer fyxm);

	/**
	 * 初始化数据字典
	 */
	public void initCostBeanCostInfo();

	/**
	 * 查询汇总表项目信息(交接班)
	 * 
	 * @param dto
	 * @return
	 */
	public LinkedHashMap<String, CostTeamItemData> getChangeShiftsCostTeamItemDatas(String pid);

	/**
	 * 查询核算指标数据(交接班)
	 * 
	 * @param dto
	 * @return
	 */
	public LinkedHashMap<String, CostTeamParamData> getChangeShiftsCostTeamParamDatas(String pid);

	/**
	 * 查询汇总表仪表信息(交接班)0
	 * 
	 * @param dto
	 * @return
	 */
	public LinkedHashMap<String, List<CostTeamInstrumentData>> getChangeShiftsCostTeamInstrumentData(String pid);

	/**
	 * 核算项目
	 * 
	 * @param dto
	 * @return
	 */
	public LinkedHashMap<String, CostSummaryItemData> getWeepCostSummaryItemDatas(CostReportQueryDto dto);

	/**
	 * 核算指标
	 * 
	 * @param dto
	 * @return
	 */
	public LinkedHashMap<String, CostSummaryParamData> getWeepCostSummaryParamDatas(CostReportQueryDto dto);

	/**
	 * 初始化项目数据
	 * @param costclass			核算对象分类
	 * @param costuint			核算对象
	 * @param costitem			核算项目
	 * @param List<costinstrument>	仪表名称
	 * @param devicetypelibrary
	 * @return
	 */
	public List<CostItemFormula> initCostItemFormula(Costclass costclass,Costuint costuint, Costitem costitem,List<Costinstrument> costinstruments,Devicetypelibrary devicetypelibrary,List<String> fTypes);
	/**
	 * 初始化仪表数据
	 * 
	 * @param costuint       核算对象
	 * @param costinstrument 仪表名称
	 * @return
	 */
	public CostItemFormula initCostInstrumentFormula(Costuint costuint, Costinstrument costinstrument);

	/**
	 * 
	 * @param costitemMap
	 * @param costuintMap
	 * @param formulas
	 * @return
	 */
	public Map<String, List<CostItemFormula>> initCostItemFormula_(Map<String, Costitem> costitemMap,
			Map<String, Costuint> costuintMap, List<CostItemFormula> formulas);


	/**
	 * 产品项目
	 * 
	 * @return
	 */
	public CostEnvironmentConfig getItemsync();

	/**
	 * 费用项目
	 * 
	 * @return
	 */
	public CostEnvironmentConfig getItemsyncFyItem();

	/**
	 * 核算单元-复制
	 */
	/**
	 * 费用项目
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @return
	 */
	public Costclass getCostclass(String maxVersion, String unitCode);
	
	/**
	 * 获得基础数据
	 * @param maxVersion
	 * @param unitCode
	 * @param classId
	 * @return
	 */
	public Map<String, Costitem> getCostitems(String maxVersion, String unitCode, String classId);

	
	/**
	 * 保存项目基础数据
	 * @param maxVersion
	 * @param unitCode
	 * @param list
	 * @param dataMap
	 * @param dataMap_
	 * @return
	 */
	public String saveCostindicatorMap(String maxVersion, String unitCode, List<DeviceTypelibItem> list,
			Map<String, Costitem> dataMap, Map<String, CostMeteringUnit> dataMap_);

	/**
	 * 核算指标
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @return
	 */
	public Map<String, Costindicator> getCostindicatorMap(String maxVersion, String unitCode);

	/**
	 * 核算指标-保存
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @param list
	 * @param dataMap_ 
	 * @return
	 */
	public String saveCostindicator(String maxVersion, String unitCode, List<DeviceTypelibParam> list, Map<String, CostMeteringUnit> dataMap_);

	/**
	 * 采集点-分类
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @return
	 */
	public Costunitsampleclass getCostunitsampleclass(String maxVersion, String unitCode);

	/**
	 * 采集点数据
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @return
	 */
	public List<Costunitsampledot> getCostunitsampledots(String maxVersion, String unitCode);

	/**
	 * 采集点数据保存
	 * @param maxVersion
	 * @param unitCode
	 * @param dotId
	 * @param maxtmsort
	 * @param list
	 * @param dataMap
	 * @param dataMap_
	 * @return
	 */
	public String saveCostunitsampledot(String maxVersion, String unitCode, String dotId, int maxtmsort,
			List<DeviceTypelibSampledot> list, Map<String, CostMeteringUnit> dataMap_);
	
	/**
	 * 判断名称是否重复
	 * @param beans
	 * @return
	 */
	public String repeatParamData(List<DeviceTypelibParamSaveDto> beans);

	public String repeatSampledotData(List<DeviceTypelibSampledotSaveDto> beans);

	/**
	 * 采集点数据保存
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @param dotId
	 * @param maxtmsort
	 * @param list
	 * @param dataMap
	 * @param dataMap_
	 * @return
	 */
	

}
