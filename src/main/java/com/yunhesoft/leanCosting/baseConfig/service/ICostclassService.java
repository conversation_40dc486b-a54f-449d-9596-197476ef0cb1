package com.yunhesoft.leanCosting.baseConfig.service;

import java.util.List;

import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostclassQueryDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostclassSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostclassVo;

public interface ICostclassService {
	 /**
	 * 查询数据
	 * 
	 * @param dto
	 * @return
	 */
	public List<CostclassVo> getData(CostclassQueryDto dto);
	 /**
	 * 通过ID查询
	 * 
	 * @param id
	 * @return
	 */
	public CostclassVo getBean(String id);
	 /**
	 * 保存数据
	 * 
	 * @param bean
	 * @return
	 */
	public String save(List<CostclassSaveDto> bean);
	 /**
	 * 更新数据
	 * 
	 * @param bean
	 * @return
	 */
	public int update(List<CostclassSaveDto> bean);
	 /**
	 * 删除数据
	 * 
	 * @param bean
	 * @return
	 */
	public int delete(List<CostclassSaveDto> bean);
	List<CostclassVo> getDataPermCfgTree();

}
