package com.yunhesoft.leanCosting.baseConfig.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.MaintenanceStandardsDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.MaintenanceStandards;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.MaintenanceStandardsVo;
import com.yunhesoft.leanCosting.baseConfig.service.IMaintenanceStandardsService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/baseConfig/maintenance")
@Api(tags = "项目库分类")
public class MaintenanceStandardsConeroller extends BaseRestController {

	@Autowired
	private IMaintenanceStandardsService maintenanceStandardsService;
	
	@Autowired
	private HttpServletResponse response;

	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获取数据")
	public Res<?> getData(@RequestBody MaintenanceStandardsDto dto) {
		Res<List<MaintenanceStandards>> res = new Res<List<MaintenanceStandards>>();
		List<MaintenanceStandards> list = maintenanceStandardsService.getData(dto);
		res.setResult(list);
		return res;
	}

	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> saveData(@RequestBody List<MaintenanceStandards> bean) {
		maintenanceStandardsService.saveAndUpdate(bean);
		Res<String> res = new Res<String>();
		return res.ok();
	}

	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	@ApiOperation("删除数据")
	public Res<?> delete(@RequestBody List<MaintenanceStandards> bean) {
		Res<Boolean> res = new Res<Boolean>();
		res.setResult(false);
		int i = maintenanceStandardsService.delete(bean);
		if (i > 0) {
			res.setResult(true);
		}
		return res;
	}

	/**
	 * 导出Excel
	 * 
	 * @param querySysRoleDto
	 */
	@ApiOperation("数据导出Excel")
	@RequestMapping(value = "/toExcel", method = RequestMethod.POST)
	public void toExcel(@RequestBody MaintenanceStandardsDto dto) {
		List<MaintenanceStandards> list = maintenanceStandardsService.getData(dto);
		List<MaintenanceStandardsVo> costlibraryitemExcleVos = maintenanceStandardsService.excel(list);
		ExcelExport.exportExcel("设备维保标准", null, true, MaintenanceStandardsVo.class, costlibraryitemExcleVos, response);
	}

	@ApiOperation("数据导入")
	@RequestMapping(value = "/import", method = { RequestMethod.POST })
	public Res<?> importExcel(@RequestParam("file") MultipartFile file,
			@RequestParam("params") String productImportPid) throws Exception {
		JSONObject jsonObject = JSONObject.parseObject(productImportPid);
		ExcelImportResult<?> result = ExcelImport.importExcel(file.getInputStream(), MaintenanceStandardsVo.class, 1, 1,
				false);
		if (result != null) {
			if (result.isVerifyFail()) {// 校验失败
				return Res.OK(result.getFailList());// 校验失败的数据
			}
			List<?> list = result.getList();// 导入的结果数据
			// 将excel集合转类型
			List<MaintenanceStandardsVo> teList = ObjUtils.copyToList(list, MaintenanceStandardsVo.class);
			String message = maintenanceStandardsService.importExcel(teList, jsonObject.getString("devicetypelibraryId"));
			return Res.OK(message);
		}
		return Res.FAIL("");
	}
}
