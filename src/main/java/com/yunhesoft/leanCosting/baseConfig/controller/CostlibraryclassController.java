package com.yunhesoft.leanCosting.baseConfig.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryclassQueryDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryclassSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryitemSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costlibraryclass;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryclassTreeVo;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryclassVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryclassService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.service.ISysDictDataService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/baseConfig/Costlibraryclass")
@Api(tags = "项目库分类")
public class CostlibraryclassController extends BaseRestController {

	@Autowired
	private ICostlibraryclassService costlibraryclassService;

	@Autowired
	private ICostToolService costToolService;

	@Autowired
	private ISysDictDataService sysDictDataService;
	
	@Autowired
	private EntityService entityService;
	

	@RequestMapping(value = "/getSumTypeCombo", method = RequestMethod.POST)
	@ApiOperation(value = "汇总方式数据")
	public Res<?> getSumTypeCombo() {
		Res<List<Map<String, String>>> res = new Res<List<Map<String, String>>>();
		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
		SysDictData dictData = new SysDictData();
		// 服务类型读取数据字典
		dictData.setDictType("sum_type");
		List<SysDictData> dictDatas = sysDictDataService.selectDictDataList(dictData);
		for (SysDictData sysDictData : dictDatas) {
			if("0".equals(sysDictData.getStatus())) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("key", sysDictData.getDictValue());
				map.put("value", sysDictData.getDictLabel());
				map.put("val", sysDictData.getDictLabel());
				maps.add(map);
			}
		}
		res.setResult(maps);
		return res;
	}

	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获取数据")
	public Res<?> getData(@RequestBody CostlibraryclassQueryDto dto) {
		Res<List<CostlibraryclassTreeVo>> res = new Res<List<CostlibraryclassTreeVo>>();
		List<CostlibraryclassVo> list = costlibraryclassService.getData(dto);
		List<CostlibraryclassTreeVo> costlibraryclassTreeVos = new ArrayList<CostlibraryclassTreeVo>();
		if(dto.isFyxm()) {
			costlibraryclassTreeVos = costToolService.costClassraryitemFyxmTreeVo(list);
		}else {
			costlibraryclassTreeVos = costToolService.costlibraryitemTreeVo(list);
			costlibraryclassTreeVos = costToolService.costlibraryclassTreeVo(costlibraryclassTreeVos, 0);
		}
		res.setResult(costlibraryclassTreeVos);
		return res;	
	}
	
	
	@RequestMapping(value = "/getDataTree", method = RequestMethod.POST)
	@ApiOperation("获取数据(树形模式)")
	public Res<?> getDataTree(@RequestBody CostlibraryclassQueryDto dto) {
		Res<Map<String, Object>> res = new Res<Map<String, Object>>();
		Map<String, Object> map_ = new HashMap<String, Object>();
		List<CostlibraryclassVo> list = costlibraryclassService.getData(dto);
		List<CostlibraryclassTreeVo> costlibraryclassTreeVos = costToolService.costlibraryitemTreeVo(list);
		costlibraryclassTreeVos = costToolService.costlibraryclassTreeVo(costlibraryclassTreeVos, 0);
		map_.put("data", costlibraryclassTreeVos);
		List<CostlibraryclassTreeVo> keys = costToolService.costlibraryTreeExpansion(costlibraryclassTreeVos, dto.getTreeExpansion());
		map_.put("keys", keys);
		res.setResult(map_);
		return res;
	}


	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> saveData(@RequestBody List<CostlibraryclassSaveDto> bean) {
		String message = costlibraryclassService.save(bean);
		Res<String> res = new Res<String>();
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}

	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	@ApiOperation("删除数据")
	public Res<?> delete(@RequestBody List<CostlibraryclassSaveDto> bean) {
		Res<Boolean> res = new Res<Boolean>();
		res.setResult(false);
		int i = costlibraryclassService.delete(bean);
		if (i > 0) {
			res.setResult(true);
		}
		return res;
	}
	
	
	@RequestMapping(value = "/deleteBean", method = RequestMethod.POST)
	@ApiOperation("删除数据")
	public Res<?> deleteBean(@RequestBody CostlibraryitemSaveDto bean) {
		Res<Boolean> res = new Res<Boolean>();
		res.setResult(false);
		Costlibraryclass costlibraryitem = entityService.queryObjectById(Costlibraryclass.class, bean.getId());
		costlibraryitem.setTmused(0);
		int i = entityService.rawUpdateById(costlibraryitem);
		if(i > 0) {
			res.setResult(true);
		}
		return res;
	}

}
