package com.yunhesoft.leanCosting.baseConfig.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryitemQueryDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostDeviceTypeVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostDeviceTypeService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.service.ISysDictDataService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/baseConfig/DeviceType")
@Api(tags = "类型库")
public class CostDeviceTypeController {
	@Autowired
	private ICostDeviceTypeService costDeviceTypeService;

	@Autowired
	private ISysDictDataService sysDictDataService;
	
	@Autowired
	private EntityService entityService;


	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获取数据")
	public Res<?> getData(@RequestBody CostlibraryitemQueryDto dto) {
		Res<List<CostDeviceTypeVo>> res = new Res<List<CostDeviceTypeVo>>();
		List<CostDeviceTypeVo> list = costDeviceTypeService.getData(dto);
		res.setResult(list);
		return res;
	}

	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> saveData(@RequestBody List<Devicetypelibrary> list) {
		String message = costDeviceTypeService.save(list);
		Res<String> res = new Res<String>();
		// if (message.trim().length() > 0) {
		// 	res.setSuccess(false);
			res.setMessage(message);
		// } else {
			res.setSuccess(true);
		return res;
	}

	@RequestMapping(value = "/deleteData", method = RequestMethod.POST)
	@ApiOperation("删除数据")
	public Res<?> deleteData(@RequestBody List<Devicetypelibrary> list) {
		int i = costDeviceTypeService.delete(list);
		Res<Boolean> res = new Res<Boolean>();
		res.setResult(false);
		if (i > 0) {
			res.setResult(true);
		}
		return res;
	}

	@RequestMapping(value = "/getFmluseclassTypeCombo", method = RequestMethod.POST)
	@ApiOperation(value = "单位成本类型字典")
	public Res<?> getFmluseclassTypeCombo() {
		Res<List<Map<String, String>>> res = new Res<List<Map<String, String>>>();
		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
		SysDictData dictData = new SysDictData();
		// 单位成本类型字典
		dictData.setDictType("FMLUSECLASSTYPE");
		List<SysDictData> dictDatas = sysDictDataService.selectDictDataList(dictData);
		for (SysDictData sysDictData : dictDatas) {
			if("0".equals(sysDictData.getStatus())) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("key", sysDictData.getDictValue());
				map.put("value", sysDictData.getDictLabel());
				map.put("val", sysDictData.getDictLabel());
				maps.add(map);
			}
		}
		res.setResult(maps);
		return res;
	}

	/**
	 * 
	 * @param draggingNode
	 * @param dropType		before、after、inner
	 * @return
	 */
	@RequestMapping(value = "/dragTree", method = RequestMethod.POST)
	@ApiOperation("拖拽树形")
	public Res<?> dragTree(@RequestBody List<CostDeviceTypeVo> draggingNodes,String dropType) {
		CostDeviceTypeVo draggingNode = draggingNodes.get(0);//原树形,需要更新
		CostDeviceTypeVo dropNode = draggingNodes.get(1);//目标树形,锚节点
		String id = dropNode.getId();
		Devicetypelibrary _dataObj = entityService.queryObjectById(Devicetypelibrary.class, id);
		//目标序号
		Integer pSort = _dataObj.getTmsort();
		if(pSort==null) {
			pSort = 0;
		}
		if("inner".equals(dropType)) {//内部
			draggingNode.setPid(id);//赋值Pid
			//目标内容
			Integer tmSort = costDeviceTypeService.getCostuitSort(id, pSort);
			draggingNode.setTmsort(tmSort);
			Devicetypelibrary dataObj = new Devicetypelibrary();
			BeanUtils.copyProperties(draggingNode, dataObj); // 赋予返回对象
			entityService.update(dataObj);
		}else if("before".equals(dropType)||"after".equals(dropType)) {//前
			Integer draggSort = dropNode.getTmsort();
			if("after".equals(dropType)) {//后需要序号字段+1
				draggSort = draggSort+1;
			}
			draggingNode.setPid(dropNode.getPid());//赋值Pid
			List<Devicetypelibrary> list = costDeviceTypeService.getDataLib(dropNode.getPid());
			List<Devicetypelibrary> updateList = new ArrayList<Devicetypelibrary>();
			Map<String, Devicetypelibrary> map = new LinkedHashMap<String, Devicetypelibrary>();
			//内容
			draggingNode.setPid(dropNode.getPid());
			Devicetypelibrary dataObj = new Devicetypelibrary();
			BeanUtils.copyProperties(draggingNode, dataObj); // 赋予返回对象
			map.put(dataObj.getId(), dataObj);
			updateList.add(dataObj);
			Integer oldTmsort = draggSort;
			boolean key = false;
			for (Devicetypelibrary bean : list) {
				Integer tmSort = bean.getTmsort();
				if(draggSort<=tmSort) {
					if(!key) {
						key = true;
						oldTmsort = tmSort;
					}
				}
				if(key) {
					if(!map.containsKey(bean.getId())) {
						map.put(bean.getId(), bean);
						updateList.add(bean);
					}
				}
			}
			costDeviceTypeService.getCostLists(updateList, oldTmsort,1);
			entityService.updateBatch(updateList);
		}
		return Res.OK();
	}
}
