package com.yunhesoft.leanCosting.baseConfig.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostlibraryitemSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibItemSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibParamSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibSampledotSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibItem;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibParam;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibSampledot;
import com.yunhesoft.leanCosting.baseConfig.service.IDeviceTypelibItemService;
import com.yunhesoft.leanCosting.baseConfig.service.IDeviceTypelibParamService;
import com.yunhesoft.leanCosting.baseConfig.service.IDeviceTypelibSampledotService;
import com.yunhesoft.leanCosting.baseConfig.service.IToolService;
import com.yunhesoft.leanCosting.baseConfig.service.impl.ToolServiceImpl;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/baseConfig/deviceTypelib")
@Api(tags = "项目库分类")
public class DeviceTypelibController extends BaseRestController {
	@Autowired
	private IDeviceTypelibItemService itemService;
	@Autowired
	private IDeviceTypelibParamService paramService;
	@Autowired
	private IDeviceTypelibSampledotService sampledotService;
	@Autowired
	private IToolService toolService;
	

	@RequestMapping(value = "/getItemData", method = RequestMethod.POST)
	@ApiOperation("单元类型-项目")
	public Res<?> getItemData(@RequestBody DeviceTypelibDto dto) {
		Res<List<DeviceTypelibItem>> res = new Res<List<DeviceTypelibItem>>();
		List<DeviceTypelibItem> list = itemService.getItemData(dto);
		res.setResult(list);
		return res;
	}

	@RequestMapping(value = "/saveItemData", method = RequestMethod.POST)
	@ApiOperation("保存数据-项目")
	public Res<?> saveItemData(@RequestBody List<DeviceTypelibItemSaveDto> beans) {
		Res<String> res = new Res<String>();
		List<DeviceTypelibItem> addList = new ArrayList<DeviceTypelibItem>();
		List<DeviceTypelibItem> updList = new ArrayList<DeviceTypelibItem>();
		List<DeviceTypelibItem> delList = new ArrayList<DeviceTypelibItem>();
		for (DeviceTypelibItemSaveDto bean : beans) {
			DeviceTypelibItem deviceTypelibItem = ObjUtils.copyTo(bean, DeviceTypelibItem.class);
			String id = deviceTypelibItem.getId();
			int tmused = deviceTypelibItem.getTmused();
			if("".equals(id)) {
				deviceTypelibItem.setId(TMUID.getUID());
				addList.add(deviceTypelibItem);
			}else {
				if(tmused==0) {
					deviceTypelibItem.setTmused(0);
				}
				updList.add(deviceTypelibItem);
			}
		}
		
		String message = itemService.saveData(addList, updList, delList);
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}
	
	@RequestMapping(value = "/deleteItemData", method = RequestMethod.POST)
	@ApiOperation("保存数据-删除项目")
	public Res<?> deleteItemData(@RequestBody List<DeviceTypelibItemSaveDto> beans) {
		Res<String> res = new Res<String>();
		List<DeviceTypelibItem> updList = new ArrayList<DeviceTypelibItem>();
		for (DeviceTypelibItemSaveDto bean : beans) {
			DeviceTypelibItem deviceTypelibItem = ObjUtils.copyTo(bean, DeviceTypelibItem.class);
			deviceTypelibItem.setTmused(0);
			updList.add(deviceTypelibItem);
		}
		String message = itemService.saveData(null, updList, null);
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}
	
	@RequestMapping(value = "/getParamData", method = RequestMethod.POST)
	@ApiOperation("单元类型-核算指标")
	public Res<?> getParamData(@RequestBody DeviceTypelibDto dto) {
		Res<List<DeviceTypelibParam>> res = new Res<List<DeviceTypelibParam>>();
		List<DeviceTypelibParam> list = paramService.getItemData(dto);
		res.setResult(list);
		return res;
	}

	@RequestMapping(value = "/saveParamData", method = RequestMethod.POST)
	@ApiOperation("保存数据-核算指标")
	public Res<?> saveParamData(@RequestBody List<DeviceTypelibParamSaveDto> beans) {
		Res<String> res = new Res<String>();
		String message_ = toolService.repeatParamData(beans);
		if (message_.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message_);
			return res;
		}
		List<DeviceTypelibParam> addList = new ArrayList<DeviceTypelibParam>();
		List<DeviceTypelibParam> updList = new ArrayList<DeviceTypelibParam>();
		List<DeviceTypelibParam> delList = new ArrayList<DeviceTypelibParam>();
		for (DeviceTypelibParamSaveDto bean : beans) {
			DeviceTypelibParam deviceTypelibItem = ObjUtils.copyTo(bean, DeviceTypelibParam.class);
			String id = deviceTypelibItem.getId();
			int tmused = deviceTypelibItem.getTmused();
			if("".equals(id)) {
				deviceTypelibItem.setId(TMUID.getUID());
				addList.add(deviceTypelibItem);
			}else {
				if(tmused==0) {
					deviceTypelibItem.setTmused(0);
				}
				updList.add(deviceTypelibItem);
			}
		}
		
		String message = paramService.saveData(addList, updList, delList);
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}
	
	@RequestMapping(value = "/deleteParamData", method = RequestMethod.POST)
	@ApiOperation("保存数据-删除核算指标")
	public Res<?> deleteParamData(@RequestBody List<DeviceTypelibItemSaveDto> beans) {
		Res<String> res = new Res<String>();
		List<DeviceTypelibParam> updList = new ArrayList<DeviceTypelibParam>();
		for (DeviceTypelibItemSaveDto bean : beans) {
			DeviceTypelibParam deviceTypelibParam = ObjUtils.copyTo(bean, DeviceTypelibParam.class);
			deviceTypelibParam.setTmused(0);
			updList.add(deviceTypelibParam);
		}
		String message = paramService.saveData(null, updList, null);
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}

	@RequestMapping(value = "/getSampledotData", method = RequestMethod.POST)
	@ApiOperation("单元类型-采集点")
	public Res<?> getSampledotData(@RequestBody DeviceTypelibDto dto) {
		Res<List<DeviceTypelibSampledot>> res = new Res<List<DeviceTypelibSampledot>>();
		List<DeviceTypelibSampledot> list = sampledotService.getItemData(dto);
		res.setResult(list);
		return res;
	}

	@RequestMapping(value = "/saveSampledotData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> saveSampledotData(@RequestBody List<DeviceTypelibSampledotSaveDto> beans) {
		Res<String> res = new Res<String>();
		String message_ = toolService.repeatSampledotData(beans);
		if (message_.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message_);
			return res;
		}
		List<DeviceTypelibSampledot> addList = new ArrayList<DeviceTypelibSampledot>();
		List<DeviceTypelibSampledot> updList = new ArrayList<DeviceTypelibSampledot>();
		List<DeviceTypelibSampledot> delList = new ArrayList<DeviceTypelibSampledot>();
		for (DeviceTypelibSampledotSaveDto bean : beans) {
			DeviceTypelibSampledot deviceTypelibItem = ObjUtils.copyTo(bean, DeviceTypelibSampledot.class);
			String id = deviceTypelibItem.getId();
			int tmused = deviceTypelibItem.getTmused();
			if("".equals(id)) {
				deviceTypelibItem.setId(TMUID.getUID());
				addList.add(deviceTypelibItem);
			}else {
				if(tmused==0) {
					deviceTypelibItem.setTmused(0);
				}
				updList.add(deviceTypelibItem);
			}
		}
		String message = sampledotService.saveData(addList, updList, delList);
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}
	
	
	@RequestMapping(value = "/deleteSampledotData", method = RequestMethod.POST)
	@ApiOperation("保存数据-删除核算指标")
	public Res<?> deleteSampledotData(@RequestBody List<DeviceTypelibSampledotSaveDto> beans) {
		Res<String> res = new Res<String>();
		List<DeviceTypelibSampledot> updList = new ArrayList<DeviceTypelibSampledot>();
		for (DeviceTypelibSampledotSaveDto bean : beans) {
			DeviceTypelibSampledot deviceTypelibSampledot = ObjUtils.copyTo(bean, DeviceTypelibSampledot.class);
			deviceTypelibSampledot.setTmused(0);
			updList.add(deviceTypelibSampledot);
		}
		String message = sampledotService.saveData(null, updList, null);
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}
	
	
	
	@RequestMapping(value = "/getCostUintData", method = RequestMethod.POST)
	@ApiOperation("单元类型-获取核算单元")
	public Res<?> getCostUintData(@RequestBody DeviceTypelibDto dto) {
		List<Costuint> list = itemService.getItemDataTreeAll(dto);
		Res<List<Costuint>> res = new Res<List<Costuint>>();
		res.setResult(list);
		return res;
	}
	
	
	@RequestMapping(value = "/saveCostUintData", method = RequestMethod.POST)
	@ApiOperation("单元类型-复制保存核算单元")
	public Res<?> saveCostUintData(@RequestBody DeviceTypelibDto dto) {
		Res<String> res = new Res<String>();
		String str = paramService.copyDeviceTypelib(dto);
		res.setResult(str);
		return res;
	}
	
	
}
