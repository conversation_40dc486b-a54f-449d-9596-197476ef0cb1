package com.yunhesoft.leanCosting.baseConfig.controller;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.yunhesoft.leanCosting.baseConfig.service.IToolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostclassQueryDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.CostclassSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostclassVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostclassService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.service.ISysDictDataService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/baseConfig/Costclass")
@Api(tags = "成本项目分类")
public class CostclassController extends BaseRestController {

	@Autowired
	private ICostclassService costclassService;
	
	@Autowired
	private ISysDictDataService sysDictDataService;

	@Autowired
	private IToolService iToolService;
	
	@ApiOperation(value = "成本项目分类数据")
	@RequestMapping(value = "/getCalculateTypeCombo", method = RequestMethod.POST)
	public Res<?> getCalculateTypeCombo() {
		Res<List<Map<String, String>>> res = new Res<List<Map<String, String>>>();
		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
		SysDictData dictData = new SysDictData();
		// 服务类型读取数据字典
		dictData.setDictType("calculate_type");
		List<SysDictData> dictDatas = sysDictDataService.selectDictDataList(dictData);
		for (SysDictData sysDictData : dictDatas) {
			if("0".equals(sysDictData.getStatus())) {
				Map<String, String> map = new HashMap<String, String>();
				map.put("key", sysDictData.getDictValue());
				map.put("value", sysDictData.getDictLabel());
				map.put("val", sysDictData.getDictLabel());
				maps.add(map);
			}
		}
		res.setResult(maps);
		return res;
	}
	

	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获取数据")
	public Res<?> getData(@RequestBody CostclassQueryDto dto) {
		Res<List<CostclassVo>> res = new Res<List<CostclassVo>>();
		List<CostclassVo> list = costclassService.getData(dto);
		res.setResult(list);
		return res;
	}

	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> saveData(@RequestBody List<CostclassSaveDto> bean) {
		String message = costclassService.save(bean);
		Res<String> res = new Res<String>();
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}

	@RequestMapping(value = "/deleteDatas", method = RequestMethod.POST)
	@ApiOperation("删除数据")
	public Res<?> delete(@RequestBody List<CostclassSaveDto> bean) {
		Res<Boolean> res = new Res<Boolean>();
		res.setResult(false);
		int i = costclassService.delete(bean);
		if(i > 0) {
			res.setResult(true);
		}
		return res;
	}
	@RequestMapping(value = "/getDefaultClassName", method = RequestMethod.POST)
	@ApiOperation("获取默认分类名")
	public Res<?> getDefaultClassName() {
		return Res.OK(iToolService.getItemsync().getParamName());
	}
}
