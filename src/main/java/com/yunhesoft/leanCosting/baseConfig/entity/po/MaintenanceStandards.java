package com.yunhesoft.leanCosting.baseConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 设备维保标准
 * <AUTHOR>
 *
 */
@Entity
@Setter
@Getter
@ApiModel("设备维保标准")
@Table(name = "MAINTENANCESTANDARDS")
public class MaintenanceStandards extends BaseEntity {

	private static final long serialVersionUID = 1L;

	
	@ApiModelProperty(value = "单元类型Id")
	@Column(name = "DEVICETYPELIBRARYID", length = 2000)
	private String devicetypelibraryId;
	
	@ApiModelProperty(value = "设备名称")
	@Column(name = "SBNAME", length = 2000)
	private String sbName;
	
	@ApiModelProperty(value = "保养类型")
	@Column(name = "MAINTENANCETYPE", length = 2000)
	private String maintenanceType;
	
	@ApiModelProperty(value = "计划时间")
	@Column(name = "SCHEDULEDTIME", length = 100)
	private String scheduledTime;
	
	@ApiModelProperty(value = "保养项")
	@Column(name = "MaintenanceItems", length = 2000)
	private String maintenanceItems;
	
	@ApiModelProperty(value = "保养要求")
	@Column(name = "MAINTENANCEREQ", length = 2000)
	private String maintenanceReq;
	
	@ApiModelProperty(value = "是否检查")
	@Column(name = "WHETHERTOCHECK", length = 2000)
	private Integer whetherToCheck;
	
	@Transient
	private int rowFaly;

}
