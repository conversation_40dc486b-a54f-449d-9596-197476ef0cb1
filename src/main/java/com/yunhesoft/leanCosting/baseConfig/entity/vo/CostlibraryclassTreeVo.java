package com.yunhesoft.leanCosting.baseConfig.entity.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CostlibraryclassTreeVo {

	
	@ApiModelProperty(value="主键ID")
	private String id;
	
	@ApiModelProperty(value="type")
	private Integer objType;

	@ApiModelProperty(value="父分类ID", example="8a1244a772565c34")
    private String pid;
    
    @ApiModelProperty(value="类名", example="类名1")
    private String ccname;
    
    @ApiModelProperty(value="注释", example="注释1")
    private String memo;
    
	/** 无用 */
    private Integer tmused;
    
    /** 排序 */
    private Integer tmsort;
    
    @ApiModelProperty(value="整理内容", example="整理内容")
  	private String queryContent;
  	
    @ApiModelProperty(value="机构ID", example="机构ID1")
    private String orgcode;
    
    @ApiModelProperty(value = "类型")
    private int type;
    
    @ApiModelProperty(value="计量单位", example="计量单位1")
    private String unit;
    
    @ApiModelProperty(value="ERP代码", example="ERP代码1")
    private String erpcode;
    
    @ApiModelProperty(value="汇总方式", example="123")
    private int sumtype;
    
    @ApiModelProperty(value="汇总方式名称", example="123")
    private String sumtypeName;

	@ApiModelProperty(value="主键子节点")
	private List<CostlibraryclassTreeVo> children;
}
