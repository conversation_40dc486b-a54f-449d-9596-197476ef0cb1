package com.yunhesoft.leanCosting.baseConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;



@Entity
@Setter
@Getter
@ApiModel("项目库分类")
@Table(name = "COSTLIBRARYCLASS")
public class Costlibraryclass  extends BaseEntity {


    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="父分类ID", example="8a1244a772565c34")
    @Column(name = "PID",length=100)
    private String pid;
    
    @ApiModelProperty(value="类名", example="类名1")
    @Column(name = "CCNAME",length=200)
    private String ccname;
    
    @ApiModelProperty(value="注释", example="注释1")
    @Column(name = "MEMO",length=1000)
    private String memo;
    
    /** 无用 */
    @Column(name="TMUSED")
    private Integer tmused;
    
    /** 排序 */
    @Column(name="TMSORT", length=1000)
    private Integer tmsort;
    
    @ApiModelProperty(value="机构ID", example="机构ID1")
    @Column(name = "ORGCODE",length=100)
    private String orgcode;
    
    
    @ApiModelProperty(value="外部数据编码")
    @Column(name = "MDMCODE",length = 50)
    private String mdmCode;
    

}