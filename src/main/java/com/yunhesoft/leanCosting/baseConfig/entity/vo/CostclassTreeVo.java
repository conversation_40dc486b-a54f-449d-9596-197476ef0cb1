package com.yunhesoft.leanCosting.baseConfig.entity.vo;

import java.util.List;

import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CostclassTreeVo extends Costclass{

	
	private static final long serialVersionUID = 1L;

	private String nodeId; // 节点id
	
	private String nodeName; // 节点name
	
	private String label;
	
	private Integer nodeLevel; // 节点级别：1、分类；2、项目；3、仪表；100、指标分类；101、指标；
	
	private Integer isLeaf; // 是否为叶子节点
	
	private Boolean disabled; // 是否禁用节点
	
	private List<CostclassTreeVo> children; // 子记录
}
