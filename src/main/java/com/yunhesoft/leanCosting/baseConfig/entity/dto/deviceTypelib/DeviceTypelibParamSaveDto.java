package com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib;

import com.yunhesoft.core.common.dto.BaseQueryDto;

import lombok.Data;

@Data
public class DeviceTypelibParamSaveDto extends BaseQueryDto {

	private String editType; //del、删除；save、保存；
	
	/**Id*/
	private String id;
	
	/** 父分类ID */
	private String pid;

	  /** 名称 */
    private String cpname;
    
    /** 比较方式：0、越大越好；1、越小越好；2、趋近考核值 */
    private Integer comparetype;
    
    /** 计量单位 */
	private String itemunit;
    
	  /** 是否查询展示 */
    private Integer isSelShow;
    
    /** 是否用于记事：1、是；其他不是（默认） */
    private Integer isUseToRecordEvent;
    
    private int tmused;
}
