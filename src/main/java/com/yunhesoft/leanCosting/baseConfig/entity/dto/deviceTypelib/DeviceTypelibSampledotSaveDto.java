package com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib;

import com.yunhesoft.core.common.dto.BaseQueryDto;

import lombok.Data;

@Data
public class DeviceTypelibSampledotSaveDto extends BaseQueryDto {

	private String editType; //del、删除；save、保存；
	
	/**Id*/
	private String id;
	
	/** 父分类ID */
	private String pid;

	/** 名称 */
	private String name;
	
	/** 计量单位 */
	private String sdUnit;

	/** 采样间隔（分钟） */
	private Integer samplInterval = 5;
	
	/** 指标范围下限 */
	private Double indexRangeLower;
	
	/** 指标范围上限 */
	private Double indexRangeUpper;
	
	/** 是否显示台账（默认显示） */
	private Integer isShowLedger = 1;
	
	/** 台账显示位数（默认3位小数） */
	private Integer pointCountLedger = 3;
	
	/** 是否用于记事：1、是；其他不是（默认） */
    private Integer isUseToRecordEvent;

	/** 用途：控制指标是用于总平稳率计算，LIMS指标是用于总合格率计算，默认勾选 */
	private Integer useTo;
	
	private int tmused;
}
