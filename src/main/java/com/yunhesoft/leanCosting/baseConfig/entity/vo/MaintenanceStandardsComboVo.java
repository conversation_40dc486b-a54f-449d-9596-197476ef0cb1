package com.yunhesoft.leanCosting.baseConfig.entity.vo;

import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;

import lombok.Data;


/**
 * 移动端-接口使用
 * <AUTHOR>
 *
 */
@Data
public class MaintenanceStandardsComboVo extends Costuint{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	//key	:unitid+"_"+devicetypelibraryId+"_"+maintenanceType;
	private String id;
	
	//value : unitName +"的"+maintenanceType;
	private String name;
	
	
}
