package com.yunhesoft.leanCosting.baseConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

@Entity
@Setter
@Getter
@ApiModel("单元类型-采集点")
@Table(name = "DEVICETYPELIBSAMPLEDOT")
public class DeviceTypelibSampledot extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 父分类ID */
	@Column(name = "PID", length = 100)
	private String pid;

	/** 名称 */
	@Column(name = "NAME", length = 200)
	private String name;
	
	/** 计量单位 */
	@Column(name = "SDUNIT", length = 200)
	private String sdUnit;

	/** 采样间隔（分钟） */
	@Column(name = "SAMPLINTERVAL")
	private Integer samplInterval = 5;
	
	/** 指标范围下限 */
	@Column(name = "INDEXRANGELOWER")
	private Double indexRangeLower;
	
	/** 指标范围上限 */
	@Column(name = "INDEXRANGEUPPER")
	private Double indexRangeUpper;
	
	/** 是否显示台账（默认显示） */
	@Column(name = "ISSHOWLEDGER")
	private Integer isShowLedger = 1;
	
	/** 台账显示位数（默认3位小数） */
	@Column(name = "POINTCOUNTLEDGER")
	private Integer pointCountLedger = 3;
	
	/** 是否用于记事：1、是；其他不是（默认） */
    @Column(name="ISUSETORECORDEVENT")
    private Integer isUseToRecordEvent;

	/** 用途：控制指标是用于总平稳率计算，LIMS指标是用于总合格率计算，默认勾选 */
	@Column(name = "USETO")
	private Integer useTo;
	
	@Column(name = "TMUSED")
	private int tmused;
	
}
