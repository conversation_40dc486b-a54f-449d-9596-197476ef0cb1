package com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib;

import com.yunhesoft.core.common.dto.BaseQueryDto;

import lombok.Data;

@Data
public class DeviceTypelibItemSaveDto extends BaseQueryDto {


	private String editType; // del、删除；save、保存；

	/**Id*/
	private String id;
	
	/** 单元类型ID */
	private String pid;
	
	private String costlibraryitemId;

	/** 名称 */
	private String itemname;

	/** 计量单位 */
	private String itemunit;

	/** 分摊因子 */
	private Double apportionmentfactor;

	/** 换算系数 */
	private Double conversionfactor;

	/** 物料量由谁提供：0、车间；1、班组（默认） */
	private Integer materialSupply = 1;

	/** ERP编码 */
	private String erpcode;

	/** 比较方式：0、越大越好；1、越小越好；2、趋近考核值 */
	private Integer comparetype;
	
	private int tmused;
}
