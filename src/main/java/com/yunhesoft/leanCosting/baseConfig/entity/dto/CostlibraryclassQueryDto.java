package com.yunhesoft.leanCosting.baseConfig.entity.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CostlibraryclassQueryDto {

	private String id;
	
	private List<String> idList;
	
	private String pid;
	
	@ApiModelProperty(value="比对方式LIKE项目名称", example="项目名称")
	private String ccname;
	
	
	@ApiModelProperty(value="比对方式直等项目名称", example="项目名称")
	private String neCcname;
	
	

	@ApiModelProperty(value = "树形展开等级")
	private Integer treeExpansion;
	
	@ApiModelProperty(value = "是否过滤费用项目")
	private boolean fyxm;
}
