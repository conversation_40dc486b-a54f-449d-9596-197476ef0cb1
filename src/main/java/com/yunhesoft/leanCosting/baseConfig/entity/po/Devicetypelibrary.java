package com.yunhesoft.leanCosting.baseConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;



@Entity
@Setter
@Getter
@ApiModel("设备类型库")
@Table(name = "DEVICETYPELIBRARY")
public class Devicetypelibrary  extends BaseEntity {


    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="父类型ID", example="8a1244a772565c34")
    @Column(name = "PID",length=100)
    private String pid;
    
    @ApiModelProperty(value="类名", example="类名1")
    @Column(name = "DTNAME",length=200)
    private String dtname;
    
    @ApiModelProperty(value="注释", example="注释1")
    @Column(name = "MEMO",length=1000)
    private String memo;
    
    @ApiModelProperty(value="排序号", example="排序号1")
    @Column(name = "TMSORT")
    private Integer tmsort;
    
    @ApiModelProperty(value="无用", example="123")
    @Column(name = "TMUSED")
    private int tmused;
    
    @ApiModelProperty(value="分类内排序", example="123")
    @Column(name = "SORT")
    private int sort;
    
    @ApiModelProperty(value="单位成本", example="计算产品的单位成本")
    @Column(name = "FMLUSECLASS")
    private Integer fmluseclass;
    
    @ApiModelProperty(value="类型下对象是否使用方案", example="类型下对象是否使用方案")
    @Column(name = "ISSAMEPROGRAM")
    private Integer isSameProgram;
    
    @ApiModelProperty(value="外部数据编码")
    @Column(name = "MDMCODE",length = 50)
    private String mdmCode;
    

}