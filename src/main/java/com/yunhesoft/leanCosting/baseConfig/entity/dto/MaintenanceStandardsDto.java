package com.yunhesoft.leanCosting.baseConfig.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class MaintenanceStandardsDto {

	@ApiModelProperty(value = "核算对象ID")
	private String unitid;

	@ApiModelProperty(value = "单元类型Id")
	private String devicetypelibraryId;
	
	@ApiModelProperty(value = "保养类型")
	private String maintenanceType;
	
}
