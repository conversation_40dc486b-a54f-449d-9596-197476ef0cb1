package com.yunhesoft.leanCosting.baseConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;



@Entity
@Setter
@Getter
@ApiModel("项目库")
@Table(name = "COSTLIBRARYITEM")
public class Costlibraryitem  extends BaseEntity {


    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="分类ID", example="8a1244a772565c34")
    @Column(name = "PID",length=100)
	private String pid;
    
    @ApiModelProperty(value="项目名称", example="项目名称1")
    @Column(name = "CINAME",length=200)
    private String ciname;
    
    @ApiModelProperty(value="计量单位Id", example="计量单位")
    @Column(name = "UNITID",length=100)
    private String unitId;
    
    @ApiModelProperty(value="计量单位", example="计量单位1")
    @Column(name = "UNIT",length=100)
    private String unit;
    
    @ApiModelProperty(value="ERP代码", example="ERP代码1")
    @Column(name = "ERPCODE",length=200)
    private String erpcode;
    
    @ApiModelProperty(value="注释", example="注释1")
    @Column(name = "MEMO",length=1000)
    private String memo;
    
    
    @ApiModelProperty(value="汇总方式", example="123")
    @Column(name = "SUMTYPE")
    private int sumtype;
    
    /** 无用 */
    @Column(name="TMUSED")
    private Integer tmused;
    
    /** 排序 */
    @Column(name="TMSORT", length=1000)
    private Integer tmsort;
    
    @ApiModelProperty(value="机构ID", example="机构ID1")
    @Column(name = "ORGCODE",length=100)
    private String orgcode;
    
    @ApiModelProperty(value="数据来源", example="来源添加，来源订单")
    @Column(name = "DATASOURCES")
    private String dataSources;
    
    
    @ApiModelProperty(value="外部数据编码")
    @Column(name = "MDMCODE",length = 50)
    private String mdmCode;
    
    @ApiModelProperty(value="外部数据项目编码")
    @Column(name = "ITEMALIAS",length = 200)
    private String itemAlias;
    
    
}
