package com.yunhesoft.leanCosting.baseConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;



@Entity
@Setter
@Getter
@ApiModel("设备状态库")
@Table(name = "PROJECT_TYPE")
public class ProjectType  extends BaseEntity {


    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="父状态ID", example="8a1244a772565c34")
    @Column(name = "PID",length=100)
    private String pid;
    
    @ApiModelProperty(value="名称", example="名称1")
    @Column(name = "DSNAME",length=200)
    private String dsname;
    
    @ApiModelProperty(value="注释", example="注释1")
    @Column(name = "MEMO",length=1000)
    private String memo;
    
    @ApiModelProperty(value="排序号", example="排序号1")
    @Column(name = "tmsort",length=1000)
    private String orderno;
    
    @ApiModelProperty(value="是否可用", example="123")
    @Column(name = "TMUSED")
    private int tmused;
    
    @ApiModelProperty(value="状态颜色", example="状态颜色1")
    @Column(name = "TYPECOLOR",length=200)
    private String typecolor;
    
    @ApiModelProperty(value="设备在运行", example="123")
    @Column(name = "ISRUNING")
    private int isruning;
    
    @ApiModelProperty(value="设备在运行", example="123")
    @Column(name = "ISDEFAULT")
    private int iSDefault;
    

}
