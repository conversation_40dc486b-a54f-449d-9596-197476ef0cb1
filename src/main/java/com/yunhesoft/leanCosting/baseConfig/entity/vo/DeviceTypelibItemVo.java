package com.yunhesoft.leanCosting.baseConfig.entity.vo;

import java.util.List;

import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibItem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DeviceTypelibItemVo extends DeviceTypelibItem {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "父分类ID", example = "8a1244a772565c34")
	private String pid;

	@ApiModelProperty(value = "主键子节点")
	private List<DeviceTypelibItemVo> children;
	
}
