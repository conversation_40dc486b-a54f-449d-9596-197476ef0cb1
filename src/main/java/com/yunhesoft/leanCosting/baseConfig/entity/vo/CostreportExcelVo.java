package com.yunhesoft.leanCosting.baseConfig.entity.vo;

import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * 崔茂群-核算导出统一结构函数
 * <AUTHOR>
 *
 */
@Getter
public class CostreportExcelVo {

	
	public CostreportExcelVo(String titleName,Map<String, String> ftitleName,Map<String, String> remarks,Map<String, CostBgcsszb> BgcsszbMap,Map<String, JSONArray> datas) {
		this.titleName = titleName;
		this.ftitleName = ftitleName;
		this.BgcsszbMap = BgcsszbMap;
		this.datas = datas;
		this.remarks = remarks;
	}
	
	@ApiModelProperty(value = "标题")
	private	String titleName;
	
	@ApiModelProperty(value = "副标题")
	private	Map<String, String> ftitleName;
	
	@ApiModelProperty(value = "简要分析")
	private	Map<String, String> remarks;

	@ApiModelProperty(value = "表头设置")
	private Map<String, CostBgcsszb> BgcsszbMap; 
	
	@ApiModelProperty(value = "合并表头")
	private JSONArray column;
	
	@ApiModelProperty(value = "数据")
	private Map<String, JSONArray> datas;

	public void setColumn(JSONArray column) {
		this.column = column;
	}
	
}
