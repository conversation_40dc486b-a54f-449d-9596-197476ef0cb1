package com.yunhesoft.leanCosting.baseConfig.entity.vo;

import com.yunhesoft.system.kernel.utils.excel.ExcelExt;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;


@Data
public class MaintenanceStandardsVo {

	
	@Excel(name = "设备名称", width = 20, orderNum = "1")
	@ExcelExt(align = "left") // 导出列居左对齐
	private String sbName;
	
	@Excel(name = "保养类型", width = 10, orderNum = "2")
	@ExcelExt(align = "left") // 导出列居左对齐
	private String maintenanceType;
	
	@Excel(name = "计划时间", width = 10, orderNum = "3")
	@ExcelExt(align = "left") // 导出列居左对齐
	private String scheduledTime;
	
	@Excel(name = "保养项", width = 50, orderNum = "4")
	@ExcelExt(align = "left") // 导出列居左对齐
	private String maintenanceItems;
	
	@Excel(name = "保养要求", width = 40, orderNum = "5")
	@ExcelExt(align = "left") // 导出列居左对齐
	private String maintenanceReq;
	
	//排序
    private Integer sort;
}
