package com.yunhesoft.leanCosting.baseConfig.entity.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CostDeviceTypeVo {
	
	@ApiModelProperty(value="主键ID")
	private String id;
	


	@ApiModelProperty(value="父分类ID", example="8a1244a772565c34")
    private String pid;
	
	 
    @ApiModelProperty(value="注释", example="注释1")
    private String memo;
    
    @ApiModelProperty(value="名称", example="注释1")
    private String dtname;
	/** 无用 */
    private Integer tmused;
    
    /** 排序 */
    private Integer tmsort;
    
    @ApiModelProperty(value="单位成本", example="计算产品的单位成本")
    private Integer fmluseclass;
    
    @ApiModelProperty(value="计算产品的单位成本名称")
    private String fmluseclassName;
    
    @ApiModelProperty(value="主键子节点")
    private List<CostDeviceTypeVo> children;
    
    @ApiModelProperty(value="整理内容", example="整理内容")
    private String queryContent;
    
    
    @ApiModelProperty(value="外部数据编码")
    private String mdmCode;
    
    @ApiModelProperty(value="外部数据序号")
    private String sortNo;
    
    
    
    @ApiModelProperty(value="单位成本", example="计算产品的单位成本")
    private Integer isSameProgram;
    
}
