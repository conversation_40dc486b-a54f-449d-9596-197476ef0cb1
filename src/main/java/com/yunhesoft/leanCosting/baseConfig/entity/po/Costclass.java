package com.yunhesoft.leanCosting.baseConfig.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Entity
@Setter
@Getter
@ApiModel("成本项目分类")
@Table(name = "COSTCLASS")
public class Costclass extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "父分类ID", example = "8a1244a772565c34")
	@Column(name = "PID", length = 100)
	private String pid;

	@ApiModelProperty(value = "名称", example = "名称1")
	@Column(name = "CCNAME", length = 200)
	private String ccname;

	@ApiModelProperty(value = "注释", example = "注释1")
	@Column(name = "MEMO", length = 1000)
	private String memo;

	@ApiModelProperty(value = "分类类型", example = "分类类型1")
	@Column(name = "CCTYPE", length = 100)
	private String cctype;
	
	/** 核算对象ID */
    @Column(name="UNITID", length=100)
    private String unitid;
    
    /** 版本 */
    @Column(name="BEGINTIME", length=10)
    private String begintime;
    
	/** 无用 */
    @Column(name="TMUSED")
    private Integer tmused;
    
    /** 排序 */
    @Column(name="TMSORT", length=1000)
    private Integer tmsort;

}
