package com.yunhesoft.leanCosting.baseConfig.entity.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CostlibraryitemVo {

	@ApiModelProperty(value="主键ID")
	private String id;

	@ApiModelProperty(value="分类ID", example="8a1244a772565c34")
	private String pid;
    
    @ApiModelProperty(value="项目名称", example="项目名称1")
    private String ccname;
    
    @ApiModelProperty(value="计量单位", example="计量单位1")
    private String unit;
    
    @ApiModelProperty(value="ERP代码", example="ERP代码1")
    private String erpcode;
    
    @ApiModelProperty(value="注释", example="注释1")
    private String memo;
    
    @ApiModelProperty(value="汇总方式", example="123")
    private int sumtype;
    
    @ApiModelProperty(value="汇总方式名称", example="123")
    private String sumtypeName;
    
	/** 无用 */
    private Integer tmused;
    
    /** 排序 */
    private Integer tmsort;
    
    @ApiModelProperty(value="机构ID", example="机构ID1")
    private String orgcode;
    
    @ApiModelProperty(value = "类型")
    private int type;

	@ApiModelProperty(value="主键子节点")
	private List<CostlibraryitemVo> children;
	
	@ApiModelProperty(value="数据来源", example="来源添加，来源订单")
    private String dataSources;

	
	@ApiModelProperty(value="外部数据编码")
    private String mdmCode;
    
    @ApiModelProperty(value="外部数据序号")
    private String sortNo;
    
    @ApiModelProperty(value="外部数据项目编码")
    private String itemAlias;
    
    @ApiModelProperty(value="外部数据父级名称")
    private String pname;
    
    @ApiModelProperty(value="外部数据记录标识")
    private String rowFlag;
    
}
