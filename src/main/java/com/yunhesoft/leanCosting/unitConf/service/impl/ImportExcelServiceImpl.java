package com.yunhesoft.leanCosting.unitConf.service.impl;


import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostKeyDeviceQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostImportExcelConf;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostKeyDeviceConf;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostMeteringUnit;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostImportExcelConfVo;
import com.yunhesoft.leanCosting.unitConf.service.IImportExcelService;
import com.yunhesoft.leanCosting.unitConf.service.IKeyDeviceService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;

@Service
public class ImportExcelServiceImpl implements IImportExcelService {

	@Autowired
	private EntityService entityService;
	
	@Autowired
	private IUnitMethodService methodService;
	
	@Autowired
	private IKeyDeviceService keyDeviceService;
	
	private String MARKSIGN = "___";
	
	/**
	 *	获取导入Excel配置数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<CostImportExcelConfVo> getCostImportExcelConfListByInit(MethodQueryDto queryDto) {
		List<CostImportExcelConfVo> result = new ArrayList<CostImportExcelConfVo>();
		if(StringUtils.isNotNull(queryDto)) {
			String unitid = queryDto.getUnitid(); // 核算对象ID
			String begintime = queryDto.getBegintime(); // 版本日期
			Integer dataType = queryDto.getDataType(); //数据类型
			if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&dataType!=null) {
				//默认配置记录
				List<CostImportExcelConfVo> templateList = this.getTemplateList(unitid, begintime, dataType);
				if(StringUtils.isNotEmpty(templateList)) {
					//自定义配置记录
					List<CostImportExcelConf> queryList = this.getCostImportExcelConfList(queryDto);
					if(StringUtils.isNotEmpty(queryList)) { //赋值
						HashMap<String, CostImportExcelConf> queryMap = this.getCostImportExcelConfMap(queryList);
						for (int i = 0; i < templateList.size(); i++) {
							CostImportExcelConfVo vo = templateList.get(i);
							String colmCode = vo.getColmCode();
							if(StringUtils.isNotEmpty(queryMap)&&StringUtils.isNotEmpty(colmCode)&&queryMap.containsKey(colmCode)) {
								CostImportExcelConf obj = queryMap.get(colmCode);
								if(obj!=null) {
									vo.setId(obj.getId());
									vo.setColmVal(obj.getColmVal());
								}
							}
							result.add(vo);
						}
					}else { //返回默认配置记录
						result = templateList;
					}
				}
			}
		}
		return result;
	}
	
	//获取模板数据
	private List<CostImportExcelConfVo> getTemplateList(String unitid,String begintime,Integer dataType) {
		List<CostImportExcelConfVo> result = new ArrayList<CostImportExcelConfVo>();
		if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&dataType!=null) {
			if(dataType==1) { //控制指标
				result.add(new CostImportExcelConfVo("", "sheetName", "页签名称", "控制指标", "此sheet页名称的导入内容会按照控制指标导入", 1, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "indexClass", "指标分类", "项目,项目,名称", "哪些标题被识别为分类，多个标题用逗号分割", 1, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "indexName", "指标名称", "位号", "以此标题识别采集点的名称和位号", 1, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "indexUnit", "指标单位", "单位", "以此标题识别采集点的计量单位", 0, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "indexRange", "指标范围", "指标范围", "以此标题识别采集点的指标范围", 0, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "datasource", "实时位号", "", "以此标题识别采集点的实时位号", 0, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "samplInterval", "采样间隔", "", "以此标题识别采集点的采样间隔", 0, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "pointCountLedger", "显示位数", "", "以此标题识别采集点的显示位数", 0, unitid, begintime, dataType));
			}else if(dataType==2) { //化验指标
				result.add(new CostImportExcelConfVo("", "sheetName", "页签名称", "化验指标", "此sheet页名称的导入内容会按照化验指标导入", 1, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "indexClass", "指标分类", "项目,项目,样品名称", "哪些标题被识别为分类，多个标题用逗号分割", 1, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "indexName", "指标名称", "分析项目", "以此标题识别采集点的名称和位号", 1, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "indexUnit", "指标单位", "", "以此标题识别采集点的计量单位", 0, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "indexRange", "指标范围", "", "以此标题识别采集点的指标范围", 0, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "pointCountLedger", "显示位数", "", "以此标题识别采集点的显示位数", 0, unitid, begintime, dataType));
			}else if(dataType==3) { //关键设备
				result.add(new CostImportExcelConfVo("", "sheetName", "页签名称", "关键设备", "此sheet页名称的导入内容会按照关键设备导入", 1, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "deviceName", "设备名称", "设备名称", "以此标题识别设备名称", 1, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "deviceTagNo", "设备位号", "设备位号", "以此标题识别设备位号", 0, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "deviceMatter", "输送介质", "输送介质", "以此标题识别输送介质", 0, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "overhaulCycle", "检修周期(小时)", "检修周期(小时)", "以此标题识别检修周期(小时)", 0, unitid, begintime, dataType));
				result.add(new CostImportExcelConfVo("", "deviceStatus", "设备状态", "设备状态", "以此标题识别设备状态", 0, unitid, begintime, dataType));
			}
		}
		return result;
	}
	
	//将数据转成Map
	private HashMap<String, CostImportExcelConf> getCostImportExcelConfMap(List<CostImportExcelConf> list) {
		HashMap<String, CostImportExcelConf> map = new HashMap<String, CostImportExcelConf>();
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostImportExcelConf obj = list.get(i);
				String colmCode = obj.getColmCode();
				if(!map.containsKey(colmCode)) {
					map.put(colmCode, obj);
				}
			}
		}
		return map;
	}
	
	//将数据转成Map
	private LinkedHashMap<String, CostImportExcelConfVo> getCostImportExcelConfVoMap(List<CostImportExcelConfVo> list) {
		LinkedHashMap<String, CostImportExcelConfVo> map = new LinkedHashMap<String, CostImportExcelConfVo>();
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostImportExcelConfVo obj = list.get(i);
				String colmCode = obj.getColmCode();
				if(!map.containsKey(colmCode)) {
					map.put(colmCode, obj);
				}
			}
		}
		return map;
	}
	
	/**
	 *	获取导入Excel配置数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<CostImportExcelConf> getCostImportExcelConfList(MethodQueryDto queryDto) {
		List<CostImportExcelConf> result = new ArrayList<CostImportExcelConf>();
		try {
			String unitid = ""; // 核算对象ID
			String begintime = ""; // 版本日期
			Integer dataType = null; //数据类型
			if (StringUtils.isNotNull(queryDto)) {
				unitid = queryDto.getUnitid();
				begintime = queryDto.getBegintime();
				dataType = queryDto.getDataType();
			}
			// 检索条件
			Where where = Where.create();
			if (StringUtils.isNotEmpty(unitid)) {
				where.eq(CostImportExcelConf::getUnitid, unitid);
			}
			if (StringUtils.isNotEmpty(begintime)) {
				where.eq(CostImportExcelConf::getBegintime, begintime);
			}
			if(dataType!=null) {
				where.eq(CostImportExcelConf::getDataType, dataType);
			}
			// 排序
			Order order = Order.create();
			order.orderByAsc(CostImportExcelConf::getUnitid);
			order.orderByAsc(CostImportExcelConf::getBegintime);
			order.orderByAsc(CostImportExcelConf::getDataType);
			order.orderByAsc(CostImportExcelConf::getColmCode);
			order.orderByAsc(CostImportExcelConf::getId);
			List<CostImportExcelConf> list = entityService.queryData(CostImportExcelConf.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result.addAll(list);
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	/**
	 *	保存导入Excel配置数据
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveCostImportExcelConfData(MethodSaveDto saveDto) {
		String result = "";
		List<CostImportExcelConf> addList = new ArrayList<CostImportExcelConf>();
		List<CostImportExcelConf> updList = new ArrayList<CostImportExcelConf>();
		if (saveDto != null) {
			String unitid = saveDto.getUnitid();
			String begintime = saveDto.getBegintime();
			Integer dataType = saveDto.getDataType();
			List<CostImportExcelConf> saveList = saveDto.getCostImportExcelConf_list();
			if (StringUtils.isNotEmpty(unitid) && StringUtils.isNotEmpty(begintime) && StringUtils.isNotNull(saveList)
				&& dataType!=null) {
				
				//获取其他tab页的页签名称
				HashMap<String, String> tabNameMap = this.getSheetNameMap(dataType, unitid, begintime);
				
				Map<String, CostImportExcelConf> dataMap = new HashMap<String, CostImportExcelConf>();
				MethodQueryDto queryDto = new MethodQueryDto();
				queryDto.setUnitid(unitid);
				queryDto.setBegintime(begintime);
				queryDto.setDataType(dataType);
				List<CostImportExcelConf> dataList = this.getCostImportExcelConfList(queryDto);
				if (StringUtils.isNotEmpty(dataList)) {
					dataMap = dataList.stream().collect(Collectors.toMap(CostImportExcelConf::getId, Function.identity()));
				}
				
				for (int i = 0; i < saveList.size(); i++) {
					CostImportExcelConf saveObj = saveList.get(i);
					String saveId = saveObj.getId();
					String colmCode = saveObj.getColmCode();
					String colmVal = saveObj.getColmVal();
					if(StringUtils.isNotEmpty(colmCode)&&"sheetName".equals(colmCode)&&StringUtils.isNotEmpty(colmVal)
						&&StringUtils.isNotEmpty(tabNameMap)&&tabNameMap.containsKey(colmVal)) {
						String sheetName = tabNameMap.get(colmVal);
						result = "页签名称【"+colmVal+"】在“"+sheetName+"配置页面”中已存在，不能重复设置！";
						break;
					}
					
					if (StringUtils.isNotEmpty(dataMap) && StringUtils.isNotEmpty(saveId) && dataMap.containsKey(saveId)) { // 修改
						CostImportExcelConf dataObj = dataMap.get(saveId);
						BeanUtils.copyProperties(saveObj, dataObj); // 赋予返回对象
						updList.add(dataObj);
					}else {
						CostImportExcelConf dataObj = new CostImportExcelConf();
						BeanUtils.copyProperties(saveObj, dataObj); // 赋予返回对象
						if(StringUtils.isEmpty(saveId)) {
							dataObj.setId(TMUID.getUID());
						}
						dataObj.setUnitid(unitid);
						dataObj.setBegintime(begintime);
						dataObj.setDataType(dataType);
						addList.add(dataObj);
					}
				}
			}
		}
		if ("".equals(result)) {
			if(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(updList)) {
				result = this.saveData(addList,updList,null);
			}
		}
		return result;
	}
	
	/**
	 *	保存导入Excel配置数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	@Override
	public String saveData(List<CostImportExcelConf> addList,List<CostImportExcelConf> updList,List<CostImportExcelConf> delList) {
		String result = "";
		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
			if (entityService.insertBatch(addList, 500) == 0) {
				result = "添加失败（导入Excel配置）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
			if (entityService.updateByIdBatch(updList, 500) == 0) { //空值不赋值（拖拽排序保存）
				result = "更新失败（导入Excel配置）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
			if (entityService.deleteByIdBatch(delList, 500) == 0) {
				result = "删除失败（导入Excel配置）！";
			}
		}
		return result;
	}
	
	//获取其他tab页的页签名称
	private HashMap<String, String> getSheetNameMap(int dataType,String unitid,String begintime) {
		HashMap<String, String> map = new HashMap<String, String>();
		for (int i = 1; i <= 3; i++) {
			if(dataType!=i) { //其他tab页
				String sheetLabel = "";
				if(i==1) {
					sheetLabel = "控制指标";
				}else if(i==2) {
					sheetLabel = "化验指标";
				}else if(i==3) {
					sheetLabel = "关键设备";
				}
				if(StringUtils.isNotEmpty(sheetLabel)) {
					MethodQueryDto dto = new MethodQueryDto();
					dto.setUnitid(unitid);
					dto.setBegintime(begintime);
					dto.setDataType(i);
					List<CostImportExcelConfVo> confList = this.getCostImportExcelConfListByInit(dto);
					if(StringUtils.isNotEmpty(confList)) {
						for (int j = 0; j < confList.size(); j++) {
							CostImportExcelConfVo confObj = confList.get(j);
							String colmCode = confObj.getColmCode();
							String colmVal = confObj.getColmVal();
							if(StringUtils.isNotEmpty(colmCode)&&StringUtils.isNotEmpty(colmVal)&&"sheetName".equals(colmCode)) {
								map.put(colmVal, sheetLabel);
							}
						}
					}
				}
			}
		}
		return map;
	}

	
	
	
	//——————————————————————————  导出 ↓  —————————————————————————————————
	/**
	 *	导出Excel
	 * @param queryDto
	 * @param request
	 * @param response
	 */
	@Override
	public void exportExcel(MethodQueryDto queryDto, HttpServletRequest request, HttpServletResponse response) {
		// 创建一个工作薄
		HSSFWorkbook wb = new HSSFWorkbook();
		Map<String,HSSFCellStyle> styleMap = this.styleMapInit(wb);
		String fileName = "导出Excel文件";
		if(queryDto!=null) {
			fileName = queryDto.getExcelFileName();
			String unitid = queryDto.getUnitid();
			String begintime = queryDto.getBegintime();
			Integer dataType = queryDto.getDataType()==null?0:queryDto.getDataType();
			if(dataType==0) { //全部导出
				this.exportExcelByType(request, response, wb, styleMap, fileName, unitid, begintime, 1); //控制指标
				this.exportExcelByType(request, response, wb, styleMap, fileName, unitid, begintime, 2); //化验指标
				this.exportExcelByType(request, response, wb, styleMap, fileName, unitid, begintime, 3); //关键设备
			}else { //单个sheet页导出
				this.exportExcelByType(request, response, wb, styleMap, fileName, unitid, begintime, dataType);
			}
		}
		try {
			String excelNameISO_8859_1 = new String(fileName.getBytes("gb2312"), "ISO-8859-1");
			if(wb != null) {
				downLoadExcelFile(request, response, wb, excelNameISO_8859_1);
			}
		}catch(Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 *	根据类型导出Excel数据
	 * @param request
	 * @param response
	 * @param wb
	 * @param styleMap
	 * @param fileName
	 * @param unitid
	 * @param begintime
	 * @param dataType
	 */
	public void exportExcelByType(HttpServletRequest request, HttpServletResponse response, HSSFWorkbook wb, 
		Map<String,HSSFCellStyle> styleMap, String fileName, String unitid, String begintime, Integer dataType) {
		if(wb!=null&&styleMap!=null&&StringUtils.isNotEmpty(fileName)&&StringUtils.isNotEmpty(unitid)
			&&StringUtils.isNotEmpty(begintime)&&dataType!=null) {
			//获取导出Excel配置数据
			MethodQueryDto dto = new MethodQueryDto();
			dto.setUnitid(unitid);
			dto.setBegintime(begintime);
			dto.setDataType(dataType);
			List<CostImportExcelConfVo> confList = this.getCostImportExcelConfListByInit(dto);
			LinkedHashMap<String, CostImportExcelConfVo> confMap = this.getCostImportExcelConfVoMap(confList);
			if(StringUtils.isNotEmpty(confList)&&StringUtils.isNotEmpty(confMap)&&confMap.containsKey("sheetName")) { //不为空并且存在sheet页配置
				//sheet页名称
				String sheetName = confMap.get("sheetName").getColmVal();
				
				//获取导出的数据
				LinkedHashMap<Integer, LinkedHashMap<String, Integer>> dataMap = this.getExcelDataMap(unitid, begintime, dataType, confMap);
				
				// 生成一个表格
				HSSFSheet sheet = wb.createSheet(sheetName);
				if((StringUtils.isNotEmpty(dataMap)&&(dataType==1||dataType==2))
					||(StringUtils.isNotEmpty(dataMap)&&dataMap.size()>1&&dataType==3)) {
					sheet.setDisplayGridlines(false);
				}else {
					sheet.setDisplayGridlines(true);
				}
				// 默认列宽度
				sheet.setDefaultColumnWidth(8);
				sheet.setDefaultRowHeightInPoints(20);
				
				HSSFCellStyle styleBody_Header = styleMap.get("styleBody_Header");
				HSSFCellStyle styleBody_C = styleMap.get("styleBody_C");
				HSSFCellStyle styleBody_L = styleMap.get("styleBody_L");
				HSSFCellStyle styleBody_R = styleMap.get("styleBody_R");
				
				if(dataType==1||dataType==2) { //控制指标、化验指标：标题为第一列
					Map<String,Map<Integer,Integer>> spanColmMap = new HashMap<String, Map<Integer,Integer>>(); //分类名称_第一次出现的位置_合并列数
					String oldName = "";
					int rowNum = -1;
					for (int i = 0; i < confList.size(); i++) {
						CostImportExcelConfVo confObj = confList.get(i);
						String colmCode = confObj.getColmCode();
						String colmVal = confObj.getColmVal();
						if(StringUtils.isNotEmpty(colmCode)&&!"sheetName".equals(colmCode)&&StringUtils.isNotEmpty(colmVal)) { //有配置才导出
							if("indexClass".equals(colmCode)) { //分类可以设置多个，需要遍历
								String[] colmValArr = colmVal.replaceAll("，", ",").split(",");
								for (int j = 0; j < colmValArr.length; j++) {
									String className = colmValArr[j]==null?"":colmValArr[j].trim();
									rowNum += 1;
									this.setNewRowValue(sheet, rowNum, styleBody_Header, styleBody_C, styleBody_L, styleBody_R, className, dataMap, dataType);
									//组队合并分类单元格map
									if(oldName.equals(className)) {
										if(spanColmMap.containsKey(className)) {
											Map<Integer,Integer> spanMap = spanColmMap.get(className);
											if(StringUtils.isNotEmpty(spanMap)) {
												int maxKey = Collections.max(spanMap.keySet());
												int maxVal = spanMap.get(maxKey);
												spanMap.put(maxKey, maxVal + 1);
											}
										}
									}else {
										oldName = className;
										if(spanColmMap.containsKey(className)) {
											Map<Integer,Integer> spanMap = spanColmMap.get(className);
											spanMap.put(j, 1);
										}else {
											Map<Integer,Integer> spanMap = new HashMap<Integer, Integer>();
											spanMap.put(j, 1);
											spanColmMap.put(className, spanMap);
										}
									}
								}
							}else {
								rowNum += 1;
								this.setNewRowValue(sheet, rowNum, styleBody_Header, styleBody_C, styleBody_L, styleBody_R, colmVal, dataMap, dataType);
							}
						}
					}
					//分类合并单元格
					if(StringUtils.isNotEmpty(spanColmMap)) {
						Iterator<Entry<String,Map<Integer,Integer>>> spanColmIter = spanColmMap.entrySet().iterator();
						while(spanColmIter.hasNext()) {
							Entry<String,Map<Integer,Integer>> spanColmEntryMap = spanColmIter.next();
							Map<Integer,Integer> spanMap = spanColmEntryMap.getValue();
							if(StringUtils.isNotEmpty(spanMap)) {
								Iterator<Entry<Integer,Integer>> spanIter = spanMap.entrySet().iterator();
								while(spanIter.hasNext()) {
									Entry<Integer,Integer> spanEntryMap = spanIter.next();
									Integer key = spanEntryMap.getKey(); //位置
									Integer val = spanEntryMap.getValue(); //合并数
									if(key!=null&&val!=null&&val>1) {
										CellRangeAddress cellRange = new CellRangeAddress(key, key+val-1, 0, 0);
					   					sheet.addMergedRegion(cellRange);
									}
								}
							}
						}
					}
				}else if(dataType==3) { //关键设备：标题为第一行
					//根据配置信息设置表格宽度
					int hideCount = 0;
					for (int i = 0; i < confList.size(); i++) {
						CostImportExcelConfVo confObj = confList.get(i);
						String colmCode = confObj.getColmCode();
						String colmVal = confObj.getColmVal();
						if(StringUtils.isNotEmpty(colmCode)&&!"sheetName".equals(colmCode)) { //有配置才导出
							sheet.setColumnWidth(i, 6000);
						}
						if(StringUtils.isEmpty(colmVal)) {
							hideCount += 1;
						}
					}
					if(hideCount>0) {
						int colmCount = confList.size();
						for (int i = 0; i < hideCount; i++) {
							sheet.setColumnWidth(colmCount-1-i, 2300);
						}
					}
					//遍历数据
					if(StringUtils.isNotEmpty(dataMap)) {
						Iterator<Entry<Integer, LinkedHashMap<String, Integer>>> dataSpanIter = dataMap.entrySet().iterator();
						while(dataSpanIter.hasNext()) {
							Entry<Integer, LinkedHashMap<String, Integer>> entryMap = dataSpanIter.next();
							int rowNum = entryMap.getKey();
							this.setNewRowValue(sheet, rowNum, styleBody_Header, styleBody_C, styleBody_L, styleBody_R, "", dataMap, dataType);
						}
					}
				}
			}
		}
	}
	
	/**
	 *	设置新行数据
	 * @param sheet
	 * @param rowNum
	 * @param styleBody_Header
	 * @param styleBody_C
	 * @param colmVal
	 * @param dataMap
	 * @param dataType
	 */
	public void setNewRowValue(HSSFSheet sheet,int rowNum,HSSFCellStyle styleBody_Header,HSSFCellStyle styleBody_C,
		HSSFCellStyle styleBody_L,HSSFCellStyle styleBody_R,String colmVal,LinkedHashMap<Integer, LinkedHashMap<String, Integer>> dataMap,
		Integer dataType) {
		//创建新行（第一列为配置的标题）
		HSSFRow newRow = sheet.createRow(rowNum);
		int colmIndex = -1;
		if(dataType==1||dataType==2) { //控制指标、化验指标，在左侧显示标题
			colmIndex += 1;
			HSSFCell newCell_zero = newRow.createCell(colmIndex);
			if(rowNum==0) {
				newCell_zero.setCellStyle(styleBody_Header);
			}else {
				newCell_zero.setCellStyle(styleBody_C);
			}
			newCell_zero.setCellValue(colmVal);
		}
		//数据部分
		if(StringUtils.isNotEmpty(dataMap)&&dataMap.containsKey(rowNum)) {
			LinkedHashMap<String, Integer> dataSpanMap = dataMap.get(rowNum);
			if(StringUtils.isNotEmpty(dataSpanMap)) {
				Iterator<Entry<String, Integer>> dataSpanIter = dataSpanMap.entrySet().iterator();
				while(dataSpanIter.hasNext()) {
					Entry<String, Integer> entryMap = dataSpanIter.next();
					String valStr = entryMap.getKey()==null?"":entryMap.getKey();
					String[] valArr = valStr.split(MARKSIGN);
					String val = valArr[valArr.length-1];
					if(StringUtils.isEmpty(val)||"nullStr".equals(val)) {
						val = "";
					}
					String colmCode = "";
					if(dataType==3) {
						String[] colmKeyArr = valArr[0].split("_");
						if(colmKeyArr.length==2) {
							colmCode = colmKeyArr[1];
						}
					}
					int spanVal = entryMap.getValue()==null?1:entryMap.getValue();
					int subVal = spanVal - 1;
					colmIndex += 1;
					HSSFCell newCell = newRow.createCell(colmIndex);
					if(rowNum==0) { //标题行
						newCell.setCellStyle(styleBody_Header);
					}else {
						if(dataType==3&&colmIndex>0) { //关键设备数据记录居左对齐
							if(StringUtils.isNotEmpty(colmCode)&&"overhaulCycle".equals(colmCode)) {
								newCell.setCellStyle(styleBody_R);
							}else {
								newCell.setCellStyle(styleBody_L);
							}
						}else {
							newCell.setCellStyle(styleBody_C);
						}
					}
					newCell.setCellValue(val);
					if(subVal>0) {
						//合并单元格，划线
       					CellRangeAddress cellRange = new CellRangeAddress(rowNum, rowNum, colmIndex, colmIndex+subVal);
      		    		RegionUtil.setBorderLeft(BorderStyle.THIN, cellRange, sheet);
      		    		RegionUtil.setBorderTop(BorderStyle.THIN, cellRange, sheet);
      					RegionUtil.setBorderRight(BorderStyle.THIN, cellRange, sheet);
      					RegionUtil.setBorderBottom(BorderStyle.THIN, cellRange, sheet);
       					sheet.addMergedRegion(cellRange);
						colmIndex += subVal;
					}
				}
			}
		}
	}
	
	//初始化导出Excel表格样式
	public Map<String,HSSFCellStyle> styleMapInit(HSSFWorkbook wb) {
		Map<String,HSSFCellStyle> map = new HashMap<String, HSSFCellStyle>();
		boolean isWrapText = true; //自动换行
		short fontSize = 8;
		
		//表头样式
		HSSFCellStyle styleBody_Header = wb.createCellStyle();
		styleBody_Header.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_Header.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
		styleBody_Header.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_Header.setBorderBottom(BorderStyle.THIN);
		styleBody_Header.setBorderLeft(BorderStyle.THIN);
		styleBody_Header.setBorderRight(BorderStyle.THIN);
		styleBody_Header.setBorderTop(BorderStyle.THIN);
		styleBody_Header.setWrapText(isWrapText);
		// 生成表头字体
		Font font_Header = wb.createFont();
		font_Header.setFontName("宋体");
		font_Header.setBold(true);
		font_Header.setFontHeightInPoints(fontSize);
		styleBody_Header.setFont(font_Header);
		map.put("styleBody_Header", styleBody_Header);
		
		//居中
		HSSFCellStyle styleBody_C = wb.createCellStyle();
		styleBody_C.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_C.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
		styleBody_C.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_C.setBorderBottom(BorderStyle.THIN);
		styleBody_C.setBorderLeft(BorderStyle.THIN);
		styleBody_C.setBorderRight(BorderStyle.THIN);
		styleBody_C.setBorderTop(BorderStyle.THIN);
		styleBody_C.setWrapText(isWrapText);
		// 生成表头字体
		Font font_C = wb.createFont();
		font_C.setFontName("宋体");
		font_C.setFontHeightInPoints(fontSize);
		styleBody_C.setFont(font_C);
		map.put("styleBody_C", styleBody_C);
		
		//居左
		HSSFCellStyle styleBody_L = wb.createCellStyle();
		styleBody_L.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_L.setAlignment(HorizontalAlignment.LEFT); // 设置水平对齐方式
		styleBody_L.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_L.setBorderBottom(BorderStyle.THIN);
		styleBody_L.setBorderLeft(BorderStyle.THIN);
		styleBody_L.setBorderRight(BorderStyle.THIN);
		styleBody_L.setBorderTop(BorderStyle.THIN);
		styleBody_L.setWrapText(isWrapText);
		// 生成表头字体
		Font font_L = wb.createFont();
		font_L.setFontName("宋体");
		font_L.setFontHeightInPoints(fontSize);
		styleBody_L.setFont(font_L);
		map.put("styleBody_L", styleBody_L);
		
		//居右
		HSSFCellStyle styleBody_R = wb.createCellStyle();
		styleBody_R.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_R.setAlignment(HorizontalAlignment.RIGHT); // 设置水平对齐方式
		styleBody_R.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_R.setBorderBottom(BorderStyle.THIN);
		styleBody_R.setBorderLeft(BorderStyle.THIN);
		styleBody_R.setBorderRight(BorderStyle.THIN);
		styleBody_R.setBorderTop(BorderStyle.THIN);
		styleBody_R.setWrapText(isWrapText);
		// 生成表头字体
		Font font_R = wb.createFont();
		font_R.setFontName("宋体");
		font_R.setFontHeightInPoints(fontSize);
		styleBody_R.setFont(font_R);
		map.put("styleBody_R", styleBody_R);

		return map;
	}
	
	/**
	 *	文件推送下载
	 * @param request
	 * @param response
	 * @param wb
	 * @param fileHeader
	 */
	@SuppressWarnings("static-access")
	public static void downLoadExcelFile(HttpServletRequest request, HttpServletResponse response, HSSFWorkbook wb, String fileHeader) {
		String fullname = fileHeader+".xls";// 生成文件名
		if (request.getHeader("User-Agent").indexOf("MSIE 5.5") != -1) {
			response.setHeader("Content-Disposition", "filename=" + fullname);
		} else {
			response.addHeader("Content-Disposition", "attachment;filename=" + fullname);
		}
		response.setHeader("Content-Type", "application/msexcel");
		ServletOutputStream streamOut = null;
		try {
			streamOut = response.getOutputStream();
			wb.write(streamOut);// 将数据写入输出流
		} catch (Exception e) {
		} finally {
			if (streamOut != null) {
				try {
					streamOut.close();
				} catch (Exception e1) {
				}
			}
		}
		response.setStatus(response.SC_OK);
		try {
			response.flushBuffer();// 推送
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	//----------------------  整理导出数据  -----------------------------
	
	//获取导出Excel的数据
	private LinkedHashMap<Integer, LinkedHashMap<String, Integer>> getExcelDataMap(String unitid, String begintime,Integer dataType, 
		LinkedHashMap<String, CostImportExcelConfVo> confMap) {
		LinkedHashMap<Integer, LinkedHashMap<String, Integer>> map = new LinkedHashMap<Integer, LinkedHashMap<String,Integer>>();
		if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&dataType!=null&&StringUtils.isNotEmpty(confMap)) {
			if(dataType==1||dataType==2) { //控制指标、化验指标
				//指标分类
				int classlevel = 0; //分类层级
				LinkedHashMap<String, String> sampleclassMap = new LinkedHashMap<String, String>();
				if(confMap.containsKey("indexClass")) {
					CostImportExcelConfVo indexClassObj = confMap.get("indexClass");
					if(indexClassObj!=null) {
						String indexClass = indexClassObj.getColmVal();
						String[] indexClassArr = indexClass.replaceAll("，", ",").split(",");
						classlevel = indexClassArr.length;
						if(classlevel>0) { //根据数量，判断查询几层分类
							MethodQueryDto dto = new MethodQueryDto();
							dto.setUnitid(unitid);
							dto.setBegintime(begintime);
							List<Costunitsampleclass> classList = methodService.getCostunitsampleclassList(dto);
							sampleclassMap = this.getSampleclassMapBylevel(classList, classlevel);
						}
					}
				}
				//指标名称
				Map<String, List<Costunitsampledot>> sampledotMap = new HashMap<String, List<Costunitsampledot>>();
				if(confMap.containsKey("indexName")) {
					String ctype = "2"; //控制指标
					if(dataType==2) { //化验指标
						ctype = "3"; //lims指标
					}
					MethodQueryDto dto = new MethodQueryDto();
					dto.setUnitid(unitid);
					dto.setBegintime(begintime);
					dto.setCtype(ctype);
					List<Costunitsampledot> dotList = methodService.getCostunitsampledotList(dto);
					if(StringUtils.isNotEmpty(dotList)) {
						sampledotMap = dotList.stream().collect(Collectors.groupingBy(Costunitsampledot::getPid));
					}
				}
				
				//组队数据
				if(StringUtils.isNotEmpty(sampleclassMap)&&StringUtils.isNotEmpty(sampledotMap)&&classlevel>0) {
					Iterator<Entry<String, String>> classIter = sampleclassMap.entrySet().iterator();
					while(classIter.hasNext()) {
						Entry<String, String> entryMap = classIter.next();
						String classId = entryMap.getKey();
						String classNameLabel = entryMap.getValue();
						if(StringUtils.isNotEmpty(classId)&&StringUtils.isNotEmpty(classNameLabel)) {
							String[] classNameArr = classNameLabel.split(MARKSIGN);
							if(classNameArr.length==classlevel&&sampledotMap.containsKey(classId)) {
								List<Costunitsampledot> dotList = sampledotMap.get(classId);
								if(StringUtils.isNotEmpty(dotList)) {
									for (int i = 0; i < dotList.size(); i++) {
										Costunitsampledot dotObj = dotList.get(i);
										String dotName = dotObj.getName();
										String classLabel = "";
										for (int j = 0; j < classNameArr.length; j++) {
											String className = classNameArr[j];
											if(StringUtils.isNotEmpty(classLabel)) {
												classLabel += MARKSIGN + className;
											}else {
												classLabel = className;
											}
											//分类数据返回值Map
											if(map.containsKey(j)) {
												LinkedHashMap<String, Integer> linkMap = map.get(j);
												if(linkMap.containsKey(classLabel)) {
													linkMap.put(classLabel, linkMap.get(classLabel)+1);
												}else {
													linkMap.put(classLabel, 1);
												}
												map.put(j, linkMap);
											}else {
												LinkedHashMap<String, Integer> linkMap = new LinkedHashMap<String, Integer>();
												linkMap.put(classLabel, 1);
												map.put(j, linkMap);
											}
										}
										//采集点名称
										String dotNameLabel = classNameLabel + MARKSIGN + dotName;
										int level = classlevel;
										if(map.containsKey(level)) {
											LinkedHashMap<String, Integer> linkMap = map.get(level);
											linkMap.put(dotNameLabel, 1);
											map.put(level, linkMap);
										}else {
											LinkedHashMap<String, Integer> linkMap = new LinkedHashMap<String, Integer>();
											linkMap.put(dotNameLabel, 1);
											map.put(level, linkMap);
										}
										//采集点其他属性
										Iterator<Entry<String, CostImportExcelConfVo>> confIter = confMap.entrySet().iterator();
										while(confIter.hasNext()) {
											Entry<String, CostImportExcelConfVo> confEntry = confIter.next();
											String colmCode = confEntry.getKey();
											CostImportExcelConfVo confVo = confEntry.getValue();
											if(StringUtils.isNotEmpty(colmCode)&&confVo!=null) {
												String colmVal = confVo.getColmVal();
												if(StringUtils.isNotEmpty(colmVal)) { //有配置数据
													String retVal = null;
													if("indexUnit".equals(colmCode)) { //计量单位
														String sdUnit = dotObj.getSdUnit();
														if(StringUtils.isEmpty(sdUnit)) {
															sdUnit = "nullStr";
														}
														retVal = sdUnit;
													}else if("indexRange".equals(colmCode)) { //指标范围
														Double indexRangeLower = dotObj.getIndexRangeLower();
														Double indexRangeUpper = dotObj.getIndexRangeUpper();
														if(indexRangeLower==null&&indexRangeUpper==null) {
															retVal = "/";
														}else if(indexRangeLower==null&&indexRangeUpper!=null) {
															String indexRangeUpper_str = new BigDecimal(indexRangeUpper).setScale(6, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
															retVal = "≤"+indexRangeUpper_str;
														}else if(indexRangeLower!=null&&indexRangeUpper==null) {
															String indexRangeLower_str = new BigDecimal(indexRangeLower).setScale(6, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
															retVal = "≥"+indexRangeLower_str;
														}else if(indexRangeLower!=null&&indexRangeUpper!=null) {
															String indexRangeLower_str = new BigDecimal(indexRangeLower).setScale(6, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
															String indexRangeUpper_str = new BigDecimal(indexRangeUpper).setScale(6, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
															if(indexRangeLower.doubleValue()==indexRangeUpper.doubleValue()) {
																retVal = indexRangeUpper_str;
															}else {
																retVal = indexRangeLower_str+"~"+indexRangeUpper_str;
															}
														}
													}else if("datasource".equals(colmCode)) { //实时位号
														String datasource = dotObj.getDatasource();
														if(StringUtils.isEmpty(datasource)) {
															datasource = "nullStr";
														}
														retVal = datasource;
													}else if("samplInterval".equals(colmCode)) { //采样间隔
														Integer samplInterval = dotObj.getSamplInterval()==null?5:dotObj.getSamplInterval();
														retVal = String.valueOf(samplInterval);
													}else if("pointCountLedger".equals(colmCode)) { //显示位数
														Integer pointCountLedger = dotObj.getPointCountLedger()==null?3:dotObj.getPointCountLedger();
														retVal = String.valueOf(pointCountLedger);
													}
													if(retVal!=null) {
														String otherLabel = dotNameLabel + MARKSIGN + retVal; //为了兼容相同分类的问题--使用采集点名称+值（会多出一个“_”）
														level += 1;
														if(map.containsKey(level)) {
															LinkedHashMap<String, Integer> linkMap = map.get(level);
															linkMap.put(otherLabel, 1);
															map.put(level, linkMap);
														}else {
															LinkedHashMap<String, Integer> linkMap = new LinkedHashMap<String, Integer>();
															linkMap.put(otherLabel, 1);
															map.put(level, linkMap);
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}else if(dataType==3) { //关键设备
				String deviceName_title = "";
				if(confMap.containsKey("deviceName")) { //设备名称
					CostImportExcelConfVo deviceNameObj = confMap.get("deviceName");
					if(deviceNameObj!=null) {
						deviceName_title = deviceNameObj.getColmVal();
					}
				}
				String deviceTagNo_title = ""; 
				if(confMap.containsKey("deviceTagNo")) { //设备位号
					CostImportExcelConfVo deviceTagNoObj = confMap.get("deviceTagNo");
					if(deviceTagNoObj!=null) {
						deviceTagNo_title = deviceTagNoObj.getColmVal();
					}
				}
				String deviceMatter_title = ""; 
				if(confMap.containsKey("deviceMatter")) { //输送介质
					CostImportExcelConfVo deviceMatterObj = confMap.get("deviceMatter");
					if(deviceMatterObj!=null) {
						deviceMatter_title = deviceMatterObj.getColmVal();
					}
				}
				String overhaulCycle_title = ""; 
				if(confMap.containsKey("overhaulCycle")) { //检修周期(小时)
					CostImportExcelConfVo overhaulCycleObj = confMap.get("overhaulCycle");
					if(overhaulCycleObj!=null) {
						overhaulCycle_title = overhaulCycleObj.getColmVal();
					}
				}
				String deviceStatus_title = ""; 
				if(confMap.containsKey("deviceStatus")) { //设备状态
					CostImportExcelConfVo deviceStatusObj = confMap.get("deviceStatus");
					if(deviceStatusObj!=null) {
						deviceStatus_title = deviceStatusObj.getColmVal();
					}
				}
				if(StringUtils.isNotEmpty(deviceName_title)) { //存在配置的表头数据
					//标题返回值Map
					LinkedHashMap<String, Integer> labelMap = new LinkedHashMap<String, Integer>();
					labelMap.put("序号", 1);
					labelMap.put(deviceName_title, 1);
					if(StringUtils.isNotEmpty(deviceTagNo_title)) {
						labelMap.put(deviceTagNo_title, 1);
					}
					if(StringUtils.isNotEmpty(deviceMatter_title)) {
						labelMap.put(deviceMatter_title, 1);
					}
					if(StringUtils.isNotEmpty(overhaulCycle_title)) {
						labelMap.put(overhaulCycle_title, 1);
					}
					if(StringUtils.isNotEmpty(deviceStatus_title)) {
						labelMap.put(deviceStatus_title, 1);
					}
					map.put(0, labelMap);
					//检索关键设备数据
					CostKeyDeviceQueryDto dto = new CostKeyDeviceQueryDto();
					dto.setUnitid(unitid);
					dto.setBegintime(begintime);
					List<CostKeyDeviceConf> list = keyDeviceService.getKeyDeviceConfList(dto);
					if(StringUtils.isNotEmpty(list)) {
						for (int i = 0; i < list.size(); i++) {
							CostKeyDeviceConf obj = list.get(i);
							String deviceName = obj.getDeviceName();
							if(StringUtils.isEmpty(deviceName)) {
								deviceName = "nullStr";
							}
							LinkedHashMap<String, Integer> dataMap = new LinkedHashMap<String, Integer>();
							dataMap.put(String.valueOf(i+1), 1);
							dataMap.put(deviceName, 1);
							if(StringUtils.isNotEmpty(deviceTagNo_title)) {
								String deviceTagNo = obj.getDeviceTagNo();
								if(StringUtils.isEmpty(deviceTagNo)) {
									deviceTagNo = "nullStr";
								}
								dataMap.put(deviceName+"_deviceTagNo"+MARKSIGN+deviceTagNo, 1);
							}
							if(StringUtils.isNotEmpty(deviceMatter_title)) {
								String deviceMatter = obj.getDeviceMatter();
								if(StringUtils.isEmpty(deviceMatter)) {
									deviceMatter = "nullStr";
								}
								dataMap.put(deviceName+"_deviceMatter"+MARKSIGN+deviceMatter, 1);
							}
							if(StringUtils.isNotEmpty(overhaulCycle_title)) {
								Integer overhaulCycle = obj.getOverhaulCycle();
								if(overhaulCycle==null) {
									overhaulCycle = 0;
								}
								dataMap.put(deviceName+"_overhaulCycle"+MARKSIGN+overhaulCycle, 1);
							}
							if(StringUtils.isNotEmpty(deviceStatus_title)) {
								String deviceStatus = obj.getDeviceStatus();
								if(StringUtils.isEmpty(deviceStatus)) {
									deviceStatus = "nullStr";
								}
								dataMap.put(deviceName+"_deviceStatus"+MARKSIGN+deviceStatus, 1);
							}
							map.put(i+1, dataMap);
						}
					}
				}
			}
		}
		return map;
	}
	
	//根据配置的层级组队采集点分类数据
	private LinkedHashMap<String, String> getSampleclassMapBylevel(List<Costunitsampleclass> classList,int classlevel) {
		LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
		if(StringUtils.isNotEmpty(classList)&&classlevel>0) {
			Map<String, List<Costunitsampleclass>> classMap = classList.stream().collect(Collectors.groupingBy(Costunitsampleclass::getPid));
			this.getSampleclassMapBylevel(map, classMap, "root", "", 1, classlevel);
		}
		return map;
	}
	//递归
	private void getSampleclassMapBylevel(LinkedHashMap<String, String> map, Map<String, List<Costunitsampleclass>> classMap, String pid,
		String pname, int dataLevel, int classlevel) {
		if(map==null) {
			map = new LinkedHashMap<String, String>();
		}
		if(StringUtils.isNotEmpty(classMap)&&StringUtils.isNotEmpty(pid)&&classMap.containsKey(pid)&&dataLevel<=classlevel) {
			List<Costunitsampleclass> classList = classMap.get(pid);
			if(StringUtils.isNotEmpty(classList)) {
				for (int i = 0; i < classList.size(); i++) {
					Costunitsampleclass classObj = classList.get(i);
					String classId = classObj.getId();
					String className = classObj.getName();
					if(StringUtils.isNotEmpty(pname)) {
						className = pname+MARKSIGN+className;
					}
					if(!map.containsKey(classId)) {
						map.put(classId, className);
					}
					this.getSampleclassMapBylevel(map, classMap, classId, className, dataLevel+1, classlevel);
				}
			}	
		}
	}
	
	//——————————————————————————  导出 ↑  —————————————————————————————————
	
	
	
	
	
	//——————————————————————————  导入 ↓  —————————————————————————————————
	/**
	 *	导入Excel
	 */
	@Override
	public String importExcel(MultipartFile file, MethodQueryDto queryDto) {
		String result = "";
		if(file!=null&&queryDto!=null) {
			String unitid = queryDto.getUnitid();
			String begintime = queryDto.getBegintime();
			if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)) {
				//获取导入配置信息
				HashMap<String, List<CostImportExcelConfVo>> confMap = new HashMap<String, List<CostImportExcelConfVo>>(); //sheetName--list
				HashMap<String, Integer> sheetTypeMap = new HashMap<String, Integer>(); //判断sheet页的数据类型
				this.getImportExcelConfMapBySheet(confMap, unitid, begintime, 1, sheetTypeMap); //控制指标
				this.getImportExcelConfMapBySheet(confMap, unitid, begintime, 2, sheetTypeMap); //化验指标
				this.getImportExcelConfMapBySheet(confMap, unitid, begintime, 3, sheetTypeMap); //关键设备
				
				try {
					Workbook wb = WorkbookFactory.create(file.getInputStream());
					if(wb!=null) {
						Integer sheetNum = wb.getNumberOfSheets();
						if(sheetNum!=null&&sheetNum>0) {
							for (int i = 0; i < sheetNum; i++) {
								Sheet sheet = wb.getSheetAt(i);// 获取sheet页
								if(sheet!=null) {
									String sheetName = sheet.getSheetName();
									if(StringUtils.isNotEmpty(sheetName)) {
										sheetName = sheetName.trim();
										if(StringUtils.isNotEmpty(confMap)&&confMap.containsKey(sheetName)) {
											List<CostImportExcelConfVo> confList = confMap.get(sheetName);
											int dataType = sheetTypeMap.get(sheetName);
											String ret = "";
											if(dataType==1||dataType==2) { //控制指标、化验指标
												ret = this.importSampledot(sheet, confList, unitid, begintime, dataType);
											}else if(dataType==3) { //关键设备
												ret = this.importKeyDevice(i, file, confList, unitid, begintime);
											}
											if(StringUtils.isNotEmpty(ret)) {
												String[] retArr = ret.split("_");
												if(retArr.length==2) {
													String status_ret = retArr[0];
													String value_ret = retArr[1];
													if("SUCCESS".equals(status_ret)) {
														if(dataType==1) {
															result += "控制指标：导入 "+value_ret+" 条记录！</br>";
														}else if(dataType==2) {
															result += "化验指标：导入 "+value_ret+" 条记录！</br>";
														}else if(dataType==3) {
															result += "关键设备：导入 "+value_ret+" 条记录！</br>";
														}
													}else if("ERROR".equals(status_ret)) {
														if(dataType==1) {
															result += "控制指标："+value_ret+"！</br>";
														}else if(dataType==2) {
															result += "化验指标："+value_ret+"！</br>";
														}else if(dataType==3) {
															result += "关键设备："+value_ret+"！</br>";
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				} catch (EncryptedDocumentException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return result;
	}
	
	//按照sheet页获取导入excel配置Map
	private void getImportExcelConfMapBySheet(HashMap<String, List<CostImportExcelConfVo>> map, 
		String unitid, String begintime, Integer dataType, HashMap<String, Integer> sheetTypeMap) {
		if(map==null) {
			map = new HashMap<String, List<CostImportExcelConfVo>>();
		}
		if(sheetTypeMap==null) {
			sheetTypeMap = new HashMap<String, Integer>();
		}
		String sheetName = "";
		List<CostImportExcelConfVo> list = new ArrayList<CostImportExcelConfVo>();
		if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&dataType!=null) {
			MethodQueryDto dto = new MethodQueryDto();
			dto.setUnitid(unitid); // 核算对象ID
			dto.setBegintime(begintime); // 版本日期
			dto.setDataType(dataType); // 数据类型
			List<CostImportExcelConfVo> confList = this.getCostImportExcelConfListByInit(dto);
			if(StringUtils.isNotEmpty(confList)) {
				for (int i = 0; i < confList.size(); i++) {
					CostImportExcelConfVo confObj = confList.get(i);
					String colmCode = confObj.getColmCode();
					String colmVal = confObj.getColmVal();
					if(StringUtils.isNotEmpty(colmCode)&&"sheetName".equals(colmCode)) {
						sheetName = colmVal;
					}else {
						list.add(confObj);
					}
				}
			}
		}
		if(StringUtils.isNotEmpty(sheetName)&&StringUtils.isNotEmpty(list)) {
			map.put(sheetName, list);
			sheetTypeMap.put(sheetName, dataType);
		}
	}
	
	
	//导入采集点（控制指标、化验指标）
	private String importSampledot(Sheet sheet, List<CostImportExcelConfVo> confList,
		String unitid, String begintime, Integer dataType) {
		String result = "";
		int dataCount = 0;
		//计量单位
		List<String> meterUnitList = new ArrayList<String>();
		//excel组队的数据
		List<Costunitsampledot> excelList = new ArrayList<Costunitsampledot>();
		// 组号_列编码_位置号
		LinkedHashMap<Integer, LinkedHashMap<String, Integer>> colmBmNumMap = new LinkedHashMap<Integer, LinkedHashMap<String,Integer>>();
		// 组号_列号_行号_值（拼接）
		LinkedHashMap<Integer, LinkedHashMap<Integer, LinkedHashMap<Integer, String>>> map = new LinkedHashMap<Integer, LinkedHashMap<Integer,LinkedHashMap<Integer,String>>>();

		if(sheet!=null&&StringUtils.isNotEmpty(confList)&&StringUtils.isNotEmpty(unitid)
			&&StringUtils.isNotEmpty(begintime)&&dataType!=null) {
			
			//配置列
			String indexClass = "";
			String indexName = "";
			LinkedHashMap<String, CostImportExcelConfVo> confMap = this.getCostImportExcelConfVoMap(confList);
			if(StringUtils.isNotEmpty(confMap)) {
				if(confMap.containsKey("indexClass")) {
					CostImportExcelConfVo confObj = confMap.get("indexClass");
					if(confObj!=null) {
						indexClass = confObj.getColmVal()==null?"":confObj.getColmVal();
					}
				}
				if(confMap.containsKey("indexName")) {
					CostImportExcelConfVo confObj = confMap.get("indexName");
					if(confObj!=null) {
						indexName = confObj.getColmVal()==null?"":confObj.getColmVal();
					}
				}
			}
			String[] indexClassArr = indexClass.replaceAll("，", ",").split(",");
			
			try {
				int fristRowNum = sheet.getFirstRowNum(); //开始行
				int lastRowNum = sheet.getLastRowNum(); //截止行
				if(fristRowNum>=0&&lastRowNum>=0&&lastRowNum>=fristRowNum
					&&StringUtils.isNotEmpty(indexClassArr)&&StringUtils.isNotEmpty(indexName)) {
					
					int groupNum = -1;
					int rowNum = -1;
					int colmNum = -1;
					String oldColmVal = "";
					String oldRowVal = "";
					boolean isFrist = true;
					for (int i = fristRowNum; i <= lastRowNum; i++) {
						Row row = sheet.getRow(i); // 获取行数据
						if(row != null) { //行
							int firstCellNum = row.getFirstCellNum();
							int lastCellNum = row.getLastCellNum();
							if(firstCellNum>=0&&lastCellNum>=0&&lastCellNum>=firstCellNum) {
								for (int j = firstCellNum; j <= lastCellNum; j++) {
									Cell cell = row.getCell(j);
									if (cell != null) { //列  一列为一条记录
										String value = cell.getStringCellValue();
										if(value==null||"".equals(value.trim())||"null".equals(value.trim())) {
											value = "";
										}else {
											value = value.trim().replaceAll("\r", "\n").replaceAll("\n", "");
										}
										
										if(j == firstCellNum) { //配置的标题列
											if(StringUtils.isNotEmpty(value)) { //空值取上行记录
												oldRowVal = value;
											}else {
												value = oldRowVal;
											}
											if(value.equals(indexClassArr[0].trim())&&isFrist) { //分类第一个-----开始
												isFrist = false;
												groupNum += 1;
												rowNum = 0;
												colmNum = -1;
												if(!map.containsKey(groupNum)) {
													map.put(groupNum, null);
												}
											}else {
												rowNum += 1;
												colmNum = -1;
												
												if(indexClassArr.length>1&&rowNum<=indexClassArr.length-1) { //还有其他分类（除一个外）
													if(!value.equals(indexClassArr[rowNum].trim())) {
														map.remove(groupNum); //没有指标此分类不解析
														colmBmNumMap.remove(groupNum);
														break;
													}
												}else if(rowNum==indexClassArr.length) { //指标名称
													if(value.equals(indexName.trim())) { //出现指标名称配置后，重置分组默认值
														isFrist = true;
														//记录字段位置
														LinkedHashMap<String, Integer> colmBmMap = new LinkedHashMap<String, Integer>();
														if(colmBmNumMap.containsKey(groupNum)) {
															colmBmMap = colmBmNumMap.get(groupNum);
														}
														colmBmMap.put("indexName", rowNum);
														colmBmNumMap.put(groupNum, colmBmMap);
													}else {
														map.remove(groupNum); //没有指标名称不解析
														colmBmNumMap.remove(groupNum);
														break;
													}
												}else { //其他属性
													if(StringUtils.isNotEmpty(confList)) {
														String bm = "";
														for (int k = 0; k < confList.size(); k++) {
															CostImportExcelConfVo confObj = confList.get(k);
															String colmCode = confObj.getColmCode();
															String colmVal = confObj.getColmVal();
															if(StringUtils.isNotEmpty(colmCode)&&StringUtils.isNotEmpty(colmVal)) {
																if(!"sheetName".equals(colmCode)&&!"indexClass".equals(colmCode)&&!"indexName".equals(colmCode)) {
																	if(value.equals(colmVal.trim())) {
																		bm = colmCode;
																		break;
																	}
							        							}
															}
														}
														if(StringUtils.isNotEmpty(bm)) {
															//记录字段位置
															LinkedHashMap<String, Integer> colmBmMap = new LinkedHashMap<String, Integer>();
															if(colmBmNumMap.containsKey(groupNum)) {
																colmBmMap = colmBmNumMap.get(groupNum);
															}
															colmBmMap.put(bm, rowNum);
															colmBmNumMap.put(groupNum, colmBmMap);
														}
													}
												}
											}
										}else { //数据部分
											colmNum += 1;
											//分类部分数据处理（空值从哪里获取数据）
											if(rowNum<=indexClassArr.length-1) {
												if(colmNum==0&&groupNum>0) { //第一列，数据为空时，从上个分组中获取； 并且前面有分组
													if(StringUtils.isEmpty(value)) { //分类为空
														if(map.containsKey(groupNum-1)) { //从上组中获取分类
															LinkedHashMap<Integer, LinkedHashMap<Integer, String>> colmMap = map.get(groupNum-1);
															if(StringUtils.isNotEmpty(colmMap)) { //获取最后一列
																List<Integer> colmKeyList = new ArrayList<Integer>(colmMap.keySet());
																if(StringUtils.isNotEmpty(colmKeyList)) {
																	LinkedHashMap<Integer, String> rowMap = colmMap.get(colmKeyList.get(colmKeyList.size()-1));
																	if(StringUtils.isNotEmpty(rowMap)&&rowMap.containsKey(rowNum)) {
																		value = rowMap.get(rowNum); //上组分类的值
																		oldColmVal = value;
																	}
																}
															}
														}
													}
												}else { //其他列数据为空时，从前面的获取
													if(StringUtils.isNotEmpty(value)) {
														oldColmVal = value;
													}else {
														value = oldColmVal;
													}
												}
											}
											if(map.containsKey(groupNum)) { //有此组数据Map
												LinkedHashMap<Integer, LinkedHashMap<Integer, String>> colmMap = map.get(groupNum);
												if(colmMap==null) {
													colmMap = new LinkedHashMap<Integer, LinkedHashMap<Integer,String>>();
												}
												if(colmMap.containsKey(colmNum)) {
													LinkedHashMap<Integer, String> rowMap = colmMap.get(colmNum);
													if(StringUtils.isNotEmpty(rowMap)) {
														rowMap.put(rowNum, value);
													}else {
														rowMap = new LinkedHashMap<Integer, String>();
														rowMap.put(rowNum, value);
														colmMap.put(colmNum, rowMap);
														map.put(groupNum, colmMap);
													}
												}else {
													LinkedHashMap<Integer, String> rowMap = new LinkedHashMap<Integer, String>();
													rowMap.put(rowNum, value);
													colmMap.put(colmNum, rowMap);
													map.put(groupNum, colmMap);
												}
											}else { //无效数据
												break;
											}
										}
									}
								}
							}
						}
					}
				}
			}catch(Exception e) {
				e.printStackTrace();
			}
		}
		
		//组队解析的excel数据
		if(StringUtils.isNotEmpty(colmBmNumMap)&&StringUtils.isNotEmpty(map)) {
			//遍历列位置
			Iterator<Entry<Integer, LinkedHashMap<String, Integer>>> colmBmNumMapIter = colmBmNumMap.entrySet().iterator();
			while(colmBmNumMapIter.hasNext()) {
				Entry<Integer, LinkedHashMap<String, Integer>> colmBmNumMapEntry = colmBmNumMapIter.next();
				Integer groupId = colmBmNumMapEntry.getKey();
				LinkedHashMap<String, Integer> bmNumMap = colmBmNumMapEntry.getValue();
				if(groupId!=null&&StringUtils.isNotEmpty(bmNumMap)&&map.containsKey(groupId)) { //有组数据
					//遍历数据
					LinkedHashMap<Integer, LinkedHashMap<Integer, String>> dataMap = map.get(groupId);
					if(StringUtils.isNotEmpty(dataMap)) {
						Iterator<Entry<Integer, LinkedHashMap<Integer, String>>> dataIter = dataMap.entrySet().iterator();
						while(dataIter.hasNext()) {
							Entry<Integer, LinkedHashMap<Integer, String>> dataEntry = dataIter.next();
							LinkedHashMap<Integer, String> dataValMap = dataEntry.getValue();
							if(StringUtils.isNotEmpty(dataValMap)) {
								
								//根据字段位置赋值
								if(bmNumMap.containsKey("indexName")) { //指标名称
									Integer indexName_num = bmNumMap.get("indexName"); //列号
									if(indexName_num!=null&&indexName_num>0) {
										
										Costunitsampledot dotObj = new Costunitsampledot();
										//拼接分类
										String className = "";
										for (int i = 0; i < indexName_num; i++) {
											if(dataValMap.containsKey(i)) {
												String flmc = dataValMap.get(i);
												if(StringUtils.isNotEmpty(flmc)) {
													className += MARKSIGN+flmc.trim();
												}else { //分类名称为空，无效数据
													dotObj = null;
												}
											}else { //分类不全，无效数据
												dotObj = null;
											}
										}
										//指标名称
										String indexName = "";
										if(dataValMap.containsKey(indexName_num)) {
											String zbmc = dataValMap.get(indexName_num);
											if(StringUtils.isNotEmpty(zbmc)) {
												indexName = zbmc.trim();
											}else { //指标名称为空，无效数据
												dotObj = null;
											}
										}else { //指标名称不全，无效数据
											dotObj = null;
										}
										
										if(dotObj!=null&&StringUtils.isNotEmpty(className)&&StringUtils.isNotEmpty(indexName)) {
											className = className.substring(MARKSIGN.length());
											dotObj.setPid(className);
											dotObj.setName(indexName);
											
											//其他属性赋值
											Iterator<Entry<String, Integer>> bmNumIter = bmNumMap.entrySet().iterator();
											while(bmNumIter.hasNext()) {
												Entry<String, Integer> entryMap = bmNumIter.next();
												String bm = entryMap.getKey(); //列名
												Integer num = entryMap.getValue(); //列号
												if(StringUtils.isNotEmpty(bm)&&!"indexName".equals(bm)&&num!=null&&num>0) {
													if(dataValMap.containsKey(num)) {
														String dataVal = dataValMap.get(num);
														if(StringUtils.isNotEmpty(dataVal)) {
															dataVal = dataVal.trim();
														}else {
															dataVal = null;
														}
														if("indexUnit".equals(bm)) { //计量单位
															dotObj.setSdUnit(dataVal);
														}else if("indexRange".equals(bm)) { //指标范围
															Double indexRangeLower = null;
															Double indexRangeUpper = null;
															if(StringUtils.isEmpty(dataVal)||"/".equals(dataVal)) {
																//空值
															}else if(dataVal.indexOf("<")!=-1||dataVal.indexOf("≤")!=-1||dataVal.indexOf("<=")!=-1) {
																String[] dataValArr = null;
																dataVal = dataVal.replaceAll("<=", "<").replaceAll("≤", "<");
																if(dataVal.indexOf("<")!=-1) {
																	dataValArr = dataVal.split("<");
																}
																if(dataValArr!=null&&dataValArr.length==2) {
																	String val = dataValArr[1];
																	try {
																		if(StringUtils.isNotEmpty(val)) {
																			indexRangeUpper = Double.valueOf(val.trim());
																		}
																	}catch (Exception e) {}
																}
															}else if(dataVal.indexOf(">")!=-1||dataVal.indexOf("≥")!=-1||dataVal.indexOf(">=")!=-1) {
																String[] dataValArr = null;
																dataVal = dataVal.replaceAll(">=", ">").replaceAll("≥", ">");
																if(dataVal.indexOf(">")!=-1) {
																	dataValArr = dataVal.split(">");
																}
																if(dataValArr!=null&&dataValArr.length==2) {
																	String val = dataValArr[1];
																	try {
																		if(StringUtils.isNotEmpty(val)) {
																			indexRangeLower = Double.valueOf(val.trim());
																		}
																	}catch (Exception e) {}
																}
															}else if(dataVal.indexOf("~")!=-1) {
																String[] dataValArr = dataVal.split("~");
																if(dataValArr!=null&&dataValArr.length==2) {
																	String valMin = dataValArr[0];
																	String valMax = dataValArr[1];
																	try {
																		if(StringUtils.isNotEmpty(valMin)) {
																			indexRangeLower = Double.valueOf(valMin.trim());
																		}
																		if(StringUtils.isNotEmpty(valMax)) {
																			indexRangeUpper = Double.valueOf(valMax.trim());
																		}
																	}catch (Exception e) {}
																}
															}else {
																try {
																	if(StringUtils.isNotEmpty(dataVal)) {
																		indexRangeLower = Double.valueOf(dataVal.trim());
																		indexRangeUpper = indexRangeLower;
																	}
																}catch (Exception e) {}
															}
															dotObj.setIndexRangeLower(indexRangeLower);
															dotObj.setIndexRangeUpper(indexRangeUpper);
														}else if("datasource".equals(bm)) { //实时位号
															dotObj.setDatasource(dataVal);
														}else if("samplInterval".equals(bm)) { //采样间隔
															Integer samplInterval = 5;
															try {
																if(dataVal!=null) {
																	samplInterval = Integer.valueOf(dataVal);
																}
															}catch (Exception e) {}
															dotObj.setSamplInterval(samplInterval);
														}else if("pointCountLedger".equals(bm)) { //显示位数
															Integer pointCountLedger = 3;
															try {
																if(dataVal!=null) {
																	pointCountLedger = Integer.valueOf(dataVal);
																}
															}catch (Exception e) {}
															dotObj.setPointCountLedger(pointCountLedger);
														}
													}
												}
											}
											excelList.add(dotObj);
										}
									}
								}
							}
						}
					}
				}
			}
		}
		//保存采集点数据
		if(StringUtils.isNotEmpty(excelList)&&StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)
			&&dataType!=null&&StringUtils.isNotEmpty(confList)) {
			List<Costunitsampleclass> addClassList = new ArrayList<Costunitsampleclass>();
			List<Costunitsampledot> addList = new ArrayList<Costunitsampledot>();
			List<Costunitsampledot> updList = new ArrayList<Costunitsampledot>();
			HashMap<String, String> hasR3dbMap = new HashMap<String, String>();
			List<Costunitsampledot> synR3dbList = new ArrayList<Costunitsampledot>();
			//获取数据库中的记录
			MethodQueryDto dot = new MethodQueryDto();
			dot.setUnitid(unitid);
			dot.setBegintime(begintime);
			List<Costunitsampleclass> classList = methodService.getCostunitsampleclassList(dot);
			List<Costunitsampledot> dotList = methodService.getCostunitsampledotList(dot);
			//采集点分类Map
			HashMap<String, List<Costunitsampleclass>> classNameChildMap = new HashMap<String, List<Costunitsampleclass>>(); //分类名称——有子分类的列表Map
			HashMap<String, String> classNameIdMap = new HashMap<String, String>(); //分类名称——分类ID的Map
			HashMap<String, String> classIdNameMap = new HashMap<String, String>(); //分类ID——分类名称的Map
			this.getSampleclassMap(classList, classNameChildMap, classNameIdMap, classIdNameMap);
			//采集点Map
			Map<String, Costunitsampledot> dotNameMap = new HashMap<String, Costunitsampledot>(); //分类名称+采集点名称的Map
			Map<String, List<Costunitsampledot>> dotPidMap = new HashMap<String, List<Costunitsampledot>>();
			if(StringUtils.isNotEmpty(dotList)) {
				dotPidMap = dotList.stream().collect(Collectors.groupingBy(Costunitsampledot::getPid));
				this.getSampledotMap(dotList, classIdNameMap, dotNameMap);
			}
			
			//遍历excel解析的数据
			for (int i = 0; i < excelList.size(); i++) {
				Costunitsampledot excelObj = excelList.get(i);
				excelObj.setTmused(1);
				excelObj.setUnitid(unitid);
				excelObj.setBegintime(begintime);
				if(dataType==1) { //控制指标
					excelObj.setCtype("2");
				}else if(dataType==2) { //化验指标
					excelObj.setCtype("3");
				}
				String pname = excelObj.getPid();
				String name = excelObj.getName();
				if(StringUtils.isNotEmpty(pname)&&StringUtils.isNotEmpty(name)) {
					String dataKey = pname+MARKSIGN+name;
					if(StringUtils.isNotEmpty(dotNameMap)&&dotNameMap.containsKey(dataKey)) { //修改记录
						Costunitsampledot dotObj = dotNameMap.get(dataKey);
						
						//获取同步r3db的采集点数据
						methodService.getSynR3dbSampledotData(dotObj, excelObj, synR3dbList, hasR3dbMap);
						
						String ctype = dotObj.getCtype();
						if(StringUtils.isNotEmpty(ctype)&&ctype.equals(excelObj.getCtype())) { //类型一致再修改
							for (int j = 0; j < confList.size(); j++) {
								CostImportExcelConfVo confObj = confList.get(j);
								String colmCode = confObj.getColmCode();
								String colmVal = confObj.getColmVal();
								if(StringUtils.isNotEmpty(colmCode)&&StringUtils.isNotEmpty(colmVal)) {
									if("indexUnit".equals(colmCode)) { //计量单位
										String sdUnit = excelObj.getSdUnit();
										dotObj.setSdUnit(sdUnit);
										if(StringUtils.isNotEmpty(sdUnit)&&!meterUnitList.contains(sdUnit)) {
											meterUnitList.add(sdUnit);
										}
									}else if("indexRange".equals(colmCode)) { //指标范围
										dotObj.setIndexRangeLower(excelObj.getIndexRangeLower());
										dotObj.setIndexRangeUpper(excelObj.getIndexRangeUpper());
									}else if("datasource".equals(colmCode)) { //实时位号
										dotObj.setDatasource(excelObj.getDatasource());
									}else if("samplInterval".equals(colmCode)) { //采样间隔
										dotObj.setSamplInterval(excelObj.getSamplInterval());
									}else if("pointCountLedger".equals(colmCode)) { //显示位数
										dotObj.setPointCountLedger(excelObj.getPointCountLedger());
									}
								}
							}
							updList.add(dotObj);
						}
					}else { //新增记录
						if(StringUtils.isNotEmpty(classNameChildMap)&&classNameChildMap.containsKey(pname)) {
							//因为这层分类下面还有子分类数据，所有此条记录无效（分类和采集点不能同级）
							continue;
						}else if(StringUtils.isNotEmpty(classNameIdMap)&&classNameIdMap.containsKey(pname)) { //有此分类
							//创建采集点数据
							this.createDotData(classNameIdMap, pname, dotPidMap, excelObj, addList, meterUnitList);
							if(StringUtils.isNotEmpty(excelObj.getId())) { //数据有效
								//获取同步r3db的采集点数据
								methodService.getSynR3dbSampledotData(excelObj, null, synR3dbList, hasR3dbMap);
							}
						}else { //没有此分类，需要生成分类后，新增数据
							String[] classArr = pname.split(MARKSIGN);
							int classCount = classArr.length;
							if(classCount>0) {
								int count = 0;
								String pid_str = "root";
								String pname_str = "root";
								String classId = this.getClassBylevel(classCount, classArr, classNameIdMap); //判断从那个分类开始创建子分类数据
								if(StringUtils.isNotEmpty(classId)) { //有分类，判断从第几层开始新建分类
									if(classIdNameMap.containsKey(classId)) {
										String classNameStr = classIdNameMap.get(classId);
										if(StringUtils.isNotEmpty(classNameStr)) {
											String[] classNameArr = classNameStr.split(MARKSIGN);
											count = classNameArr.length;
											//获取子分类数据
											pid_str = classId;
											pname_str = classNameStr;
										}
									}
								}
								for (int j = count; j < classCount; j++) {
									String className = classArr[j];
									//创建分类数据
									String ret = this.createClassData(pid_str, pname_str, className, unitid, begintime,
										classNameChildMap, classNameIdMap, classIdNameMap, addClassList);
									if(StringUtils.isNotEmpty(ret)) {
										pid_str = ret;
										if("root".equals(pname_str)) {
											pname_str = className;
										}else {
											pname_str += MARKSIGN+className;
										}
									}else {
										break;
									}
								}
								//创建采集点数据
								this.createDotData(classNameIdMap, pname, dotPidMap, excelObj, addList, meterUnitList);
								if(StringUtils.isNotEmpty(excelObj.getId())) { //数据有效
									//获取同步r3db的采集点数据
									methodService.getSynR3dbSampledotData(excelObj, null, synR3dbList, hasR3dbMap);
								}
							}
						}
					}
				}
			}
			if(StringUtils.isEmpty(result)&&StringUtils.isNotEmpty(addClassList)) {
				result = methodService.saveSampleclassData(addClassList, null, null);
			}
			if(StringUtils.isEmpty(result)&&(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(updList))) {
				result = methodService.saveSampledotDataByIsUpdNull(true, addList, updList, null);
				//调用其他外部接口，同步数据
				if(StringUtils.isEmpty(result)) { //保存成功后
					dataCount = addList.size()+updList.size();
					methodService.synOtherInterfaceBySampledotChange(addList, updList);
					if(StringUtils.isNotEmpty(synR3dbList)) { //同步R3DB
						methodService.synR3dbDataBySampledot(synR3dbList, null);
					}
				}
			}
		}
		//保存计量单位
		if(StringUtils.isNotEmpty(meterUnitList)&&StringUtils.isEmpty(result)) {
			result = this.asyncMeterUnitData(meterUnitList);
		}
		if(StringUtils.isEmpty(result)) {
			result = "SUCCESS_"+dataCount;
		}else {
			result = "ERROR_"+result;
		}
		return result;
	}
	
	//创建分类数据
	private String createClassData(String pid, String pname, String className,String unitid,String begintime,
		HashMap<String, List<Costunitsampleclass>> classNameChildMap,HashMap<String, String> classNameIdMap,
		HashMap<String, String> classIdNameMap, List<Costunitsampleclass> addClassList) {
		if(addClassList==null) {
			addClassList = new ArrayList<Costunitsampleclass>();
		}
		String result = "";
		if(StringUtils.isNotEmpty(pid)&&StringUtils.isNotEmpty(pname)&&StringUtils.isNotEmpty(className)) {
			Costunitsampleclass classObj = new Costunitsampleclass();
			result = TMUID.getUID();
			int maxPx = 0;
			if(classNameChildMap.containsKey(pname)) {
				List<Costunitsampleclass> classNameChildList = classNameChildMap.get(pname);
				if(StringUtils.isNotEmpty(classNameChildList)) {
					Integer tmsort = classNameChildList.get(classNameChildList.size()-1).getTmsort();
					if(tmsort!=null) {
						maxPx = tmsort;
					}
				}
			}
			maxPx += 1;
			classObj.setId(result);
			classObj.setName(className);
			classObj.setBegintime(begintime);
			classObj.setUnitid(unitid);
			classObj.setPid(pid);
			classObj.setTmused(1);
			classObj.setTmsort(maxPx);
			addClassList.add(classObj);
			if(classNameChildMap.containsKey(pname)) {
				List<Costunitsampleclass> classNameChildList = classNameChildMap.get(pname);
				classNameChildList.add(classObj);
				classNameChildMap.put(pname, classNameChildList);
			}else {
				List<Costunitsampleclass> classNameChildList = new ArrayList<Costunitsampleclass>();
				classNameChildList.add(classObj);
				classNameChildMap.put(pname, classNameChildList);
			}
			if("root".equals(pname)) {
				classNameIdMap.put(className, result);
				classIdNameMap.put(result, className);
			}else {
				classNameIdMap.put(pname+MARKSIGN+className, result);
				classIdNameMap.put(result, pname+MARKSIGN+className);
			}
		}
		return result;
	}
	
	//创建采集点数据
	private void createDotData(HashMap<String, String> classNameIdMap, String pname, Map<String, List<Costunitsampledot>> dotPidMap,
			Costunitsampledot excelObj, List<Costunitsampledot> addList, List<String> meterUnitList) {
		if(StringUtils.isNotEmpty(classNameIdMap)&&classNameIdMap.containsKey(pname)) { //有此分类
			String classId = classNameIdMap.get(pname);
			int maxPx = 0;
			if(dotPidMap.containsKey(classId)) {
				List<Costunitsampledot> sampledotList = dotPidMap.get(classId);
				if(StringUtils.isNotEmpty(sampledotList)) {
					Integer tmsort = sampledotList.get(sampledotList.size()-1).getTmsort();
					if(tmsort!=null) {
						maxPx = tmsort;
					}
				}
			}
			maxPx += 1;
			excelObj.setId(TMUID.getUID());
			excelObj.setPid(classId);
			excelObj.setUseTo(1);
			excelObj.setTmsort(maxPx);
			excelObj.setTmused(1);
			addList.add(excelObj);
			if(dotPidMap.containsKey(classId)) {
				List<Costunitsampledot> sampledotList = dotPidMap.get(classId);
				sampledotList.add(excelObj);
				dotPidMap.put(classId, sampledotList);
			}else {
				List<Costunitsampledot> sampledotList = new ArrayList<Costunitsampledot>();
				sampledotList.add(excelObj);
				dotPidMap.put(classId, sampledotList);
			}
			//计量单位
			String sdUnit = excelObj.getSdUnit();
			if(StringUtils.isNotEmpty(sdUnit)&&!meterUnitList.contains(sdUnit)) {
				meterUnitList.add(sdUnit);
			}
		}
	}
	
	//获取上级分类ID（递归至出现分类截止）
	private String getClassBylevel(int count, String[] classArr,HashMap<String, String> classNameIdMap) {
		String result = "";
		if(count>=0&&classArr!=null&&classArr.length>=count&&StringUtils.isNotEmpty(classNameIdMap)) {
			String className = "";
			for (int i = 0; i < count; i++) {
				className += MARKSIGN+classArr[i];
			}
			if(StringUtils.isNotEmpty(className)) {
				className = className.substring(MARKSIGN.length());
				if(classNameIdMap.containsKey(className)) {
					result = classNameIdMap.get(className);
				}else {
				    result = this.getClassBylevel(count-1, classArr, classNameIdMap);
				}
			}
		}
		return result;
	}
	
	//获取计量单位Map
	private HashMap<String, CostMeteringUnit> getCostMeteringUnitMap(List<CostMeteringUnit> list) {
		HashMap<String, CostMeteringUnit> map = new HashMap<String, CostMeteringUnit>();
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostMeteringUnit obj = list.get(i);
				String muName = obj.getMuName();
				if(StringUtils.isNotEmpty(muName)&&!map.containsKey(muName)) {
					map.put(muName, obj);
				}
			}
		}
		return map;
	}
	
	//获取采集点分类Map
	private void getSampleclassMap(List<Costunitsampleclass> list,HashMap<String, List<Costunitsampleclass>> nameChildMap, 
		HashMap<String, String> nameIdMap, HashMap<String, String> idNameMap) {
		if(nameChildMap==null) {
			nameChildMap = new HashMap<String, List<Costunitsampleclass>>();
		}
		if(nameIdMap==null) {
			nameIdMap = new HashMap<String, String>();
		}
		if(idNameMap==null) {
			idNameMap = new HashMap<String, String>();
		}
		if(StringUtils.isNotEmpty(list)) {
			Map<String, List<Costunitsampleclass>> pidMap = list.stream().collect(Collectors.groupingBy(Costunitsampleclass::getPid));
			if(StringUtils.isNotEmpty(pidMap)&&pidMap.containsKey("root")) {
				List<Costunitsampleclass> childList = pidMap.get("root");
				if(StringUtils.isNotEmpty(childList)) {
					nameChildMap.put("root", childList);
					for (int i = 0; i < childList.size(); i++) {
						Costunitsampleclass classObj = childList.get(i);
						String id = classObj.getId();
						String name = classObj.getName();
						nameIdMap.put(name, id);
						idNameMap.put(id, name);
						this.getChildSampleclassMap(id, name, pidMap, nameChildMap, nameIdMap, idNameMap);
					}
				}
			}
		}
	}
	
	//递归获取所有子采集点数据Map
	private void getChildSampleclassMap(String pid, String pname, Map<String, List<Costunitsampleclass>> pidMap,
		HashMap<String, List<Costunitsampleclass>> nameChildMap, HashMap<String, String> nameIdMap,
		HashMap<String, String> idNameMap) {
		if(nameChildMap==null) {
			nameChildMap = new HashMap<String, List<Costunitsampleclass>>();
		}
		if(nameIdMap==null) {
			nameIdMap = new HashMap<String, String>();
		}
		if(idNameMap==null) {
			idNameMap = new HashMap<String, String>();
		}
		if(StringUtils.isNotEmpty(pid)&&StringUtils.isNotEmpty(pidMap)&&pidMap.containsKey(pid)) {
			List<Costunitsampleclass> list = pidMap.get(pid);
			if(StringUtils.isNotEmpty(list)) {
				nameChildMap.put(pname, list);
				for (int i = 0; i < list.size(); i++) {
					Costunitsampleclass obj = list.get(i);
					String id = obj.getId();
					String name = obj.getName();
					String nameKey = pname+MARKSIGN+name;
					nameIdMap.put(nameKey, id);
					idNameMap.put(id, nameKey);
					this.getChildSampleclassMap(id, nameKey, pidMap,nameChildMap, nameIdMap, idNameMap);
				}
			}
		}
	}
	
	//采集点分类名称Map
	private void getSampledotMap(List<Costunitsampledot> list, HashMap<String, String> classIdNameMap, 
		Map<String, Costunitsampledot> dotNameMap) {
		if(dotNameMap==null) {
			dotNameMap = new HashMap<String, Costunitsampledot>();
		}
		if(StringUtils.isNotEmpty(list)&&StringUtils.isNotEmpty(classIdNameMap)) {
			for (int i = 0; i < list.size(); i++) {
				Costunitsampledot obj = list.get(i);
				String pid = obj.getPid();
				String name = obj.getName();
				if(StringUtils.isNotEmpty(pid)&&StringUtils.isNotEmpty(name)&&StringUtils.isNotEmpty(classIdNameMap)
					&&classIdNameMap.containsKey(pid)) {
					String classNameStr = classIdNameMap.get(pid);
					String nameStr = classNameStr+MARKSIGN+name;
					dotNameMap.put(nameStr, obj);
				}
			}
		}
	}
	
	/**
	 *	同步计量单位数据
	 * @param meterUnitList
	 * @return
	 */
	@Override
	public String asyncMeterUnitData(List<String> meterUnitList) {
		String result = "";
		List<CostMeteringUnit> addList = new ArrayList<CostMeteringUnit>();
		if(StringUtils.isNotEmpty(meterUnitList)) {
			//获取数据库中的记录
			int maxPx = 0;
			List<CostMeteringUnit> dataList = methodService.getCostMeteringUnitList(null);
			HashMap<String, CostMeteringUnit> dataMap = this.getCostMeteringUnitMap(dataList);
			if(StringUtils.isNotEmpty(dataList)) {
				Integer tmSort = dataList.get(dataList.size()-1).getTmSort();
				if(tmSort!=null) {
					maxPx = tmSort;
				}
			}
			for (int i = 0; i < meterUnitList.size(); i++) {
				String muName = meterUnitList.get(i);
				if(StringUtils.isEmpty(dataMap)||!dataMap.containsKey(muName)) { //新增
					maxPx += 1;
					CostMeteringUnit obj = new CostMeteringUnit();
					obj.setId(TMUID.getUID());
					obj.setMuName(muName);
					obj.setTmSort(maxPx);
					obj.setTmUsed(1);
					addList.add(obj);
				}
			}
		}
		if(StringUtils.isNotEmpty(addList)) {
			result = methodService.saveDataCostMeteringUnit(addList, null, null);
		}
		return result;
	}
	
	
	
	//导入关键设备
	@SuppressWarnings("unchecked")
	private String importKeyDevice(Integer sheetIndex, MultipartFile file, List<CostImportExcelConfVo> confList,
		String unitid, String begintime) {
		String result = "";
		int dataCount = 0;
		List<CostKeyDeviceConf> addList = new ArrayList<CostKeyDeviceConf>();
		List<CostKeyDeviceConf> updList = new ArrayList<CostKeyDeviceConf>();
		if(sheetIndex!=null&&file!=null&&StringUtils.isNotEmpty(confList)&&StringUtils.isNotEmpty(unitid)
			&&StringUtils.isNotEmpty(begintime)) {
			//检索关键设备数据
			int maxPx = 0;
			Map<String, CostKeyDeviceConf> queryMap = new HashMap<String, CostKeyDeviceConf>();
			CostKeyDeviceQueryDto dto = new CostKeyDeviceQueryDto();
			dto.setUnitid(unitid);
			dto.setBegintime(begintime);
			List<CostKeyDeviceConf> queryList = keyDeviceService.getKeyDeviceConfList(dto);
			if(StringUtils.isNotEmpty(queryList)) {
				queryMap = queryList.stream().collect(Collectors.toMap(CostKeyDeviceConf::getDeviceName, Function.identity(), (key1, key2) -> key2));
				Integer tmSort = queryList.get(queryList.size()-1).getTmSort();
				if(tmSort!=null) {
					maxPx = tmSort;
				}
			}
			try {
				ImportParams importParams = new ImportParams();
				importParams.setStartRows(0);
				importParams.setStartSheetIndex(sheetIndex);
		        ExcelImportResult<?> excelResult = ExcelImport.importExcel(file.getInputStream(), Map.class, importParams);
		        if (excelResult != null) {
		        	List<LinkedHashMap<String, String>> dataList = (List<LinkedHashMap<String, String>>) excelResult.getList();
	        		if(StringUtils.isNotEmpty(dataList)) {
	        			HashMap<String, String> confMap = new HashMap<String, String>();
	        			HashMap<String, String> hasTagNoMap = new HashMap<String, String>();
	        			List<String> hasDeviceNameList = new ArrayList<String>();
	        			for (int j = 0; j < dataList.size(); j++) {
	        				LinkedHashMap<String, String> dataMap = dataList.get(j);
	        				if(StringUtils.isNotEmpty(dataMap)) {
	        					CostKeyDeviceConf excelData = null;
	        					//遍历配置信息
	        					for (int i = 0; i < confList.size(); i++) {
	        						CostImportExcelConfVo confObj = confList.get(i);
	        						String colmCode = confObj.getColmCode();
	        						String colmVal = confObj.getColmVal();
	        						if(StringUtils.isNotEmpty(colmCode)&&StringUtils.isNotEmpty(colmVal)) {
	        							if("deviceName".equals(colmCode)||"deviceStatus".equals(colmCode)||"deviceTagNo".equals(colmCode)
	        								||"deviceMatter".equals(colmCode)||"overhaulCycle".equals(colmCode)) { //设备名称、设备状态
	        								if(dataMap.containsKey(colmVal)) {
	        									String value_excel = dataMap.get(colmVal);
	        									if(StringUtils.isNotEmpty(value_excel)) {
	        										value_excel = value_excel.trim();
	        									}else {
	        										value_excel = "";
	        									}
	        									if(excelData==null) {
	        										excelData = new CostKeyDeviceConf();
	        									}
	        									if("deviceName".equals(colmCode)) { //设备名称
	        										excelData.setDeviceName(value_excel);
	        									}else if("deviceStatus".equals(colmCode)) { //设备状态
	        										excelData.setDeviceStatus(value_excel);
	        									}else if("deviceTagNo".equals(colmCode)) { //设备位号
	        										excelData.setDeviceTagNo(value_excel);
	        									}else if("deviceMatter".equals(colmCode)) { //输送介质
	        										excelData.setDeviceMatter(value_excel);
	        									}else if("overhaulCycle".equals(colmCode)) { //检修周期(小时)
	        										int overhaulCycle = 0;
	        										if(StringUtils.isNotEmpty(value_excel)) {
	        											try {
	        												overhaulCycle  = Integer.valueOf(value_excel);
	        											}catch(Exception e) {
	        												
	        											}
	        										}
	        										if(overhaulCycle<0) {
	        											overhaulCycle = 0;
	        										}else if(overhaulCycle>99999999) {
	        											overhaulCycle = 99999999;
	        										}
	        										excelData.setOverhaulCycle(overhaulCycle);
	        									}
	        									//赋值用
	        									if(!confMap.containsKey(colmCode)) {
		        									confMap.put(colmCode, colmVal);
		        								}
	        								}
	        							}
	        						}
								}
	        					if(excelData!=null) { //excel有数据
	        						String deviceName = excelData.getDeviceName();
	        						String deviceStatus = excelData.getDeviceStatus();
	        						String deviceTagNo = excelData.getDeviceTagNo();
	        						String deviceMatter = excelData.getDeviceMatter();
	        						Integer overhaulCycle = excelData.getOverhaulCycle();
	        						String ret = this.checkKeyDeviceData(excelData);
	        						if(StringUtils.isNotEmpty(ret)) {
	        							result = ret;
	        							break;
	        						}
	        						if(StringUtils.isNotEmpty(deviceName)) {
	        							//判断位号重复
	        							if(confMap.containsKey("deviceTagNo")&&StringUtils.isNotEmpty(deviceTagNo)) {
											if(hasTagNoMap.containsKey(deviceTagNo)) {
												String val = hasTagNoMap.get(deviceTagNo);
												if(!deviceName.equals(val)) {
													result = "设备位号【"+deviceTagNo+"】已存在，不能重复设置";
													break;
												}
											}else {
												hasTagNoMap.put(deviceTagNo, deviceName);
											}
	    								}
	        							if(!hasDeviceNameList.contains(deviceName)) {
	        								hasDeviceNameList.add(deviceName);
	        							}
	        							if(StringUtils.isNotEmpty(queryMap)&&queryMap.containsKey(deviceName)) { //修改
	        								CostKeyDeviceConf obj = queryMap.get(deviceName);
	        								if(obj!=null) {
	        									if(confMap.containsKey("deviceStatus")) {
	        										obj.setDeviceStatus(deviceStatus);
		        								}
	        									if(confMap.containsKey("deviceTagNo")) {
	        										obj.setDeviceTagNo(deviceTagNo);
		        								}
	        									if(confMap.containsKey("deviceMatter")) {
	        										obj.setDeviceMatter(deviceMatter);
		        								}
	        									if(confMap.containsKey("overhaulCycle")) {
	        										obj.setOverhaulCycle(overhaulCycle);
		        								}
	        									updList.add(obj);
	        								}
	        							}else { //新增
	        								maxPx += 1;
	        								CostKeyDeviceConf obj = new CostKeyDeviceConf();
	        								obj.setId(TMUID.getUID());
	        								obj.setUnitid(unitid);
	        								obj.setBegintime(begintime);
	        								obj.setPid("root");
	        								obj.setDeviceName(deviceName);
	        								if(confMap.containsKey("deviceStatus")) {
        										obj.setDeviceStatus(deviceStatus);
	        								}
        									if(confMap.containsKey("deviceTagNo")) {
        										obj.setDeviceTagNo(deviceTagNo);
	        								}
        									if(confMap.containsKey("deviceMatter")) {
        										obj.setDeviceMatter(deviceMatter);
	        								}
        									if(confMap.containsKey("overhaulCycle")) {
        										obj.setOverhaulCycle(overhaulCycle);
	        								}
	        								obj.setIsUseToRecordEvent(0); //是否用于记事：1、是；其他不是（默认）
	        								obj.setTmUsed(1);
	        								obj.setTmSort(maxPx);
	        							    addList.add(obj);
	        							}
	        						}
	        					}
	        				}
						}
	        			//校验其他数据设备位号是否重复
	        			if(StringUtils.isEmpty(result)) {
	        				result = this.checkOtherSameTagNo(queryList, hasTagNoMap, hasDeviceNameList);
	        			}
	        		}
		        }
			}catch(Exception e) {
				e.printStackTrace();
			}
		}
		if(StringUtils.isEmpty(result)&&(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(updList))) {
			result = keyDeviceService.saveDataCostKeyDevice(addList, updList, null);
			if(StringUtils.isEmpty(result)) {
				dataCount = addList.size()+updList.size();
			}
		}
		if(StringUtils.isEmpty(result)) {
			result = "SUCCESS_"+dataCount;
		}else {
			result = "ERROR_"+result;
		}
		return result;
	}
	
	//校验关键设备数据长度
	private String checkKeyDeviceData(CostKeyDeviceConf dataObj) {
		String result = "";
		if(StringUtils.isNotNull(dataObj)) {
			String deviceName = dataObj.getDeviceName();
			String deviceTagNo = dataObj.getDeviceTagNo();
			String deviceMatter = dataObj.getDeviceMatter();
			String deviceStatus = dataObj.getDeviceStatus();
			if(StringUtils.isNotEmpty(deviceName)&&deviceName.length()>100) {
				result = "设备名称【"+deviceName.substring(0,30)+" 。。。】过长（超过100个字符），请重新设置";
			}else if(StringUtils.isNotEmpty(deviceTagNo)&&deviceTagNo.length()>200) {
				result = "设备位号【"+deviceTagNo.substring(0,30)+" 。。。】过长（超过200个字符），请重新设置";
			}else if(StringUtils.isNotEmpty(deviceMatter)&&deviceMatter.length()>50) {
				result = "输送介质【"+deviceMatter.substring(0,30)+" 。。。】过长（超过50个字符），请重新设置";
			}else if(StringUtils.isNotEmpty(deviceStatus)&&deviceStatus.length()>1000) {
				result = "设备状态【"+deviceStatus.substring(0,30)+" 。。。】过长（超过1000个字符），请重新设置";
			}
		}
		return result;
	}
	
	//校验其他同名设备位号是否重复
	private String checkOtherSameTagNo(List<CostKeyDeviceConf> list, HashMap<String, String> hasMap, List<String> hasDeviceNameList) {
		String result = "";
		if(StringUtils.isNotEmpty(list)&&StringUtils.isNotEmpty(hasMap)&&StringUtils.isNotEmpty(hasDeviceNameList)) {
			for (int i = 0; i < list.size(); i++) {
				CostKeyDeviceConf obj = list.get(i);
				String deviceName = obj.getDeviceName();
				if(!hasDeviceNameList.contains(deviceName)) { //其他数据
					String deviceTagNo = obj.getDeviceTagNo();
					if(StringUtils.isNotEmpty(deviceTagNo)&&hasMap.containsKey(deviceTagNo)) {
						result = "设备位号【"+deviceTagNo+"】已存在，不能重复设置";
						break;
					}
				}
			}
		}
		return result;
	}
	
	//——————————————————————————  导入 ↑  —————————————————————————————————
	
	
}
