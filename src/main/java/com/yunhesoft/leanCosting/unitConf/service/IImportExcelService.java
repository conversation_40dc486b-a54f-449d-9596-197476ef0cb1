package com.yunhesoft.leanCosting.unitConf.service;


import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostImportExcelConf;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostImportExcelConfVo;

/**
 * 核算导入Excel服务接口
 * <AUTHOR>
 * @date 2024-01-26
 */
public interface IImportExcelService {

	/**
	 *	获取导入Excel配置数据（没有数据时，初始化）
	 * @param queryDto
	 * @return
	 */
	public List<CostImportExcelConfVo> getCostImportExcelConfListByInit(MethodQueryDto queryDto);
	
	/**
	 *	获取导入Excel配置数据
	 * @param queryDto
	 * @return
	 */
	public List<CostImportExcelConf> getCostImportExcelConfList(MethodQueryDto queryDto);
	
	/**
	 *	保存导入Excel配置数据
	 * @param saveDto
	 * @return
	 */
	public String saveCostImportExcelConfData(MethodSaveDto saveDto);
	
	/**
	 *	保存数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveData(List<CostImportExcelConf> addList,List<CostImportExcelConf> updList,List<CostImportExcelConf> delList);
	
	/**
	 *	导出Excel
	 * @param queryDto
	 * @param request
	 * @param response
	 */
	public void exportExcel(MethodQueryDto queryDto, HttpServletRequest request, HttpServletResponse response);
	
	/**
	 *	导入Excel
	 * @param file
	 * @param queryDto
	 * @return
	 */
	public String importExcel(MultipartFile file, MethodQueryDto queryDto);
	
	/**
	 *	同步计量单位数据
	 * @param meterUnitList
	 * @return
	 */
	public String asyncMeterUnitData(List<String> meterUnitList);
	
}
