package com.yunhesoft.leanCosting.unitConf.service;


import com.yunhesoft.leanCosting.unitConf.entity.dto.BriefAnalysisQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.BriefAnalysisSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.BriefAnalysisConf;
import com.yunhesoft.leanCosting.unitConf.entity.po.BriefAnalysisMeasureConf;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BriefAnalysisConfVo;

import java.util.List;


/**
 * 简要分析设置相关服务接口
 * <AUTHOR>
 * @date 2023-10-30
 */
public interface IBriefAnalysisService {
	
	/**
	 * 获取简要分析设置数据（vo带附加属性）
	 * @param queryDto
	 * @return
	 */
	public List<BriefAnalysisConfVo> getBriefAnalysisConfVoList(BriefAnalysisQueryDto queryDto);
	
	/**
	 * 获取简要分析设置数据（单表）
	 * @param queryDto
	 * @return
	 */
	public List<BriefAnalysisConf> getBriefAnalysisConfList(BriefAnalysisQueryDto queryDto);

	/**
	 * 保存简要分析设置数据
	 * @param saveDto
	 * @return
	 */
	public String saveBriefAnalysisConfData(BriefAnalysisSaveDto saveDto);


	/**
	 * 查询措施
	 * @param analysisId	简要分析id
	 * @return
	 */
	List<BriefAnalysisMeasureConf> queryMeasureList (String analysisId);

	/**
	 * 保存措施
	 * @return
	 */
	List<BriefAnalysisMeasureConf> saveMeasureList (List<BriefAnalysisMeasureConf> list);

	/**
	 * 批量删除
	 * @param idList
	 * @return
	 */
	String deleteMeasureByIdList (List<String> idList);

	/**
	 *	获取简要分析原因措施数据
	 * @param queryDto
	 * @return
	 */
	public List<BriefAnalysisMeasureConf> getBriefAnalysisMeasureConfList(BriefAnalysisQueryDto queryDto);
	
	/**
	 *	同步更新简要分析设置的物资名称（项目、仪表、指标修改名称后调用）
	 * @param itemList
	 * @param instrumentList
	 * @param indicatorList
	 * @return
	 */
	public String updateBriefAnalysisName(List<Costitem> itemList, List<Costinstrument> instrumentList, List<Costindicator> indicatorList);
	
}
