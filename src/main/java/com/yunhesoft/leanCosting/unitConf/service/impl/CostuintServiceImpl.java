package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.service.IMaintenanceStandardsService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostBindOrgDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitmanager;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;

/**
 * 核算对象查询
 * 
 * <AUTHOR>
 *
 */
@Service
public class CostuintServiceImpl implements ICostuintService {

	@Autowired
	private EntityService entityService;
	@Autowired
	private ICostService costService;
	@Autowired
	private IMaintenanceStandardsService maintenanceStandardsService;
	
	@Override
	public List<Costuint> getDatas(CostuintQueryDto dto) {
		Where where = Where.create();
		String tenantId = null;
		if (dto != null) {
			tenantId = dto.getTenantId();
			String id = dto.getId();
			if (StringUtils.isNotEmpty(id)) {
				// ID
				where.eq(Costuint::getId, id);
			}
			List<String> ids = dto.getIds();
			if (StringUtils.isNotEmpty(ids)) {
				// ID
				where.in(Costuint::getId, ids.toArray());
			}
			List<String> orgIds = dto.getOrgIds();
			if (StringUtils.isNotEmpty(orgIds)) {
				// 维护机构
				where.in(Costuint::getOrgId, orgIds.toArray());
			}
			String pid = dto.getPid();
			if (StringUtils.isNotEmpty(pid)) {
				where.eq(Costuint::getPid, pid);
			}
			
			//生产活动 
			Integer productive = dto.getProductive();
			if (productive!=null) {
				where.eq(Costuint::getProductive, productive);
			}
			
			//活动类型
			Integer mobileInput = dto.getMobileInput();
			if (mobileInput!=null) {
				where.eq(Costuint::getMobileInput, mobileInput);
			}
			
			//活动类型
			Integer productiveType = dto.getProductiveType();
			if (productiveType!=null) {
				where.eq(Costuint::getProductiveType, productiveType);
			}
			
			
			//调度台体现
			Integer dispatchDesk = dto.getDispatchDesk();
			if (dispatchDesk!=null) {
				where.eq(Costuint::getDispatchDesk, dispatchDesk);
			}
			Integer useAccounting = dto.getUseAccounting();
			if (dispatchDesk!=null) {
				where.eq(Costuint::getUseAccounting, useAccounting);
			}
			Integer isOntime = dto.getIsOntime();
			if (dispatchDesk!=null) {
				where.eq(Costuint::getIsOntime, isOntime);
			}
			//设备ID
			List<String> deviceIds = dto.getDeviceIds();
			if (StringUtils.isNotEmpty(deviceIds)) {
				where.in(Costuint::getId, deviceIds.toArray());
			}
			where.eq(Costuint::getTmused, 1);
		}
		Order order = Order.create();
		order.orderByAsc(Costuint::getTmSort);
		List<Costuint> configs = new ArrayList<Costuint>();
		if(tenantId!=null) {
			configs = entityService.rawQueryListByWhereWithTenant(tenantId,Costuint.class, where, order);
		}else {
			configs = entityService.queryList(Costuint.class, where, order);
		}
		return configs;
	}
	
	@Override
	public List<Costitem> getCostitemDatas(CostuintQueryDto dto) {
		Where where = Where.create();
		if (dto != null) {
			String pid = dto.getPid();
			if (StringUtils.isNotEmpty(pid)) {
				where.eq(Costitem::getPid, pid);
			}
			where.eq(Costitem::getTmused, 1);
		}
		Order order = Order.create();
		order.orderByDesc(Costitem::getTmsort);
		return entityService.queryList(Costitem.class, where, order);
	}
	
	@Override
	public List<Costinstrument> getCostinstrumentDatas(CostuintQueryDto dto) {
		Where where = Where.create();
		if (dto != null) {
			String pid = dto.getPid();
			if (StringUtils.isNotEmpty(pid)) {
				where.eq(Costinstrument::getPid, pid);
			}
			where.eq(Costinstrument::getTmused, 1);
		}
		Order order = Order.create();
		order.orderByDesc(Costinstrument::getTmsort);
		return entityService.queryList(Costinstrument.class, where, order);
	}


	/**
	 * 获得其他租户<核算对象ID,List<操作机构>>
	 * @return
	 */
	@Override
	public Map<String, List<String>> getTenantUintMap(String tenantId){
		CostuintQueryDto dto = new CostuintQueryDto();
		dto.setTenantId(tenantId);
		List<Costuint> dataList = getDatas(dto);
		return getLinkedHashMap(dataList);
	}
	
	/**
	 * 获得租户内<核算对象ID,List<操作机构>>
	 * @return
	 */
	@Override
	public Map<String, List<String>> getUintMap(){
		List<Costuint> dataList = getDatas(null);
		return getLinkedHashMap(dataList);
	}
	
	private Map<String, List<String>> getLinkedHashMap(List<Costuint> dataList){
		Map<String, Costuint> dataMap = dataList.stream().collect(Collectors.toMap(Costuint::getId, Function.identity()));
		CostBindOrgDto dto_ = new CostBindOrgDto();
		dto_.setObjType("org");//查询机构
		Map<String, List<String>> map = new LinkedHashMap<String,List<String>>();
//		List<Costunitoperator> costunitoperators = costService.getCostunitoperatorList(dto_);
		List<Costunitoperator> costunitoperators = costService.getCostunitoperatorOrgList(dto_);
		for (Costunitoperator costunitoperator : costunitoperators) {
			String orgCode = costunitoperator.getObjid();
			String unitId = costunitoperator.getUnitid();
			if(dataMap.containsKey(unitId)) {//过滤used=0的数据
				List<String> orgCodes = new ArrayList<String>();
				if(map.containsKey(unitId)) {
					orgCodes = map.get(unitId);
				}
				orgCodes.add(orgCode);
				map.put(unitId, orgCodes);
			}
		}
		return map;
	}
	
	@Override
	public Map<String, Object> getUinToperator(String uintId) {
		Map<String, Object> map = new LinkedHashMap<String,Object>();
		CostBindOrgDto dto_ = new CostBindOrgDto();
		dto_.setUnitid(uintId);
		List<Costunitoperator> list = costService.getCostunitoperatorList(dto_);
		List<SysOrg> sysOrgs = new ArrayList<SysOrg>();
		List<SysEmployeeInfo> employeeInfos = new ArrayList<SysEmployeeInfo>();
		List<SysEmployeeOrgPost> sysPosts = new ArrayList<SysEmployeeOrgPost>();
		for (Costunitoperator bean : list) {
			String objid = bean.getObjid();
			String objType = bean.getObjType();
			if (objType==null) {
				objType="org";
			}
			if("org".equals(objType)) {
				SysOrg org = entityService.queryObjectById(SysOrg.class, objid);
				if(org!=null) sysOrgs.add(org);
			}else if("user".equals(objType)) {
				SysEmployeeInfo info = entityService.queryObjectById(SysEmployeeInfo.class, objid);
				if(employeeInfos!=null) employeeInfos.add(info);
			}else if("post".equals(objType)) {
 				String[] objs = objid.split("_");
  				String orgcode =  objs[0];
  				String postid =  objs[1];
  				Where where = Where.create();
				if (StringUtils.isNotEmpty(orgcode)) {
					where.eq(SysEmployeeOrgPost::getOrgcode, orgcode);
				}
				if (StringUtils.isNotEmpty(postid)) {
					where.eq(SysEmployeeOrgPost::getPostid, postid);
				}
				where.eq(SysEmployeeOrgPost::getUsed, 1);
				List<SysEmployeeOrgPost> posts = entityService.queryList(SysEmployeeOrgPost.class, where, null);
				if(posts.size()>0) {
					if(sysPosts!=null) sysPosts.addAll(posts);
				}
			}
		}
		
		map.put("sysOrg", sysOrgs);
		map.put("sysEmp", employeeInfos);
		map.put("sysPost", sysPosts);
		return map;
	}

	
	@Override
	public List<SysOrg> getUintManager(String uintId) {
		List<SysOrg> sysOrgs = new ArrayList<SysOrg>();
		CostBindOrgDto dto_ = new CostBindOrgDto();
		dto_.setUnitid(uintId);
		List<Costunitmanager> list =  costService.getCostunitmanagerList(dto_);
		for (Costunitmanager bean : list) {
			String objid = bean.getObjid();
			SysOrg org = entityService.queryObjectById(SysOrg.class, objid);
			if(org!=null) sysOrgs.add(org);
		}
		return sysOrgs;
	}

	@Override
	public Costuint getUnitId(String uintId) {
		return entityService.queryObjectById(Costuint.class, uintId);
	}
	
	/**
	 * 得到核算对象的涉及设备。
	 * @param uintId
	 * @return
	 */
	@Override
	public List<Costuint> getDeviceIds(String uintId){
//		List<MaintenanceStandardsComboVo> comboVos = new ArrayList<MaintenanceStandardsComboVo>();
		List<Costuint> costuints = new ArrayList<Costuint>();
		Costuint costuint = costService.getCostuintObjById(uintId);
		if (costuint!=null) {
			String deviceIds = costuint.getDeviceIds();
			if(deviceIds==null) {
				deviceIds = "";
			}
			if ("".equals(deviceIds)) {
				//没有涉及设备时，添加自身
				costuints.add(costuint);
			} else {
				String[] deviceIds_ = deviceIds.split(",");
				List<String> _deviceIds =new ArrayList<String>();
				for (String deviceId : deviceIds_) {//涉及设备ID（核算对象ID）
					_deviceIds.add(deviceId);
				}
				CostuintQueryDto dto_ = new CostuintQueryDto();
				dto_.setDeviceIds(_deviceIds);
				costuints = getDatas(dto_);
				if(costuints==null||costuints.size()==0) {//查询不到记录时，返回当前核算对象
					costuints.add(costuint);
				}else {
					Integer hdlx=costuint.getProductiveType();
					if (hdlx==null) {
						hdlx=0;
					}
					String id,lxid;
					if(hdlx==1) {//设备维保
						List<Costuint> newList = new ArrayList<Costuint>();
						for (Costuint costuint_ : costuints) {//TODO:ID和名称,需要去重，重组
							List<Costuint> comboVos = maintenanceStandardsService.getMaintenanceType(costuint_);
							newList.addAll(comboVos);
						}
						return newList;
					} else {
						//其余活动，要在核算对象上添加活动类型
						int count=costuints.size();
						for (int i=0;count>i;i++) {
							Costuint cu=costuints.get(i);
							id=cu.getId();
							lxid=cu.getUnittype();
							cu.setId(id+"_"+lxid+"_"+String.valueOf(hdlx));
						}
					}
				}
			}
		}
		return costuints;
	}

}
