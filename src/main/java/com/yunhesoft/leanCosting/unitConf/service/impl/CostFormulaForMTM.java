package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aliyuncs.utils.StringUtils;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.service.ICostEnvironmentConfigServer;
import com.yunhesoft.leanCosting.calcLogic.ICalcUnitCostIndicator;
import com.yunhesoft.leanCosting.calcLogic.TeamLimsService;
import com.yunhesoft.leanCosting.calcLogic.TeamLimsTotalVo;
import com.yunhesoft.leanCosting.steadyRate.entity.vo.TeamSteadyRateTotalVo;
import com.yunhesoft.leanCosting.steadyRate.service.ISteadyRateServise;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaTreeVo;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaValueVo;
import com.yunhesoft.system.tools.classExec.utils.MtmFormulaModel;

import lombok.extern.log4j.Log4j2;

//必须带@Service，否则工厂无法实例化
@Service
@Log4j2
public class CostFormulaForMTM extends MtmFormulaModel {

	@Autowired
	ICostEnvironmentConfigServer icecs;

	@Autowired
	ICostService ics;

	@Autowired
	ICostuintService icus;

	@Autowired
	UnitItemInfoService uiis;

	@Autowired
	ICalcUnitCostIndicator icuci;

	@Autowired
	TeamLimsService TLS;

	@Autowired
	ISteadyRateServise ISRS;

	@Autowired
	IUnitMethodService IUMS;

	@Override
	public String getModuleCode() {
		return "UnitCost";// 本模块的编码
	}

	@Override
	public String getModuleName() {
		String mname = this.icecs.getConfig();
		if (StringUtils.isEmpty(mname)) {
			mname = "核算对象";
		}
		return mname;// 本模块的名称
	}

	/**
	 * @category 成本项目
	 * @param pvo
	 * @param unitid
	 * @param unitname
	 * @param pflid
	 * @param xjxm
	 * @param xjfl
	 */
	private void getXjFlxm(MtmFormulaTreeVo pvo, String unitid, String unitname, String pflid,
			HashMap<String, List<Costitem>> xjxm, HashMap<String, List<Costclass>> xjfl) {
		String flid, xmid, xmmc, zbname;
		if (xjfl != null && xjfl.containsKey(pflid)) {
			// 有子分类
			List<Costclass> xjfll = xjfl.get(pflid);
			if (xjfll != null) {
				int count = xjfll.size();
				for (int i = 0; count > i; i++) {
					Costclass xj = xjfll.get(i);
					MtmFormulaTreeVo fl1 = new MtmFormulaTreeVo();
					fl1.setFormulaName(xj.getCcname());
					fl1.setIsLeaf(0);// 非公式节点
					pvo.getChildren().add(fl1);// 添加到分类下
					flid = xj.getId();
					this.getXjFlxm(fl1, unitid, unitname, flid, xjxm, xjfl);
				}
			}
		}
		if (xjxm != null && xjxm.containsKey(pflid)) {
			// 分类下有项目
			List<Costitem> xjxml = xjxm.get(pflid);
			if (xjxml != null) {
				int count = xjxml.size();
				for (int i = 0; count > i; i++) {
					Costitem xm = xjxml.get(i);
					xmid = xm.getId();
					xmmc = xm.getItemname();
					MtmFormulaTreeVo fl2 = new MtmFormulaTreeVo();
					fl2.setFormulaName(xmmc);
					fl2.setIsLeaf(0);// 非公式节点
					pvo.getChildren().add(fl2);// 添加到分类下
					zbname = "消耗量";
					StringBuffer sb1 = new StringBuffer(unitid).append(".").append(xmid);
					StringBuffer sb2 = new StringBuffer(unitname).append(".").append(xmmc);
					MtmFormulaTreeVo zb1 = new MtmFormulaTreeVo();
					zb1.setFormulaName(zbname);
					zb1.setFormulaCode((new StringBuffer(sb1).append(".xhl")).toString());
					zb1.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
					zb1.setIsLeaf(1);// 公式节点
					fl2.getChildren().add(zb1);// 添加到项目下
					zbname = "单耗";
					MtmFormulaTreeVo zb2 = new MtmFormulaTreeVo();
					zb2.setFormulaName(zbname);
					zb2.setFormulaCode((new StringBuffer(sb1).append(".dh")).toString());
					zb2.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
					zb2.setIsLeaf(1);// 公式节点
					fl2.getChildren().add(zb2);// 添加到项目下
					zbname = "总成本";
					MtmFormulaTreeVo zb3 = new MtmFormulaTreeVo();
					zb3.setFormulaName(zbname);
					zb3.setFormulaCode((new StringBuffer(sb1).append(".zcb")).toString());
					zb3.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
					zb3.setIsLeaf(1);// 公式节点
					fl2.getChildren().add(zb3);// 添加到项目下
					zbname = "单位成本";
					MtmFormulaTreeVo zb4 = new MtmFormulaTreeVo();
					zb4.setFormulaName(zbname);
					zb4.setFormulaCode((new StringBuffer(sb1).append(".dwcb")).toString());
					zb4.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
					zb4.setIsLeaf(1);// 公式节点
					fl2.getChildren().add(zb4);// 添加到项目下
				}
			}
		}
	}

	/**
	 * @category 采集点
	 * @param pvo
	 * @param unitid
	 * @param unitname
	 * @param pflid
	 * @param xjxm
	 * @param xjfl
	 */
	private void getXjFldot(MtmFormulaTreeVo pvo, String unitid, String unitname, String pflid,
			HashMap<String, List<Costunitsampledot>> xjxm, HashMap<String, List<Costunitsampleclass>> xjfl) {
		String flid, xmid, xmmc, zbname;
		if (xjfl != null && xjfl.containsKey(pflid)) {
			// 有子分类
			List<Costunitsampleclass> xjfll = xjfl.get(pflid);
			if (xjfll != null) {
				int count = xjfll.size();
				for (int i = 0; count > i; i++) {
					Costunitsampleclass xj = xjfll.get(i);
					MtmFormulaTreeVo fl1 = new MtmFormulaTreeVo();
					fl1.setFormulaName(xj.getName());
					fl1.setIsLeaf(0);// 非公式节点
					pvo.getChildren().add(fl1);// 添加到分类下
					flid = xj.getId();
					this.getXjFldot(fl1, unitid, unitname, flid, xjxm, xjfl);
				}
			}
		}
		if (xjxm != null && xjxm.containsKey(pflid)) {
			// 分类下有项目
			List<Costunitsampledot> xjxml = xjxm.get(pflid);
			if (xjxml != null) {
				int count = xjxml.size();
				for (int i = 0; count > i; i++) {
					Costunitsampledot xm = xjxml.get(i);
					xmid = xm.getId();
					xmmc = xm.getName();
					MtmFormulaTreeVo fl2 = new MtmFormulaTreeVo();
					fl2.setFormulaName(xmmc);
					fl2.setIsLeaf(0);// 非公式节点
					pvo.getChildren().add(fl2);// 添加到分类下
					StringBuffer sb1 = new StringBuffer(unitid).append(".").append(xmid);
					StringBuffer sb2 = new StringBuffer(unitname).append(".").append(xmmc);
					zbname = "采样均值";
					MtmFormulaTreeVo zb1 = new MtmFormulaTreeVo();
					zb1.setFormulaName(zbname);
					zb1.setFormulaCode((new StringBuffer(sb1).append(".cjz")).toString());
					zb1.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
					zb1.setIsLeaf(1);// 公式节点
					fl2.getChildren().add(zb1);// 添加到项目下
					zbname = "超限点";
					MtmFormulaTreeVo zb2 = new MtmFormulaTreeVo();
					zb2.setFormulaName(zbname);
					zb2.setFormulaCode((new StringBuffer(sb1).append(".cxd")).toString());
					zb2.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
					zb2.setIsLeaf(1);// 公式节点
					fl2.getChildren().add(zb2);// 添加到项目下
					zbname = "总点";
					MtmFormulaTreeVo zb3 = new MtmFormulaTreeVo();
					zb3.setFormulaName(zbname);
					zb3.setFormulaCode((new StringBuffer(sb1).append(".zds")).toString());
					zb3.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
					zb3.setIsLeaf(1);// 公式节点
					fl2.getChildren().add(zb3);// 添加到项目下
				}
			}
		}
	}

	/**
	 * 获取公式tree
	 * 
	 * @category 获取公式tree
	 * <AUTHOR>
	 * @param pId        父ID 加载根节点时会传入机构代码(为了扩展性，这里可能传入多个机构代码，用逗号分隔)
	 * @param isRootLoad 是否在加载根节点
	 * @return
	 */
	@Override
	public List<MtmFormulaTreeVo> getFormulaTree(String pId, boolean isRootLoad) {
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();
		if (isRootLoad) {
			// 添加核算对象
			String unitid, unitname;
			// List<String> formulaTextList=new ArrayList<String>();
			// formulaTextList.add("ZQF7SXAB507E41WFP11291.TotalSteadyRate.tjz");
			// formulaTextList.add("ZQF7SXAB507E41WFP11291.TotalSteadyRate.cxd");
			// formulaTextList.add("ZQF7SXAB507E41WFP11291.TotalSteadyRate.zds");
			// getFormulaValue("2024-05-01 00:00:00", "2024-05-31 23:00:00",
			// formulaTextList,null);
			if (StringUtils.isEmpty(pId)) {
				// 未指定机构,根据登录人员，得到他可看到的核算对象
				SysUser user = SysUserHolder.getCurrentUser();
				String orgId = user.getOrgId();
				String userId = user.getId();// 人员ID
				String postId = user.getPostId();
				postId = orgId + "_" + postId;// 机构ID
				List<Costuint> ul = this.ics.getCostuintListByOrgId(orgId, userId, postId, 2);
				if (ul != null) {
					int count = ul.size();
					for (int i = 0; count > i; i++) {
						Costuint x = ul.get(i);
						unitid = x.getId();
						unitname = x.getName();
						MtmFormulaTreeVo fl1 = new MtmFormulaTreeVo();
						fl1.setFormulaName(unitname);
						fl1.setIsLeaf(0);// 非公式节点
						fl1.setTreeNodeId(unitid);
						result.add(fl1);// 添加到根节点
					}
				}
			} else {
				// 给了机构
				HashMap<String, String> um = new HashMap<String, String>();
				String[] zdl = pId.split(",");
				for (String d : zdl) {
					List<Costuint> ul = this.ics.getCostuintListByUser(d, 2);
					if (ul != null) {
						int count = ul.size();
						for (int i = 0; count > i; i++) {
							Costuint x = ul.get(i);
							unitid = x.getId();
							if (um.containsKey(unitid)) {
								continue;
							} else {
								um.put(unitid, "1");
							}
							unitname = x.getName();
							MtmFormulaTreeVo fl1 = new MtmFormulaTreeVo();
							fl1.setFormulaName(unitname);
							fl1.setIsLeaf(0);// 非公式节点
							fl1.setTreeNodeId(unitid);
							result.add(fl1);// 添加到根节点
						}
					}
				}
			}

		} else {
			String[] dd = pId.split("\\.");
			if (dd.length == 1) {
				// 加载核算对象下的节点
				result.addAll(this.addPointByUnit(pId));
			} else if (dd.length == 2) {
				// 加载分类下节点
				result.addAll(this.addClassByPoint(dd[0], dd[1]));
			} else if (dd.length == 3) {
				// 加载分类和项目下节点
				result.addAll(this.addPointByPoint(dd[0], dd[1], dd[2]));
			}
		}
		return result;
	}

	private List<MtmFormulaTreeVo> addPointByUnit(String unitid) {
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();
		MtmFormulaTreeVo p1 = new MtmFormulaTreeVo();
		p1.setFormulaName("核算项目");
		p1.setIsLeaf(0);// 非公式节点
		p1.setTreeNodeId(unitid + ".costitem");
		result.add(p1);
		MtmFormulaTreeVo p2 = new MtmFormulaTreeVo();
		p2.setFormulaName("采集点");
		p2.setIsLeaf(0);// 非公式节点
		p2.setTreeNodeId(unitid + ".sampledot");
		result.add(p2);
		return result;
	}

	private List<MtmFormulaTreeVo> addClassByPoint(String unitid, String point) {
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();
		String pflid;
		String dqrq = DateTimeUtils.getNowDateStr();// 当前日期
		if ("costitem".equals(point)) {
			// 成本项目
			List<Costclass> flls = this.uiis.getCostClass(unitid, dqrq);
			if (flls != null) {
				int count = flls.size();
				for (int i = 0; count > i; i++) {
					Costclass fl = flls.get(i);
					pflid = fl.getPid();
					if (pflid == null) {
						continue;
					}
					if ("root".equalsIgnoreCase(pflid)) {
						// 根分类
						MtmFormulaTreeVo p = new MtmFormulaTreeVo();
						p.setFormulaName(fl.getCcname());
						p.setIsLeaf(0);// 非公式节点
						p.setTreeNodeId((new StringBuffer(unitid).append(".costClass.").append(fl.getId())).toString());
						result.add(p);
					}
				}
			}
			// 核算指标
			List<Costindicator> zbl = this.uiis.getCostIndicator(unitid, dqrq);
			if (zbl != null && zbl.size() > 0) {
				MtmFormulaTreeVo p = new MtmFormulaTreeVo();
				p.setFormulaName("核算指标");
				p.setIsLeaf(0);// 非公式节点
				p.setTreeNodeId((new StringBuffer(unitid).append(".costClass.costindicator")).toString());
				result.add(p);
			}
		} else if ("sampledot".equals(point)) {
			// 采集点
			HashMap<String, String> dlxm = new HashMap<String, String>();
			List<Costunitsampledot> sdl = this.uiis.getSampleDot(unitid, dqrq, null);
			if (sdl != null) {
				int count = sdl.size();
				String ctype;
				String un = this.getUnitName(unitid);
				for (int i = 0; count > i; i++) {
					Costunitsampledot xm = sdl.get(i);
					pflid = xm.getPid();
					if (pflid == null) {
						continue;
					}
					ctype = xm.getCtype();
					if (ctype == null) {
						ctype = "0";
					}
					if ("2".equals(ctype)) {
						// 控制指标
						dlxm.put("pwl", "1");
					}
					if ("3".equals(ctype)) {
						dlxm.put("hgl", "1");
					}
				}
				if (dlxm.containsKey("pwl")) {
					MtmFormulaTreeVo zb2 = new MtmFormulaTreeVo();
					String zbname = "总平稳率";
					zb2.setFormulaName(zbname);
					zb2.setFormulaCode((new StringBuffer(unitid).append(".TotalSteadyRate.tjz")).toString());
					zb2.setFormulaDesc((new StringBuffer(un).append(".").append(zbname)).toString());
					zb2.setIsLeaf(1);// 公式节点
					result.add(zb2);
					MtmFormulaTreeVo zb21 = new MtmFormulaTreeVo();
					zbname = "平稳率超限点";
					zb21.setFormulaName(zbname);
					zb21.setFormulaCode((new StringBuffer(unitid).append(".TotalSteadyRate.cxd")).toString());
					zb21.setFormulaDesc((new StringBuffer(un).append(".").append(zbname)).toString());
					zb21.setIsLeaf(1);// 公式节点
					result.add(zb21);
					MtmFormulaTreeVo zb22 = new MtmFormulaTreeVo();
					zbname = "平稳率总点";
					zb22.setFormulaName(zbname);
					zb22.setFormulaCode((new StringBuffer(unitid).append(".TotalSteadyRate.zds")).toString());
					zb22.setFormulaDesc((new StringBuffer(un).append(".").append(zbname)).toString());
					zb22.setIsLeaf(1);// 公式节点
					result.add(zb22);
				}
				if (dlxm.containsKey("hgl")) {
					MtmFormulaTreeVo zb3 = new MtmFormulaTreeVo();
					String zbname = "总合格率";
					zb3.setFormulaName(zbname);
					zb3.setFormulaCode(
							(new StringBuffer(unitid).append(".TotalStableQualificationRate.tjz")).toString());
					zb3.setFormulaDesc((new StringBuffer(un).append(".").append(zbname)).toString());
					zb3.setIsLeaf(1);// 公式节点
					result.add(zb3);
					MtmFormulaTreeVo zb31 = new MtmFormulaTreeVo();
					zbname = "合格率超限点";
					zb31.setFormulaName(zbname);
					zb31.setFormulaCode(
							(new StringBuffer(unitid).append(".TotalStableQualificationRate.cxd")).toString());
					zb31.setFormulaDesc((new StringBuffer(un).append(".").append(zbname)).toString());
					zb31.setIsLeaf(1);// 公式节点
					result.add(zb31);
					MtmFormulaTreeVo zb32 = new MtmFormulaTreeVo();
					zbname = "合格率总点";
					zb32.setFormulaName(zbname);
					zb32.setFormulaCode(
							(new StringBuffer(unitid).append(".TotalStableQualificationRate.zds")).toString());
					zb32.setFormulaDesc((new StringBuffer(un).append(".").append(zbname)).toString());
					zb32.setIsLeaf(1);// 公式节点
					result.add(zb32);
				}
				String bd = this.IUMS.getMaxVersionByCostunitsampleclass(unitid, dqrq);
				MethodQueryDto queryDto = new MethodQueryDto();
				queryDto.setUnitid(unitid);
				queryDto.setBegintime(bd);
				List<Costunitsampleclass> scl = this.IUMS.getCostunitsampleclassList(queryDto);
				if (scl != null) {
					int dcount = scl.size();
					for (int i = 0; dcount > i; i++) {
						Costunitsampleclass fl = scl.get(i);
						pflid = fl.getPid();
						if (pflid == null) {
							continue;
						}
						unitid = fl.getUnitid();
						if (unitid == null) {
							continue;
						}
						if ("root".equalsIgnoreCase(pflid)) {
							// 根分类
							MtmFormulaTreeVo p = new MtmFormulaTreeVo();
							p.setFormulaName(fl.getName());
							p.setIsLeaf(0);// 非公式节点
							p.setTreeNodeId(
									(new StringBuffer(unitid).append(".dotClass.").append(fl.getId())).toString());
							result.add(p);
						}
					}
				}
			}
		}
		return result;
	}

	private List<MtmFormulaTreeVo> addPointByPoint(String unitid, String type, String pointId) {
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();
		String pflid, zbname;
		String dqrq = DateTimeUtils.getNowDateStr();// 当前日期
		if ("costClass".equals(type)) {
			// 成本项目或核算指标
			if ("costindicator".equals(pointId)) {
				// 核算指标
				List<Costindicator> zbl = this.uiis.getCostIndicator(unitid, dqrq);
				if (zbl != null) {
					String un = this.getUnitName(unitid);
					int count = zbl.size();
					for (int i = 0; count > i; i++) {
						Costindicator ci = zbl.get(i);
						MtmFormulaTreeVo p = new MtmFormulaTreeVo();
						zbname = ci.getCpname();
						p.setFormulaName(zbname);
						p.setFormulaCode(
								(new StringBuffer(unitid).append(".").append(ci.getId()).append(".tjz")).toString());
						p.setFormulaDesc((new StringBuffer(un).append(".").append(zbname).append(".统计结果")).toString());
						p.setIsLeaf(1);// 公式节点
						// p.setTreeNodeId("isLeaf");
						result.add(p);
					}
				}
			} else {
				// 核算分类或项目
				// 下级分类
				List<Costclass> flls = this.uiis.getCostClass(unitid, dqrq);
				if (flls != null) {
					int count = flls.size();
					for (int i = 0; count > i; i++) {
						Costclass fl = flls.get(i);
						pflid = fl.getPid();
						if (pflid == null) {
							continue;
						}
						if (pointId.equalsIgnoreCase(pflid)) {
							// 根分类
							MtmFormulaTreeVo p = new MtmFormulaTreeVo();
							p.setFormulaName(fl.getCcname());
							p.setIsLeaf(0);// 非公式节点
							p.setTreeNodeId(
									(new StringBuffer(unitid).append(".costClass.").append(fl.getId())).toString());
							result.add(p);
						}
					}
				}
				// 下级项目
				List<Costitem> il = this.uiis.getItem(unitid, dqrq);
				if (il != null) {
					int count = il.size();
					for (int i = 0; count > i; i++) {
						Costitem ci = il.get(i);
						pflid = ci.getPid();
						if (pflid == null) {
							continue;
						}
						if (pointId.equalsIgnoreCase(pflid)) {
							MtmFormulaTreeVo p = new MtmFormulaTreeVo();
							p.setFormulaName(ci.getItemname());
							p.setIsLeaf(0);// 非公式节点
							p.setTreeNodeId(
									(new StringBuffer(unitid).append(".costItem.").append(ci.getId())).toString());
							result.add(p);
						}
					}
				}
			}
		} else if ("dotClass".equals(type)) {
			// 采集点分类
			String bd = this.IUMS.getMaxVersionByCostunitsampleclass(unitid, dqrq);
			MethodQueryDto queryDto = new MethodQueryDto();
			queryDto.setUnitid(unitid);
			queryDto.setBegintime(bd);
			List<Costunitsampleclass> scl = this.IUMS.getCostunitsampleclassList(queryDto);
			if (scl != null) {
				int dcount = scl.size();
				for (int i = 0; dcount > i; i++) {
					Costunitsampleclass fl = scl.get(i);
					pflid = fl.getPid();
					if (pflid == null) {
						continue;
					}
					unitid = fl.getUnitid();
					if (unitid == null) {
						continue;
					}
					if (pointId.equalsIgnoreCase(pflid)) {
						// 根分类
						MtmFormulaTreeVo p = new MtmFormulaTreeVo();
						p.setFormulaName(fl.getName());
						p.setIsLeaf(0);// 非公式节点
						p.setTreeNodeId((new StringBuffer(unitid).append(".dotClass.").append(fl.getId())).toString());
						result.add(p);
					}
				}
			}
			// 采集点项目
			List<Costunitsampledot> sdl = this.uiis.getSampleDot(unitid, dqrq, null);
			if (sdl != null) {
				int count = sdl.size();
				for (int i = 0; count > i; i++) {
					Costunitsampledot xm = sdl.get(i);
					pflid = xm.getPid();
					if (pflid == null) {
						continue;
					}
					if (pointId.equals(pflid)) {
						MtmFormulaTreeVo p = new MtmFormulaTreeVo();
						p.setFormulaName(xm.getName());
						p.setIsLeaf(0);// 非公式节点
						p.setTreeNodeId((new StringBuffer(unitid).append(".dotItem.").append(xm.getId())).toString());
						result.add(p);
					}
				}
			}
		} else if ("costItem".equals(type)) {
			// 核算项目下的公式节点
			String un = this.getUnitName(unitid);
			String xmmc = this.getItemName(pointId);
			StringBuffer sb1 = new StringBuffer(unitid).append(".").append(pointId);
			StringBuffer sb2 = new StringBuffer(un).append(".").append(xmmc);
			zbname = "消耗量";
			MtmFormulaTreeVo zb1 = new MtmFormulaTreeVo();
			zb1.setFormulaName(zbname);
			zb1.setFormulaCode((new StringBuffer(sb1).append(".xhl")).toString());
			zb1.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
			zb1.setIsLeaf(1);// 公式节点
			// zb1.setTreeNodeId("isLeaf");
			result.add(zb1);
			zbname = "单耗";
			MtmFormulaTreeVo zb2 = new MtmFormulaTreeVo();
			zb2.setFormulaName(zbname);
			zb2.setFormulaCode((new StringBuffer(sb1).append(".dh")).toString());
			zb2.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
			zb2.setIsLeaf(1);// 公式节点
			// zb2.setTreeNodeId("isLeaf");
			result.add(zb2);
			zbname = "总成本";
			MtmFormulaTreeVo zb3 = new MtmFormulaTreeVo();
			zb3.setFormulaName(zbname);
			zb3.setFormulaCode((new StringBuffer(sb1).append(".zcb")).toString());
			zb3.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
			zb3.setIsLeaf(1);// 公式节点
			// zb3.setTreeNodeId("isLeaf");
			result.add(zb3);
			zbname = "单位成本";
			MtmFormulaTreeVo zb4 = new MtmFormulaTreeVo();
			zb4.setFormulaName(zbname);
			zb4.setFormulaCode((new StringBuffer(sb1).append(".dwcb")).toString());
			zb4.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
			zb4.setIsLeaf(1);// 公式节点
			// zb4.setTreeNodeId("isLeaf");
			result.add(zb4);
		} else if ("dotItem".equals(type)) {
			// 采集点下的公式节点
			String un = this.getUnitName(unitid);
			String xmmc = this.getDotName(pointId);
			StringBuffer sb1 = new StringBuffer(unitid).append(".").append(pointId);
			StringBuffer sb2 = new StringBuffer(un).append(".").append(xmmc);
			zbname = "采样均值";
			MtmFormulaTreeVo zb1 = new MtmFormulaTreeVo();
			zb1.setFormulaName(zbname);
			zb1.setFormulaCode((new StringBuffer(sb1).append(".cjz")).toString());
			zb1.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
			zb1.setIsLeaf(1);// 公式节点
			// zb1.setTreeNodeId("isLeaf");
			result.add(zb1);
			zbname = "超限点";
			MtmFormulaTreeVo zb2 = new MtmFormulaTreeVo();
			zb2.setFormulaName(zbname);
			zb2.setFormulaCode((new StringBuffer(sb1).append(".cxd")).toString());
			zb2.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
			zb2.setIsLeaf(1);// 公式节点
			// zb2.setTreeNodeId("isLeaf");
			result.add(zb2);
			zbname = "总点";
			MtmFormulaTreeVo zb3 = new MtmFormulaTreeVo();
			zb3.setFormulaName(zbname);
			zb3.setFormulaCode((new StringBuffer(sb1).append(".zds")).toString());
			zb3.setFormulaDesc((new StringBuffer(sb2).append(".").append(zbname)).toString());
			zb3.setIsLeaf(1);// 公式节点
			// zb3.setTreeNodeId("isLeaf");
			result.add(zb3);
		}
		return result;
	}

	private String getUnitName(String unitid) {
		Costuint ui = this.icus.getUnitId(unitid);
		String un = "";
		if (ui != null) {
			un = ui.getName();
			if (un == null) {
				un = "";
			}
		}
		return un;
	}

	private String getItemName(String itemid) {
		String inx = "";
		Costitem di = this.uiis.fetchItemInfo(itemid);
		if (di != null) {
			inx = di.getItemname();
			if (inx == null) {
				inx = "";
			}
		}
		return inx;
	}

	private String getDotName(String dotid) {
		String inx = "";
		Costunitsampledot di = this.uiis.fetchDotInfo(dotid);
		if (di != null) {
			inx = di.getName();
			if (inx == null) {
				inx = "";
			}
		}
		return inx;
	}

	private List<MtmFormulaTreeVo> gg() {
		// 根据登录人员，得到他可看到的核算对象
		SysUser user = SysUserHolder.getCurrentUser();
		String orgId = user.getOrgId();
		String userId = user.getId();// 人员ID
		String postId = user.getPostId();
		postId = orgId + "_" + postId;// 机构ID
		String unitid, flid, pflid;
		// 租户内所有核算对象的分类
		HashMap<String, List<Costclass>> flm = new HashMap<String, List<Costclass>>();// 仅是根分类
		HashMap<String, HashMap<String, List<Costclass>>> sflm = new HashMap<String, HashMap<String, List<Costclass>>>();
		List<Costclass> flls = this.uiis.getCostClasses();
		if (flls != null) {
			int count = flls.size();
			for (int i = 0; count > i; i++) {
				Costclass fl = flls.get(i);
				pflid = fl.getPid();
				if (pflid == null) {
					continue;
				}
				unitid = fl.getUnitid();
				if (unitid == null) {
					continue;
				}
				if ("root".equalsIgnoreCase(pflid)) {
					// 根分类
					if (flm.containsKey(unitid)) {
						flm.get(unitid).add(fl);
					} else {
						List<Costclass> xx = new ArrayList<Costclass>();
						xx.add(fl);
						flm.put(unitid, xx);
					}
				} else {
					// 子分类
					if (sflm.containsKey(unitid)) {
						HashMap<String, List<Costclass>> xflm = sflm.get(unitid);
						if (xflm.containsKey(pflid)) {
							xflm.get(pflid).add(fl);
						} else {
							List<Costclass> xx = new ArrayList<Costclass>();
							xx.add(fl);
							xflm.put(pflid, xx);
						}
					} else {
						List<Costclass> xx = new ArrayList<Costclass>();
						xx.add(fl);
						HashMap<String, List<Costclass>> xflm = new HashMap<String, List<Costclass>>();
						xflm.put(pflid, xx);
						sflm.put(unitid, xflm);
					}
				}
			}
		}
		// 租户内所有核算对象的项目
		HashMap<String, HashMap<String, List<Costitem>>> xmm = new HashMap<String, HashMap<String, List<Costitem>>>();
		List<Costitem> xmls = this.uiis.getCostItems();
		if (xmls != null) {
			int count = xmls.size();
			for (int i = 0; count > i; i++) {
				Costitem xm = xmls.get(i);
				pflid = xm.getPid();
				if (pflid == null) {
					continue;
				}
				unitid = xm.getUnitid();
				if (unitid == null) {
					continue;
				}
				if (xmm.containsKey(unitid)) {
					HashMap<String, List<Costitem>> xxmm = xmm.get(unitid);
					if (xxmm.containsKey(pflid)) {
						xxmm.get(pflid).add(xm);
					} else {
						List<Costitem> xx = new ArrayList<Costitem>();
						xx.add(xm);
						xxmm.put(pflid, xx);
					}
				} else {
					List<Costitem> xx = new ArrayList<Costitem>();
					xx.add(xm);
					HashMap<String, List<Costitem>> xxmm = new HashMap<String, List<Costitem>>();
					xxmm.put(pflid, xx);
					xmm.put(unitid, xxmm);
				}
			}
		}
		// 租户内所有核算对象的核算指标
		HashMap<String, List<Costindicator>> zbm = new HashMap<String, List<Costindicator>>();
		List<Costindicator> zbls = this.uiis.getCostIndicators();
		if (zbls != null) {
			int count = zbls.size();
			for (int i = 0; count > i; i++) {
				Costindicator yn = zbls.get(i);
				unitid = yn.getUnitid();
				if (zbm.containsKey(unitid)) {
					zbm.get(unitid).add(yn);
				} else {
					List<Costindicator> xx = new ArrayList<Costindicator>();
					xx.add(yn);
					zbm.put(unitid, xx);
				}
			}
		}
		// 租户内所有核算对象的采集点分类
		HashMap<String, List<Costunitsampleclass>> dflm = new HashMap<String, List<Costunitsampleclass>>();// 仅是根分类
		HashMap<String, HashMap<String, List<Costunitsampleclass>>> dsflm = new HashMap<String, HashMap<String, List<Costunitsampleclass>>>();
		List<Costunitsampleclass> scl = this.uiis.getSampleCalss();
		if (scl != null) {
			int count = scl.size();
			for (int i = 0; count > i; i++) {
				Costunitsampleclass fl = scl.get(i);
				pflid = fl.getPid();
				if (pflid == null) {
					continue;
				}
				unitid = fl.getUnitid();
				if (unitid == null) {
					continue;
				}
				if ("root".equalsIgnoreCase(pflid)) {
					// 根分类
					if (dflm.containsKey(unitid)) {
						dflm.get(unitid).add(fl);
					} else {
						List<Costunitsampleclass> xx = new ArrayList<Costunitsampleclass>();
						xx.add(fl);
						dflm.put(unitid, xx);
					}
				} else {
					// 子分类
					if (dsflm.containsKey(unitid)) {
						HashMap<String, List<Costunitsampleclass>> dxflm = dsflm.get(unitid);
						if (dxflm.containsKey(pflid)) {
							dxflm.get(pflid).add(fl);
						} else {
							List<Costunitsampleclass> xx = new ArrayList<Costunitsampleclass>();
							xx.add(fl);
							dxflm.put(pflid, xx);
						}
					} else {
						List<Costunitsampleclass> xx = new ArrayList<Costunitsampleclass>();
						xx.add(fl);
						HashMap<String, List<Costunitsampleclass>> dxflm = new HashMap<String, List<Costunitsampleclass>>();
						dxflm.put(pflid, xx);
						dsflm.put(unitid, dxflm);
					}
				}
			}
		}
		// 租户内所有核算对象的采集点
		HashMap<String, String> dlxm = new HashMap<String, String>();
		HashMap<String, HashMap<String, List<Costunitsampledot>>> dxmm = new HashMap<String, HashMap<String, List<Costunitsampledot>>>();
		List<Costunitsampledot> sdl = this.uiis.getSampleDot();
		if (sdl != null) {
			int count = sdl.size();
			String ctype, key;
			for (int i = 0; count > i; i++) {
				Costunitsampledot xm = sdl.get(i);
				pflid = xm.getPid();
				if (pflid == null) {
					continue;
				}
				unitid = xm.getUnitid();
				if (unitid == null) {
					continue;
				}
				ctype = xm.getCtype();
				if ("2".equals(ctype)) {
					// 控制指标
					key = (new StringBuffer(unitid).append(".pwl")).toString();
					if (!dlxm.containsKey(key)) {
						dlxm.put(key, "1");
					}
				}
				if ("3".equals(ctype)) {
					// 合格率指标
					key = (new StringBuffer(unitid).append(".hgl")).toString();
					// 控制指标
					if (!dlxm.containsKey(key)) {
						dlxm.put(key, "1");
					}
				}
				if (dxmm.containsKey(unitid)) {
					HashMap<String, List<Costunitsampledot>> xxmm = dxmm.get(unitid);
					if (xxmm.containsKey(pflid)) {
						xxmm.get(pflid).add(xm);
					} else {
						List<Costunitsampledot> xx = new ArrayList<Costunitsampledot>();
						xx.add(xm);
						xxmm.put(pflid, xx);
					}
				} else {
					List<Costunitsampledot> xx = new ArrayList<Costunitsampledot>();
					xx.add(xm);
					HashMap<String, List<Costunitsampledot>> xxmm = new HashMap<String, List<Costunitsampledot>>();
					xxmm.put(pflid, xx);
					dxmm.put(unitid, xxmm);
				}
			}
		}
		List<Costuint> ul = this.ics.getCostuintListByOrgId(orgId, userId, postId, 2);
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();
		if (ul != null) {
			int count = ul.size();
			String unitname, zbname, key;
			for (int i = 0; count > i; i++) {
				Costuint x = ul.get(i);
				unitid = x.getId();
				unitname = x.getName();
				MtmFormulaTreeVo fl1 = new MtmFormulaTreeVo();
				fl1.setFormulaName(x.getName());
				fl1.setIsLeaf(0);// 非公式节点
				result.add(fl1);// 添加到根节点
				// 成本项目
				if (xmm.containsKey(unitid) || zbm.containsKey(unitid)) {
					// 核算对象有成本项目或核算指标时开启
					MtmFormulaTreeVo fl1_1 = new MtmFormulaTreeVo();
					fl1_1.setFormulaName("核算项目");
					fl1_1.setIsLeaf(0);// 非公式节点
					fl1.getChildren().add(fl1_1);// 添加到分类1下
					List<Costclass> tfll = flm.get(unitid);
					if (tfll != null) {
						// 核算项目
						HashMap<String, List<Costclass>> xjfl = null;
						if (sflm.containsKey(unitid)) {
							// 顶级分类下的分类
							xjfl = sflm.get(unitid);
						} else {
							xjfl = new HashMap<String, List<Costclass>>();
						}
						HashMap<String, List<Costitem>> xjxm = xmm.get(unitid);
						int count2 = tfll.size();
						for (int j = 0; count2 > j; j++) {
							Costclass djfl = tfll.get(j);
							MtmFormulaTreeVo fl1_1_1 = new MtmFormulaTreeVo();
							fl1_1_1.setFormulaName(djfl.getCcname());
							fl1_1_1.setIsLeaf(0);// 非公式节点
							fl1_1.getChildren().add(fl1_1_1);// 添加到分类1下
							flid = djfl.getId();
							this.getXjFlxm(fl1_1_1, unitid, unitname, flid, xjxm, xjfl);// 添加根分类下级的分类和项目
						}
					}
					// 核算指标
					List<Costindicator> zbl = zbm.get(unitid);
					if (zbl != null && zbl.size() > 0) {
						MtmFormulaTreeVo fl1_1_2 = new MtmFormulaTreeVo();
						fl1_1_2.setFormulaName("核算指标");
						fl1_1_2.setIsLeaf(0);// 非公式节点
						fl1_1.getChildren().add(fl1_1_2);// 添加到分类1下
						// 添加核算对象下具体的核算指标
						int count3 = zbl.size();
						for (int k = 0; count3 > k; k++) {
							Costindicator zb = zbl.get(k);
							MtmFormulaTreeVo zb1 = new MtmFormulaTreeVo();
							zbname = zb.getCpname();
							zb1.setFormulaName(zbname);
							zb1.setFormulaCode((new StringBuffer(unitid).append(".").append(zb.getId()).append(".tjz"))
									.toString());
							zb1.setFormulaDesc(
									(new StringBuffer(unitname).append(".").append(zbname).append(".统计结果")).toString());
							zb1.setIsLeaf(1);// 公式节点
							fl1_1_2.getChildren().add(zb1);// 添加到分类1_1下
						}
					}
				}
				if (dxmm.containsKey(unitid)) {
					// 核算对象下有采集点才开启
					MtmFormulaTreeVo fl1_2 = new MtmFormulaTreeVo();
					fl1_2.setFormulaName("采集点");
					fl1_2.setIsLeaf(0);// 非公式节点
					fl1.getChildren().add(fl1_2);// 添加到分类1下
					key = (new StringBuffer(unitid).append(".pwl")).toString();
					if (dlxm.containsKey(key)) {
						MtmFormulaTreeVo zb2 = new MtmFormulaTreeVo();
						zbname = "总平稳率";
						zb2.setFormulaName(zbname);
						zb2.setFormulaCode((new StringBuffer(unitid).append(".TotalSteadyRate.tjz")).toString());
						zb2.setFormulaDesc((new StringBuffer(unitname).append(".").append(zbname)).toString());
						zb2.setIsLeaf(1);// 公式节点
						fl1_2.getChildren().add(zb2);// 添加到分类1_2下
					}
					key = (new StringBuffer(unitid).append(".hgl")).toString();
					if (dlxm.containsKey(key)) {
						MtmFormulaTreeVo zb3 = new MtmFormulaTreeVo();
						zbname = "总合格率";
						zb3.setFormulaName(zbname);
						zb3.setFormulaCode(
								(new StringBuffer(unitid).append(".TotalStableQualificationRate.tjz")).toString());
						zb3.setFormulaDesc((new StringBuffer(unitname).append(".").append(zbname)).toString());
						zb3.setIsLeaf(1);// 公式节点
						fl1_2.getChildren().add(zb3);// 添加到分类1_2下
					}
					List<Costunitsampleclass> tfll = dflm.get(unitid);
					if (tfll != null) {
						// 根分类
						HashMap<String, List<Costunitsampleclass>> xjfl = null;
						if (dsflm.containsKey(unitid)) {
							// 顶级分类下的分类
							xjfl = dsflm.get(unitid);
						} else {
							xjfl = new HashMap<String, List<Costunitsampleclass>>();
						}
						HashMap<String, List<Costunitsampledot>> xjxm = dxmm.get(unitid);
						int count2 = tfll.size();
						for (int j = 0; count2 > j; j++) {
							Costunitsampleclass djfl = tfll.get(j);
							MtmFormulaTreeVo fl1_2_1 = new MtmFormulaTreeVo();
							fl1_2_1.setFormulaName(djfl.getName());
							fl1_2_1.setIsLeaf(0);// 非公式节点
							fl1_2.getChildren().add(fl1_2_1);// 添加到分类1下
							flid = djfl.getId();
							this.getXjFldot(fl1_2_1, unitid, unitname, flid, xjxm, xjfl);// 添加根分类下级的分类和项目
						}
					}
				}
			}
		}
		return result;
	}

	/**
	 * 解析公式
	 * 
	 * @param startMonth      开始月份
	 * @param endMonth        截止月份
	 * @param formulaTextList 公式列表
	 */
	@Override
	public List<MtmFormulaValueVo> getFormulaValue(String startDt, String endDt, List<String> formulaTextList,
			List<MtmFormulaValueVo> formulaTextObjList) {
		List<MtmFormulaValueVo> result = new ArrayList<MtmFormulaValueVo>();
		// 传入的内容 startDt = 2024-01-01:08:00:00 endDt = 2024-03-01 18:00:00
		// formulaTextList = [a.zb1,b.zb2]
		if (formulaTextList != null && formulaTextList.size() > 0) {
			HashMap<String, String> alm = new HashMap<String, String>();
			HashMap<String, Costunitsampledot> dotm = new HashMap<String, Costunitsampledot>();
			HashMap<String, HashMap<String, List<TeamSteadyRateTotalVo>>> pwlm = new HashMap<String, HashMap<String, List<TeamSteadyRateTotalVo>>>();
			HashMap<String, HashMap<String, List<TeamLimsTotalVo>>> limsm = new HashMap<String, HashMap<String, List<TeamLimsTotalVo>>>();
			String Uid = TMUID.getUID();
			log.info(Uid+"计算开始", DateTimeUtils.getDTStr());
			Double val;
			int count = formulaTextList.size();
			for (int i = 0; count > i; i++) {
				String f = formulaTextList.get(i);
				String[] s = f.split("\\.");
				if (s.length == 3) {
					// 目前核算仅支持3个点的参数unitid.ybid.tjz
					if ("TotalSteadyRate".equals(s[1]) || "TotalStableQualificationRate".equals(s[1])) {
						if (!alm.containsKey(s[0])) {
							List<Costunitsampledot> dotl = this.uiis.getSampleDot(s[0], endDt, "");
							if (dotl != null) {
								int count1 = dotl.size();
								for (int j = 0; count1 > j; j++) {
									Costunitsampledot dd = dotl.get(j);
									dotm.put(dd.getId(), dd);
								}
							}
							alm.put(s[0], "1");
						}
						if ("TotalSteadyRate".equals(s[1])) {
							// 总平稳率
							HashMap<String, List<TeamSteadyRateTotalVo>> valm = null;
							if (pwlm.containsKey(s[0])) {
								valm = pwlm.get(s[0]);
							} else {
								valm = this.ISRS.getTeamSteadyRateTotal(s[0], startDt, endDt, dotm);
								pwlm.put(s[0], valm);
							}
							if (valm != null) {
								if (valm.containsKey(s[1])) {
									List<TeamSteadyRateTotalVo> vol = valm.get(s[1]);
									if (vol != null) {
										int count2 = vol.size();
										for (int k = 0; count2 > k; k++) {
											TeamSteadyRateTotalVo jg = vol.get(k);
											val = 0.0;
											if ("zds".equals(s[2])) {
												val = jg.getTotalCount();// 总点数
											} else if ("cxd".equals(s[2])) {
												val = jg.getOverLowLimit();// 超限点
											} else {
												val = jg.getRate();// 平稳率
											}
											if (val == null) {
												val = 0.0;
											}
											MtmFormulaValueVo zbv = new MtmFormulaValueVo();
											zbv.setObjCode(jg.getOrgCode());
											zbv.setObjType(1);
											zbv.setFormulaText(f);
											zbv.setFormulaValue(String.valueOf(val));
											log.info("formula:" + f + ",orgid:" + jg.getOrgCode() + ",value:"
													+ String.valueOf(val), DateTimeUtils.getDTStr());
											result.add(zbv);
										}
									}
								}
							}
						} else if ("TotalStableQualificationRate".equals(s[1])) {
							// 总合格率
							HashMap<String, List<TeamLimsTotalVo>> valm = null;
							if (limsm.containsKey(s[0])) {
								valm = limsm.get(s[0]);
							} else {
								valm = this.TLS.getTeamLimsTotal(s[0], startDt, endDt, dotm);
								limsm.put(s[0], valm);
							}
							if (valm != null) {
								if (valm.containsKey(s[1])) {
									List<TeamLimsTotalVo> vol = valm.get(s[1]);
									if (vol != null) {
										int count2 = vol.size();
										for (int h = 0; count2 > h; h++) {
											TeamLimsTotalVo jg = vol.get(h);
											val = 0.0;
											if ("zds".equals(s[2])) {
												val = (double) jg.getPointCount();// 总点数
											} else if ("cxd".equals(s[2])) {
												val = (double) jg.getOverCount();// 超限点
											} else {
												val = jg.getRate();// 合格率
											}
											if (val == null) {
												val = 0.0;
											}
											MtmFormulaValueVo zbv = new MtmFormulaValueVo();
											zbv.setObjCode(jg.getOrgCode());
											zbv.setObjType(1);
											zbv.setFormulaText(f);
											zbv.setFormulaValue(String.valueOf(val));
											log.info("formula:" + f + ",orgid:" + jg.getOrgCode() + ",value:"
													+ String.valueOf(val), DateTimeUtils.getDTStr());
											result.add(zbv);
										}
									}
								}
							}
						}
					} else if ("cjz".equals(s[2]) || "cxd".equals(s[2]) || "zds".equals(s[2])) {
						// 普通采集点
						if (!alm.containsKey(s[0])) {
							List<Costunitsampledot> dotl = this.uiis.getSampleDot(s[0], endDt, "");
							if (dotl != null) {
								int count1 = dotl.size();
								for (int j = 0; count1 > j; j++) {
									Costunitsampledot dd = dotl.get(j);
									dotm.put(dd.getId(), dd);
								}
							}
							alm.put(s[0], "1");
						}
						if (dotm.containsKey(s[1])) {
							Costunitsampledot dd = dotm.get(s[1]);
							String ctype = dd.getCtype();
							if (ctype == null) {
								ctype = "0";
							}
							if ("2".equals(ctype)) {
								// 控制点
								HashMap<String, List<TeamSteadyRateTotalVo>> valm = null;
								if (pwlm.containsKey(s[0])) {
									valm = pwlm.get(s[0]);
								} else {
									valm = this.ISRS.getTeamSteadyRateTotal(s[0], startDt, endDt, dotm);
									pwlm.put(s[0], valm);
								}
								if (valm != null) {
									if (valm.containsKey(s[1])) {
										List<TeamSteadyRateTotalVo> vol = valm.get(s[1]);
										if (vol != null) {
											Double csxd, cxxd, pcsxd, pcxxd, zds;
											int count2 = vol.size();
											for (int k = 0; count2 > k; k++) {
												val = 0.0;
												TeamSteadyRateTotalVo jg = vol.get(k);
												if ("cjz".equals(s[2])) {// 采样均值
													zds = jg.getTotalCount();
													if (zds == null) {
														zds = 0.0;
													}
													val = jg.getFetchValue();
													if (val == null) {
														val = 0.0;
													}
													if (zds.compareTo(0.0) == 0) {
														val = 0.0;
													} else {
														val = val / zds;
													}
												} else if ("cxd".equals(s[2])) {
													csxd = jg.getOverUpLimit();
													if (csxd == null) {
														csxd = 0.0;
													}
													cxxd = jg.getOverLowLimit();
													if (cxxd == null) {
														cxxd = 0.0;
													}
													pcsxd = jg.getDelUpLimit();
													if (pcsxd == null) {
														pcsxd = 0.0;
													}
													pcxxd = jg.getDelLowLimit();
													if (pcxxd == null) {
														pcxxd = 0.0;
													}
													val = csxd + cxxd - pcsxd - pcxxd;
													if (val <= 0.0) {
														val = 0.0;
													}
												} else if ("zds".equals(s[2])) {
													val = jg.getTotalCount();
													if (val == null) {
														val = 0.0;
													}
												}
												MtmFormulaValueVo zbv = new MtmFormulaValueVo();
												zbv.setObjCode(jg.getOrgCode());
												zbv.setObjType(1);
												zbv.setFormulaText(f);
												zbv.setFormulaValue(String.valueOf(val));
												log.info("formula:" + f + ",orgid:" + jg.getOrgCode() + ",value:"
														+ String.valueOf(val), DateTimeUtils.getDTStr());
												result.add(zbv);
											}
										}
									}
								}
							} else if ("3".equals(ctype)) {
								// LIMS
								HashMap<String, List<TeamLimsTotalVo>> valm = null;
								if (limsm.containsKey(s[0])) {
									valm = limsm.get(s[0]);
								} else {
									valm = this.TLS.getTeamLimsTotal(s[0], startDt, endDt, dotm);
									limsm.put(s[0], valm);
								}
								if (valm != null) {
									if (valm.containsKey(s[1])) {
										List<TeamLimsTotalVo> vol = valm.get(s[1]);
										if (vol != null) {
											Integer zds, cxd, pcxd;
											int count2 = vol.size();
											String sval;
											for (int h = 0; count2 > h; h++) {
												TeamLimsTotalVo jg = vol.get(h);
												if ("cjz".equals(s[2])) {
													val = jg.getFetchValue();
													if (val == null) {
														val = 0.0;
													}
													zds = jg.getPointCount();
													if (zds == 0) {
														sval = "0";
													} else {
														sval = String.valueOf(val / zds);
													}
												} else if ("cxd".equals(s[2])) {
													cxd = jg.getOverCount();
													pcxd = jg.getDelCount();
													if ((cxd - pcxd) <= 0) {
														sval = "0";
													} else {
														sval = String.valueOf(cxd - pcxd);
													}
												} else if ("zds".equals(s[2])) {
													zds = jg.getPointCount();
													sval = String.valueOf(zds);
												} else {
													sval = "0";
												}
												MtmFormulaValueVo zbv = new MtmFormulaValueVo();
												zbv.setObjCode(jg.getOrgCode());
												zbv.setObjType(1);
												zbv.setFormulaText(f);
												zbv.setFormulaValue(sval);
												log.info(
														"formula:" + f + ",orgid:" + jg.getOrgCode() + ",value:" + sval,
														DateTimeUtils.getDTStr());
												result.add(zbv);
											}
										}
									}
								}
							}
						}
					} else {
						// 核算指标
						result.addAll(this.icuci.calc(Uid, s[0], startDt, endDt, s[1], f, s[2]));
					}
				} else {
					// 不可识别的参数，直接返回0，不区分机构和人员
					MtmFormulaValueVo zbv = new MtmFormulaValueVo();
					zbv.setFormulaText(f);// 把传入的公式写到这个里
					zbv.setFormulaValue("0");
					log.info("formula:" + f + ",无法识别公式", DateTimeUtils.getDTStr());
					result.add(zbv);
				}
			}
			this.icuci.removeMapVal(Uid);
			log.info(Uid+"计算结束", DateTimeUtils.getDTStr());
		}
		return result;
	}

	/**
	 * 根据条件获取模块内部数据
	 * 
	 * @category 根据条件获取模块内部数据
	 * <AUTHOR>
	 * @param startDt   开始日期
	 * @param endDt     截止日期
	 * @param queryList 查询条件列表 MtmFormulaValueVo.objType 类型（机构、岗位、人员）
	 *                  MtmFormulaValueVo.objCode 对应类型的代码
	 *                  MtmFormulaValueVo.paramValue存放要取数据的表
	 * @return 查询结果转为 json存入MtmFormulaValueVo.paramResult中
	 */
	@Override
	public void getJsonData(String startDt, String endDt, List<MtmFormulaValueVo> queryList) {
		// TODO Auto-generated method stub

	}

	/**
	 * 保存模块数据
	 * 
	 * @category 保存模块数据
	 * <AUTHOR>
	 * @param saveList 要保存的数据列表，json格式，数据存储在MtmFormulaValueVo.formulaValue字段中，对应表存储在MtmFormulaValueVo.paramValue中
	 * @return
	 */
	@Override
	public boolean saveJsonData(List<MtmFormulaValueVo> saveList) {
		// TODO Auto-generated method stub
		return false;
	}

	/**
	 * 公式模块数据初始化
	 * 
	 * @category <AUTHOR>
	 */
	@Override
	protected void init() {
		// TODO Auto-generated method stub

	}
}
