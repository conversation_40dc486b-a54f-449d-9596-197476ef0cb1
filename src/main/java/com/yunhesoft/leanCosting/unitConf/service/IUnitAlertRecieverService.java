package com.yunhesoft.leanCosting.unitConf.service;


import com.yunhesoft.leanCosting.unitConf.entity.dto.UnitAlertRecieverQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.UnitAlertRecieverSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.UnitAlertRecieverConfig;
import com.yunhesoft.leanCosting.unitConf.entity.vo.UnitAlertRecieverConfigVo;

import java.util.List;


/**
 *	核算对象的默认预警设置相关服务接口
 * <AUTHOR>
 * @date 2023-11-27
 */
public interface IUnitAlertRecieverService {
	
	/**
	 *	核算对象的默认预警设置数据（vo带附加属性）
	 * @param queryDto
	 * @return
	 */
	public List<UnitAlertRecieverConfigVo> getUnitAlertRecieverConfigVoList(UnitAlertRecieverQueryDto queryDto);
	
	/**
	 *	核算对象的默认预警设置数据（单表）
	 * @param queryDto
	 * @return
	 */
	public List<UnitAlertRecieverConfig> getUnitAlertRecieverList(UnitAlertRecieverQueryDto queryDto);

	/**
	 *	保存核算对象的默认预警设置数据
	 * @param saveDto
	 * @return
	 */
	public String saveUnitAlertRecieverData(UnitAlertRecieverSaveDto saveDto);

	/**
	 *	保存数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataUnitAlertRecieverConfig(List<UnitAlertRecieverConfig> addList,List<UnitAlertRecieverConfig> updList,List<UnitAlertRecieverConfig> delList);
	
}
