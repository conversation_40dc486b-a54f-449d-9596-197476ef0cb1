package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.service.ICostDeviceTypeService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.baseConfig.service.IToolService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostFormulaQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostItemFormulaSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostinstrumentLog;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BeanVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostFormulaVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemInfoVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.FormulaSort;
import com.yunhesoft.leanCosting.unitConf.entity.vo.TreeVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostFormulaService;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.dict.entity.SysDictData;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class CostFormulaServiceImpl implements ICostFormulaService {

	@Autowired
	private ICostService costService;

	@Autowired
	private ICostuintService costuintService;

	@Autowired
	private IToolService toolService;

	@Autowired
	private UnitItemInfoService unitItemInfoService;

	@Autowired
	private EntityService entityService;

	@Autowired
	private ICostToolService costToolService;
	
	@Autowired
    RedisUtil redisUtil;
	
	@Autowired
	private IUnitMethodService unitMethodService;
	
	@Autowired
	private ICostDeviceTypeService costDeviceTypeService;
	
	/**
	 * 初始化项目公式
	 * 
	 * @param costitem
	 * @return
	 */
	@Override
	public boolean initCostitem(List<Costitem> costitems) {
		boolean str = false;
		if (costitems == null) {
			return str;
		}
		List<CostItemFormula> formulas = initGetCostitem(costitems);
		String strs = unitMethodService.saveDataCostItemFormula(formulas,null,  null);
		if (strs!=null && strs.trim().length() == 0) {
			str = true;
		}
		return str;
	}

	public List<CostItemFormula> initGetCostitem(List<Costitem> costitems) {
		List<String> ftypes = new ArrayList<String>();// 项目公式使用
		ftypes.add("dh");
		ftypes.add("dwcb");
		ftypes.add("zcb");
		ftypes.add("khxhl");
		ftypes.add("khzcb");
		List<CostItemFormula> formulas = new ArrayList<CostItemFormula>();
		for (Costitem costitem : costitems) {
			String unitcode = costitem.getUnitid();// 核算对象ID
			//判断一下costuint是否为null，查找核算单元，如果都是null，数据异常，返回结果
			Costuint costuint = entityService.queryObjectById(Costuint.class, unitcode);
			if(costuint==null) {
				//TODO：复制核算对象ID和名称数据
				Devicetypelibrary devicetypelibrary = entityService.queryObjectById(Devicetypelibrary.class, unitcode);
				if(devicetypelibrary==null) {
					log.info("核算模块 - 单元类型复制，相关公式异常", unitcode);
					continue;
				}
				costuint = new Costuint();
				costuint.setId(devicetypelibrary.getId());
				costuint.setName(devicetypelibrary.getDtname());
				costuint.setUnittype(devicetypelibrary.getId());
			}
			
			String pid = costitem.getPid();// 分类ID
			Costclass costclass = entityService.queryObjectById(Costclass.class, pid);
			String unittype = costuint.getUnittype();
			Devicetypelibrary devicetypelibrary = entityService.queryObjectById(Devicetypelibrary.class, unittype);
			// 准备工具已完成
			formulas.addAll(
					toolService.initCostItemFormula(costclass, costuint, costitem, null, devicetypelibrary, ftypes));
		}
		return formulas;
	}

	/**
	 * 初始化项目-消耗量
	 * 
	 * @param costitemMap Map<Costitem.ID,formula> 核算项目ID，核算项目-消耗量
	 * @param objMap      Map<Costitem.ID,Map<String, Costinstrument>>
	 *                    核算项目ID，核算项目-仪表对象
	 * @return
	 */
	@Override
	public Map<String, List<CostItemFormula>> initCostItemHxl(Map<String, String> costitemMap,
			Map<String, Map<String, Costinstrument>> objMap) {
		List<Costuint> costUintList = costuintService.getDatas(new CostuintQueryDto());
		Map<String, Costuint> costUintMap_ = costUintList.stream()
				.collect(Collectors.toMap(Costuint::getId, Function.identity()));
		List<Costitem> costinstrumentList = costuintService.getCostitemDatas(new CostuintQueryDto());
		Map<String, Costitem> costitemMap_ = costinstrumentList.stream()
				.collect(Collectors.toMap(Costitem::getId, Function.identity()));

		Map<String, List<CostItemFormula>> dataMap = new LinkedHashMap<String, List<CostItemFormula>>();
		List<CostItemFormula> inserts = new ArrayList<CostItemFormula>();
		List<CostItemFormula> updatas = new ArrayList<CostItemFormula>();
		for (Entry<String, Map<String, Costinstrument>> entry : objMap.entrySet()) {
			String costItemId = entry.getKey();// 核算对象ID
			Map<String, Costinstrument> instrumentMap = entry.getValue();// 采样点-<仪表位号，bean>
			List<Costinstrument> costinstruments = new ArrayList<Costinstrument>();
			String fotmula = null;
			if (costitemMap.containsKey(costItemId)) {// 方案ID
				fotmula = costitemMap.get(costItemId);
			}
			if (objMap.containsKey(costItemId)) {
				Map<String, Costinstrument> _map = objMap.get(costItemId);
				for (Entry<String, Costinstrument> entry2 : _map.entrySet()) {
					costinstruments.add(entry2.getValue());
				}
			}
			Map<String, List<CostItemFormula>> map = initCostinstrument(costinstruments, fotmula, instrumentMap,
					costUintMap_, costitemMap_);
			if (map != null) {
				if (map.containsKey("insert")) {
					List<CostItemFormula> inserts_ = map.get("insert");
					inserts.addAll(inserts_);
				} else if (map.containsKey("updata")) {
					List<CostItemFormula> updatas_ = map.get("updata");
					updatas.addAll(updatas_);
				}
			}
		}
//		if (inserts.size() > 0) {
//			entityService.insertBatch(inserts);
//		}
//		if (updatas.size() > 0) {
//			entityService.updateBatch(updatas);
//		}
		unitMethodService.saveDataCostItemFormula(inserts, updatas, null);
//		dataMap.put("insert", inserts);
//		dataMap.put("updata", updatas);
		return dataMap;
	}

	/**
	 * 
	 * @param costinstruments
	 * @param fotmula
	 * @param instrumentMap
	 * @return
	 */
	private Map<String, List<CostItemFormula>> initCostinstrument(List<Costinstrument> costinstruments, String fotmula,
			Map<String, Costinstrument> instrumentMap, Map<String, Costuint> costUintMap_,
			Map<String, Costitem> costitemMap_) {
		Map<String, List<CostItemFormula>> dataMap = new LinkedHashMap<String, List<CostItemFormula>>();
		List<CostItemFormula> inserts = new ArrayList<CostItemFormula>();
		List<CostItemFormula> updatas = new ArrayList<CostItemFormula>();
		if (costinstruments == null) {
			return null;
		}
		String unitcode = null;
		String beginTime = null;
		Costuint costuint = null;
		Costitem costitem = null;
		Map<String, Costuint> costuintMap = new HashMap<String, Costuint>();
		Map<String, Costitem> costitemMap = new HashMap<String, Costitem>();
		for (Costinstrument costinstrument : costinstruments) {
			// 核算对象
			unitcode = costinstrument.getUnitid();
			beginTime = costinstrument.getBegintime();// 版本
			if (costuintMap.containsKey(unitcode)) {
				costuint = costuintMap.get(unitcode);
			} else {
				costuint = costUintMap_.get(unitcode);
				costuintMap.put(unitcode, costuint);
			}
			// 成本项目
			String pid = costinstrument.getPid();// 成本项目唯一ID
			if (costitemMap.containsKey(pid)) {
				costitem = costitemMap.get(pid);
			} else {
				costitem = costitemMap_.get(pid);
				costitemMap.put(pid, costitem);
			}
			CostItemFormula formula = toolService.initCostInstrumentFormula(costuint, costinstrument);
			inserts.add(formula);
		}
		Where where1 = Where.create();
		Order order1 = Order.create();
		where1.eq(CostItemFormula::getFType, "hxl");
		List<CostItemFormula> formulas = entityService.queryList(CostItemFormula.class, where1, order1);
		Map<String, List<CostItemFormula>> map = toolService.initCostItemFormula_(costitemMap, costuintMap, formulas);

		String costItemId1 = "";
		String costItemVali = "";
		List<String> operation = new ArrayList<String>();
		operation.add("+");
		operation.add("-");
		operation.add("*");
		operation.add("/");
		operation.add("(");
		operation.add(")");
		operation.add(".");
		List<CostinstrumentLog> logs = new ArrayList<CostinstrumentLog>();
		if (fotmula != null) {// 如果有公式那么进行替换
			String[] fotmulas_ = fotmula.replaceAll("\\{", "&&&&").replaceAll("\\}", "&&&&").split("&&&&");
			for (String fotmula_ : fotmulas_) {
				if (fotmula_.length() > 0) {
					if (instrumentMap.containsKey(fotmula_)) {
						Costinstrument costinstrument = instrumentMap.get(fotmula_);
						costItemId1 += costuint.getId() + "." + costinstrument.getId() + ".dbxhl";
						costItemVali += costuint.getName() + "." + costinstrument.getName() + ".仪表消耗量";
					} else {
						if (fotmula_.length() > 1) {
							if (!operation.contains(fotmula_)) {
								String val = new String(fotmula_);
								for (String oper : operation) {
									val = val.replaceAll("\\" + oper, "");
								}
								try {
									Long.parseLong(val);
								} catch (Exception e) {
									// 核算对象
									// 准备数据已完成
									CostinstrumentLog log = new CostinstrumentLog();
									log.setId(TMUID.getUID());
									log.setTagnumber(fotmula_);
									log.setUnitid(unitcode);
									log.setBegintime(beginTime);
									log.setFtype("dbxhl");
									log.setCostitemid(costitem.getId());
									logs.add(log);
									fotmula_ = "{" + fotmula_ + "}";
								}
							}
						}
						costItemId1 += fotmula_;
						costItemVali += fotmula_;
					}
				}
			}
		}

		if (map.containsKey("insert")) {
			List<CostItemFormula> costItemFormulas = map.get("insert");
			if (costItemFormulas.size() > 0) {
				if (costItemId1.trim().length() > 0) {// 如果有数据，更新公式
					for (CostItemFormula _formula : costItemFormulas) {
						_formula.setCFormula(costItemVali.toString());
						_formula.setFormula(costItemId1.toString());
					}
				}
				inserts.addAll(costItemFormulas);
			}
			List<Costitem> costitems = new ArrayList<Costitem>();
			costitems.add(costitem);
			List<CostItemFormula> initCostItem = initGetCostitem(costitems);
			if (initCostItem.size() > 0) {
				inserts.addAll(initCostItem);
			}
		} else if (map.containsKey("updata")) {
			List<CostItemFormula> costItemFormulas = map.get("updata");
			if (costItemFormulas.size() > 0) {
				if (costItemId1.trim().length() > 0) {// 如果有数据，更新公式
					for (CostItemFormula _formula : costItemFormulas) {
						_formula.setCFormula(costItemVali.toString());
						_formula.setFormula(costItemId1.toString());
					}
				}
				updatas.addAll(costItemFormulas);
			}
		}
		if (logs.size() > 0) {
			entityService.insertBatch(logs);
		}
		dataMap.put("insert", inserts);
		dataMap.put("updata", updatas);
		return dataMap;
	}

	/**
	 * 初始化仪表公式
	 * 
	 * @param costinstrument
	 * @return
	 */
	@Override
	public boolean initCostinstrument(List<Costinstrument> costinstruments) {
		List<Devicetypelibrary> costDeviceTypeList =  costDeviceTypeService.getData();
		Map<String, Devicetypelibrary> devicetypelibraryMap_ = costDeviceTypeList.stream()
				.collect(Collectors.toMap(Devicetypelibrary::getId, Function.identity()));
		
		List<Costuint> costUintList = costuintService.getDatas(new CostuintQueryDto());
		Map<String, Costuint> costUintMap_ = costUintList.stream()
				.collect(Collectors.toMap(Costuint::getId, Function.identity()));
		List<Costitem> costinstrumentList = costuintService.getCostitemDatas(new CostuintQueryDto());
		Map<String, Costitem> costitemMap_ = costinstrumentList.stream()
				.collect(Collectors.toMap(Costitem::getId, Function.identity()));
		boolean str = false;
		if (costinstruments == null) {
			return str;
		}
		String unitcode = null;
		List<CostItemFormula> insertFormula = new ArrayList<CostItemFormula>();
		Map<String, Costuint> costuintMap = new HashMap<String, Costuint>();
		Map<String, Costitem> costitemMap = new HashMap<String, Costitem>();
		for (Costinstrument costinstrument : costinstruments) {
			Costuint costuint = null;
			Costitem costitem = null;
			// 核算对象
			unitcode = costinstrument.getUnitid();
			if (devicetypelibraryMap_.containsKey(unitcode)) {//核算单元类型
				Devicetypelibrary devicetypelibrary = devicetypelibraryMap_.get(unitcode);
				costuint = new Costuint();
				costuint.setId(devicetypelibrary.getId());
				costuint.setName(devicetypelibrary.getDtname());
				costuint.setUnittype(devicetypelibrary.getId());
			} else if(costuintMap.containsKey(unitcode)){
				costuint = costuintMap.get(unitcode);
			} else if(costUintMap_.containsKey(unitcode)){
				costuint = costUintMap_.get(unitcode);
				costuintMap.put(unitcode, costuint);
			}
			// 成本项目
			String pid = costinstrument.getPid();// 成本项目唯一ID
			if (costitemMap.containsKey(pid)) {
				costitem = costitemMap.get(pid);
			} else {
				costitem = costitemMap_.get(pid);
				costitemMap.put(pid, costitem);
			}
			CostItemFormula formula = toolService.initCostInstrumentFormula(costuint, costinstrument);
			insertFormula.add(formula);
		}
		if (insertFormula.size() > 0) {
//			entityService.rawInsertBatch(insertFormula);
			unitMethodService.saveDataCostItemFormula(insertFormula,null,  null);
		}

		Where where1 = Where.create();
		Order order1 = Order.create();
		where1.eq(CostItemFormula::getFType, "hxl");
		List<CostItemFormula> formulas = entityService.queryList(CostItemFormula.class, where1, order1);
		Map<String, List<CostItemFormula>> map = toolService.initCostItemFormula_(costitemMap, costuintMap, formulas);
		if (map.containsKey("insert")) {
			List<CostItemFormula> costItemFormulas = map.get("insert");
			if (costItemFormulas.size() > 0) {
//				entityService.rawInsertBatch(costItemFormulas);
				unitMethodService.saveDataCostItemFormula(costItemFormulas, null, null);
			}
		} else if (map.containsKey("updata")) {
			List<CostItemFormula> costItemFormulas = map.get("updata");
			if (costItemFormulas.size() > 0) {
//				entityService.rawUpdateByIdBatch(costItemFormulas);
				unitMethodService.saveDataCostItemFormula(null,costItemFormulas,  null);
			}
		}
		
		return str;
	}

	@Override
	public CostFormulaVo getData(CostFormulaQueryDto dto) {
		CostFormulaVo costFormulaVo = new CostFormulaVo();
		String costuintId = dto.getCostuintId();
		Integer type = dto.getType();
		List<Costuint> costuints = new ArrayList<Costuint>();
		if (type == null || type == 0) {
			Costuint costuint = entityService.queryObjectById(Costuint.class, costuintId);
			costuints.add(costuint);
		} else {
			SysUser user = SysUserHolder.getCurrentUser();
			String orgId = user.getOrgId();
			String userId = user.getId();// 人员ID
			String postId = user.getPostId();
			postId = orgId + "_" + postId;// 机构ID
			// 根据机构ID查询管理机构--tree根
			costuints = costService.getCostuintListByOrgId(orgId, userId, postId, 2);
			boolean isNot = false;
			for (Costuint costuint : costuints) {
				if (costuintId.equals(costuint.getId())) {
					isNot = true;
				}
			}
			if (!isNot) {
				Costuint costuint = entityService.queryObjectById(Costuint.class, costuintId);
				costuints.add(0, costuint);
			}
		}

		List<Costclass> costclasss = new ArrayList<Costclass>();
		List<Costitem> costitems = new ArrayList<Costitem>();
		List<Costinstrument> costinstruments = new ArrayList<Costinstrument>();
		Map<String, List<Costindicator>> costindicatorMap = new LinkedHashMap<String, List<Costindicator>>();
		List<CostItemFormula> costItemFormulas = new ArrayList<CostItemFormula>();
		List<CostItemInfoVo> vos = unitItemInfoService.getCostData(costuints);
		for (CostItemInfoVo vo : vos) {
			List<Costclass> classList = vo.getClassList();
			if (classList != null) {
				for (Costclass costclass : classList) {
					String pid = costclass.getPid();
					if ("root".equals(pid)) {
						costclass.setPid(vo.getUnitId());
					}
				}
				costclasss.addAll(classList);
			}
			List<Costitem> itemList = vo.getItemList();
			if (itemList != null) {
				costitems.addAll(itemList);
			}
			List<Costinstrument> instrumentList = vo.getInstrumentList();
			if (instrumentList != null) {
				costinstruments.addAll(instrumentList);
			}
			List<Costindicator> indicatorList = vo.getIndicatorList();
			if (indicatorList != null) {
				costindicatorMap.put(vo.getUnitId() + "param", indicatorList);
			}

			List<CostItemFormula> costItemFormulasList = vo.getFormulaList();
			if (costItemFormulasList != null) {
				costItemFormulas.addAll(costItemFormulasList);
			}
		}

//		for (Costuint costuint : costuints) {
//			System.out.println(costuint.getId()+","+dto.getBegintime());
//			CostItemInfoVo vo = unitItemInfoService.getCostData(costuint.getId(), dto.getBegintime());
//			List<Costclass> classList = vo.getClassList();
//			if (classList != null) {
//				for (Costclass costclass : classList) {
//					String pid = costclass.getPid();
//					if ("root".equals(pid)) {
//						costclass.setPid(costuint.getId());
//					}
//				}
//				costclasss.addAll(classList);
//			}
//			List<Costitem> itemList = vo.getItemList();
//			if (itemList != null) {
//				costitems.addAll(itemList);
//			}
//			List<Costinstrument> instrumentList = vo.getInstrumentList();
//			if (instrumentList != null) {
//				costinstruments.addAll(instrumentList);
//			}
//			List<Costindicator> indicatorList = vo.getIndicatorList();
//			if (indicatorList != null) {
//				costindicatorMap.put(costuint.getId() + "param", indicatorList);
//			}
//
//			List<CostItemFormula> costItemFormulasList = vo.getFormulaList();
//			if (costItemFormulasList != null) {
//				costItemFormulas.addAll(costItemFormulasList);
//			}
//		}
		List<TreeVo> treeVos = new ArrayList<TreeVo>();
		if (type == 0) {
			treeVos = costToolService.queryContent(
					getTree(costuints, costclasss, costitems, costinstruments, costindicatorMap, costItemFormulas));
			costFormulaVo.setTreeVos(treeVos);
		} else {
			costFormulaVo = getUpdateTree(costuintId,costuints, costclasss, costitems, costinstruments, costindicatorMap,
					costItemFormulas);
			costFormulaVo.setTreeVos(costToolService.queryContentBm(costFormulaVo.getTreeVos()));
		}
		// 整理selectContent
//		costFormulaVo.setTreeVos(costToolService.queryContent(costFormulaVo.getTreeVos()));

		return costFormulaVo;
	}

	/**
	 * 修改查询树形
	 * 
	 * @param costuintId       核算主键ID
	 * @param type             修改模式和查询模式
	 * @param costuints        核算对象
	 * @param costclasss       成本项目分类
	 * @param costitems        成本项目
	 * @param costinstruments  成本项目仪表
	 * @param costindicatorMap 核算指标
	 * @param CostItemFormulas 涉及到所有公式
	 * @return
	 */
	private CostFormulaVo getUpdateTree(String costuintId,List<Costuint> costuints, List<Costclass> costclasss, List<Costitem> costitems,
			List<Costinstrument> costinstruments, Map<String, List<Costindicator>> costindicatorMap,
			List<CostItemFormula> costItemFormulas) {
		List<BeanVo> vos = new ArrayList<BeanVo>();
		Map<String, String> voMap = new HashMap<String, String>();
		// 服务类型读取数据字典
		// 项目使用
		List<SysDictData> ufTypes = toolService.getDataList("UFTYPE");
		// 仪表使用
		List<SysDictData> uinstrTypes = toolService.getDataList("UINSTRTYPE");

		Map<String, List<Costclass>> costclassMap = costclasss.stream()
				.collect(Collectors.groupingBy(Costclass::getPid));
		Map<String, List<Costitem>> costitemMap = costitems.stream().collect(Collectors.groupingBy(Costitem::getPid));
		Map<String, List<Costinstrument>> costinstrumentMap = costinstruments.stream()
				.collect(Collectors.groupingBy(Costinstrument::getPid));
		Map<String, Costuint> costuints_Map = costuints.stream()
				.collect(Collectors.toMap(Costuint::getId, Function.identity()));
//		Map<String, Costitem> costitem_Map = costitems.stream()
//				.collect(Collectors.toMap(Costitem::getId, Function.identity()));

		Map<String, Costuint> costuint_Map = costuints.stream()
				.collect(Collectors.toMap(Costuint::getId, Function.identity()));
		// 最总整理
		Map<String, List<TreeVo>> TreeMap = new HashMap<String, List<TreeVo>>();

		Map<String, Costclass> costclassMaps = new HashMap<String, Costclass>();

		// 整合数据-成本项目分类
		for (Entry<String, List<Costclass>> entry : costclassMap.entrySet()) {
			String key = entry.getKey();
			List<Costclass> value = entry.getValue();
			List<TreeVo> list = new ArrayList<TreeVo>();
			for (Costclass bean : value) {
				TreeVo treeVo = new TreeVo();
				treeVo.setId(bean.getId());
//				treeVo.setPid(bean.getPid());
				treeVo.setName(bean.getCcname());
				treeVo.setTypeCode("1");
				list.add(treeVo);
				if (!costclassMaps.containsKey(bean.getId())) {
					costclassMaps.put(bean.getId(), bean);
				}
			}
			TreeMap.put(key, list);
		}
		// 成本项目
		for (Entry<String, List<Costitem>> entry : costitemMap.entrySet()) {
			String key = entry.getKey();
			List<Costitem> value = entry.getValue();
			List<TreeVo> list = new ArrayList<TreeVo>();
			for (Costitem bean : value) {
				TreeVo treeVo = new TreeVo();
				treeVo.setId(bean.getId());
				treeVo.setPid(bean.getPid());
				treeVo.setName(bean.getItemname());
				treeVo.setTypeCode("2");

				List<TreeVo> costitemFormulas = new ArrayList<TreeVo>();
//				Costitem bean = costitem_Map.get(key);//成本项目
				String unitid = bean.getUnitid();// 核算对象ID
				String id = bean.getId();// 仪表ID
				String name = bean.getItemname();// 项目名称
				// 成本项目
				for (SysDictData sysDictData : ufTypes) {
					TreeVo treeVo1 = new TreeVo();
					treeVo1.setId(sysDictData.getDictValue());
					treeVo1.setFType(sysDictData.getDictValue());
					treeVo1.setPfType(sysDictData.getRemark());
					treeVo1.setPid(key);
					treeVo1.setName(sysDictData.getDictLabel());
					// 公式
					treeVo1.setFormula(unitid + "." + id + "." + sysDictData.getDictValue());
					treeVo1.setCFormula(costuints_Map.get(unitid).getName() + "." + name + "." + sysDictData.getDictLabel());

					BeanVo beanVo = new BeanVo();
					beanVo.setKey(treeVo1.getFormula());
					beanVo.setValue(treeVo1.getCFormula());
					vos.add(beanVo);
					voMap.put(beanVo.getKey(), beanVo.getValue());
					treeVo1.setTypeCode("21");
					costitemFormulas.add(treeVo1);
				}
				treeVo.setChildren(costitemFormulas);
				list.add(treeVo);
			}

			if (costclassMaps.containsKey(key)) {
				Costclass costclass = costclassMaps.get(key);
				// 项目综合能耗
				TreeVo _treeVo = new TreeVo();
				if (costuint_Map.containsKey(costclass.getUnitid())) {
					Costuint costuint = costuint_Map.get(costclass.getUnitid());
					String xmxhlId = costuint.getId() + "." + costclass.getCctype() + ".zhnh";
					String xmxhlVal = costuint.getName() + "." + costclass.getCctype() + ".综合能耗";
					_treeVo.setId(xmxhlId);
					_treeVo.setName("项目综合能耗");
					_treeVo.setFType("zhnh");
					_treeVo.setPfType("zhnh");
					_treeVo.setPid(key);
					_treeVo.setFormula(xmxhlId);
					_treeVo.setCFormula(xmxhlVal);
					BeanVo beanVo = new BeanVo();
					beanVo.setKey(_treeVo.getFormula());
					beanVo.setValue(_treeVo.getCFormula());
					vos.add(beanVo);
					voMap.put(beanVo.getKey(), beanVo.getValue());
					_treeVo.setTypeCode("21");
					_treeVo.setPid(key);
					list.add(0, _treeVo);
				}
			}

			if (costclassMaps.containsKey(key)) {
				Costclass costclass = costclassMaps.get(key);
				// 项目总成本
				TreeVo _treeVo = new TreeVo();
				if (costuint_Map.containsKey(costclass.getUnitid())) {
					Costuint costuint = costuint_Map.get(costclass.getUnitid());
					String xmxhlId = costuint.getId() + "." + costclass.getCctype() + ".zcb";
					String xmxhlVal = costuint.getName() + "." + costclass.getCctype() + ".总成本";
					_treeVo.setId(xmxhlId);
					_treeVo.setName("项目总成本");
					_treeVo.setFType("zcb");
					_treeVo.setPfType("zcb");
					_treeVo.setPid(key);
					_treeVo.setFormula(xmxhlId);
					_treeVo.setCFormula(xmxhlVal);
					BeanVo beanVo = new BeanVo();
					beanVo.setKey(_treeVo.getFormula());
					beanVo.setValue(_treeVo.getCFormula());
					vos.add(beanVo);
					voMap.put(beanVo.getKey(), beanVo.getValue());
					_treeVo.setTypeCode("21");
					_treeVo.setPid(key);
					list.add(0, _treeVo);
				}
			}

			if (costclassMaps.containsKey(key)) {
				Costclass costclass = costclassMaps.get(key);
				// 项目量合计
				TreeVo _treeVo = new TreeVo();
				if (costuint_Map.containsKey(costclass.getUnitid())) {
					Costuint costuint = costuint_Map.get(costclass.getUnitid());
					String xmxhlId = costuint.getId() + "." + costclass.getCctype() + ".xhl";
					String xmxhlVal = costuint.getName() + "." + costclass.getCctype() + ".消耗量";
					_treeVo.setId(xmxhlId);
					_treeVo.setName("项目量合计");
					_treeVo.setFType("xhl");
					_treeVo.setPfType("xhl");
					_treeVo.setPid(key);
					_treeVo.setFormula(xmxhlId);
					_treeVo.setCFormula(xmxhlVal);
					BeanVo beanVo = new BeanVo();
					beanVo.setKey(_treeVo.getFormula());
					beanVo.setValue(_treeVo.getCFormula());
					vos.add(beanVo);
					voMap.put(beanVo.getKey(), beanVo.getValue());
					_treeVo.setTypeCode("21");
					_treeVo.setPid(key);
					list.add(0, _treeVo);
				}
			}

			TreeMap.put(key, list);
		}

		// 成本项目仪表
		for (Entry<String, List<Costinstrument>> entry : costinstrumentMap.entrySet()) {
			String key = entry.getKey();
			List<Costinstrument> value = entry.getValue();
			List<TreeVo> list = new ArrayList<TreeVo>();
			for (Costinstrument bean : value) {
				TreeVo treeVo = new TreeVo();
				// 仪表公式获取，当前是一对一关系
				treeVo.setFType("dbxh");
				treeVo.setId(bean.getId());
				treeVo.setPid(bean.getUnitid());
				treeVo.setName(bean.getName());
				treeVo.setTypeCode("3");// 查询模式仪表是分类
				// 仪表
				List<TreeVo> costitemFormulas = new ArrayList<TreeVo>();
				String unitid = bean.getUnitid();// 核算对象ID
				String id = bean.getId();// 仪表ID
				String name = bean.getName();// 仪表名称
				for (SysDictData sysDictData : uinstrTypes) {
					TreeVo treeVo1 = new TreeVo();
					treeVo1.setId(sysDictData.getDictValue());
					treeVo1.setFType(sysDictData.getDictValue());
					treeVo1.setPfType(sysDictData.getRemark());
					treeVo1.setPid(key);
					treeVo1.setName(sysDictData.getDictLabel());
					treeVo1.setTypeCode("31");
					// 公式
					treeVo1.setFormula(unitid + "." + id + "." + sysDictData.getDictValue());
					treeVo1.setCFormula(
							costuints_Map.get(unitid).getName() + "." + name + "." + sysDictData.getDictLabel());
					// 公式翻译
					BeanVo beanVo = new BeanVo();
					beanVo.setKey(treeVo1.getFormula());
					beanVo.setValue(treeVo1.getCFormula());
					vos.add(beanVo);
					voMap.put(beanVo.getKey(), beanVo.getValue());
					costitemFormulas.add(treeVo1);
				}
				treeVo.setChildren(costitemFormulas);
				list.add(treeVo);
			}
			List<TreeVo> costitemFormulas = new ArrayList<TreeVo>();
			if (TreeMap.containsKey(key)) {
				costitemFormulas = TreeMap.get(key);
			}
			costitemFormulas.addAll(list);
			// 增加自定义
			TreeMap.put(key, costitemFormulas);
		}

		// 核算指标
		for (Entry<String, List<Costindicator>> entry : costindicatorMap.entrySet()) {
			String key = entry.getKey();
			List<Costindicator> value = entry.getValue();
			List<TreeVo> list = new ArrayList<TreeVo>();
			//costuintId
			
			for (Costindicator bean : value) {
				TreeVo treeVo = new TreeVo();
				String unitId = bean.getUnitid();
				treeVo.setId(bean.getId());
				treeVo.setFType("param");
				// 公式
				treeVo.setFormula(bean.getId());
				treeVo.setCFormula(bean.getCpname());
				BeanVo beanVo = new BeanVo();
				beanVo.setKey(bean.getId());// 核算公式ID
				beanVo.setValue(bean.getCpname());// 核算公式名称
				vos.add(beanVo);
				treeVo.setPid(bean.getId());
				treeVo.setName(bean.getCpname());
				treeVo.setTypeCode("99");
				
				if(unitId.equals(costuintId)) {//TODO:核算指标只显示，当前核算公式的核算指标
					voMap.put(beanVo.getKey(), beanVo.getValue());
					list.add(treeVo);
				}
			}
			// 增加自定义
			TreeMap.put(key, list);
		}
		// 整合树形
		List<TreeVo> list = new ArrayList<TreeVo>();
		for (Costuint costuint : costuints) {
			String id = costuint.getId();
			TreeVo treeVo = new TreeVo();
			treeVo.setId(id);
			treeVo.setName(costuint.getName());
			treeVo.setTypeCode("0");
			if (TreeMap.containsKey(id)) {
				List<TreeVo> children = TreeMap.get(id);
				// 硬性增加
				TreeVo _treeVo = new TreeVo();
				_treeVo.setId(costuint.getId() + "param");
				_treeVo.setPid(costuint.getId());
				_treeVo.setName("核算指标");
				_treeVo.setTypeCode("98");
				children.add(_treeVo);
				treeVo.setChildren(children);
				if (children != null) {
					getTreeData(children, TreeMap);
				}
			}
			list.add(treeVo);
		}

		CostFormulaVo costFormulaVo = new CostFormulaVo();
		costFormulaVo.setTreeVos(list);
		SysUser user = SysUserHolder.getCurrentUser();
		String userId = user.getId();// 人员ID
		String updateKey = "updateKey_"+userId;
		redisUtil.setObject(updateKey , voMap);
//		costFormulaVo.setFormulaMap(vos);
		return costFormulaVo;
	}

	/**
	 * 通用查询树形
	 * 
	 * @param type             修改模式和查询模式
	 * @param costuints        核算对象
	 * @param costclasss       成本项目分类
	 * @param costitems        成本项目
	 * @param costinstruments  成本项目仪表
	 * @param costindicatorMap 核算指标
	 * @param CostItemFormulas 涉及到所有公式
	 * @return
	 */
	private List<TreeVo> getTree(List<Costuint> costuints, List<Costclass> costclasss, List<Costitem> costitems,
			List<Costinstrument> costinstruments, Map<String, List<Costindicator>> costindicatorMap,
			List<CostItemFormula> costItemFormulas) {
//		SysDictData dictData = new SysDictData();
//		dictData.setDictType("FTYPE");
//		List<SysDictData> dictDatas = sysDictDataService.selectDictDataList(dictData);
		// 公式类型-取数据字典
		List<SysDictData> dictDatas = toolService.getDataList("FTYPE");

		Map<String, List<Costclass>> costclassMap = costclasss.stream()
				.collect(Collectors.groupingBy(Costclass::getPid));
		Map<String, List<Costitem>> costitemMap = costitems.stream().collect(Collectors.groupingBy(Costitem::getPid));

		Map<String, Costitem> _costitemMap = costitems.stream()
				.collect(Collectors.toMap(Costitem::getId, Function.identity()));

		Map<String, List<Costinstrument>> costinstrumentMap = costinstruments.stream()
				.collect(Collectors.groupingBy(Costinstrument::getPid));
		Map<String, List<CostItemFormula>> costItemFormula_Map = costItemFormulas.stream()
				.collect(Collectors.groupingBy(CostItemFormula::getPid));
		// 最总整理
		Map<String, List<TreeVo>> TreeMap = new HashMap<String, List<TreeVo>>();
		// 整合数据-成本项目分类
		for (Entry<String, List<Costclass>> entry : costclassMap.entrySet()) {
			String key = entry.getKey();
			List<Costclass> value = entry.getValue();
			List<TreeVo> list = new ArrayList<TreeVo>();
			for (Costclass bean : value) {
				TreeVo treeVo = new TreeVo();
				treeVo.setId(bean.getId());
//				treeVo.setPid(bean.getPid());
				treeVo.setName(bean.getCcname());
				treeVo.setTypeCode("1");
				list.add(treeVo);
			}
			TreeMap.put(key, list);
		}
		// 成本项目
		for (Entry<String, List<Costitem>> entry : costitemMap.entrySet()) {
			String key = entry.getKey();
			List<Costitem> value = entry.getValue();
			List<TreeVo> list = new ArrayList<TreeVo>();
//			Costclass costclass =  entityService.queryObjectById(Costclass.class, key);
//			//项目消耗量
//			TreeVo _treeVo = new TreeVo();
//			String xmxhlId = costclass.getUnitid()+"."+costclass.getCctype()+".xhl";
//			_treeVo.setId(xmxhlId);
//			_treeVo.setName("项目量合计");
//			_treeVo.setFType("xhl");
//			_treeVo.setPfType("xhl");
//			_treeVo.setCostitemName("项目量合计");
//			_treeVo.setTypeCode("21");
//			_treeVo.setPid(key);
//			list.add(_treeVo);
			for (Costitem bean : value) {
				String id = bean.getId();// 项目ID
//				String unitid = bean.getUnitid();
				TreeVo treeVo = new TreeVo();
				treeVo.setId(bean.getId());
//				treeVo.setPid(bean.getPid());
				treeVo.setName(bean.getItemname());
				treeVo.setTypeCode("2");

				List<TreeVo> costitemFormulas = new ArrayList<TreeVo>();
				// 成本项目
				Map<String, CostItemFormula> costuintsType_Map = new HashMap<String, CostItemFormula>();
				if (costItemFormula_Map.containsKey(id)) {
					List<CostItemFormula> costItemFormula = costItemFormula_Map.get(id);
					// 根据类型进一步区分数据
					costuintsType_Map = costItemFormula.stream()
							.collect(Collectors.toMap(CostItemFormula::getFType, Function.identity()));
				}
				for (SysDictData sysDictData : dictDatas) {
					TreeVo treeVo1 = new TreeVo();
					treeVo1.setId(sysDictData.getDictValue());
					treeVo1.setFType(sysDictData.getDictValue());
					treeVo1.setPfType(sysDictData.getRemark());
					if (costuintsType_Map.containsKey(sysDictData.getDictValue())) {
						CostItemFormula _formulaBean = costuintsType_Map.get(sysDictData.getDictValue());
						// 赋值
						setTreeVo(treeVo1, _formulaBean);
					}
					// 通过ID获得项目名称
					if (_costitemMap.containsKey(id)) {
						treeVo1.setCostitemName(_costitemMap.get(id).getItemname());
					}
					treeVo1.setPid(id);
					treeVo1.setName(sysDictData.getDictLabel());
					treeVo1.setTypeCode("21");
					costitemFormulas.add(treeVo1);
				}
				treeVo.setChildren(costitemFormulas);
				list.add(treeVo);
			}
			TreeMap.put(key, list);
		}
		// 成本项目仪表
		for (Entry<String, List<Costinstrument>> entry : costinstrumentMap.entrySet()) {
			String key = entry.getKey();
			List<Costinstrument> value = entry.getValue();
			List<TreeVo> list = new ArrayList<TreeVo>();
			for (Costinstrument bean : value) {
				TreeVo treeVo = new TreeVo();
				treeVo.setId(bean.getId());
				// 仪表公式获取，当前是一对一关系
				treeVo.setFType("dbxh");
				if (costItemFormula_Map.containsKey(bean.getId())) {
					List<CostItemFormula> costItemFormula = costItemFormula_Map.get(bean.getId());
					if (costItemFormula.size() > 0) {
						for (CostItemFormula _formulaBean : costItemFormula) {
							if (_formulaBean.getFType().equals("dbxh")) {
								setTreeVo(treeVo, _formulaBean);
							}
						}
					}
				}
				// 通过ID获得项目名称
				if (_costitemMap.containsKey(bean.getPid())) {
					treeVo.setCostitemName(_costitemMap.get(bean.getPid()).getItemname());
				}
				treeVo.setPid(bean.getId());
				treeVo.setName(bean.getName());
				treeVo.setTypeCode("3");
				list.add(treeVo);
			}
			List<TreeVo> costitemFormulas = new ArrayList<TreeVo>();
			if (TreeMap.containsKey(key)) {
				costitemFormulas = TreeMap.get(key);
			}
			costitemFormulas.addAll(list);
			// 增加自定义
			TreeMap.put(key, costitemFormulas);
		}

		// 核算指标
		for (Entry<String, List<Costindicator>> entry : costindicatorMap.entrySet()) {
			String key = entry.getKey();
			List<Costindicator> value = entry.getValue();
			List<TreeVo> list = new ArrayList<TreeVo>();
			for (Costindicator bean : value) {
				TreeVo treeVo = new TreeVo();
				treeVo.setId(bean.getId());
				treeVo.setFType("param");
				// 寻找到公式
				if (costItemFormula_Map.containsKey(bean.getId())) {
					List<CostItemFormula> costItemFormula = costItemFormula_Map.get(bean.getId());
					if (costItemFormula.size() > 0) {
						CostItemFormula _formulaBean = costItemFormula.get(0);
						// 赋值
						setTreeVo(treeVo, _formulaBean);
					}
				}
				treeVo.setPid(bean.getId());// 核算对象-核算指标
//				treeVo.setCostitemName("核算指标");
				treeVo.setName(bean.getCpname());
				treeVo.setTypeCode("99");
				list.add(treeVo);
			}
			// 增加自定义
			TreeMap.put(key, list);
		}

		// 整合树形
		List<TreeVo> list = new ArrayList<TreeVo>();
		for (Costuint costuint : costuints) {
			String id = costuint.getId();
			TreeVo treeVo = new TreeVo();
			treeVo.setId(id);
			treeVo.setName(costuint.getName());
			treeVo.setTypeCode("0");
			if (TreeMap.containsKey(id)) {
				List<TreeVo> children = TreeMap.get(id);
				// 硬性增加
				TreeVo _treeVo = new TreeVo();
				_treeVo.setId(costuint.getId() + "param");
				_treeVo.setPid(costuint.getId());
				_treeVo.setName("核算指标");
				_treeVo.setTypeCode("98");
				children.add(_treeVo);
				treeVo.setChildren(children);
				if (children != null) {
					getTreeData(children, TreeMap);
				}
			}
			list.add(treeVo);
		}
		return list;
	}

	/**
	 * 将公式赋值刀treeVo中
	 * 
	 * @param treeVo
	 * @param _formulaBean
	 */
	private void setTreeVo(TreeVo treeVo, CostItemFormula _formulaBean) {
		String fId = _formulaBean.getId();
		String fType = _formulaBean.getFType();
		String formula = _formulaBean.getFormula();
		String cFormula = _formulaBean.getCFormula();
		treeVo.setFId(fId);
		treeVo.setFType(fType);
		treeVo.setFormula(formula);
		treeVo.setCFormula(cFormula);
	}

	/**
	 * 递归
	 * 
	 * @param _list
	 * @param map
	 */
	private void getTreeData(List<TreeVo> _list, Map<String, List<TreeVo>> map) {
		for (TreeVo costlibraryclassVo : _list) {
			String id = costlibraryclassVo.getId();
			if (map.containsKey(id)) {
				List<TreeVo> children = map.get(id);
				if (children != null) {
					List<TreeVo> treeVos = costlibraryclassVo.getChildren();
					if (treeVos != null && treeVos.size() > 0) {
						children.addAll(0, treeVos);
					}
					costlibraryclassVo.setChildren(children);
					getTreeData(children, map);
				}
			}
		}
	}

	@Override
	public String saveData(CostItemFormulaSaveDto saveBean) {
		// 核算对象的最大版本
		String str = "";
		CostItemFormula saveObj = new CostItemFormula();
		ObjUtils.copyTo(saveBean, saveObj);
		String unitId = saveObj.getUnitId();
		// 查询
		String pid = saveObj.getPid();
		String fType = saveObj.getFType();
		Where where = Where.create();
		where.eq(CostItemFormula::getUnitId, unitId);
		where.eq(CostItemFormula::getFType, fType);
		where.eq(CostItemFormula::getPid, pid);
		List<CostItemFormula> list = entityService.queryData(CostItemFormula.class, where, null, null);

		String id = null;
		if (list != null && list.size() > 0) {
			id = list.get(0).getId();
			saveObj.setId(id);
		}
		//公式翻译：
		String formula = translationFormula(saveBean.getCFormula());
		saveObj.setFormula(formula);
		List<CostItemFormula> inserts = new ArrayList<CostItemFormula>();
		List<CostItemFormula> updatas = new ArrayList<CostItemFormula>();
		if (id == null) {
			saveObj.setId(TMUID.getUID());
//			entityService.insert(saveObj);
			inserts.add(saveObj);
		} else {
//			entityService.update(saveObj);
			updatas.add(saveObj);
		}
		
		unitMethodService.saveDataCostItemFormula(inserts,updatas,  null);
		
		
		return str;
	}

	@Override
	public String initCostFormulaService() {
		// 单元类型
		Where where__ = Where.create();
		where__.eq(Devicetypelibrary::getTmused, 1);
		List<Devicetypelibrary> devicetypelibrarys = entityService.queryData(Devicetypelibrary.class, where__, null,
				null);
		Map<String, Devicetypelibrary> devicetypelibraryMap_ = devicetypelibrarys.stream()
				.collect(Collectors.toMap(Devicetypelibrary::getId, Function.identity()));
		// 1.核算项目分类
		Where where_ = Where.create();
		where_.eq(Costclass::getTmused, 1);
		List<Costclass> costclasss = entityService.queryData(Costclass.class, where_, null, null);
		Map<String, Costclass> costclassMap_ = costclasss.stream()
				.collect(Collectors.toMap(Costclass::getId, Function.identity()));
		// 核算对象
		List<Costuint> costUintList = costuintService.getDatas(new CostuintQueryDto());
		Map<String, Costuint> costUintMap_ = costUintList.stream()
				.collect(Collectors.toMap(Costuint::getId, Function.identity()));
		// 1.获得全部核算项目数据
		Where where = Where.create();
		where.eq(Costitem::getTmused, 1);
		List<Costitem> costitems = entityService.queryData(Costitem.class, where, null, null);
		// 核算项目
		Map<String, Costitem> costitemMap = new LinkedHashMap<String, Costitem>();
		for (Costitem bean : costitems) {
			String unitId = bean.getUnitid();// 核算对象ID
//			String itemId = bean.getItemid();//项目ID
			String itemId = bean.getId();
			String begintime = bean.getBegintime();// 版本
			String key = unitId + "_" + itemId + "_" + begintime;
			if (!costitemMap.containsKey(key)) {
				costitemMap.put(key, bean);
			}
		}
		// 2.全部核算项目，仪表数据
		Where where1 = Where.create();
		where1.eq(Costinstrument::getTmused, 1);
		List<Costinstrument> costinstruments = entityService.queryData(Costinstrument.class, where1, null, null);
		// 仪表数据
		Map<String, Costinstrument> costinstrumentMap = new LinkedHashMap<String, Costinstrument>();
		for (Costinstrument bean : costinstruments) {
			String unitId = bean.getUnitid();// 核算对象ID
			String itemId = bean.getId();// 仪表ID
			String begintime = bean.getBegintime();// 版本
			String key = unitId + "_" + itemId + "_" + begintime;
			costinstrumentMap.put(key, bean);
		}
		// 3.获得全部公式
		Where where2 = Where.create();
		List<CostItemFormula> formulas = entityService.queryData(CostItemFormula.class, where2, null, null);
		Map<String, Map<String, CostItemFormula>> costItemFormulaMap = new LinkedHashMap<String, Map<String, CostItemFormula>>();
		// 仪表消耗量
		Map<String, CostItemFormula> instrumentMap = new LinkedHashMap<String, CostItemFormula>();
		for (CostItemFormula bean : formulas) {
			String unitId = bean.getUnitId();// 核算对象ID
			String itemId = bean.getPid();// 项目ID;公式对应项目的ID：可能是成本项目ID、仪表ID、核算指标ID
			String begintime = bean.getBegintime();// 版本
			String fType = bean.getFType();// 公式类型
			String key = unitId + "_" + itemId + "_" + begintime;
			if ("dbxh".equals(fType)) {// 仪表，[单表消耗]
				instrumentMap.put(key, bean);
			} else {
				Map<String, CostItemFormula> map = new LinkedHashMap<String, CostItemFormula>();
				if (costItemFormulaMap.containsKey(key)) {
					map = costItemFormulaMap.get(key);
				}
				map.put(fType, bean);
				costItemFormulaMap.put(key, map);
			}
		}
		List<CostItemFormula> costItemFormulas = new ArrayList<CostItemFormula>();
		// 4.补充数据，进行仪表初始化
		for (Entry<String, Costinstrument> entry : costinstrumentMap.entrySet()) {
			String key = entry.getKey();
			Costinstrument value = entry.getValue();
			if (!instrumentMap.containsKey(key)) {// 没有数据，则初始化单表消耗
				Costuint costuint = costUintMap_.get(value.getUnitid());
				if (costuint != null) {
					// 单表消耗内容
					CostItemFormula itemFormula = toolService.initCostInstrumentFormula(costuint, value);
					costItemFormulas.add(itemFormula);
				}
			}
		}
		// 消耗量公式
//		entityService.insertBatch(costItemFormulas, 5000);
		unitMethodService.saveDataCostItemFormula(costItemFormulas,null,  null);
		// 2.全部核算项目，仪表数据
		List<Costinstrument> costinstruments1 = entityService.queryData(Costinstrument.class, where1, null, null);
		// 仪表数据
		Map<String, List<Costinstrument>> costinstrumentMaps = new LinkedHashMap<String, List<Costinstrument>>();
		for (Costinstrument bean : costinstruments1) {
			String unitId = bean.getUnitid();// 核算对象ID
			String itemId = bean.getPid();// 项目ID
			String begintime = bean.getBegintime();// 版本
			String key = unitId + "_" + itemId + "_" + begintime;
			List<Costinstrument> costinstruments11 = new ArrayList<Costinstrument>();
			if (costinstrumentMaps.containsKey(key)) {
				costinstruments11 = costinstrumentMaps.get(key);
			}
			costinstruments11.add(bean);
			costinstrumentMaps.put(key, costinstruments11);
		}

		// 5.补充数据，进行项目初始化
		for (Entry<String, Costitem> entry : costitemMap.entrySet()) {
			String key = entry.getKey();
			Costitem costitem = entry.getValue();
//			List<String> inserts = new ArrayList<String>();
			List<String> ftypes = new ArrayList<String>();// 项目公式使用
			ftypes.add("dh");
			ftypes.add("dwcb");
			ftypes.add("zcb");
			ftypes.add("khxhl");
			ftypes.add("khzcb");
			ftypes.add("hxl");
			if (costItemFormulaMap.containsKey(key)) {// 没有数据，则初始化单表消耗
				Map<String, CostItemFormula> map = costItemFormulaMap.get(key);
				for (Entry<String, CostItemFormula> entry2 : map.entrySet()) {
					String fType = entry2.getKey();
					if (ftypes.contains(fType)) {
						ftypes.remove(fType);
					}

				}
			}

			String unitId = costitem.getUnitid();// 核算对象ID
			String itemPid = costitem.getPid();// 项目ID;公式对应项目的ID：可能是成本项目ID、仪表ID、核算指标ID
			String itemId = costitem.getId();// 项目ID
			String begintime = costitem.getBegintime();// 版本
			String key1 = unitId + "_" + itemPid + "_" + begintime;
			// 仪表，[单表消耗]
			if (instrumentMap.containsKey(key1)) {
				instrumentMap.get(key1);
			}
			Costuint costuint = costUintMap_.get(costitem.getUnitid());
			Costclass costclass = costclassMap_.get(costitem.getPid());
			if (costuint != null) {
				Devicetypelibrary devicetypelibrary = devicetypelibraryMap_.get(costuint.getUnittype());
				if (devicetypelibrary != null) {
					String key__ = unitId + "_" + itemId + "_" + begintime;
					List<Costinstrument> costinstruments_ = costinstrumentMaps.get(key__);
					if (costinstruments_ != null && ftypes.size() > 0) {
						// 初始化核算项目
						costItemFormulas.addAll(toolService.initCostItemFormula(costclass, costuint, costitem,
								costinstruments_, devicetypelibrary, ftypes));
					}
				}
			}
		}
//		entityService.insertBatch(costItemFormulas, 2000);
		unitMethodService.saveDataCostItemFormula(costItemFormulas,null,  null);
		return null;
	}
	
	
	/**
	 * 翻译
	 */
	private String translationFormula(String cformula) {
		//TODO:替换公式只能替换一次,保证替换数据ID不被随机【核算参数】替换
		SysUser user = SysUserHolder.getCurrentUser();
		String userId = user.getId();// 人员ID
		String updateKey = "updateKey_"+userId;
		Map<String, String> beanVos = redisUtil.getObject(updateKey);
		
        List<FormulaSort> formulaSorts = new ArrayList<FormulaSort>();
        		
		for (Entry<String, String> beanVo : beanVos.entrySet()) {
			String key = beanVo.getKey();
			String value = beanVo.getValue();
			cformula = cformula.replace(value, key);
			if(cformula.indexOf(value)>-1) {
				//记录被替换数据
				FormulaSort formulaSort = new FormulaSort(key,value,value.length());
				formulaSorts.add(formulaSort);
			}
		}
		
		 // 使用 Comparable 自定的规则进行排序
        Collections.sort(formulaSorts);
		//替换
		for (FormulaSort formulaSort : formulaSorts) {
			String key = formulaSort.getKey();
			String value = formulaSort.getValue();
			if(cformula.indexOf(value)>-1) {
				cformula = cformula.replace(value, key);
			}
		}
		
		return cformula;
	}

}
