package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.accountTools.service.IAccountToolsService;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostclassService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.paramDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitversion;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BeanVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampledotVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostFormulaService;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.ICostitemService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitConfService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.entity.po.TdsAccountMeter;
import com.yunhesoft.system.tds.entity.vo.TdsAccountMeterVo;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;


@Service
public class UnitConfServiceImpl implements UnitConfService {
	
	@Autowired
	EntityService entityService;
	
	@Autowired
	ICostclassService costclassService;
	
	@Autowired
	ICostitemService costitemService;
	
//	@Autowired
//	private IUnitInterfaceService interfaceService;
	
	@Autowired
	private ICostToolService costToolService;
	
	@Autowired
	private IUnitMethodService methodService;
	
	@Autowired
	private ICostFormulaService formulaService;
	
	@Autowired
	private IDataSourceAccountService accountServ; // 台账服务
	
	@Autowired
	private ICostService costService;
	
	@Autowired
    private IAccountToolsService accountToolsService;
	

	@Override
	public CostitemVo loadTree(paramDto dto) {
		
		String unitCode = dto.getUnitcode();
		String version = dto.getVersion();
		
		Map<String, List<CostitemVo>> cmap = new HashMap<String, List<CostitemVo>>();
		
		//核算分类信息
        MethodQueryDto classDto = new MethodQueryDto();
        classDto.setUnitid(unitCode);
        classDto.setBegintime(version);
        List<Costclass> clist = methodService.getCostclassList(classDto);
        if(clist!=null&&clist.size()==0) { // 没有分类
        	List<Costclass> costclass_list = new ArrayList<Costclass>();
        	List<BeanVo> classTypeList = methodService.getCostClassTypeList();
        	if(StringUtils.isNotEmpty(classTypeList)) {
        		for (int i = 0; i < classTypeList.size(); i++) {
        			String classType = classTypeList.get(i).getKey();
            		// 默认新增分类【按照分类类型生成】
                	Costclass classObj = new Costclass();
                	classObj.setId(TMUID.getUID());
                	classObj.setPid("root");
                	classObj.setCcname(classType);
                	classObj.setMemo("");
                	classObj.setCctype(classType);
                	classObj.setUnitid(unitCode);
                	classObj.setBegintime(version);
                	classObj.setTmused(1);
                	classObj.setTmsort(i+1);
                	costclass_list.add(classObj);
				}
        	}
        	if(StringUtils.isNotEmpty(costclass_list)) { // 保存分类
            	MethodSaveDto classSaveDto = new MethodSaveDto();
            	classSaveDto.setEditType("save");
            	classSaveDto.setUnitid(unitCode);
            	classSaveDto.setBegintime(version);
            	classSaveDto.setCostclass_list(costclass_list);
            	String ret = methodService.saveCostclassData(classSaveDto);
            	if(StringUtils.isEmpty(ret)) { // 保存成功
            		clist.addAll(costclass_list);
            	}
        	}
        }
		
        if(StringUtils.isNotEmpty(clist)) {
        	for (Costclass obj : clist) {
    			String pid = obj.getPid();
    			CostitemVo item = new CostitemVo();
    			item.setNodeTypeMark(1);//分类标识
    			item.setId(obj.getId());
    			item.setNameStr(obj.getCcname());
    			item.setPid(pid);
    			item.setUnitid(obj.getUnitid());
    			item.setBegintime(obj.getBegintime());
    			item.setClassType(obj.getCctype());
    			item.setMemo(obj.getMemo());
    			if(cmap.containsKey(pid)) {
    				cmap.get(pid).add(item);
    			}else {
    				List<CostitemVo> tlist = new ArrayList<CostitemVo>();
    				tlist.add(item);
    				cmap.put(pid, tlist);
    			}
    		}
        }
		
		//核算项目信息
		Map<String, List<CostitemVo>> imap = new HashMap<String, List<CostitemVo>>();
		List<CostitemVo> ilist = costitemService.getDatas(dto);
		for (CostitemVo obj : ilist) {
			obj.setNodeTypeMark(2);//项目标识
			obj.setNameStr(obj.getItemname());
			if(imap.containsKey(obj.getPid())) {
				imap.get(obj.getPid()).add(obj);
			}else {
				List<CostitemVo> tlist = new ArrayList<CostitemVo>();
				tlist.add(obj);
				imap.put(obj.getPid(), tlist);
			}
		}
		
		//仪表采集点信息
		Map<String, List<CostitemVo>> dmap = new HashMap<String, List<CostitemVo>>();
		Where whered = Where.create();
		whered.eq(Costinstrument::getTmused, 1);
		whered.eq(Costinstrument::getBegintime, version);
		whered.eq(Costinstrument::getUnitid, unitCode);
        Order orderd = Order.create();
        orderd.orderByAsc(Costinstrument::getTmsort);
        List<Costinstrument> dList = entityService.queryList(Costinstrument.class, whered, orderd);
        if(StringUtils.isNotEmpty(dList)) {
        	//获取采集点名称
    		//Map<String, Costunitsampledot> sampledotMap = interfaceService.getSampledotMap(unitCode, version);
        	for (Costinstrument obj : dList) {
//        		String dotid = obj.getDotid();
//        		if(StringUtils.isNotEmpty(dotid)&&StringUtils.isNotEmpty(sampledotMap)&&sampledotMap.containsKey(dotid)) {
//        			obj.setName(sampledotMap.get(dotid).getName());
//        		}
            	CostitemVo vo = ObjUtils.copyTo(obj, CostitemVo.class);
            	vo.setNameStr(obj.getName());
    			vo.setNodeTypeMark(3);
    			if(dmap.containsKey(obj.getPid())) {
    				dmap.get(obj.getPid()).add(vo);
    			}else {
    				List<CostitemVo> tlist = new ArrayList<CostitemVo>();
    				tlist.add(vo);
    				dmap.put(obj.getPid(), tlist);
    			}
    		}
        }
        
		List<CostitemVo> rlist = cmap.get("root");//获取分类节点
		//建立虚拟根节点
		CostitemVo rootNode = new CostitemVo();
		rootNode.setId("root");
		rootNode.setNameStr("分类信息");
		rootNode.setPid("root");
		rootNode.setNodeTypeMark(0);
		if(StringUtils.isNotEmpty(rlist)) {//构建分类、项目、仪表树形
			rootNode.setChildren(rlist);
			for (CostitemVo node : rlist) {
				setNodeData(node, cmap, imap, dmap);
			}
		}else {
			rootNode.setChildren(new ArrayList<CostitemVo>());
		}

		//增加虚拟核算指标
		CostitemVo indexNode = new CostitemVo();
		indexNode.setId("indexNodeId");
		indexNode.setPid("root");
		indexNode.setNameStr("核算指标");
		indexNode.setNodeTypeMark(98);//核算指标
		indexNode.setTmsort(99999);
		indexNode.setTmused(1);
		//获取指标信息
		Where wherei = Where.create();
        wherei.eq(Costindicator::getTmused, 1);
        wherei.eq(Costindicator::getBegintime, version);
        wherei.eq(Costindicator::getUnitid, unitCode);
        Order orderi = Order.create();
        orderi.orderByAsc(Costindicator::getTmsort);
        List<Costindicator> indexList = entityService.queryList(Costindicator.class, wherei, orderi);
		
		if(StringUtils.isNotEmpty(indexList))  {//构建核算指标及指标树形
			List<CostitemVo> tlist = new ArrayList<CostitemVo>();
			for (Costindicator obj : indexList) {
				CostitemVo vo = new CostitemVo();
				vo.setId(obj.getId());
				vo.setNameStr(obj.getCpname());
				vo.setNodeTypeMark(99);
				vo.setPid("indexNodeId");
				tlist.add(vo);
			}
			indexNode.setChildren(tlist);
		}else {
			indexNode.setChildren(new ArrayList<CostitemVo>());
		}
		rootNode.getChildren().add(indexNode);
		costToolService.CostitemVo(rootNode);
		return rootNode;
	}
	
	//数据整理(树）
    private void setNodeData(CostitemVo node, Map<String, List<CostitemVo>> cmap, Map<String, List<CostitemVo>> imap) {
    	
    	List<CostitemVo> nodeList = new ArrayList<CostitemVo>();//cmap.get(node.getId());
    	
    	List<CostitemVo> clist = cmap.get(node.getId());
    	List<CostitemVo> ilist = imap.get(node.getId());
    	
    	if(StringUtils.isNotEmpty(clist)) {
    		nodeList.addAll(clist);
    	}
    	if(StringUtils.isNotEmpty(ilist)) {
    		nodeList.addAll(ilist);
    	}
    	
        List<CostitemVo> children = new ArrayList<CostitemVo>();
        
        for (CostitemVo data : nodeList) {
        	CostitemVo rn = data;
            children.add(rn);
            setNodeData(rn, cmap, imap);
        }
        if (!children.isEmpty()) {
            node.setChildren(children);
        }
    }
    
  //数据整理(树）
    private void setNodeData(CostitemVo node, Map<String, List<CostitemVo>> cmap, Map<String, List<CostitemVo>> imap, Map<String, List<CostitemVo>> dmap) {
    	
    	List<CostitemVo> nodeList = new ArrayList<CostitemVo>();//cmap.get(node.getId());
    	
    	List<CostitemVo> clist = cmap.get(node.getId());
    	List<CostitemVo> ilist = imap.get(node.getId());
    	List<CostitemVo> dlist = dmap.get(node.getId());
    	
    	if(StringUtils.isNotEmpty(clist)) {
    		nodeList.addAll(clist);
    	}
    	if(StringUtils.isNotEmpty(ilist)) {
    		nodeList.addAll(ilist);
    	}
    	if(StringUtils.isNotEmpty(dlist)) {
    		nodeList.addAll(dlist);
    	}
    	
        List<CostitemVo> children = new ArrayList<CostitemVo>();
        
        for (CostitemVo data : nodeList) {
        	CostitemVo rn = data;
            children.add(rn);
            setNodeData(rn, cmap, imap, dmap);
        }
        if (!children.isEmpty()) {
            node.setChildren(children);
        }
    }

	@Override
	public CostitemVo saveNode(paramDto dto) {
		CostitemVo vo = new CostitemVo();
		
		String op = dto.getOp();
		String id = dto.getId();
		String type = dto.getType();
		if(StringUtils.isNotEmpty(id)) {//更新
			if("1".equals(type)) {//分类
				Costclass obj = entityService.queryObjectById(Costclass.class, id);
				
				if("del".equals(op)) {
					obj.setTmused(0);
				}else {
					obj.setCcname(dto.getName());
					obj.setCctype(dto.getClassType());
					obj.setMemo(dto.getMemo());
				}
				List<Costclass> updList = new ArrayList<Costclass>();
				updList.add(obj);
				methodService.saveDataCostclass(null, updList, null);
				
				vo = ObjUtils.copyTo(obj, CostitemVo.class);
				vo.setNameStr(obj.getCcname());
				vo.setNodeTypeMark(1);
			}else if("2".equals(type)) {//项目
				Costitem obj = entityService.queryObjectById(Costitem.class, id);
				if(obj!=null) {
					if("del".equals(op)) {
						obj.setTmused(0);
					}else {
						obj.setItemname(dto.getName());
					}
					vo = ObjUtils.copyTo(obj, CostitemVo.class);
					vo.setNameStr(obj.getItemname());
				}
				vo = ObjUtils.copyTo(obj, CostitemVo.class);
				vo.setNameStr(obj.getItemname());
				vo.setNodeTypeMark(2);
				costitemService.save(vo);
			}else if("3".equals(type)) {//仪表
				Costinstrument obj = entityService.queryObjectById(Costinstrument.class, id);
				if("del".equals(op)) {
					obj.setTmused(0);
				}else {
					obj.setName(dto.getName());
				}
				List<Costinstrument> updList = new ArrayList<Costinstrument>();
				updList.add(obj);
				methodService.saveDataCostinstrument(null, updList, null);
				vo = ObjUtils.copyTo(obj, CostitemVo.class);
				vo.setNameStr(obj.getName());
				vo.setNodeTypeMark(3);
				if("del".equals(op)) { //删除规定时间
					methodService.deleteCostStipulateTimeByPid(obj.getId());
				}
			}else if("99".equals(type)) {//指标
				Costindicator obj = entityService.queryObjectById(Costindicator.class, id);
				if("del".equals(op)) {
					obj.setTmused(0);
				}else {
					obj.setCpname(dto.getName());
				}
				List<Costindicator> updList = new ArrayList<Costindicator>();
				updList.add(obj);
				methodService.saveDataCostindicator(null, updList, null);
				vo = ObjUtils.copyTo(obj, CostitemVo.class);
				vo.setNameStr(obj.getCpname());
				vo.setNodeTypeMark(99);
			}
		}else {//新增
			if("1".equals(type)) {//分类
				Costclass obj = new Costclass();
				obj.setId(TMUID.getUID());
				obj.setPid(dto.getPid());
				obj.setCcname(dto.getName());
				obj.setCctype(dto.getClassType());
				obj.setMemo(dto.getMemo());
				obj.setUnitid(dto.getUnitcode());
				obj.setBegintime(dto.getVersion());
				obj.setTmused(1);
				int tmsort = costToolService.maxSprt(Costclass.class);
				obj.setTmsort(tmsort);
				List<Costclass> addList = new ArrayList<Costclass>();
				addList.add(obj);
				methodService.saveDataCostclass(addList, null, null);
				vo = ObjUtils.copyTo(obj, CostitemVo.class);
				vo.setNameStr(obj.getCcname());
				vo.setNodeTypeMark(1);
			}else if("2".equals(type)) {//项目
				Costitem obj = new Costitem();
				obj.setId(TMUID.getUID());
				obj.setPid(dto.getPid());
				obj.setItemname(dto.getName());
				obj.setUnitid(dto.getUnitcode());
				obj.setBegintime(dto.getVersion());
				obj.setConversionfactor(1d);
				obj.setEnergyfactor(0d);
				obj.setApportionmentfactor(0d);
				obj.setSumtype(0);
				obj.setComparetype(0);
				obj.setCalcprice(0);//
				obj.setUsecalcprice(0);
				obj.setPricefactor(1d);
				
				obj.setTmused(1);
				vo = ObjUtils.copyTo(obj, CostitemVo.class);
				vo.setNameStr(obj.getItemname());
				vo.setNodeTypeMark(2);
				costitemService.save(vo);
//			}else if("3".equals(type)) {//仪表
//				vo.setNameStr(null);
//				vo.setNodeTypeMark(3);
			}else if("99".equals(type)) {//指标
				Costindicator obj = new Costindicator();
				obj.setId(TMUID.getUID());
				obj.setCpname(dto.getName());
				obj.setUnitid(dto.getUnitcode());
				obj.setBegintime(dto.getVersion());
				obj.setComparetype(0);
				obj.setIsAnalysisItem(0);
				obj.setIsSelShow(1);
				obj.setIsUseToRecordEvent(0);
				obj.setTmused(1);
				int tmsort = costToolService.maxSprt(Costindicator.class);
				obj.setTmsort(tmsort);
				List<Costindicator> addList = new ArrayList<Costindicator>();
				addList.add(obj);
				methodService.saveDataCostindicator(addList, null, null);
				
				vo = ObjUtils.copyTo(obj, CostitemVo.class);
				
				vo.setNameStr(obj.getCpname());
				vo.setNodeTypeMark(99);
			}
		}
		return vo;
	}

	@Override
	public CostitemVo loadPotTree(paramDto dto) {
		
		String unitCode = dto.getUnitcode();
		String version = dto.getVersion();
		List<SampledotVo> dotList = dto.getDotList(); //传入空数组时，此属性返回采集点数据；传入null时，此属性不返回采集点数据；
		
		Map<String, List<CostitemVo>> cmap = new HashMap<String, List<CostitemVo>>();
		
		Where where = Where.create();
        where.eq(Costunitsampleclass::getTmused, 1);
        where.eq(Costunitsampleclass::getBegintime, version);
        where.eq(Costunitsampleclass::getUnitid, unitCode);
        Order order = Order.create();
        order.orderByAsc(Costunitsampleclass::getTmsort);
        List<Costunitsampleclass> clist = entityService.queryList(Costunitsampleclass.class, where, order);
        
        for (Costunitsampleclass obj : clist) {
			String pid = obj.getPid();
			CostitemVo item = new CostitemVo();
			item.setNodeTypeMark(1);//分类标识
			item.setId(obj.getId());
			item.setNameStr(obj.getName());
			item.setPid(pid);
			item.setUnitid(obj.getUnitid());
			item.setBegintime(obj.getBegintime());
			item.setTmsort(obj.getTmsort());
			if(cmap.containsKey(pid)) {
				cmap.get(pid).add(item);
			}else {
				List<CostitemVo> tlist = new ArrayList<CostitemVo>();
				tlist.add(item);
				cmap.put(pid, tlist);
			}
		}
        
        Map<String, List<CostitemVo>> imap = new HashMap<String, List<CostitemVo>>();
        Where wheredot = Where.create();
        wheredot.eq(Costunitsampledot::getTmused, 1);
        wheredot.eq(Costunitsampledot::getBegintime, version);
        wheredot.eq(Costunitsampledot::getUnitid, unitCode);
        wheredot.ne(Costunitsampledot::getCtype, "1"); //非成本仪表（采集类型）
        Order orderdot = Order.create();
        orderdot.orderByAsc(Costunitsampledot::getTmsort);
        List<Costunitsampledot> dlist = entityService.queryList(Costunitsampledot.class, wheredot, orderdot);
        if(StringUtils.isNotEmpty(dlist)) {
        	List<SampledotVo> dotVoList = new ArrayList<SampledotVo>(); //临时存储返回的采集点数据
        	for (Costunitsampledot obj : dlist) {
    			CostitemVo vo = ObjUtils.copyTo(obj, CostitemVo.class);
    			vo.setNodeTypeMark(3);//
    			vo.setNameStr(obj.getName());
    			if(imap.containsKey(obj.getPid())) {
    				imap.get(obj.getPid()).add(vo);
    			}else {
    				List<CostitemVo> tlist = new ArrayList<CostitemVo>();
    				tlist.add(vo);
    				imap.put(obj.getPid(), tlist);
    			}
    			//暂存采集点记录，调用接口可能会使用（替换对象-全属性）
    			if(dotList!=null&&dotList.size()==0) {
    				SampledotVo dotVo = ObjUtils.copyTo(obj, SampledotVo.class);
        			dotVoList.add(dotVo);
    			}
    		}
        	if(StringUtils.isNotEmpty(dotVoList)) {
        		dto.setDotList(dotVoList);
        	}
        }
        
        List<CostitemVo> rlist = cmap.get("root");//获取分类节点
		//建立虚拟根节点
		CostitemVo rootNode = new CostitemVo();
		rootNode.setId("root");
		rootNode.setNameStr("采集点分类");
		rootNode.setPid("root");
		rootNode.setNodeTypeMark(0);
		if(StringUtils.isNotEmpty(rlist)) {
			rootNode.setChildren(rlist);
			for (CostitemVo node : rlist) {
				setNodeData(node, cmap, imap);
			}
		}
		
		costToolService.CostitemVo(rootNode);
		return rootNode;
	}

	@Override
	public SampledotVo savePotNode(paramDto dto) {
		SampledotVo vo = new SampledotVo();
		
		String op = dto.getOp();
		String id = dto.getId();
		String type = dto.getType();
		if(StringUtils.isNotEmpty(id)) {//更新
			if("1".equals(type)) {//分类
				Costunitsampleclass obj = entityService.queryObjectById(Costunitsampleclass.class, id);
				
				if("del".equals(op)) {
					obj.setTmused(0);
				}else {
					obj.setName(dto.getName());
				}
				List<Costunitsampleclass> updList = new ArrayList<Costunitsampleclass>();
				updList.add(obj);
				methodService.saveSampleclassData(null, updList, null);
				
				vo = ObjUtils.copyTo(obj, SampledotVo.class);
				vo.setNodeTypeMark(1);
				vo.setNameStr(obj.getName());
			}else if("3".equals(type)) {//仪表
				List<Costunitsampledot> synR3dbList = new ArrayList<Costunitsampledot>();
				Costunitsampledot obj = entityService.queryObjectById(Costunitsampledot.class, id);
				//获取同步r3db的采集点数据
				Costunitsampledot savedot = new Costunitsampledot();
				if("del".equals(op)) {
					savedot.setTmused(0);
				}else {
					savedot.setName(dto.getName());
					savedot.setDatasource(obj.getDatasource());
					savedot.setTmused(obj.getTmused());
					savedot.setCtype(obj.getCtype());
				}
				methodService.getSynR3dbSampledotData(obj, savedot, synR3dbList, null);
				
				if("del".equals(op)) {
					obj.setTmused(0);
				}else {
					obj.setName(dto.getName());
				}
				List<Costunitsampledot> updList = new ArrayList<Costunitsampledot>();
				updList.add(obj);
				methodService.saveSampledotData(null, updList, null);
				vo = ObjUtils.copyTo(obj, SampledotVo.class);
				vo.setNodeTypeMark(3);
				vo.setNameStr(obj.getName());
				if("del".equals(op)) { //删除规定时间
					methodService.deleteCostStipulateTimeByPid(obj.getId());
				}
				
				//调用同步其他数据的接口
				List<Costunitsampledot> synOtherList = new ArrayList<Costunitsampledot>();
				synOtherList.add(obj);
				methodService.synOtherInterfaceBySampledotChange(null, synOtherList);
				//同步R3DB仪表数据
				if(StringUtils.isNotEmpty(synR3dbList)) {
					methodService.synR3dbDataBySampledot(synR3dbList, null);
				}
			}
		}else {//新增
			if("1".equals(type)) {//分类
				Costunitsampleclass obj = new Costunitsampleclass();
				obj.setId(TMUID.getUID());
				obj.setPid(dto.getPid());
				obj.setName(dto.getName());
				obj.setUnitid(dto.getUnitcode());
				obj.setBegintime(dto.getVersion());
				obj.setTmused(1);
				int tmsort = costToolService.maxSprt(Costunitsampleclass.class);
				obj.setTmsort(tmsort);
				List<Costunitsampleclass> addList = new ArrayList<Costunitsampleclass>();
				addList.add(obj);
				methodService.saveSampleclassData(addList, null, null);
				
				vo = ObjUtils.copyTo(obj, SampledotVo.class);
				vo.setNodeTypeMark(1);
				vo.setNameStr(obj.getName());
			}else if("3".equals(type)) {//仪表
				Costunitsampledot obj = new Costunitsampledot();
				BeanUtils.copyProperties(dto, obj); // 赋予返回对象
				obj.setId(TMUID.getUID());
				obj.setUnitid(dto.getUnitcode());
				obj.setBegintime(dto.getVersion());
				obj.setTmused(1);
				int tmsort = costToolService.maxSprt(Costunitsampledot.class);
				obj.setTmsort(tmsort);
				List<Costunitsampledot> addList = new ArrayList<Costunitsampledot>();
				addList.add(obj);
				methodService.saveSampledotData(addList, null, null);
				
				vo = ObjUtils.copyTo(obj, SampledotVo.class);
				
				vo.setNodeTypeMark(3);
				vo.setNameStr(obj.getName());
				
				//调用同步其他数据的接口
				List<Costunitsampledot> synOtherList = new ArrayList<Costunitsampledot>();
				synOtherList.add(obj);
				methodService.synOtherInterfaceBySampledotChange(synOtherList, null);
				//同步R3DB仪表数据
				List<Costunitsampledot> synR3dbList = new ArrayList<Costunitsampledot>();
				methodService.getSynR3dbSampledotData(obj, null, synR3dbList, null);
				if(StringUtils.isNotEmpty(synR3dbList)) {
					methodService.synR3dbDataBySampledot(synR3dbList, null);
				}
			}
		}
		return vo;
	}
	
	//数据整理(树）
//    private void setNodeData(CostitemVo node, Map<String, List<CostitemVo>> cmap) {
//    	
//    	List<CostitemVo> nodeList = new ArrayList<CostitemVo>();//cmap.get(node.getId());
//    	
//    	List<CostitemVo> clist = cmap.get(node.getId());
//    	
//    	if(StringUtils.isNotEmpty(clist)) {
//    		nodeList.addAll(clist);
//    	}
//    	
//        List<CostitemVo> children = new ArrayList<CostitemVo>();
//        
//        for (CostitemVo data : nodeList) {
//        	CostitemVo rn = data;
//            children.add(rn);
//            setNodeData(rn, cmap);
//        }
//        if (!children.isEmpty()) {
//            node.setChildren(children);
//        }
//    }

	@Override
	public List<CostitemVo> getDataList(paramDto dto) {
		String unitCode = dto.getUnitcode();
		String sendVersion = dto.getVersion();
		Date sendDt = DateTimeUtils.parseD(sendVersion, DateTimeUtils.DateFormat_YMD);
		String version = "";
		
		List<CostitemVo> rlist = new ArrayList<CostitemVo>();
		
		Where wherever = Where.create();
		wherever.eq(Costunitversion::getUnitid, unitCode);
        Order orderver = Order.create();
        orderver.orderByDesc(Costunitversion::getBegintime);
        List<Costunitversion> vlist = entityService.queryList(Costunitversion.class, wherever, orderver);
        //从小到大日期
        for (Costunitversion ver : vlist) {
			String t = ver.getBegintime();
			Date dt = DateTimeUtils.parseD(t, DateTimeUtils.DateFormat_YMD);
			if(DateTimeUtils.bjDate(dt, sendDt) >= 0) {
				version = DateTimeUtils.formatDate(dt, DateTimeUtils.DateFormat_YMD);
				break;
			}
		}
        
        if("".equals(version)) {
        	if(StringUtils.isNotEmpty(vlist)) {
        		version = vlist.get(0).getBegintime();
        	}
        }
        
        if("".equals(version)) {
        	return rlist;
        }
		
        //获取分类map信息
		Map<String, String> cmap = new HashMap<String, String>();
		Where where = Where.create();
        where.eq(Costclass::getTmused, 1);
        where.eq(Costclass::getBegintime, version);
        where.eq(Costclass::getUnitid, unitCode);
        Order order = Order.create();
        order.orderByAsc(Costclass::getTmsort);
        List<Costclass> clist = entityService.queryList(Costclass.class, where, order);
		
		for (Costclass obj : clist) {
			String id = obj.getId();
			cmap.put(id, obj.getCcname());
		}
		
		//获取项目信息
		dto.setVersion(version);//更新版本日期
//		Map<String, List<CostitemVo>> imap = new HashMap<String, List<CostitemVo>>();
		List<CostitemVo> ilist = costitemService.getDatas(dto);
		for (CostitemVo obj : ilist) {
			obj.setNodeTypeMark(2);//项目标识
			obj.setNameStr(obj.getItemname());
			
			obj.setParentName(cmap.get(obj.getPid()));//父分类名称（目前项目无父项目）
			rlist.add(obj);
		}
		
		//获取指标信息
		Where wherei = Where.create();
        wherei.eq(Costindicator::getTmused, 1);
        wherei.eq(Costindicator::getBegintime, version);
        wherei.eq(Costindicator::getUnitid, unitCode);
        Order orderi = Order.create();
        orderi.orderByAsc(Costindicator::getTmsort);
        List<Costindicator> indexList = entityService.queryList(Costindicator.class, wherei, orderi);
		
		for (Costindicator obj : indexList) {
			CostitemVo vo = ObjUtils.copyTo(obj, CostitemVo.class);
			vo.setNodeTypeMark(99);//指标标识
			vo.setNameStr(obj.getCpname());
			vo.setParentName("");
			rlist.add(vo);
		}
		
		return rlist;
	}

	@Override
	public List<CostitemVo> saveDotNodes(paramDto dto) {
		List<CostitemVo> rlist = new ArrayList<CostitemVo>();
		
		String unitCode = dto.getUnitcode();
		String version = dto.getVersion();
		
		List<SampledotVo> volist = dto.getDotList();
		
		List<Costinstrument> addList = new ArrayList<Costinstrument>();
		int tmsort = costToolService.maxSprt(Costinstrument.class);
		for (SampledotVo vo : volist) {
			Costinstrument obj = new Costinstrument();
			obj.setId(TMUID.getUID());
			obj.setDotid(vo.getId());
			obj.setName(vo.getName());
			obj.setTagnumber(vo.getTagnumber()); //仪表位号
			obj.setInstrumentType(vo.getInstrumentType()); //仪表类型
			obj.setShowInShiftWrite(vo.getShowInShiftWrite()); //录入时显示
			obj.setIsIntervalUse(vo.getIsIntervalUse()); //间隔使用
			
			obj.setPid(dto.getPid());
			obj.setUnitid(unitCode);
			obj.setBegintime(version);
			obj.setTmused(1);
			obj.setTmsort(tmsort);
			addList.add(obj);
			tmsort++;
		}
		
		int jg = 0;
		if(StringUtils.isNotEmpty(addList)) {
			jg = entityService.insertBatch(addList);
		}
		if(1 == jg) {
			for (Costinstrument obj : addList) {
				CostitemVo rv = ObjUtils.copyTo(obj, CostitemVo.class);
				rv.setNameStr(obj.getName());
				rv.setNodeTypeMark(Integer.valueOf(dto.getType()));
				rlist.add(rv);
			}
			try { //新增仪表成功后，调用初始化公式接口（赵维民）
				formulaService.initCostinstrument(addList);
			}catch(Exception e) {
				e.printStackTrace();
			}
		}
		
		return rlist;
	}
	
	
	/**
	 *	保存项目数据
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveCostitemData(MethodSaveDto saveDto) {
		String result = "";
		if (saveDto != null) {
			String editType = saveDto.getEditType();
			String unitid = saveDto.getUnitid();
			String begintime = saveDto.getBegintime();
			List<Costitem> saveList = saveDto.getCostitem_list();
			boolean saveInstrumentByItem = saveDto.getSaveInstrumentByItem();
			if (StringUtils.isNotEmpty(editType) && StringUtils.isNotEmpty(unitid) && StringUtils.isNotEmpty(begintime)
					&& StringUtils.isNotNull(saveList)) {
				for (int i = 0; i < saveList.size(); i++) {
					Costitem itemObj = saveList.get(i);
					String id = itemObj.getId();
					if(StringUtils.isEmpty(id)) {
						itemObj.setId(TMUID.getUID());
					}
					String itemid = itemObj.getItemid();
					if(StringUtils.isEmpty(itemid)) { //新增项目时，会出现空值（单条数据）
						//项目id为空，即新增项目；调用接口保存项目到项目库，并返回项目id
						//未开发
					}
				}
				saveDto.setCostitem_list(saveList);
				String itemRet = methodService.saveCostitemData(saveDto);
				if(StringUtils.isEmpty(itemRet)) {
					try { //新增项目成功后，调用初始化公式接口（赵维民）
						formulaService.initCostitem(saveList);
					}catch(Exception e) {
						e.printStackTrace();
					}
				}
				if("save".equals(editType)&&StringUtils.isEmpty(itemRet)&&saveInstrumentByItem) { //项目保存成功，并且要保存同名采集点
					List<Costinstrument> addInstrumentList = new ArrayList<Costinstrument>();
					for (int i = 0; i < saveList.size(); i++) {
						Costitem itemObj = saveList.get(i);
						String itemId = itemObj.getId();
						String itemName = itemObj.getItemname();
						//新增同名采集点(成本项目仪表)
						Costinstrument instrumentObj = new Costinstrument();
						instrumentObj.setId(TMUID.getUID());
						instrumentObj.setPid(itemId);
						instrumentObj.setName(itemName);
						instrumentObj.setTagnumber(itemName);
						instrumentObj.setBegintime(begintime);
						instrumentObj.setUnitid(unitid);
						instrumentObj.setTmused(1);
						instrumentObj.setTmsort(1+i);
						addInstrumentList.add(instrumentObj);
					}
					if(StringUtils.isNotEmpty(addInstrumentList)) {
						saveDto.setCostinstrument_list(addInstrumentList);
						itemRet = methodService.saveCostinstrumentData(saveDto);
						if(StringUtils.isEmpty(itemRet)) {
							try { //新增仪表成功后，调用初始化公式接口（赵维民）
								formulaService.initCostinstrument(addInstrumentList);
							}catch(Exception e) {
								e.printStackTrace();
							}
						}
					}
				}
				result = itemRet;
			}
		}
		return result;
	}
	
	/**--台账数据源使用--------------------------------------------------------------------**/
	
	/**
	 * @category 获取对应核算对象/机构的所有仪表
	 */
	@Override
	public List<SampledotVo> getUnitDotList(paramDto dto) {
		String sendVersion = dto.getVersion();//版本日期，如果未传递，取当前时间
		Date sendDt = DateTimeUtils.parseD(DateTimeUtils.getNowDateStr(), DateTimeUtils.DateFormat_YMD);
		if(StringUtils.isNotEmpty(sendVersion)) {
			sendDt = DateTimeUtils.parseD(sendVersion, DateTimeUtils.DateFormat_YMD);
		}
		return getUnitDotList(dto, sendDt);
	}

	/**
	 * @category 获取对应核算对象/机构的所有仪表
	 * @param unitCode 核算对象id，多个用逗号分割
	 * @param sendDt 时间版本
	 * @return
	 */
	public List<SampledotVo> getUnitDotList(paramDto dto, Date sendDt) {
		//String unitCode, Date sendDt, String name, String tdsAlias, String confCode, String showMark
		String tdsAlias = dto.getTdsAlias();//数据源别名
		String confCode = dto.getConfCode();//顶部核算对象
		
		Integer mode = dto.getMode();//1核算对象 2机构
		String unitCode = dto.getUnitcode();//考核单元代码
		String orgCode = dto.getOrgCode();//机构代码
		String useStatus = dto.getUseStatus();//使用状态 1显示 2不显示
		String tagName = dto.getTagName();//仪表名称
		
		String ucode = new Integer(1).equals(mode)?confCode:unitCode;
		
		//获取配置编码，用于查询台账设置仪表信息
		if(new Integer(2).equals(mode)) {
			confCode = orgCode;
		}
		
		List<SampledotVo> rlist = null;
		List<SampledotVo> dlist = null;
		
//		String unitCode = dto.getUnitcode();//考核单元代码
//		String sendVersion = dto.getVersion();//版本日期，如果未传递，取当前时间
//		Date sendDt = DateTimeUtils.parseD(DateTimeUtils.getNowDateStr(), DateTimeUtils.DateFormat_YMD);
//		if(StringUtils.isNotEmpty(sendVersion)) {
//			sendDt = DateTimeUtils.parseD(sendVersion, DateTimeUtils.DateFormat_YMD);
//		}
		
		//提取已设置的核算对象仪表数据，进行过滤操作，只获取相同仪表位号的数据
		List<TdsAccountMeter> confList = accountServ.getAccountMeterList(tdsAlias, confCode, null);
		
		//没设置过，查不显示的，直接返回空
		if(StringUtils.isEmpty(confList) && "2".equals(useStatus)) {
			return new ArrayList<SampledotVo>();
		}
		
		// 提取对应核算对象的所有仪表
		if(new Integer(1).equals(mode) || (new Integer(2).equals(mode) && StringUtils.isNotEmpty(unitCode))) {//选择指定考核单元
			
			String version = "";
			Where wherever = Where.create();
			wherever.eq(Costunitversion::getUnitid, ucode);
			Order orderver = Order.create();
			orderver.orderByDesc(Costunitversion::getBegintime);
			List<Costunitversion> vlist = entityService.queryList(Costunitversion.class, wherever, orderver);
			
			//从大到小日期
			for (Costunitversion ver : vlist) {
				String t = ver.getBegintime();
				Date dt = DateTimeUtils.parseD(t, DateTimeUtils.DateFormat_YMD);
				if(DateTimeUtils.bjDate(dt, sendDt) <= 0) {
					version = DateTimeUtils.formatDate(dt, DateTimeUtils.DateFormat_YMD);
					break;
				}
			}
			if("".equals(version)) {
				if(StringUtils.isNotEmpty(vlist)) {
					version = vlist.get(0).getBegintime();
				}
			}
			
//			dlist = getOneUnitDotList(ucode, version, tagName);
			dlist = getUnitCostClass(ucode, version, tagName);
			
		}else {//所属机构的全部考核单元
			if(StringUtils.isEmpty(orgCode)) {
				return rlist;
			}
			
			List<String> unitList = new ArrayList<String>();
			unitList.add("");
			
			List<Costuint> clist = costService.getCostuintListByOrgId(orgCode, 2);
			if(StringUtils.isNotEmpty(clist)) {
				for (Costuint obj : clist) {
					unitList.add(obj.getId());
				}
			}
			//获取各自核算单元的版本
			Map<String, String> vmap = getUnitVerMap(unitList, sendDt);
			//获取仪表列表数据
//			dlist = getManyUnitDotList(unitList, vmap, tagName);
			dlist = getUnitCostClass(unitList, vmap, tagName);
		}
		
		if(confList.size() > 0) {
			List<String> clist = new ArrayList<String>();
			confList.forEach(item -> {
				clist.add(item.getTagid());
			});
			dlist.forEach(item -> {
				item.setShowMark(clist.contains(item.getId()));
			});
			if("1".equals(useStatus)) {//显示
				rlist = new ArrayList<SampledotVo>();
				for (SampledotVo item : dlist) {
					if(item.getShowMark()) {
						rlist.add(item);
					}
				}
			}else if("2".equals(useStatus)) {//不显示
				rlist = new ArrayList<SampledotVo>();
				for (SampledotVo item : dlist) {
					if(!item.getShowMark()) {
						rlist.add(item);
					}
				}
			}else {
				rlist = dlist;
			}
		}else {
			dlist.forEach(item -> {
				item.setShowMark(true);
			});
			rlist = dlist;
		}
        
		return rlist;
	}

	/**
	 * @category 获取核算单元仪表
	 * @param unitCode
	 * @param version
	 * @return
	 */
	private List<SampledotVo> getOneUnitDotList(String unitCode, String version, String name) {
		List<SampledotVo> rlist = new ArrayList<SampledotVo>();
		Where wheredot = Where.create();
        wheredot.eq(Costunitsampledot::getTmused, 1);
        wheredot.eq(Costunitsampledot::getBegintime, version);
        wheredot.eq(Costunitsampledot::getUnitid, unitCode);
        if(StringUtils.isNotEmpty(name)) {
        	wheredot.like(Costunitsampledot::getName, name);
        }
        Order orderdot = Order.create();
        orderdot.orderByAsc(Costunitsampleclass::getTmsort);
        List<Costunitsampledot> dlist = entityService.queryList(Costunitsampledot.class, wheredot, orderdot);
        
        Costuint unit = entityService.queryObjectById(Costuint.class, unitCode);
        
        for (Costunitsampledot obj : dlist) {
        	SampledotVo vo = ObjUtils.copyTo(obj, SampledotVo.class);
        	vo.setUnitname(unit.getName());
        	rlist.add(vo);
		}
        
		return rlist;
	}
	
	/**
	 * @category 获取多个核算对象仪表信息
	 * @param unitList	核算对象Id数组
	 * @param vmap	核算对象对应版本map
	 * @param name	仪表名称
	 * @return
	 */
	private List<SampledotVo> getManyUnitDotList(List<String> unitList, Map<String, String> vmap, String name) {
		List<SampledotVo> rlist = new ArrayList<SampledotVo>();
		
		Where wheredot = Where.create();
        wheredot.eq(Costunitsampledot::getTmused, 1);
        if(StringUtils.isNotEmpty(name)) {
        	wheredot.like(Costunitsampledot::getName, name);
        }
        wheredot.and().lb();
        IntStream.range(0, unitList.size()).forEach(i -> {
        	String unitCode = unitList.get(i);
        	String version = vmap.get(unitCode);
        	if(i > 0) {
        		wheredot.or();
        	}
        	wheredot.lb();
        	wheredot.eq(Costunitsampledot::getBegintime, version);
        	wheredot.eq(Costunitsampledot::getUnitid, unitCode);
        	wheredot.rb();
        });
        wheredot.rb();
        
        Order orderdot = Order.create();
        orderdot.order(Costunitsampledot::getUnitid).orderByAsc(Costunitsampledot::getTmsort);
        List<Costunitsampledot> dlist = entityService.queryList(Costunitsampledot.class, wheredot, orderdot);//仪表数据
        
        Map<String, String> umap = new HashMap<String, String>();
        List<Costuint> ulist = entityService.queryList(Costuint.class, Where.create().in(Costuint::getId, unitList.toArray()), null);
        ulist.forEach(obj -> {
        	umap.put(obj.getId(), obj.getName());
        });
        
        for (Costunitsampledot obj : dlist) {
        	SampledotVo vo = ObjUtils.copyTo(obj, SampledotVo.class);
        	vo.setUnitname(umap.get(obj.getUnitid()));
        	rlist.add(vo);
		}
        
		return rlist;
	}

	/**
	 * @category 获取核算单元对应日期版本的对应关系
	 * @param unitList
	 * @param sendDt
	 * @return
	 */
	private Map<String, String> getUnitVerMap(List<String> unitList, Date sendDt) {
		Map<String, String> rmap = new HashMap<String, String>();
		
		Where wherever = Where.create();
		wherever.in(Costunitversion::getUnitid, unitList.toArray());
        Order orderver = Order.create();
        orderver.order(Costunitversion::getUnitid).orderByDesc(Costunitversion::getBegintime);
        List<Costunitversion> vlist = entityService.queryList(Costunitversion.class, wherever, orderver);
        
        Map<String, List<Costunitversion>> vmap = new HashMap<String, List<Costunitversion>>();
        String tempUnit = null;
        for (Costunitversion v : vlist) {
        	if(v.getUnitid().equals(tempUnit)) {
        		vmap.get(v.getUnitid()).add(v);
        	}else {
        		List<Costunitversion> tlist = new ArrayList<Costunitversion>();
        		tlist.add(v);
        		vmap.put(v.getUnitid(), tlist);
        		tempUnit = v.getUnitid();
        	}
        }
        
        for (String unitCode : unitList) {
        	List<Costunitversion> clist = vmap.get(unitCode);
        	if(StringUtils.isNotEmpty(clist)) {
        		String version = "";
        		//从大到小日期
                for (Costunitversion ver : clist) {
        			String t = ver.getBegintime();
        			Date dt = DateTimeUtils.parseD(t, DateTimeUtils.DateFormat_YMD);
        			if(DateTimeUtils.bjDate(dt, sendDt) <= 0) {
        				version = DateTimeUtils.formatDate(dt, DateTimeUtils.DateFormat_YMD);
        				break;
        			}
        		}
                if("".equals(version)) {
                	if(StringUtils.isNotEmpty(clist)) {
                		version = clist.get(0).getBegintime();
                	}
                }
                rmap.put(unitCode, version);
        	}
		}
        
		return rmap;
	}
	
	/**
	 * @category 获取数据源台账设置的仪表
	 */
	@Override
	public List<TdsAccountMeterVo> getTdsAccountMeter(paramDto dto) {
		List<TdsAccountMeterVo> rlist = new ArrayList<TdsAccountMeterVo>();
		
		String tdsAlias = dto.getTdsAlias();//
		String confCode = dto.getConfCode();//配置
		String tagName = dto.getTagName();//仪表名称
		
//		//仪表都设置了名称和位号
//		if(StringUtils.isNotEmpty(tagName)) {
//			Where wheredot = Where.create();
//			wheredot.eq(Costunitsampledot::getTmused, 1);
//			wheredot.like(Costunitsampledot::getName, tagName);
//			List<Costunitsampledot> dlist = entityService.queryList(Costunitsampledot.class, wheredot, null);
//			
//			if(StringUtils.isNotEmpty(dlist)) {
//				StringBuffer sb = new StringBuffer();
//				for (Costunitsampledot obj : dlist) {
//					if(StringUtils.isEmpty(obj.getTagnumber())) {
//						continue;
//					}
//					sb.append(",");
//					sb.append(obj.getTagnumber());
//				}
//				if(sb.length() > 1) {
//					tagName = sb.substring(1);
//				}
//			}else {
//				tagName = "";
//			}
//		}
		
		//获取仪表信息
		List<TdsAccountMeter> tamList = accountServ.getAccountMeterList(tdsAlias, confCode, tagName);
		
		//获取仪表位号
		List<String> tagList = new ArrayList<String>();
		if(StringUtils.isNotEmpty(tamList)) {
			for (TdsAccountMeter obj : tamList) {
				tagList.add(obj.getTagnumber());
			}
		}
		
		if(StringUtils.isNotEmpty(tagList)) {
			//获取核算对象列表ID->名称
			CostDto cdto = new CostDto();
			List<Costuint> list = costService.getData(cdto);
			Map<String, String> umap = new HashMap<String, String>();
			for (Costuint unit : list) {
				umap.put(unit.getId(), unit.getName());
			}
			
			//获取所有仪表信息ID->名称
			Where wheredot = Where.create();
			wheredot.eq(Costunitsampledot::getTmused, 1);
			wheredot.in(Costunitsampledot::getTagnumber, tagList.toArray());
			Order orderdot = Order.create();
			orderdot.orderByAsc(Costunitsampleclass::getUnitid);
			orderdot.orderByAsc(Costunitsampleclass::getTmsort);
			List<Costunitsampledot> dlist = entityService.queryList(Costunitsampledot.class, wheredot, orderdot);
			Map<String, String> dotmap = new HashMap<String, String>();
			for (Costunitsampledot obj : dlist) {
				dotmap.put(obj.getTagnumber(), obj.getName());
			}
			
			//补充属性内容
			for (TdsAccountMeter dot : tamList) {
				TdsAccountMeterVo vo = ObjUtils.copyTo(dot, TdsAccountMeterVo.class);
				String unitname = umap.get(dot.getUnitCode());
				String tagname = dotmap.get(dot.getTagnumber());
				vo.setUnitName(unitname==null?"已删除":unitname);
				vo.setName(tagname);
				if(StringUtils.isEmpty(dot.getTagnumber())) {
					vo.setName(dot.getShowName());
				}
				rlist.add(vo);
			}
		}
        
        return rlist;
	}

	/**
	 * @category 获取核算对象列表数据
	 */
	@Override
	public List<Costuint> getCostuintList(paramDto dto) {
		CostDto cdto = new CostDto();
		cdto.setIsLedgerEntry(true);
		cdto.setProductive(0);
		List<Costuint> list = costService.getData(cdto);
		return list;
	}
	
	/**
	 * @category 获取核算对象列表数据
	 */
	@Override
	public List<Costuint> getOrgCostuintList(paramDto dto) {
		String orgCode = dto.getOrgCode();//
		List<Costuint> list = costService.getCostuintListByOrgId(orgCode, 2);
		return list;
	}
	
	/**
	 * @category 单个核算单元的仪表信息
	 * @param unitCode
	 * @param version
	 * @return
	 */
	private List<SampledotVo> getUnitCostClass(String unitCode, String version, String tagName) {
		
		List<SampledotVo> rlist = new ArrayList<SampledotVo>();
		
		Map<String, List<CostitemVo>> cmap = new HashMap<String, List<CostitemVo>>();
		
		Where where = Where.create();
        where.eq(Costunitsampleclass::getTmused, 1);
        where.eq(Costunitsampleclass::getBegintime, version);
        where.eq(Costunitsampleclass::getUnitid, unitCode);
        Order order = Order.create();
        order.orderByAsc(Costunitsampleclass::getTmsort);
        List<Costunitsampleclass> clist = entityService.queryList(Costunitsampleclass.class, where, order);
        
        for (Costunitsampleclass obj : clist) {
			String pid = obj.getPid();
			CostitemVo item = new CostitemVo();
			item.setNodeTypeMark(1);//分类标识
			item.setId(obj.getId());
			item.setNameStr(obj.getName());
			item.setPid(pid);
			item.setUnitid(obj.getUnitid());
			item.setBegintime(obj.getBegintime());
			
			if(cmap.containsKey(pid)) {
				cmap.get(pid).add(item);
			}else {
				List<CostitemVo> tlist = new ArrayList<CostitemVo>();
				tlist.add(item);
				cmap.put(pid, tlist);
			}
		}
        
        Map<String, List<Costunitsampledot>> dmap = new HashMap<String, List<Costunitsampledot>>();
        Where wheredot = Where.create();
        wheredot.eq(Costunitsampledot::getTmused, 1);
        wheredot.eq(Costunitsampledot::getBegintime, version);
        wheredot.eq(Costunitsampledot::getUnitid, unitCode);
        wheredot.ne(Costunitsampledot::getCtype, "1"); //非成本仪表（采集类型）
        if(StringUtils.isNotEmpty(tagName)) {
        	wheredot.like(Costunitsampledot::getName, tagName);
        }
      //TODO 过滤台账应用仪表数据
        
        Order orderdot = Order.create();
        orderdot.orderByAsc(Costunitsampledot::getTmsort);
        List<Costunitsampledot> dlist = entityService.queryList(Costunitsampledot.class, wheredot, orderdot);
		for (Costunitsampledot obj : dlist) {
			if(dmap.containsKey(obj.getPid())) {
				dmap.get(obj.getPid()).add(obj);
			}else {
				List<Costunitsampledot> tlist = new ArrayList<Costunitsampledot>();
				tlist.add(obj);
				dmap.put(obj.getPid(), tlist);
			}
		}
		
		Map<String, List<String>> clsNameMap = new HashMap<String, List<String>>();
        
        List<CostitemVo> list = cmap.get("root");//获取分类节点
		if(StringUtils.isNotEmpty(list)) {
			for (CostitemVo obj : list) {
				List<String> nameList = new ArrayList<String>();
				nameList.add(obj.getNameStr());
				clsNameMap.put(obj.getId(), nameList);
				setDotData(obj, cmap, dmap, rlist, clsNameMap);
			}
		}
        
        return rlist;
		
	}
	/**
	 * @category 递归获取仪表及分类信息
	 * @param obj
	 * @param cmap
	 * @param dmap
	 * @param rlist
	 * @param clsNameMap
	 */
	private void setDotData(CostitemVo obj, Map<String, List<CostitemVo>> cmap, Map<String, List<Costunitsampledot>> dmap, 
			List<SampledotVo> rlist, Map<String, List<String>> clsNameMap) {
		List<CostitemVo> clist = cmap.get(obj.getId());
		if(StringUtils.isNotEmpty(clist)) {
			for (CostitemVo vo : clist) {
				//获取父名称信息，同时保存子信息
				List<String> nameList = clsNameMap.get(obj.getId());
				if(StringUtils.isNotEmpty(nameList)) {
//					ArrayList<String> tlist = ObjUtils.copyTo(nameList, ArrayList.class) ;
					List<String> tlist = new ArrayList<String>();
					for (String name : nameList) {
						tlist.add(name);
					}
					tlist.add(vo.getNameStr());
					clsNameMap.put(vo.getId(), tlist);
				}else {
					nameList = new ArrayList<String>();
					nameList.add(obj.getNameStr());
					clsNameMap.put(obj.getId(), nameList);
					
//					List<String> tlist = new ArrayList<String>();
//					tlist.add(obj.getNameStr());
//					tlist.add(vo.getNameStr());
//					clsNameMap.put(vo.getId(), tlist);
				}
				
				setDotData(vo, cmap, dmap, rlist, clsNameMap);
			}
		}
		
		List<Costunitsampledot> dlist = dmap.get(obj.getId());
		if(StringUtils.isNotEmpty(dlist)) {
			for (Costunitsampledot dot : dlist) {
				SampledotVo vo = ObjUtils.copyTo(dot, SampledotVo.class);
				//带入单元、设备名称（分类名）
				List<String> nameList = clsNameMap.get(obj.getId());
				String zone="", dev="";
				if(StringUtils.isNotEmpty(nameList)) {
					if(nameList.size() == 1) {
						zone=nameList.get(0);
					}else if(nameList.size() >= 2) {
						zone = nameList.get(nameList.size()-2);
						dev = nameList.get(nameList.size()-1);
					}
				}
				vo.setZone(zone);
				vo.setDev(dev);
				rlist.add(vo);
			}
		}
	}

	/**
	 * @category 多个个核算单元的仪表信息
	 * @param unitList
	 * @param vmap
	 * @return
	 */
	private List<SampledotVo> getUnitCostClass(List<String> unitList, Map<String, String> vmap, String tagName) {
		
		List<SampledotVo> rlist = new ArrayList<SampledotVo>();
		
		
		/*-----------------------------------------------------------------------------------------------------------*/
		Where where = Where.create();
        where.eq(Costunitsampleclass::getTmused, 1);
        where.and().lb();
        IntStream.range(0, unitList.size()).forEach(i -> {
        	String unitCode = unitList.get(i);
        	String version = vmap.get(unitCode);
        	if(i > 0) {
        		where.or();
        	}
        	where.lb();
        	where.eq(Costunitsampleclass::getBegintime, version);
        	where.eq(Costunitsampleclass::getUnitid, unitCode);
        	where.rb();
        });
        where.rb();
        Order order = Order.create();
        order.order(Costunitsampleclass::getUnitid).orderByAsc(Costunitsampleclass::getTmsort);
        List<Costunitsampleclass> clist = entityService.queryList(Costunitsampleclass.class, where, order);
        
        Map<String, List<CostitemVo>> cmap = new HashMap<String, List<CostitemVo>>();
        for (Costunitsampleclass obj : clist) {
			String pid = obj.getPid();
			CostitemVo item = new CostitemVo();
			item.setNodeTypeMark(1);//分类标识
			item.setId(obj.getId());
			item.setNameStr(obj.getName());
			item.setPid(pid);
			item.setUnitid(obj.getUnitid());
			item.setBegintime(obj.getBegintime());
			
			if(cmap.containsKey(pid)) {
				cmap.get(pid).add(item);
			}else {
				List<CostitemVo> tlist = new ArrayList<CostitemVo>();
				tlist.add(item);
				cmap.put(pid, tlist);
			}
		}
        
        /*-----------------------------------------------------------------------------------------------------------*/
        Where wheredot = Where.create();
        wheredot.eq(Costunitsampledot::getTmused, 1);
        if(StringUtils.isNotEmpty(tagName)) {
        	wheredot.like(Costunitsampledot::getName, tagName);
        }
        wheredot.and().lb();
        IntStream.range(0, unitList.size()).forEach(i -> {
        	String unitCode = unitList.get(i);
        	String version = vmap.get(unitCode);
        	if(i > 0) {
        		wheredot.or();
        	}
        	wheredot.lb();
        	wheredot.eq(Costunitsampledot::getBegintime, version);
        	wheredot.eq(Costunitsampledot::getUnitid, unitCode);
        	wheredot.rb();
        });
        wheredot.rb();
      //TODO 过滤台账应用仪表数据
        
        Order orderdot = Order.create();
        orderdot.order(Costunitsampledot::getUnitid).orderByAsc(Costunitsampledot::getTmsort);
        List<Costunitsampledot> dlist = entityService.queryList(Costunitsampledot.class, wheredot, orderdot);//仪表数据
        
        
        Map<String, List<Costunitsampledot>> dmap = new HashMap<String, List<Costunitsampledot>>();
		for (Costunitsampledot obj : dlist) {
			if(dmap.containsKey(obj.getPid())) {
				dmap.get(obj.getPid()).add(obj);
			}else {
				List<Costunitsampledot> tlist = new ArrayList<Costunitsampledot>();
				tlist.add(obj);
				dmap.put(obj.getPid(), tlist);
			}
		}
		
		/*-----------------------------------------------------------------------------------------------------------*/
		
		Map<String, List<String>> clsNameMap = new HashMap<String, List<String>>();
        
        List<CostitemVo> list = cmap.get("root");//获取分类节点
		if(StringUtils.isNotEmpty(list)) {
			for (CostitemVo obj : list) {
				List<String> nameList = new ArrayList<String>();
				nameList.add(obj.getNameStr());
				clsNameMap.put(obj.getId(), nameList);
				setDotData(obj, cmap, dmap, rlist, clsNameMap);
			}
		}
        
        return rlist;
	}
	
	
	/**
	 *	保存采集点拖拽排序数据
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveSampledotSortData(MethodSaveDto saveDto) {
		String result = "";
		List<String> unitidList = new ArrayList<String>();
		List<Costunitsampleclass> updClassList = new ArrayList<Costunitsampleclass>();
		List<Costunitsampledot> updDotList = new ArrayList<Costunitsampledot>();
		if(StringUtils.isNotNull(saveDto)) {
			List<Costunitsampleclass> classList = saveDto.getCostunitsampleclass_list(); //核算对象的采集点分类
		    if(StringUtils.isNotEmpty(classList)) {
		    	Costunitsampleclass dataObj = classList.get(0);
		    	MethodQueryDto queryDto = new MethodQueryDto();
		    	queryDto.setUnitid(dataObj.getUnitid());
		    	queryDto.setBegintime(dataObj.getBegintime());
		    	List<Costunitsampleclass> list = methodService.getCostunitsampleclassList(queryDto);
		    	if(StringUtils.isNotEmpty(list)) {
		    		Map<String, Costunitsampleclass> map = list.stream().collect(Collectors.toMap(Costunitsampleclass::getId, Function.identity(), (key1, key2) -> key2));
		    		if(StringUtils.isNotEmpty(map)) {
		    			for (int i = 0; i < classList.size(); i++) {
		    				Costunitsampleclass classObj = classList.get(i);
				    		String id = classObj.getId();
				    		if(StringUtils.isNotEmpty(id)&&map.containsKey(id)) {
				    			Costunitsampleclass obj = map.get(id);
				    			String unitid = obj.getUnitid();
					    		if(StringUtils.isNotEmpty(unitid)&&!unitidList.contains(unitid)) {
					    			unitidList.add(unitid);
					    		}
					    		obj.setPid(classObj.getPid());
					    		obj.setTmsort(classObj.getTmsort());
					    		updClassList.add(obj);
				    		}
						}
		    		}
		    	}
		    }
		    List<Costunitsampledot> dotList = saveDto.getCostunitsampledot_list(); //核算对象的采集点
		    if(StringUtils.isNotEmpty(dotList)) {
		    	Costunitsampledot dataObj = dotList.get(0);
		    	MethodQueryDto queryDto = new MethodQueryDto();
		    	queryDto.setUnitid(dataObj.getUnitid());
		    	queryDto.setBegintime(dataObj.getBegintime());
		    	List<Costunitsampledot> list = methodService.getCostunitsampledotList(queryDto);
		    	if(StringUtils.isNotEmpty(list)) {
		    		Map<String, Costunitsampledot> map = list.stream().collect(Collectors.toMap(Costunitsampledot::getId, Function.identity(), (key1, key2) -> key2));
		    		if(StringUtils.isNotEmpty(map)) {
		    			for (int i = 0; i < dotList.size(); i++) {
		    				Costunitsampledot dotObj = dotList.get(i);
				    		String id = dotObj.getId();
				    		if(StringUtils.isNotEmpty(id)&&map.containsKey(id)) {
				    			Costunitsampledot obj = map.get(id);
				    			String unitid = obj.getUnitid();
					    		if(StringUtils.isNotEmpty(unitid)&&!unitidList.contains(unitid)) {
					    			unitidList.add(unitid);
					    		}
					    		obj.setPid(dotObj.getPid());
					    		obj.setTmsort(dotObj.getTmsort());
					    		updDotList.add(obj);
				    		}
						}
		    		}
		    	}
		    }
		}
		if("".equals(result) && StringUtils.isNotEmpty(updClassList)) {
			result = methodService.saveSampleclassData(null, updClassList, null);
		}
		if("".equals(result) && StringUtils.isNotEmpty(updDotList)) {
			result = methodService.saveSampledotData(null, updDotList, null);
		}
		if("".equals(result) && StringUtils.isNotEmpty(unitidList)) { //调用同步其他数据的接口
			for (int i = 0; i < unitidList.size(); i++) {
				String unitid = unitidList.get(i);
				accountToolsService.syncBatchTaginfo(null, unitid, null); //op=null表示需要重新排序
			}
		}
		return result;
	}
	
	/**
	 *	保存成本项目拖拽排序数据
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveCostItemSortData(MethodSaveDto saveDto) {
		String result = "";
		List<Costclass> updCostclassList = new ArrayList<Costclass>(); //成本项目分类
		List<Costitem> updCostitemList = new ArrayList<Costitem>(); //成本项目
		List<Costinstrument> updCostinstrumentList = new ArrayList<Costinstrument>(); //成本项目仪表
		List<Costindicator> updCostindicatorList = new ArrayList<Costindicator>(); //核算指标
		if(StringUtils.isNotNull(saveDto)) {
			List<Costclass> classList = saveDto.getCostclass_list(); //分类
		    if(StringUtils.isNotEmpty(classList)) {
		    	Costclass dataObj = classList.get(0);
		    	MethodQueryDto queryDto = new MethodQueryDto();
		    	queryDto.setUnitid(dataObj.getUnitid());
		    	queryDto.setBegintime(dataObj.getBegintime());
		    	List<Costclass> list = methodService.getCostclassList(queryDto);
		    	if(StringUtils.isNotEmpty(list)) {
		    		Map<String, Costclass> map = list.stream().collect(Collectors.toMap(Costclass::getId, Function.identity(), (key1, key2) -> key2));
		    		if(StringUtils.isNotEmpty(map)) {
		    			for (int i = 0; i < classList.size(); i++) {
				    		Costclass classObj = classList.get(i);
				    		String id = classObj.getId();
				    		if(StringUtils.isNotEmpty(id)&&map.containsKey(id)) {
				    			Costclass obj = map.get(id);
					    		obj.setPid(classObj.getPid());
					    		obj.setTmsort(classObj.getTmsort());
					    		updCostclassList.add(obj);
				    		}
						}
		    		}
		    	}
		    }
		    List<Costitem> itemList = saveDto.getCostitem_list(); //项目
		    if(StringUtils.isNotEmpty(itemList)) {
		    	Costitem dataObj = itemList.get(0);
		    	MethodQueryDto queryDto = new MethodQueryDto();
		    	queryDto.setUnitid(dataObj.getUnitid());
		    	queryDto.setBegintime(dataObj.getBegintime());
		    	List<Costitem> list = methodService.getCostitemList(queryDto);
		    	if(StringUtils.isNotEmpty(list)) {
		    		Map<String, Costitem> map = list.stream().collect(Collectors.toMap(Costitem::getId, Function.identity(), (key1, key2) -> key2));
		    		if(StringUtils.isNotEmpty(map)) {
		    			for (int i = 0; i < itemList.size(); i++) {
		    				Costitem itemObj = itemList.get(i);
				    		String id = itemObj.getId();
				    		if(StringUtils.isNotEmpty(id)&&map.containsKey(id)) {
				    			Costitem obj = map.get(id);
					    		obj.setPid(itemObj.getPid());
					    		obj.setTmsort(itemObj.getTmsort());
					    		updCostitemList.add(obj);
				    		}
						}
		    		}
		    	}
		    }
		    List<Costinstrument> instrumentList = saveDto.getCostinstrument_list(); //仪表
		    if(StringUtils.isNotEmpty(instrumentList)) {
		    	Costinstrument dataObj = instrumentList.get(0);
		    	MethodQueryDto queryDto = new MethodQueryDto();
		    	queryDto.setUnitid(dataObj.getUnitid());
		    	queryDto.setBegintime(dataObj.getBegintime());
		    	List<Costinstrument> list = methodService.getCostinstrumentList(queryDto);
		    	if(StringUtils.isNotEmpty(list)) {
		    		Map<String, Costinstrument> map = list.stream().collect(Collectors.toMap(Costinstrument::getId, Function.identity(), (key1, key2) -> key2));
		    		if(StringUtils.isNotEmpty(map)) {
		    			for (int i = 0; i < instrumentList.size(); i++) {
		    				Costinstrument instrumentObj = instrumentList.get(i);
				    		String id = instrumentObj.getId();
				    		if(StringUtils.isNotEmpty(id)&&map.containsKey(id)) {
				    			Costinstrument obj = map.get(id);
					    		obj.setPid(instrumentObj.getPid());
					    		obj.setTmsort(instrumentObj.getTmsort());
					    		updCostinstrumentList.add(obj);
				    		}
						}
		    		}
		    	}
		    }
		    List<Costindicator> indicatorList = saveDto.getCostindicator_list(); //指标
		    if(StringUtils.isNotEmpty(indicatorList)) {
		    	Costindicator dataObj = indicatorList.get(0);
		    	MethodQueryDto queryDto = new MethodQueryDto();
		    	queryDto.setUnitid(dataObj.getUnitid());
		    	queryDto.setBegintime(dataObj.getBegintime());
		    	List<Costindicator> list = methodService.getCostindicatorList(queryDto);
		    	if(StringUtils.isNotEmpty(list)) {
		    		Map<String, Costindicator> map = list.stream().collect(Collectors.toMap(Costindicator::getId, Function.identity(), (key1, key2) -> key2));
		    		if(StringUtils.isNotEmpty(map)) {
		    			for (int i = 0; i < indicatorList.size(); i++) {
		    				Costindicator instrumentObj = indicatorList.get(i);
				    		String id = instrumentObj.getId();
				    		if(StringUtils.isNotEmpty(id)&&map.containsKey(id)) {
				    			Costindicator obj = map.get(id);
					    		obj.setTmsort(instrumentObj.getTmsort());
					    		updCostindicatorList.add(obj);
				    		}
						}
		    		}
		    	}
		    }
		}
		if("".equals(result) && StringUtils.isNotEmpty(updCostclassList)) {
			result = methodService.saveDataCostclass(null, updCostclassList, null);
		}
		if("".equals(result) && StringUtils.isNotEmpty(updCostitemList)) {
			result = methodService.saveDataCostitem(null, updCostitemList, null);
		}
		if("".equals(result) && StringUtils.isNotEmpty(updCostinstrumentList)) {
			result = methodService.saveDataCostinstrument(null, updCostinstrumentList, null);
		}
		if("".equals(result) && StringUtils.isNotEmpty(updCostindicatorList)) {
			result = methodService.saveDataCostindicator(null, updCostindicatorList, null);
		}
		return result;
	}
	
}
