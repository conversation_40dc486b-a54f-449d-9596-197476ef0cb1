package com.yunhesoft.leanCosting.unitConf.service;

import java.util.List;

import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.OutSystemTagNumber;
import com.yunhesoft.leanCosting.unitConf.entity.po.OutSystemUnitCompare;

/**
 *	外部系统服务接口
 * <AUTHOR>
 * @date 2023-11-14
 */
public interface IOutSystemService {

	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 * 外部系统仪表位号（查询）
	 * @param queryDto
	 * @return
	 */
	public List<OutSystemTagNumber> getOutSystemTagNumberList(MethodQueryDto queryDto);

	/**
	 *	外部系统仪表位号（保存）
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataOutSystemTagNumber(List<OutSystemTagNumber> addList,List<OutSystemTagNumber> updList,List<OutSystemTagNumber> delList);

	// ————————————————————————————————————————————————————————————————————————————————————————————
	
	/**
	 * 获取外部系统核算对象编码对照（装置）
	 * @param queryDto
	 * @return
	 */
	public List<OutSystemUnitCompare> getOutSystemUnitCompareList();
	
	
}
