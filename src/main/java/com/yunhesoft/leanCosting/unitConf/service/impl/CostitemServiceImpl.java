package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yunhesoft.accountTools.service.IAccountToolsService;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.constants.UserConstants;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramIndexService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramLibraryCostService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.paramDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostStipulateTime;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.po.ElemLib;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostStipulateTimeVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampledotVo;
import com.yunhesoft.leanCosting.unitConf.service.IBriefAnalysisService;
import com.yunhesoft.leanCosting.unitConf.service.ICostitemService;
import com.yunhesoft.leanCosting.unitConf.service.IElemLibService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitConfService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.entity.SysDictType;
import com.yunhesoft.system.tools.dict.service.ISysDictDataService;
import com.yunhesoft.system.tools.dict.service.ISysDictTypeService;

@Service
public class CostitemServiceImpl implements ICostitemService {
	
	@Autowired
	private EntityService entityService;
	@Autowired
	private ICostToolService costToolService;
	
	@Autowired
	private IUnitMethodService methodService;
	
	@Autowired
	private ISysDictTypeService dictTypeService; //数据字典类型服务
	
	@Autowired
	private ISysDictDataService dictDataService; //数据字典数据服务
	
	@Autowired
	private UnitConfService unitConfService;
	
	@Autowired
	private UnitItemInfoService unitItemInfoService;
	
	@Autowired
	private IBriefAnalysisService analysisService;
	
	@Autowired
    private IAccountToolsService accountToolsService;
	
	@Autowired
	private RedisUtil redisUtil;
	
	@Autowired
	private IProgramIndexService indexService;
	
	@Autowired
	private IProgramLibraryCostService costService;
	
	@Autowired
	private IElemLibService elemLibService; //采集要素服务
	
	
	@Override
	public List<CostitemVo> getDatas(paramDto dto) {
		Where where = Where.create();
		where.eq(Costitem::getTmused, 1);//
        where.eq(Costitem::getUnitid, dto.getUnitcode());//核算单元ID
        where.eq(Costitem::getBegintime, dto.getVersion());//日期版本
        Order order = Order.create();
        order.orderByAsc(Costitem::getTmsort);
        List<Costitem> list = entityService.queryList(Costitem.class, where, order);
        
        List<CostitemVo> rlist = new ArrayList<CostitemVo>();
        for (Costitem obj : list) {
        	CostitemVo vo = ObjUtils.copyTo(obj, CostitemVo.class);
        	vo.setNameStr(obj.getItemname());
        	vo.setNodeTypeMark(2);
        	rlist.add(vo);
		}
		return rlist;
	}

	@Override
	public CostitemVo getData(String id, String type) {
		CostitemVo vo = new CostitemVo();
		//1分类 2项目 3仪表 99指标
		if("1".equals(type)) {
			Costclass obj = entityService.queryObjectById(Costclass.class, id);
			vo = ObjUtils.copyTo(obj, CostitemVo.class);
			vo.setNameStr(obj.getCcname());
			vo.setClassType(obj.getCctype());
			vo.setNodeTypeMark(1);
		}else if("2".equals(type)) { //项目
			Costitem obj = entityService.queryObjectById(Costitem.class, id);
			if(obj!=null) { //存在项目
				vo = ObjUtils.copyTo(obj, CostitemVo.class);
		    	vo.setNameStr(obj.getItemname());
		    	//设置默认值
		    	Integer feedShowType = vo.getFeedShowType(); //反馈时，通过添加选择增加项目。0 默认显示项目，1 通过新增添加项目，默认是0
		    	if(feedShowType==null) {
		    		vo.setFeedShowType(0);
		    	}
		    	Integer isFeeItem = vo.getIsFeeItem(); //反馈时，通过添加选择增加项目。0 默认显示项目，1 通过新增添加项目，默认是0
		    	if(isFeeItem==null) {
		    		boolean isCost = false;
		    		String pid = vo.getPid();
		    		if(StringUtils.isNotEmpty(pid)) {
		    			Costclass classObj = methodService.getCostclassById(pid);
		    			if(classObj!=null) {
		    				String ccType = classObj.getCctype();
		    				if(StringUtils.isNotEmpty(ccType)&&"费用".equals(ccType)) {
		    					isCost = true;
		    				}
		    			}
		    		}
		    		if(isCost) {
		    			vo.setIsFeeItem(1);
		    		}else {
		    			vo.setIsFeeItem(0);
		    		}
		    	}
			}else {
				//新增项目时，还未生成数据，可能不存在项目；
				//前台根据数据id判断是否存在，如果id==null即不存在，显示默认值即可，保存时，在生成记录，同时同步到项目库中；
			}
	    	vo.setNodeTypeMark(2);
		}else if("3".equals(type)) {
			Costinstrument obj = entityService.queryObjectById(Costinstrument.class, id);
			vo = ObjUtils.copyTo(obj, CostitemVo.class);
	        String dotid = obj.getDotid();
	        if(StringUtils.isNotEmpty(dotid)) {
	        	Costunitsampledot sampledotObj = methodService.getCostunitsampledotById(dotid);
	        	if(sampledotObj!=null) {
	        		vo.setSourceype(sampledotObj.getSourceype());
	        		vo.setDatasource(sampledotObj.getDatasource());
	        	}
	        }
	        String sourceype = vo.getSourceype();
	        if(StringUtils.isEmpty(sourceype)) {
	        	vo.setSourceype("3"); //默认值 --- 来源类型：1、influxdb；2、数据源；3、手工填写；
	        }
	    	vo.setNameStr(obj.getName());
	    	vo.setNodeTypeMark(3);
		}else if("99".equals(type)) {
			Costindicator obj = entityService.queryObjectById(Costindicator.class, id);
	        vo = ObjUtils.copyTo(obj, CostitemVo.class);
	    	vo.setNameStr(obj.getCpname());
	    	vo.setNodeTypeMark(99);
	    	int isAnalysisItem = vo.getIsAnalysisItem()==null?0:vo.getIsAnalysisItem();
	    	vo.setIsAnalysisItem(isAnalysisItem);
	    	int isSelShow = vo.getIsSelShow()==null?1:vo.getIsSelShow();
	    	vo.setIsSelShow(isSelShow);
	    	int isUseToRecordEvent = vo.getIsUseToRecordEvent()==null?0:vo.getIsUseToRecordEvent();
	    	vo.setIsUseToRecordEvent(isUseToRecordEvent);
		}
		
		return vo;
	}

	@Override
	public CostitemVo save(CostitemVo vo) {
		String id = vo.getId();
		if(StringUtils.isNotEmpty(id)) {//更新
			//更新简要分析设置的物资名称（用）
			List<Costitem> itemList = new ArrayList<Costitem>();
			List<Costinstrument> instrumentList = new ArrayList<Costinstrument>();
			List<Costindicator> indicatorList = new ArrayList<Costindicator>();
			if(new Integer(1).equals(vo.getNodeTypeMark())) {
				Costclass obj = entityService.queryObjectById(Costclass.class, id);
				if("-1".equals(vo.getRowFlag())) {//删除
					obj.setTmused(0);
				}else {//修改
					obj.setCcname(vo.getNameStr());
					obj.setCctype(vo.getClassType());
					obj.setMemo(vo.getMemo());
				}
				List<Costclass> updList = new ArrayList<Costclass>();
				updList.add(obj);
				methodService.saveDataCostclass(null, updList, null);
				
				vo.setNameStr(obj.getCcname());
			}else if(new Integer(2).equals(vo.getNodeTypeMark())) {
				Costitem obj = entityService.queryObjectById(Costitem.class, id);
				if(obj!=null) { //修改 或 删除
					Integer tmsort = obj.getTmsort();
					obj = ObjUtils.copyTo(vo, Costitem.class);
					obj.setTmsort(tmsort); //保存单条记录不修改排序（影响拖拽排序）
					if("-1".equals(vo.getRowFlag())) {//删除
						obj.setTmused(0);
					}else {//修改
						if(StringUtils.isNotEmpty(vo.getNameStr())) {
							obj.setItemname(vo.getNameStr());
						}
						itemList.add(obj); //更新简要分析设置的物资名称（用）
					}
					List<Costitem> updList = new ArrayList<Costitem>();
					updList.add(obj);
					methodService.saveDataCostitem(null, updList, null);
					
					int tmused = obj.getTmused()==null?0:obj.getTmused();
					if(tmused==1) {
						costService.initProgramLibraryByAddCostitem(updList);
					}
				}else { //新增（新增项目）
					obj = ObjUtils.copyTo(vo, Costitem.class);
					if(StringUtils.isNotEmpty(vo.getNameStr())) {
						obj.setItemname(vo.getNameStr());
					}
					//调用保存数据接口
					String unitid = vo.getUnitid();
					String begintime = vo.getBegintime();
					boolean saveInstrumentByItem = vo.getSaveInstrumentByItem();
					List<Costitem> costitem_list = new ArrayList<Costitem>();
					costitem_list.add(obj);
					MethodSaveDto saveDto = new MethodSaveDto();
					saveDto.setEditType("save");
					saveDto.setUnitid(unitid);
					saveDto.setBegintime(begintime);
					saveDto.setCostitem_list(costitem_list);
					saveDto.setSaveInstrumentByItem(saveInstrumentByItem);
					unitConfService.saveCostitemData(saveDto);
				}
				vo.setNameStr(obj.getItemname());
			}else if(new Integer(3).equals(vo.getNodeTypeMark())) {
				//仪表
				Costinstrument obj = entityService.queryObjectById(Costinstrument.class, id);				
				if("-1".equals(vo.getRowFlag())) {//删除
					obj.setTmused(0);
				}else {//修改
					if(StringUtils.isNotEmpty(vo.getNameStr())) {
						obj.setName(vo.getNameStr());
					}
					obj.setInstrumentRange(vo.getInstrumentRange()); //仪表量程
					obj.setConversionfactor(vo.getConversionfactor()); //换算系数
					obj.setTagnumber(vo.getTagnumber()); //仪表位号
					obj.setInstrumentType(vo.getInstrumentType()); //仪表类型： 1、累计仪表；  2、规定时间； 3、人工清零；
					obj.setShowInShiftWrite(vo.getShowInShiftWrite()); //录入时显示
					obj.setIsIntervalUse(vo.getIsIntervalUse()); //间隔使用
					instrumentList.add(obj); //更新简要分析设置的物资名称（用）
				}
				List<Costinstrument> updList = new ArrayList<Costinstrument>();
				updList.add(obj);
				methodService.saveDataCostinstrument(null, updList, null);
				//采集点(成本仪表)
				String dotid = obj.getDotid();
		        if(StringUtils.isNotEmpty(dotid)) {
		        	Costunitsampledot sampledotObj = methodService.getCostunitsampledotById(dotid);
		        	if(sampledotObj!=null) {
		        		sampledotObj.setSourceype(vo.getSourceype());
		        		sampledotObj.setDatasource(vo.getDatasource());
		        		entityService.rawUpdateByIdIncludeNull(sampledotObj);
		        		//清理redis
		        		String unitcode = sampledotObj.getUnitid();
						String version = sampledotObj.getBegintime();
						if(StringUtils.isNotEmpty(unitcode)&&StringUtils.isNotEmpty(version)) {
							String redisKey = "COST:COSTUNITSAMPLEDOT:"+unitcode+":"+version;
							redisUtil.delete(redisKey);
						}
		        	}
		        }
				vo.setNameStr(obj.getName());
			}else if(new Integer(99).equals(vo.getNodeTypeMark())) {
				Costindicator obj = entityService.queryObjectById(Costindicator.class, id);
				if("-1".equals(vo.getRowFlag())) {//删除
					obj.setTmused(0);
				}else {//修改
					obj.setCpname(vo.getNameStr());
					obj.setStandardval(vo.getStandardval());
					obj.setComparetype(vo.getComparetype());
					obj.setItemunit(vo.getItemunit());
					obj.setIsAnalysisItem(vo.getIsAnalysisItem());
					obj.setIsSelShow(vo.getIsSelShow());
					obj.setIsUseToRecordEvent(vo.getIsUseToRecordEvent());
					obj.setBaseConsumption(vo.getBaseConsumption());
					indicatorList.add(obj); //更新简要分析设置的物资名称（用）
				}
				List<Costindicator> updList = new ArrayList<Costindicator>();
				updList.add(obj);
				methodService.saveDataCostindicator(null, updList, null);
				vo.setNameStr(obj.getCpname());
				
				int tmused = obj.getTmused()==null?0:obj.getTmused();
				if(tmused==1) {
					costService.initProgramLibraryByAddCostindicator(indicatorList);
				}
			}
			//更新简要分析设置的物资名称
			analysisService.updateBriefAnalysisName(itemList, instrumentList, indicatorList);
		}else {//添加
			Costitem obj = ObjUtils.copyTo(vo, Costitem.class);
			obj.setId(TMUID.getUID());
			obj.setTmused(1);
			if(StringUtils.isNotEmpty(vo.getNameStr())) {
				obj.setItemname(vo.getNameStr());
			}
			int tmsort = costToolService.maxSprt(Costitem.class);
			obj.setTmsort(tmsort);
			entityService.insert(obj);
			vo.setId(obj.getId());
			vo.setNameStr(obj.getItemname());
		}
		
		return vo;
	}

	@Override
	public String save(List<CostitemVo> list) {
		
		return null;
	}

	@Override
	public SampledotVo getDot(String id, String type) {
		SampledotVo vo = new SampledotVo();
		if("1".equals(type)) {
			Costunitsampleclass obj = entityService.queryObjectById(Costunitsampleclass.class, id);
			vo = ObjUtils.copyTo(obj, SampledotVo.class);
			vo.setNodeTypeMark(1);
		}else if("3".equals(type)) {
			Costunitsampledot obj = methodService.getCostunitsampledotById(id);
			vo = ObjUtils.copyTo(obj, SampledotVo.class);
			vo.setNodeTypeMark(3);
			Integer useTo = vo.getUseTo();
			if(useTo==null) {
				vo.setUseTo(1);
			}
			Integer isShowLedger = vo.getIsShowLedger();
			if(isShowLedger==null) {
				vo.setIsShowLedger(1);
			}
			Integer pointCountLedger = vo.getPointCountLedger();
			if(pointCountLedger==null) {
				vo.setPointCountLedger(3);
			}
			Integer isWriteInput = vo.getIsWriteInput();
			if(isWriteInput==null) {
				vo.setIsWriteInput(0);
			}
			Integer controlType = vo.getControlType();
			if(controlType==null) {
				vo.setControlType(0);
			}
			Integer defaultVal = vo.getDefaultVal();
			if(defaultVal==null) {
				vo.setDefaultVal(0);
			}
			Integer timeType = vo.getTimeType();
			if(timeType==null) {
				vo.setTimeType(0);
			}
			Integer isWriteBackInfluxdb = vo.getIsWriteBackInfluxdb();
			if(isWriteBackInfluxdb==null) {
				vo.setIsWriteBackInfluxdb(0);
			}
			Integer samplInterval = vo.getSamplInterval();
			if(samplInterval==null) {
				vo.setSamplInterval(0);
			}
			String elemId = vo.getElemId();
			if(StringUtils.isNotEmpty(elemId)) { //获取采集要素名称
				ElemLib elemObj = elemLibService.getElemLibById(elemId);
				if(elemObj!=null) {
					vo.setElemName(elemObj.getElemName());
				}
			}
		}
		if(StringUtils.isEmpty(vo.getAlign())){
			vo.setAlign("center");
		}
		if(vo.getAutoSwapRow()==null){
			vo.setAutoSwapRow(0);
		}
		if(vo.getShowWidth()==null){
			vo.setShowWidth(150);
		}
		return vo;
	}

	@Override
	public SampledotVo saveDot(SampledotVo vo) {
		String id = vo.getId();
		if(StringUtils.isNotEmpty(id)) {//更新
			
			if(new Integer(1).equals(vo.getNodeTypeMark())) {
				Costunitsampleclass obj = entityService.queryObjectById(Costunitsampleclass.class, id);
				obj.setName(vo.getName());

				//班组记事栏目相关数据
				obj.setTeamLogColumnId(vo.getTeamLogColumnId());
				obj.setTeamLogColumnName(vo.getTeamLogColumnName());
				obj.setTeamLogTemplateId(vo.getTeamLogTemplateId());

				List<Costunitsampleclass> updList = new ArrayList<Costunitsampleclass>();
				updList.add(obj);
				methodService.saveSampleclassData(null, updList, null);
				accountToolsService.syncBatchTaginfo(null, obj.getUnitid(), null); //op=null表示需要重新排序
			}else if(new Integer(3).equals(vo.getNodeTypeMark())) {
				List<Costunitsampledot> synR3dbList = new ArrayList<Costunitsampledot>();
				Costunitsampledot obj = entityService.queryObjectById(Costunitsampledot.class, id);
				//获取同步r3db的采集点数据
				Costunitsampledot savedot = new Costunitsampledot();
				if("-1".equals(vo.getRowFlag())) {
					savedot.setTmused(0);
				}else {
					savedot.setName(vo.getName());
					savedot.setDatasource(vo.getDatasource());
					savedot.setTmused(vo.getTmused());
					savedot.setCtype(vo.getCtype());
				}
				methodService.getSynR3dbSampledotData(obj, savedot, synR3dbList, null);
				
				if("-1".equals(vo.getRowFlag())) {//删除
					obj.setTmused(0);
				}else {//修改
					Integer tmsort = obj.getTmsort();
					obj = ObjUtils.copyTo(vo, Costunitsampledot.class, true);
					obj.setTmsort(tmsort); //保存单条记录不修改排序（影响拖拽排序）
				}
				entityService.rawUpdateByIdIncludeNull(obj);
				//清理redis
        		String unitcode = obj.getUnitid();
				String version = obj.getBegintime();
				if(StringUtils.isNotEmpty(unitcode)&&StringUtils.isNotEmpty(version)) {
					String redisKey = "COST:COSTUNITSAMPLEDOT:"+unitcode+":"+version;
					redisUtil.delete(redisKey);
				}
				
				//调用同步其他数据的接口
				List<Costunitsampledot> synOtherList = new ArrayList<Costunitsampledot>();
				synOtherList.add(obj);
				methodService.synOtherInterfaceBySampledotChange(null, synOtherList);
				//同步R3DB仪表数据
				if(StringUtils.isNotEmpty(synR3dbList)) {
					methodService.synR3dbDataBySampledot(synR3dbList, null);
				}
				
				//修改记录保存时，同步方案指标数据
				int tmused = obj.getTmused()==null?0:obj.getTmused();
				if(tmused==1) {
					List<Costunitsampledot> updList = new ArrayList<Costunitsampledot>();
					updList.add(obj);
					indexService.initProgramIndicatorByAddSampledot(updList);
				}
				
			}
		}else {//添加
			Costunitsampledot obj = ObjUtils.copyTo(vo, Costunitsampledot.class);
			int tmsort = costToolService.maxSprt(Costunitsampledot.class);
			obj.setTmsort(tmsort);
			obj.setId(TMUID.getUID());
			obj.setTmused(1);
			entityService.insert(obj);
			vo.setId(obj.getId());
		}
		
		return vo;
	}

	/**
	 *	核算项目中获取仪表数据
	 */
	@Override
	public List<Costunitsampledot> getDotList(SampledotVo dto) {
		List<Costunitsampledot> result = new ArrayList<Costunitsampledot>();
		if(StringUtils.isNotNull(dto)) {
			String unitcode = dto.getUnitcode();
			String version = dto.getVersion();
			String ctype = dto.getCtype();
			String name = dto.getName();
			if(StringUtils.isNotEmpty(unitcode)&&StringUtils.isNotEmpty(version)) {
				MethodQueryDto queryDto = new MethodQueryDto();
				queryDto.setUnitid(unitcode);
				queryDto.setBegintime(version);
				queryDto.setCtype(ctype);
				queryDto.setName(name);
				List<Costunitsampledot> queryList = methodService.getCostunitsampledotList(queryDto);
				if(StringUtils.isNotEmpty(queryList)) {
					result = queryList;
				}
			}
		}
		return result;
	}
	

	@Override
	public List<Costclass> getCostClass(String unitcode, String version) {
		List<Costclass> result = new ArrayList<Costclass>();
		if(StringUtils.isNotEmpty(unitcode)&&StringUtils.isNotEmpty(version)) {
			String redisKey = "COST:COSTCLASS:"+unitcode+":"+version;
			Object redisList = redisUtil.getObject(redisKey);
			if(redisList!=null) {
				try {
					ObjectMapper mapper = new ObjectMapper();
					TypeReference<List<Costclass>> typeRef = new TypeReference<List<Costclass>>(){};
					result = mapper.readValue(JSON.toJSONString(redisList), typeRef);
				}catch(Exception e) {}
			}else {
				Where where = Where.create();
				if(StringUtils.isNotEmpty(unitcode)){
					where.eq(Costclass::getUnitid, unitcode);
				}
				if(StringUtils.isNotEmpty(version)){
					where.eq(Costclass::getBegintime, version);
				}
				where.eq(Costclass::getTmused, 1);
				Order order = Order.create();
				order.orderByAsc(Costclass::getTmsort);
				List<Costclass> queryList = this.entityService.queryList(Costclass.class, where, order);
				if(StringUtils.isNotEmpty(queryList)) {
					result = queryList;
					redisUtil.setObject(redisKey, queryList);
				}
			}
		}
		return result;
	}
	
	@Override
	public List<Costitem> getItemData(String unitCode, String version) {
		List<Costitem> result = new ArrayList<Costitem>();
		if(StringUtils.isNotEmpty(unitCode)&&StringUtils.isNotEmpty(version)) {
			String redisKey = "COST:COSTITEM:"+unitCode+":"+version;
			Object redisList = redisUtil.getObject(redisKey);
			if(redisList!=null) {
				try {
					ObjectMapper mapper = new ObjectMapper();
					TypeReference<List<Costitem>> typeRef = new TypeReference<List<Costitem>>(){};
					result = mapper.readValue(JSON.toJSONString(redisList), typeRef);
				}catch(Exception e) {}
			}else {
				Where where = Where.create();
				where.eq(Costitem::getTmused, 1);
				if(StringUtils.isNotEmpty(unitCode)){
					where.eq(Costitem::getUnitid, unitCode);
				}
				if(StringUtils.isNotEmpty(version)){
					where.eq(Costitem::getBegintime, version);
				}
				Order order = Order.create();
				order.orderByAsc(Costitem::getTmsort);
				List<Costitem> queryList = entityService.queryList(Costitem.class, where, order);;
				if(StringUtils.isNotEmpty(queryList)) {
					result = queryList;
					redisUtil.setObject(redisKey, queryList);
				}
			}
		}
		return result;
	}
	
	@Override
	public List<Costinstrument> getCostinstrument(String unitCode, String version) {
		List<Costinstrument> result = new ArrayList<Costinstrument>();
		if(StringUtils.isNotEmpty(unitCode)&&StringUtils.isNotEmpty(version)) {
			String redisKey = "COST:COSTINSTRUMENT:"+unitCode+":"+version;
			Object redisList = redisUtil.getObject(redisKey);
			if(redisList!=null) {
				try {
					ObjectMapper mapper = new ObjectMapper();
					TypeReference<List<Costinstrument>> typeRef = new TypeReference<List<Costinstrument>>(){};
					result = mapper.readValue(JSON.toJSONString(redisList), typeRef);
				}catch(Exception e) {}
			}else {
				Where whereyb = Where.create();
				whereyb.eq(Costinstrument::getTmused, 1);
				if(StringUtils.isNotEmpty(unitCode)){
					whereyb.eq(Costinstrument::getUnitid, unitCode);
				}
				if(StringUtils.isNotEmpty(version)){
					whereyb.eq(Costinstrument::getBegintime, version);
				}
				Order order = Order.create();
				order.orderByAsc(Costinstrument::getTmsort);
				List<Costinstrument> queryList = entityService.queryList(Costinstrument.class, whereyb, order);
				if(StringUtils.isNotEmpty(queryList)) {
					result = queryList;
					redisUtil.setObject(redisKey, queryList);
				}
			}
		}
		return result;
	}
	
	@Override
	public List<Costindicator> getCostindicator(String unitcode, String version) {
		List<Costindicator> result = new ArrayList<Costindicator>();
		if(StringUtils.isNotEmpty(unitcode)&&StringUtils.isNotEmpty(version)) {
			String redisKey = "COST:COSTINDICATOR:"+unitcode+":"+version;
			Object redisList = redisUtil.getObject(redisKey);
			if(redisList!=null) {
				try {
					ObjectMapper mapper = new ObjectMapper();
					TypeReference<List<Costindicator>> typeRef = new TypeReference<List<Costindicator>>(){};
					result = mapper.readValue(JSON.toJSONString(redisList), typeRef);
				}catch(Exception e) {}
			}else {
				Where where = Where.create();
				where.eq(Costindicator::getTmused, 1);
				if(StringUtils.isNotEmpty(unitcode)){
					where.eq(Costindicator::getUnitid, unitcode);
				}
				if(StringUtils.isNotEmpty(version)){
					where.eq(Costindicator::getBegintime, version);
				}
				Order order = Order.create();
				order.orderByAsc(Costindicator::getTmsort);
				List<Costindicator> queryList = entityService.queryList(Costindicator.class, where, order);
				if(StringUtils.isNotEmpty(queryList)) {
					result = queryList;
					redisUtil.setObject(redisKey, queryList);
				}
			}
		}
		return result;
	}
	
	@Override
	public List<Costclass> getCostClass(List<String> unitcodes, String version) {
		Where where = Where.create();
		if(StringUtils.isNotEmpty(unitcodes)){
			where.in(Costclass::getUnitid, unitcodes.toArray());
		}
		if(StringUtils.isNotEmpty(version)){
			where.eq(Costclass::getBegintime, version);
		}
		where.eq(Costclass::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costclass::getTmsort);
		return this.entityService.queryList(Costclass.class, where, order);
	}
	
	@Override
	public List<Costitem> getItemData(List<String> unitCodes, String version) {
		Where where = Where.create();
		where.eq(Costitem::getTmused, 1);//
		if(StringUtils.isNotEmpty(unitCodes)){
			where.in(Costitem::getUnitid, unitCodes.toArray());
		}
		if(StringUtils.isNotEmpty(version)){
			where.eq(Costitem::getBegintime, version);
		}
		Order order = Order.create();
		order.orderByAsc(Costitem::getTmsort);
		return entityService.queryList(Costitem.class, where, order);
	}
	
	@Override
	public List<Costinstrument> getCostinstrument(List<String> unitCodes, String version) {
		Where whereyb = Where.create();
		whereyb.eq(Costinstrument::getTmused, 1);//
		if(StringUtils.isNotEmpty(unitCodes)){
			whereyb.in(Costinstrument::getUnitid, unitCodes.toArray());
		}
		if(StringUtils.isNotEmpty(version)){
			whereyb.eq(Costinstrument::getBegintime, version);
		}
		Order order = Order.create();
		order.orderByAsc(Costinstrument::getTmsort);
		return entityService.queryList(Costinstrument.class, whereyb, order);
	}
	@Override
	public List<Costindicator> getCostindicator(List<String> unitcodes, String version) {
		Where where = Where.create();
		where.eq(Costindicator::getTmused, 1);
		if(StringUtils.isNotEmpty(unitcodes)){
			where.in(Costindicator::getUnitid, unitcodes.toArray());
		}
		if(StringUtils.isNotEmpty(version)){
			where.eq(Costindicator::getBegintime, version);
		}
		Order order = Order.create();
		order.orderByAsc(Costindicator::getTmsort);
		return this.entityService.queryList(Costindicator.class, where, order);
	}
	
	
	
	
	
	@Override
	public List<Costclass> getCostClasss(List<String> unitcode, List<String> version) {
		if(unitcode.size()==0) {
			return new ArrayList<Costclass>();
		}
		Where where = Where.create();
		where.in(Costclass::getUnitid, unitcode.toArray());
		where.in(Costclass::getBegintime, version.toArray());
		where.eq(Costclass::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costclass::getTmsort);
		return this.entityService.queryList(Costclass.class, where, order);
	}

	@Override
	public List<Costitem> getItemDatas(List<String> unitcode, List<String> version) {
		Where where = Where.create();
		where.eq(Costitem::getTmused, 1);//
		where.in(Costclass::getUnitid, unitcode.toArray());
		where.in(Costclass::getBegintime, version.toArray());
		Order order = Order.create();
		order.orderByAsc(Costitem::getTmsort);
		return entityService.queryList(Costitem.class, where, order);
	}

	@Override
	public List<Costinstrument> getCostinstruments(List<String> unitcode, List<String> version) {
		Where where = Where.create();
		where.eq(Costinstrument::getTmused, 1);//
		where.in(Costclass::getUnitid, unitcode.toArray());
		where.in(Costclass::getBegintime, version.toArray());
		Order order = Order.create();
		order.orderByAsc(Costinstrument::getTmsort);
		return entityService.queryList(Costinstrument.class, where, order);
	}

	@Override
	public List<Costindicator> getCostindicators(List<String> unitcode, List<String> version) {
		Where where = Where.create();
		where.in(Costclass::getUnitid, unitcode.toArray());
		where.in(Costclass::getBegintime, version.toArray());
		where.eq(Costindicator::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costindicator::getTmsort);
		return this.entityService.queryList(Costindicator.class, where, order);
	}
	
	
	//—————————————————————————————————————————  数据字典修改  ↓  —————————————————————————————————————————————
	/**
	 *	初始化采集点相关信息（数据字典：来源类型、采集类型等）
	 */
	@Override
	public void initSampledotInfo() {
		//来源类型（数据字典）
		this.initDictInfo("leanCosting","采集点的来源类型","leanCosting_sampledot_sourceType");
		//采集类型（数据字典）
		this.initDictInfo("leanCosting","采集点的采集类型","leanCosting_sampledot_gatherType");
		//仪表类型（数据字典）
		this.initDictInfo("leanCosting","项目仪表的仪表类型","leanCosting_itemInstrument_instrumentType");
		//规定时间类型（数据字典）
		this.initDictInfo("leanCosting","仪表类型的规定时间类型","leanCosting_instrumentType_stipulateType");
		//简要分析的比较内容（数据字典）
		this.initDictInfo("leanCosting","简要分析的比较内容","leanCosting_briefAnalysis_compareContent");
		//简要分析的比较类型（数据字典）
		this.initDictInfo("leanCosting","简要分析的比较内容","leanCosting_briefAnalysis_compareType");
		//简要分析的比较方法（数据字典）
		this.initDictInfo("leanCosting","简要分析的比较内容","leanCosting_briefAnalysis_compareMethod");
	}
	
	/**
	 *	初始化数据字典信息
	 */
	private void initDictInfo(String moduleCode,String dictName,String dictType) {
		//字典类型
		SysDictType typeObj = new SysDictType();
		typeObj.setModuleCode(moduleCode);
		typeObj.setDictName(dictName);
		typeObj.setDictType(dictType);
		typeObj.setStatus("0");
		if (!UserConstants.NOT_UNIQUE.equals(dictTypeService.checkDictTypeUnique(typeObj))) {
			dictTypeService.insertDictType(typeObj);
		}
		//字典数据（按照类型检索）
		List<SysDictData> queryList = dictTypeService.selectDictDataByType(dictType);
		if(StringUtils.isEmpty(queryList)) { //无数据
			LinkedHashMap<String, String> dataMap = this.getDictInitDataMap(dictType); //获取初始化数据
			if(StringUtils.isNotEmpty(dataMap)) {
				Iterator<Entry<String, String>> entryMap = dataMap.entrySet().iterator();
				int num = 0;
				while(entryMap.hasNext()){
					num += 1;
					Entry<String, String> entry = entryMap.next();
					SysDictData dataObj = new SysDictData();
					dataObj.setDictSort(Long.valueOf(num));
					dataObj.setDictLabel(entry.getValue());
					dataObj.setDictValue(entry.getKey());
					dataObj.setDictType(dictType);
					dataObj.setStatus("0");
					dictDataService.insertDictData(dataObj);
				}
			}
		}
	}
	
	/**
	 *	获取数据字典初始化数据
	 * @return
	 */
	private LinkedHashMap<String, String> getDictInitDataMap(String dictType) {
		LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
		if(StringUtils.isNotEmpty(dictType)) {
			if("leanCosting_sampledot_sourceType".equals(dictType)) { //采集点的来源类型
				map.put("1", "influxdb");
				map.put("2", "数据源");
				map.put("3", "手工填写");
			}else if("leanCosting_sampledot_gatherType".equals(dictType)) { //采集点的采集类型
				map.put("1", "成本仪表");
				map.put("2", "控制指标");
				map.put("3", "lims指标");
			}else if("leanCosting_itemInstrument_instrumentType".equals(dictType)) { //项目仪表的仪表类型
				map.put("1", "累计仪表");
				map.put("2", "规定时间");
				map.put("3", "人工清零");
			}else if("leanCosting_instrumentType_stipulateType".equals(dictType)) { //仪表类型的规定时间类型
				map.put("1", "上班时间之前");
				map.put("2", "上班时间之后");
				map.put("3", "下班时间之前");
				map.put("4", "下班时间之后");
			}else if("leanCosting_briefAnalysis_compareContent".equals(dictType)) { //简要分析的比较内容
				map.put("1", "消耗量/产量");
				map.put("2", "单耗/收率");
				map.put("3", "单位成本");
			}else if("leanCosting_briefAnalysis_compareType".equals(dictType)) { //简要分析的比较类型
				map.put("1", "与上班比");
				map.put("2", "与同班次比");
				map.put("3", "与计划比");
			}else if("leanCosting_briefAnalysis_compareMethod".equals(dictType)) { //简要分析的比较方法
				map.put("1", "差值");
				map.put("2", "百分比");
			}
		}
		return map;
	}
	//—————————————————————————————————————————  数据字典修改  ↑  ————————————————————————————————————————————— 
	
	
	/**
	 *	成本仪表中的规定时间设置数据
	 * @param dto
	 * @return
	 */
	@Override
	public List<CostStipulateTimeVo> getStipulateTimeList(MethodQueryDto dto) {
		List<CostStipulateTimeVo> result = new ArrayList<CostStipulateTimeVo>();
		List<CostStipulateTime> addList = new ArrayList<CostStipulateTime>();
		List<CostStipulateTime> delList = new ArrayList<CostStipulateTime>();
		if(StringUtils.isNotNull(dto)) {
			String unitid = dto.getUnitid();
			String begintime = dto.getBegintime();
			String pid = dto.getPid();
			if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&StringUtils.isNotEmpty(pid)) {
				String currDateStr = DateTimeUtils.getNowDateStr();
				List<ShiftForeignVo> shiftClassList = unitItemInfoService.getShiftList(unitid, currDateStr);
				if(StringUtils.isNotEmpty(shiftClassList)) {
					Map<String, CostStipulateTime> stipulateTimeMap = new HashMap<String, CostStipulateTime>();
					MethodQueryDto queryDto = new MethodQueryDto();
					queryDto.setUnitid(unitid);
					queryDto.setBegintime(begintime);
					queryDto.setPid(pid);
					List<CostStipulateTime> stipulateTimeList = methodService.getCostStipulateTimeList(queryDto);
					if(StringUtils.isNotEmpty(stipulateTimeList)) {
						stipulateTimeMap = stipulateTimeList.stream().collect(Collectors.toMap(CostStipulateTime::getShiftClassCode,Function.identity()));
					}
					for (int i = 0; i < shiftClassList.size(); i++) {
						ShiftForeignVo shiftClassObj = shiftClassList.get(i);
						String shiftClassCode = shiftClassObj.getShiftClassCode();
						String shiftClassName = shiftClassObj.getShiftClassName();
						CostStipulateTime obj = new CostStipulateTime();
						if(StringUtils.isNotEmpty(stipulateTimeMap)&&stipulateTimeMap.containsKey(shiftClassCode)) {
							obj = stipulateTimeMap.get(shiftClassCode);
							stipulateTimeMap.remove(shiftClassCode);
						}else {
							obj.setId(TMUID.getUID());
							obj.setUnitid(unitid);
							obj.setBegintime(begintime);
							obj.setPid(pid);
							obj.setShiftClassCode(shiftClassCode);
							obj.setStipulateType(1); // 规定时间类型：1、上班时间之前；2、上班时间之后；3、下班时间之前；4、下班时间之后；
							obj.setStipulateTime(0); // 规定时间（分钟）
							obj.setDeviationTime(0); // 偏差时间（分钟）
							addList.add(obj);
						}
						CostStipulateTimeVo vo = new CostStipulateTimeVo();
						BeanUtils.copyProperties(obj, vo); //赋予返回对象
						vo.setShiftClassName(shiftClassName);
						result.add(vo);
					}
					if(StringUtils.isNotEmpty(stipulateTimeMap)) {
						Iterator<Map.Entry<String, CostStipulateTime>> iterMap = stipulateTimeMap.entrySet().iterator();
						while (iterMap.hasNext()) {
							Map.Entry<String, CostStipulateTime> entryMap = iterMap.next();
							CostStipulateTime entryObj = entryMap.getValue();
							delList.add(entryObj);
						}
					}
				}
			}
		}
		if(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(delList)) {
			String ret = methodService.saveDataCostStipulateTime(addList, null, delList);
			if(StringUtils.isNotEmpty(ret)) {
				result.clear();
			}
		}
		return result;
	}

	/**
	 * 通过核算对象ID、班组记事栏目ID获取所有绑定的采集点分类及其子节点数据
	 * @param unitid
	 * @param teamLogColumnId
	 * @return
	 */
	public SqlRowSet getDotInfoSet(String unitid, String teamLogColumnId) {
		String sqlStr = "select t1.id flid,t1.name flmc,t1.team_log_column_id,t1.tmsort flsn," +
				" t2.id pointid,t2.name pointname,t2.tmsort pointsn,t2.combinitkey,t2.combinitval,t2.controltype,t2.datasource,t2.defaultval,t2.devicedefaultval,t2.sdunit,t2.pointcountledger" +
				" from (" +
				" select id,name,team_log_column_id,tmsort from COSTUNITSAMPLECLASS" +
				" where unitid='" + unitid + "' and team_log_column_id='" + teamLogColumnId + "' and tmused=1" +
				" ) t1" +
				" left join costunitsampledot t2 on t2.unitid='" + unitid + "' and t2.pid=t1.id" +
				" where t2.tmused=1 and t2.id is not null" +
				" order by t1.tmsort,t2.tmsort";

		SqlRowSet sqlRowSet = entityService.rawQuery(sqlStr);

		return sqlRowSet;
	}
}
