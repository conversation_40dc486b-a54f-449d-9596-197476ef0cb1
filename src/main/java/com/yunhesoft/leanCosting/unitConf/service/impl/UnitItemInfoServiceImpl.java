package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedCaseInsensitiveMap;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.MaintenanceStandardsDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.entity.po.MaintenanceStandards;
import com.yunhesoft.leanCosting.baseConfig.service.IMaintenanceStandardsService;
import com.yunhesoft.leanCosting.calcLogic.FetchRealTimeDataVo;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramLibraryCostQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramContingencyPlan;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramIndicator;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramLibraryCostItem;
import com.yunhesoft.leanCosting.programConfig.service.IProgramLibraryCostService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.ProductListDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.RetrieveProductDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.UnitAlertRecieverQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitversion;
import com.yunhesoft.leanCosting.unitConf.entity.po.UnitAlertRecieverConfig;
import com.yunhesoft.leanCosting.unitConf.entity.vo.ClassVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemInfoVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.IndicatorInfo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampleDotByClassVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostitemService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitAlertRecieverService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitInterfaceService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.shift.shift.entity.dto.ShiftForeignDto;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class UnitItemInfoServiceImpl implements UnitItemInfoService {

	@Autowired
	EntityService entityService;

	@Autowired
	ICostitemService costItemService;

	@Autowired
	IUnitMethodService unitMethodService;

	@Autowired
	IShiftService shiftService;

	@Autowired
	IProgramLibraryCostService programLibraryCostService;

	@Autowired
	private IUnitInterfaceService interfaceService;

	@Autowired
	private IUnitAlertRecieverService iuars;

	@Autowired
	IMaintenanceStandardsService imss;

	@Autowired
	IProgramService ips;

	/**
	 * @category 得到核算单元的版本
	 * @param unitcode 核算单元ID
	 * @param gettime  查询日期
	 * @param tenantid 租户ID
	 * @return
	 */
	private String getVersion(String unitcode, String queryDate, String tenantid) {
		Where where = Where.create();
		where.eq(Costunitversion::getUnitid, unitcode);
		if (StringUtils.isNotEmpty(queryDate)) {
			where.le(Costunitversion::getBegintime, queryDate);
		}
		String rtn = "";
		if (StringUtils.isEmpty(tenantid)) {
			// 按照租户查询
			rtn = this.entityService.findMaxValue(Costunitversion.class, Costunitversion::getBegintime, String.class,
					where);
		} else {
			// 按照给定租户查询
			String sql = "select max(BEGINTIME) beginTime from COSTUNITVERSION"
					+ " where UNITID=? and BEGINTIME<=? and TENANT_ID=?";
			List<Map<String, Object>> list = entityService.queryListMap(sql, unitcode, queryDate, tenantid);
			if (list != null && list.size() > 0) {
				Map<String, Object> data = list.get(0);
				rtn = (String) data.get("beginTime");
			}
		}

		return rtn;
	}

	@Override
	public CostItemInfoVo getUnitData(String unitcode, String queryDate, String programId) {
		CostItemInfoVo rtn = getCostData(unitcode, queryDate);
		// 传入的方案ID不为空时，查找方案内容
		if (StringUtils.isNotEmpty(programId) && !"0".equals(programId)) {
			// 核算对象使用方案的最大版本
			String pvid = this.programLibraryCostService.getMaxProgramVersionId(programId, queryDate, "");
			rtn.setPvid(pvid);
			// 核算方案使用的项目和指标
			List<ProgramLibraryCostItem> costItemList = this.programLibraryCostService
					.getProgramLibraryCostItemListByRedis(pvid);
			rtn.setPItemList(costItemList);
		}
		return rtn;
	};

	@Override
	public List<ProgramLibraryCostItem> getProgramItem(String queryDate, String programId) {
		// 核算对象使用方案的最大版本
		String pvid = this.programLibraryCostService.getMaxProgramVersionId(programId, queryDate, "");
		// 核算方案使用的项目和指标
		return this.programLibraryCostService.getProgramLibraryCostItemListByRedis(pvid);
	}

	/**
	 * 整理通过减少访问数据库提高效率
	 * 
	 * @param costuints
	 * @param queryDate
	 * @return
	 */
	@Override
	public Map<String, CostItemInfoVo> getCostDatas(List<Costuint> costuints, String queryDate) {
		Map<String, CostItemInfoVo> map = new LinkedHashMap<String, CostItemInfoVo>();
		Map<String, String> keyMap = new LinkedHashMap<String, String>();// 存放核算对象ID，最大版本
		List<String> unitcodes = new ArrayList<String>();
		for (Costuint costuint : costuints) {
			String unitcode = costuint.getId();
			CostItemInfoVo rtn = new CostItemInfoVo();
			// 核算对象的最大版本
			String beginTime = this.getVersion(unitcode, queryDate, "");
			rtn.setBeginTime(beginTime);
			keyMap.put(unitcode, beginTime);
			map.put(unitcode, rtn);
			unitcodes.add(unitcode);
		}
		// 成本分类
		List<Costclass> fll = this.costItemService.getCostClass(unitcodes, null);
		for (Costclass costclass : fll) {
			String unitcode = costclass.getUnitid();
			String begintime = costclass.getBegintime();
			if (keyMap.containsKey(unitcode) && keyMap.containsValue(begintime)) {// 核算对象ID和最大版本都匹配情况下加入最终数据
				CostItemInfoVo rtn = new CostItemInfoVo();
				if (map.containsKey(unitcode)) {
					rtn = map.get(unitcode);
				}
				List<Costclass> fll_ = rtn.getClassList();
				if (fll_ == null) {
					fll_ = new ArrayList<Costclass>();
				}
				fll_.add(costclass);
				rtn.setClassList(fll_);
				map.put(unitcode, rtn);
			}
		}
		// 成本项目unitcodes
		List<Costitem> xml = this.costItemService.getItemData(unitcodes, null);
		for (Costitem costitem : xml) {
			String unitcode = costitem.getUnitid();
			String begintime = costitem.getBegintime();
			if (keyMap.containsKey(unitcode) && keyMap.containsValue(begintime)) {// 核算对象ID和最大版本都匹配情况下加入最终数据
				CostItemInfoVo rtn = new CostItemInfoVo();
				if (map.containsKey(unitcode)) {
					rtn = map.get(unitcode);
				}
				List<Costitem> xml_ = rtn.getItemList();
				if (xml_ == null) {
					xml_ = new ArrayList<Costitem>();
				}
				xml_.add(costitem);
				rtn.setItemList(xml_);
				map.put(unitcode, rtn);
			}
		}
		// 成本仪表
		List<Costinstrument> ybl = this.costItemService.getCostinstrument(unitcodes, null);
		for (Costinstrument costinstrument : ybl) {
			String unitcode = costinstrument.getUnitid();
			String begintime = costinstrument.getBegintime();
			if (keyMap.containsKey(unitcode) && keyMap.containsValue(begintime)) {// 核算对象ID和最大版本都匹配情况下加入最终数据
				CostItemInfoVo rtn = new CostItemInfoVo();
				if (map.containsKey(unitcode)) {
					rtn = map.get(unitcode);
				}
				List<Costinstrument> ybl_ = rtn.getInstrumentList();
				if (ybl_ == null) {
					ybl_ = new ArrayList<Costinstrument>();
				}
				ybl_.add(costinstrument);
				rtn.setInstrumentList(ybl_);
				map.put(unitcode, rtn);
			}
		}
		// 指标
		List<Costindicator> zbl = this.costItemService.getCostindicator(unitcodes, null);
		for (Costindicator costindicator : zbl) {
			String unitcode = costindicator.getUnitid();
			String begintime = costindicator.getBegintime();
			if (keyMap.containsKey(unitcode) && keyMap.containsValue(begintime)) {// 核算对象ID和最大版本都匹配情况下加入最终数据
				CostItemInfoVo rtn = new CostItemInfoVo();
				if (map.containsKey(unitcode)) {
					rtn = map.get(unitcode);
				}
				List<Costindicator> zbl_ = rtn.getIndicatorList();
				if (zbl_ == null) {
					zbl_ = new ArrayList<Costindicator>();
				}
				zbl_.add(costindicator);
				rtn.setIndicatorList(zbl_);
				map.put(unitcode, rtn);
			}
		}
		List<CostItemFormula> gsl = this.unitMethodService.getCostItemFormulaList(unitcodes);
		for (CostItemFormula costItemFormula : gsl) {
			String unitcode = costItemFormula.getUnitId();
			String begintime = costItemFormula.getBegintime();
			if (keyMap.containsKey(unitcode) && keyMap.containsValue(begintime)) {// 核算对象ID和最大版本都匹配情况下加入最终数据
				CostItemInfoVo rtn = new CostItemInfoVo();
				if (map.containsKey(unitcode)) {
					rtn = map.get(unitcode);
				}
				List<CostItemFormula> zbl_ = rtn.getFormulaList();
				if (zbl_ == null) {
					zbl_ = new ArrayList<CostItemFormula>();
				}
				zbl_.add(costItemFormula);
				rtn.setFormulaList(zbl_);
				map.put(unitcode, rtn);
			}
		}
		return map;
	}

	@Override
	public List<CostItemInfoVo> getCostData(List<Costuint> costuints) {
		Map<String, String> map = new LinkedCaseInsensitiveMap<String>();
		String dbtype = entityService.getDatabaseType();
		StringBuffer sql = new StringBuffer();
		if ("mysql".equalsIgnoreCase(dbtype)) {
			sql.append(
					" SELECT MAX(BEGINTIME) AS BEGINTIME,UNITID FROM COSTUNITVERSION WHERE IFNULL(UNITID,'') !='' GROUP BY UNITID");
		} else if ("oracle".equalsIgnoreCase(dbtype)) {
			sql.append(
					" SELECT MAX(BEGINTIME) AS BEGINTIME,UNITID FROM COSTUNITVERSION WHERE LENGTH(NVL(UNITID,''))>0 GROUP BY UNITID");
		} else {
			sql.append(
					" SELECT MAX(BEGINTIME) AS BEGINTIME,UNITID FROM COSTUNITVERSION WHERE (UNITID!='' or UNITID is not null) GROUP BY UNITID");
		}
		SqlRowSet result = entityService.rawQuery(sql.toString());
		List<String> unitcodes = new ArrayList<String>();
		List<String> beginTimes = new ArrayList<String>();
		List<String> keys = new ArrayList<String>();
		List<String> costuintId = new ArrayList<String>();
		for (Costuint costuint : costuints) {
			costuintId.add(costuint.getId());
		}
		if (result != null) {
			while (result.next()) {
				// 核算对象，最新的版本
				String begintime = result.getString("BEGINTIME");
				String unitid = result.getString("UNITID");
				if (costuintId.contains(unitid)) {
					map.put(unitid, begintime);
					if (!unitcodes.contains(unitid)) {
						unitcodes.add(unitid);
					}
					if (!beginTimes.contains(begintime)) {
						beginTimes.add(begintime);
					}
					String key = unitid + "_" + begintime;
					if (!keys.contains(key)) {
						keys.add(key);
					}
				}
			}
		}
		// 成本分类
		List<Costclass> fll = this.costItemService.getCostClasss(unitcodes, beginTimes);
		Map<String, List<Costclass>> costclasss = new LinkedHashMap<String, List<Costclass>>();
		for (Costclass costclass : fll) {
			String unitid = costclass.getUnitid();
			String begintime = costclass.getBegintime();
			String key = unitid + "_" + begintime;
			if (keys.contains(key)) {
				List<Costclass> costItemInfoVo = new ArrayList<Costclass>();
				if (costclasss.containsKey(key)) {
					costItemInfoVo = costclasss.get(key);
				}
				costItemInfoVo.add(costclass);
				costclasss.put(key, costItemInfoVo);
			}
		}
		// 成本项目
		List<Costitem> xml = this.costItemService.getItemDatas(unitcodes, beginTimes);
		Map<String, List<Costitem>> costitems = new LinkedHashMap<String, List<Costitem>>();
		for (Costitem costitem : xml) {
			String unitid = costitem.getUnitid();
			String begintime = costitem.getBegintime();
			String key = unitid + "_" + begintime;
			if (keys.contains(key)) {
				List<Costitem> costItemInfoVo = new ArrayList<Costitem>();
				if (costitems.containsKey(key)) {
					costItemInfoVo = costitems.get(key);
				}
				costItemInfoVo.add(costitem);
				costitems.put(key, costItemInfoVo);
			}
		}
		// 成本仪表
		List<Costinstrument> ybl = this.costItemService.getCostinstruments(unitcodes, beginTimes);
		Map<String, List<Costinstrument>> costinstruments = new LinkedHashMap<String, List<Costinstrument>>();
		for (Costinstrument costinstrument : ybl) {
			String unitid = costinstrument.getUnitid();
			String begintime = costinstrument.getBegintime();
			String key = unitid + "_" + begintime;
			if (keys.contains(key)) {
				List<Costinstrument> costinstrumentInfoVo = new ArrayList<Costinstrument>();
				if (costinstruments.containsKey(key)) {
					costinstrumentInfoVo = costinstruments.get(key);
				}
				costinstrumentInfoVo.add(costinstrument);
				costinstruments.put(key, costinstrumentInfoVo);
			}
		}
		// 指标
		List<Costindicator> zbl = this.costItemService.getCostindicators(unitcodes, beginTimes);
		Map<String, List<Costindicator>> costindicators = new LinkedHashMap<String, List<Costindicator>>();
		for (Costindicator costindicator : zbl) {
			String unitid = costindicator.getUnitid();
			String begintime = costindicator.getBegintime();
			String key = unitid + "_" + begintime;
			if (keys.contains(key)) {
				List<Costindicator> costindicatorInfoVo = new ArrayList<Costindicator>();
				if (costindicators.containsKey(key)) {
					costindicatorInfoVo = costindicators.get(key);
				}
				costindicatorInfoVo.add(costindicator);
				costindicators.put(key, costindicatorInfoVo);
			}
		}
		MethodQueryDto queryDto = new MethodQueryDto();
		queryDto.setUnitids(unitcodes);
		queryDto.setBegintimes(beginTimes); // 暂时不用了
		// 公式
		List<CostItemFormula> gsl = this.unitMethodService.getCostItemFormulaList(queryDto);
		Map<String, List<CostItemFormula>> costItemFormulas = new LinkedHashMap<String, List<CostItemFormula>>();
		for (CostItemFormula costItemFormula : gsl) {
			String unitid = costItemFormula.getUnitId();
			String begintime = costItemFormula.getBegintime();
			String key = unitid + "_" + begintime;
			if (keys.contains(key)) {
				List<CostItemFormula> costItemFormulaInfoVo = new ArrayList<CostItemFormula>();
				if (costItemFormulas.containsKey(key)) {
					costItemFormulaInfoVo = costItemFormulas.get(key);
				}
				costItemFormulaInfoVo.add(costItemFormula);
				costItemFormulas.put(key, costItemFormulaInfoVo);
			}
		}

		List<CostItemInfoVo> resList = new ArrayList<CostItemInfoVo>();
		for (String key : keys) {
			CostItemInfoVo rtn = new CostItemInfoVo();
			String[] ids = key.split("_");
			rtn.setUnitId(ids[0]);
			rtn.setBeginTime(ids[1]);
			// 成本分类
			if (costclasss.containsKey(key)) {
				rtn.setClassList(costclasss.get(key));
			}
			// 成本项目
			if (costitems.containsKey(key)) {
				rtn.setItemList(costitems.get(key));
			}
			// 成本仪表
			if (costinstruments.containsKey(key)) {
				rtn.setInstrumentList(costinstruments.get(key));
			}
			// 指标
			if (costindicators.containsKey(key)) {
				rtn.setIndicatorList(costindicators.get(key));
			}
			// 公式
			if (costItemFormulas.containsKey(key)) {
				rtn.setFormulaList(costItemFormulas.get(key));
			}
			resList.add(rtn);
		}
		return resList;
	}

	@Override
	public CostItemInfoVo getCostData(String unitcode, String queryDate) {
		CostItemInfoVo rtn = new CostItemInfoVo();
		// 核算对象的最大版本
		String beginTime = this.getVersion(unitcode, queryDate, "");
		rtn.setBeginTime(beginTime);
		// 成本分类
		List<Costclass> fll = this.costItemService.getCostClass(unitcode, beginTime);
		rtn.setClassList(fll);
		// 成本项目
		List<Costitem> xml = this.costItemService.getItemData(unitcode, beginTime);
		rtn.setItemList(xml);
		// 成本仪表
		List<Costinstrument> ybl = this.costItemService.getCostinstrument(unitcode, beginTime);
		rtn.setInstrumentList(ybl);
		// 指标
		List<Costindicator> zbl = this.costItemService.getCostindicator(unitcode, beginTime);
		rtn.setIndicatorList(zbl);
		// 公式
		List<CostItemFormula> gsl = this.unitMethodService.getCostItemFormulaListByRedis(unitcode, beginTime);
		rtn.setFormulaList(gsl);
		return rtn;
	};

	@Override
	public CostItemInfoVo getItemData(String unitcode, String queryDate) {
		CostItemInfoVo rtn = new CostItemInfoVo();
		// 核算对象的最大版本
		String beginTime = this.getVersion(unitcode, queryDate, "");
		rtn.setBeginTime(beginTime);
		// 成本分类
		List<Costclass> fll = this.costItemService.getCostClass(unitcode, beginTime);
		rtn.setClassList(fll);
		// 成本项目
		List<Costitem> xml = this.costItemService.getItemData(unitcode, beginTime);
		rtn.setItemList(xml);
		// 指标
		List<Costindicator> zbl = this.costItemService.getCostindicator(unitcode, beginTime);
		rtn.setIndicatorList(zbl);
		return rtn;
	};

	@Override
	public List<Costitem> getItem(String unitcode, String queryDate) {
		// 核算对象的最大版本
		String beginTime = this.getVersion(unitcode, queryDate, "");
		// 成本项目
		return this.costItemService.getItemData(unitcode, beginTime);
	};

	@Override
	public List<Costclass> getCostClass(String unitcode, String queryDate) {
		// 核算对象的最大版本
		String beginTime = this.getVersion(unitcode, queryDate, "");
		return this.costItemService.getCostClass(unitcode, beginTime);
	};

	@Override
	public JSONObject getProductItem(RetrieveProductDto rpDto) {
		JSONObject jsonObject = new JSONObject();
		List<ProductListDto> rtn = new ArrayList<ProductListDto>();
		String unitcode = rpDto.getUnitId();
		if (unitcode == null) {
			unitcode = "";
		}
		String queryDate = rpDto.getQueryDate();
		if (queryDate == null) {
			queryDate = "";
		}
		String queryCondition = rpDto.getQueryCondition();
		if (queryCondition == null) {
			queryCondition = "";
		}
		Integer pageNum = rpDto.getPageNum();
		if (pageNum == null) {
			pageNum = 1;
		}
		Integer pageSize = rpDto.getPageSize();
		if (pageSize == null) {
			pageSize = 20;
		}
		Integer start, end, cur;
		cur = 0;
		start = (pageNum - 1) * pageSize + 1;
		end = pageNum * pageSize;

		// 核算对象的最大版本
		String beginTime = this.getVersion(unitcode, queryDate, "");
		// 成本分类
		List<Costclass> fll = this.costItemService.getCostClass(unitcode, beginTime);
		if (fll != null) {
			// 得到产品对应的分类
			String iscp = "", flmc;
			HashMap<String, String> fm = new HashMap<String, String>();
			for (Costclass x : fll) {
				iscp = x.getCctype();
				if (iscp == null) {
					iscp = "";
				}
				flmc = x.getCcname();
				if (flmc == null) {
					flmc = "";
				}
				if ("产品".equals(iscp)) {
					fm.put(x.getId(), flmc);
				}
			}
			if (fm != null && fm.size() > 0) {
				// 成本项目
				List<Costitem> xml = this.costItemService.getItemData(unitcode, beginTime);
				if (xml != null) {
					String pid, xmmc;
					for (Costitem y : xml) {
						pid = y.getPid();
						if (fm.containsKey(pid)) {
							// 是产品
							xmmc = y.getItemname();
							if (xmmc == null) {
								xmmc = "";
							}
							if ("".equals(queryCondition)) {
								cur = cur + 1;// 没有过滤条件，直接加入输出
							} else {
								if (xmmc.indexOf(queryCondition) >= 0) {
									cur = cur + 1;// 包含过滤条件，加入输出
								} else {
									continue;
								}
							}
							if (cur >= start && end >= cur) {
								flmc = fm.get(pid);
								ProductListDto pld = new ProductListDto();
								pld.setId(y.getId());
								pld.setClassName(flmc);
								pld.setItemName(xmmc);
								pld.setItemUnit(y.getItemunit());
								pld.setErpCode(y.getErpcode());
								rtn.add(pld);
							}
						}
					}
				}
			}
		}
		jsonObject.put("total", cur);
		jsonObject.put("productList", rtn);
		return jsonObject;
	};

	@Override
	public List<ShiftForeignVo> getShiftList(String unitcode, String queryDate) {
		MethodQueryDto qdto = new MethodQueryDto();
		qdto.setUnitid(unitcode);
		qdto.setObjType("org");
		List<Costunitoperator> jg = this.unitMethodService.getCostunitoperatorList(qdto);
		if (jg != null && jg.size() > 0) {
			// 配置了操作机构
			String jgid = jg.get(0).getObjid();
			if (jgid != null && !"".equals(jgid)) {
				ShiftForeignDto sdto = new ShiftForeignDto();
				sdto.setOrgCode(jgid);
				sdto.setNowDt(queryDate);
				return this.shiftService.getShiftClassList(sdto);
			}
		}
		return null;
	}

	@Override
	public List<ShiftForeignVo> getShift(String unitCode, String queryDate, String shiftId) {
		MethodQueryDto qdto = new MethodQueryDto();
		qdto.setUnitid(unitCode);
		qdto.setObjType("org");
		List<Costunitoperator> jg = this.unitMethodService.getCostunitoperatorList(qdto);
		if (jg != null && jg.size() > 0) {
			// 配置了操作机构
			List<String> czjgl = new ArrayList<String>();
			for (Costunitoperator x : jg) {
				String jgid = x.getObjid();
				if (jgid != null && !"".equals(jgid)) {
					czjgl.add(jgid);
				}
			}
			if (czjgl != null && czjgl.size() > 0) {
				ShiftForeignDto sdto = new ShiftForeignDto();
				sdto.setShfitClassId(shiftId);
				sdto.setNowDt(queryDate);
				sdto.setListObj(czjgl);
				return this.shiftService.getShiftDataByBcrq(sdto);
			}
		}
		return null;
	}

	@Override
	public ShiftForeignVo getShiftInfo(String unitCode, String queryDate) {
		MethodQueryDto qdto = new MethodQueryDto();
		qdto.setUnitid(unitCode);
		qdto.setObjType("org");
		List<Costunitoperator> jg = this.unitMethodService.getCostunitoperatorList(qdto);
		if (jg != null && jg.size() > 0) {
			// 配置了操作机构
			List<String> czjg = new ArrayList<String>();
			for (Costunitoperator yn : jg) {
				czjg.add(yn.getObjid());
			}
			return this.shiftService.getShiftByOrgListDateTime(czjg, queryDate);
		} else {
			return null;
		}
	}

	@Override
	public HashMap<String, String> getStartEndTime(String unitid, String ksrq, String jzrq, Integer type) {
		MethodQueryDto qdto = new MethodQueryDto();
		qdto.setUnitid(unitid);
		qdto.setObjType("org");
		List<Costunitoperator> jg = this.unitMethodService.getCostunitoperatorList(qdto);
		if (jg != null && jg.size() > 0) {
			// 配置了操作机构
			List<String> czjg = new ArrayList<String>();
			for (Costunitoperator yn : jg) {
				czjg.add(yn.getObjid());
			}
			String kssj = ksrq, jzsj = jzrq;
			JSONObject ksobj = this.shiftService.getMinSbsjMaxXbsj(czjg, ksrq, type);
			if (ksobj != null) {
				kssj = ksobj.getString("sbsj");
				if (StringUtils.isEmpty(kssj)) {
					kssj = ksrq;
				}
			}
			JSONObject jzobj = this.shiftService.getMinSbsjMaxXbsj(czjg, jzrq, type);
			if (jzobj != null) {
				jzsj = jzobj.getString("xbsj");
				if (StringUtils.isEmpty(jzsj)) {
					jzsj = jzrq;
				}
			}
			HashMap<String, String> rtn = new HashMap<String, String>();
			rtn.put("kssj", kssj);
			rtn.put("jzsj", jzsj);
			return rtn;
		} else {
			return null;
		}
	}

	@Override
	public List<Costunitsampledot> getSampleDot(String unitCode, String queryDate, String tenantid) {
		// 核算对象的最大版本
		String beginTime = this.getVersion(unitCode, queryDate, tenantid);
		MethodQueryDto queryDto = new MethodQueryDto();
		queryDto.setUnitid(unitCode);
		queryDto.setBegintime(beginTime);
		queryDto.setTenantId(tenantid);
		return this.unitMethodService.getCostunitsampledotList(queryDto);
	}

	@Override
	public List<Costunitsampledot> getSampleDotByInputRight(String unitid, String queryDate) {
		List<Costunitsampledot> rtn = new ArrayList<Costunitsampledot>();
		Integer sbwb = 0,wb=0;
		if (unitid != null) {
			String[] deviceIds = unitid.split("_");
			if (deviceIds.length == 3) {
				// 涉及设备要读取获取采集点类型
				if ("1".equals(deviceIds[2])) {
					// 设备维保，错误的进入
				} else if ("2".equals(deviceIds[2])) {
					// 设备状态，需要得到设备的方案设置，然后生成采集点
					String byx = "", byyq = "", outn, byid, cid;
					StringBuffer sb1 = new StringBuffer();
					StringBuffer sb2 = new StringBuffer();
					cid = "ChangeStatus";
					// 切换时间选择
					Costunitsampledot b1 = new Costunitsampledot();
					byid = "ChangeStatusOfEquipment_1";
					outn = "切换时间";
					b1.setPid(cid);
					b1.setId(byid);
					b1.setTagnumber(outn);
					b1.setName(outn);
					b1.setDatasource(byid);
					b1.setSourceype("3");
					b1.setControlType(2);// 时间控件
					b1.setIsWriteInput(1);// 手工录入
					b1.setTimeType(1);// 开始时间
					b1.setIsEquipmentMaintenance(sbwb);
					b1.setIsWriteBackInfluxdb(wb);//不写入influxdb
					rtn.add(b1);
					Costunitsampledot b2 = new Costunitsampledot();
					byid = "ChangeStatusOfEquipment_2";
					outn = "切换到";
					b2.setPid(cid);
					b2.setId(byid);
					b2.setTagnumber(outn);
					b2.setName(outn);
					b2.setDatasource(byid);
					b2.setSourceype("3");
					b2.setControlType(4);// 下拉框
					ProgramQueryDto dto = new ProgramQueryDto();
					dto.setDeviceType(deviceIds[1]);
					List<ProgramItem> pil = this.ips.getProgramItemList(dto);
					if (pil != null) {
						int count = pil.size();
						for (int i = 0; count > i; i++) {
							ProgramItem pi = pil.get(i);
							sb1.append(",").append(pi.getPiName());
							sb2.append(",").append(pi.getId());
						}
					}
					if (sb1.length() > 0) {
						byx = sb2.substring(1);
					}
					if (sb2.length() > 0) {
						byyq = sb1.substring(1);
					}
					b2.setCombInitKey(byx);// ID
					b2.setCombInitVal(byyq);// 名称
					b2.setIsWriteInput(1);// 手工录入
					b2.setTimeType(0);
					b2.setIsEquipmentMaintenance(sbwb);
					b2.setIsWriteBackInfluxdb(wb);//不写入influxdb
					rtn.add(b2);
					Costunitsampledot b3 = new Costunitsampledot();
					byid = "ChangeStatusOfEquipment_3";
					outn = "备注";
					b3.setPid(cid);
					b3.setId(byid);
					b3.setTagnumber(outn);
					b3.setName(outn);
					b3.setDatasource(byid);
					b3.setSourceype("3");
					b3.setControlType(0);// 文本
					b3.setIsWriteInput(1);// 手工录入
					b3.setTimeType(0);
					b3.setIsEquipmentMaintenance(sbwb);
					b3.setIsWriteBackInfluxdb(wb);//不写入influxdb
					rtn.add(b3);
				} else if ("0".equals(deviceIds[2]) || "3".equals(deviceIds[2]) || "4".equals(deviceIds[2])
						|| "5".equals(deviceIds[2])) {
					// 普通采集点
					String cjlx = "2";// 默认是平稳率
					if ("4".equals(deviceIds[2])) {
						// LIMS
						cjlx = "3";
					} else if ("5".equals(deviceIds[2])) {
						// 成本
						cjlx = "1";
					}
					// 核算对象的最大版本
					String beginTime = this.getVersion(deviceIds[0], queryDate, "");
					List<Costunitsampledot> dl = this.unitMethodService.getSampledotListByInputRight(deviceIds[0],
							beginTime, cjlx, null);
					int count = dl.size();
					for (int i = 0; count > i; i++) {
						Costunitsampledot b = dl.get(i);
						b.setIsEquipmentMaintenance(sbwb);
						wb=b.getIsWriteBackInfluxdb();
						if (wb==null) {
							wb=0;
						}
						b.setIsWriteBackInfluxdb(wb);
						rtn.add(b);
					}
				} else {
					// 设备保养
					sbwb = 1;
					MaintenanceStandardsDto dto = new MaintenanceStandardsDto();
					dto.setDevicetypelibraryId(deviceIds[1]);// 设备类型
					dto.setMaintenanceType(deviceIds[2]);// 保养类型
					List<MaintenanceStandards> bzl = this.imss.getItemData(dto);
					if (bzl != null) {
						int count = bzl.size();
						String byx, byyq, outn, byid;
						for (int i = 0; count > i; i++) {
							MaintenanceStandards bz = bzl.get(i);
							if (bz != null) {
								byid = bz.getId();
								byx = bz.getMaintenanceItems();// 保养项
								byyq = bz.getMaintenanceReq();// 保养要求
								outn = (new StringBuffer(byx).append(":").append(byyq)).toString();
								Costunitsampledot b = new Costunitsampledot();
								b.setPid("DeviceMaintain");
								;
								b.setId(byid);
								b.setTagnumber(outn);
								b.setName(outn);
								b.setDatasource(byid);
								b.setDefaultVal(bz.getWhetherToCheck());
								b.setSourceype("3");
								b.setIsEquipmentMaintenance(sbwb);
								b.setIsWriteBackInfluxdb(wb);//不写入influxdb
								rtn.add(b);
							}
						}
					}
				}
			} else {
				// 活动本身的采集点
				String beginTime = this.getVersion(unitid, queryDate, "");
				List<Costunitsampledot> dl = this.unitMethodService.getSampledotListByInputRight(unitid, beginTime, "2",
						null);
				int count = dl.size();
				for (int i = 0; count > i; i++) {
					Costunitsampledot b = dl.get(i);
					b.setIsEquipmentMaintenance(sbwb);
					wb=b.getIsWriteBackInfluxdb();
					if (wb==null) {
						wb=0;
					}
					b.setIsWriteBackInfluxdb(wb);;
					rtn.add(b);
				}
			}
		}
		return rtn;
	}

	public List<SampleDotByClassVo> getSampleDotByInputRightFromClass(String unitCode, String queryDate) {
		List<SampleDotByClassVo> rtn = new ArrayList<SampleDotByClassVo>();
		// 得到核算对象的采集点
		List<Costunitsampledot> DOTL = getSampleDotByInputRight(unitCode, queryDate);
		if (DOTL != null) {
			String pid;
			HashMap<String, List<Costunitsampledot>> dm = new HashMap<String, List<Costunitsampledot>>();
			for (Costunitsampledot x : DOTL) {
				pid = x.getPid();
				if (pid == null) {
					pid = "";
				}
				if (dm.containsKey(pid)) {
					dm.get(pid).add(x);
				} else {
					List<Costunitsampledot> yn = new ArrayList<Costunitsampledot>();
					yn.add(x);
					dm.put(pid, yn);
				}
			}

			// 设备维护
			pid = "DeviceMaintain";
			if (dm.containsKey(pid)) {
				List<Costunitsampledot> yn = dm.get(pid);
				SampleDotByClassVo vo = new SampleDotByClassVo();
				vo.setClassId(pid);
				vo.setClassName("设备保养");
				vo.setDotList(yn);
				rtn.add(vo);
				dm.remove(pid);
			}
			pid = "ChangeStatus";
			if (dm.containsKey(pid)) {
				List<Costunitsampledot> yn = dm.get(pid);
				SampleDotByClassVo vo = new SampleDotByClassVo();
				vo.setClassId(pid);
				vo.setClassName("设备状态切换");
				vo.setDotList(yn);
				rtn.add(vo);
				dm.remove(pid);
			}
			pid = "";
			if (dm.containsKey(pid)) {
				List<Costunitsampledot> yn = dm.get(pid);
				SampleDotByClassVo vo = new SampleDotByClassVo();
				vo.setClassId(pid);
				vo.setClassName("其它");
				vo.setDotList(yn);
				rtn.add(vo);
				dm.remove(pid);
			}

			List<Costunitsampleclass> cl = null;
			if (dm.size() > 0) {
				// 加载采集点分类
				String unitid;
				String[] deviceIds = unitCode.split("_");
				if (deviceIds.length == 3) {
					unitid = deviceIds[0];
				} else {
					unitid = unitCode;
				}
				String cv = this.unitMethodService.getMaxVersionByCostunitsampleclass(unitid, queryDate);
				MethodQueryDto queryDto = new MethodQueryDto();
				queryDto.setBegintime(cv);
				queryDto.setUnitid(unitid);
				cl = this.unitMethodService.getCostunitsampleclassList(queryDto);
				if (cl != null) {
					// 按照分类添加采集点
					for (Costunitsampleclass y : cl) {
						pid = y.getId();
						if (dm.containsKey(pid)) {
							List<Costunitsampledot> yn = dm.get(pid);
							SampleDotByClassVo vo = new SampleDotByClassVo();
							vo.setClassId(pid);
							vo.setClassName(y.getName());
							vo.setDotList(yn);
							rtn.add(vo);
						}
					}
				}
			}
		}
		return rtn;
	}

	private List<Costunitsampledot> getSampleDoti(String unitCode, String queryDate, String tenantid) {
		// 核算对象的最大版本
		MethodQueryDto queryDto = new MethodQueryDto();
		queryDto.setUnitid(unitCode);
		queryDto.setBegintime(queryDate);
		queryDto.setTenantId(tenantid);
		return this.unitMethodService.getCostunitsampledotList(queryDto);
	}

	@Override
	public List<IndicatorInfo> getIndicators(String unitid, String faid, String cxrq, String TENANT_ID) {
		List<IndicatorInfo> iil = null;
		String beginTime = this.getVersion(unitid, cxrq, TENANT_ID);
		List<Costunitsampledot> sdl = this.getSampleDoti(unitid, beginTime, TENANT_ID);
		if (sdl != null) {
			// 核算对象下配置了采集点：只包含使用的(tmused=1)
			// 预警的通用配置
			UnitAlertRecieverQueryDto queryDto = new UnitAlertRecieverQueryDto();
			queryDto.setUnitid(unitid);
			queryDto.setBegintime(beginTime);
			List<ProgramContingencyPlan> mrl = new ArrayList<ProgramContingencyPlan>();
			List<UnitAlertRecieverConfig> arcl = this.iuars.getUnitAlertRecieverList(queryDto);
			if (arcl != null) {
				for (UnitAlertRecieverConfig x : arcl) {
					ProgramContingencyPlan bx = new ProgramContingencyPlan();
					bx.setId(x.getId());
					bx.setStartPeriod(x.getStartPeriod());
					bx.setEndPeriod(x.getEndPeriod());
					bx.setDuration(x.getDuration());
					bx.setInDurationPos(x.getInDurationPos());
					bx.setInDurationPosName(x.getInDurationPosName());
					bx.setInOnduty(x.getInOnduty());
					bx.setOutDurationPos(x.getOutDurationPos());
					bx.setOutDurationPosName(x.getOutDurationPosName());
					bx.setOutOnduty(x.getOutOnduty());
					bx.setLimitType("key");
					mrl.add(bx);
				}
			}
			if ("0".equals(faid)) {
				// 未启用方案
				String did, name;
				iil = new ArrayList<IndicatorInfo>();
				// 循环核算对象下的采样点
				for (Costunitsampledot x : sdl) {
					did = x.getId();
					if (did == null) {
						continue;
					}
					name = x.getName();
					if (name == null) {
						name = "";
					}
					// 方案下使用了采样点才添加
					IndicatorInfo ii = new IndicatorInfo();
					ii.setUnitid(unitid);
					ii.setSdid(did);
					ii.setBegindate(x.getBegintime());
					ii.setPvid("");
					ii.setPid(x.getPid());
					ii.setTagnumber(x.getTagnumber());
					ii.setName(name);
					ii.setDatasource(x.getDatasource());
					ii.setSourceype(x.getSourceype());
					ii.setValrange(x.getValrange());
					ii.setSampleInterval(x.getSamplInterval());
					ii.setCtype(x.getCtype());
					ii.setTmsort(x.getTmsort());
					ii.setKeyUpLimit(x.getIndexRangeUpper());
					ii.setKeyLowLimit(x.getIndexRangeLower());
					// 使用默认的设置
					ii.setPlanList(mrl);
					iil.add(ii);
				}
			} else {
				// 给定方案下启用的采集点
				// 得到采集点的方案设置
				String pvid = this.programLibraryCostService.getMaxProgramVersionId(faid, cxrq, TENANT_ID);
				if (pvid == null || "".equals(pvid)) {
					return iil;
				} else {
					ProgramLibraryCostQueryDto dto = new ProgramLibraryCostQueryDto();
					dto.setPvId(pvid);
					dto.setTenantid(TENANT_ID);
					List<ProgramIndicator> pil = this.programLibraryCostService.getProgramIndicators(dto);
					if (pil != null && pil.size() > 0) {
						// 方案下有启用的采集点
						String did, name;
						HashMap<String, ProgramIndicator> pim = new HashMap<String, ProgramIndicator>();
						for (ProgramIndicator pi : pil) {
							name = pi.getIname();
							if (name == null) {
								name = "";
							}
							pim.put(name, pi);
						}
						// 得到方案版本下配置的预案和接收人
						String pid;
						HashMap<String, List<ProgramContingencyPlan>> pcpm = new HashMap<String, List<ProgramContingencyPlan>>();
						List<ProgramContingencyPlan> pcpl = this.programLibraryCostService
								.getProgramContingencyPlanList(dto);
						if (pcpl != null) {
							for (ProgramContingencyPlan pcp : pcpl) {
								pid = pcp.getPId();// 指标ID
								// 预案
								if (pcpm.containsKey(pid)) {
									pcpm.get(pid).add(pcp);
								} else {
									List<ProgramContingencyPlan> pr = new ArrayList<ProgramContingencyPlan>();
									pr.add(pcp);
									pcpm.put(pid, pr);
								}
							}
						}
						iil = new ArrayList<IndicatorInfo>();
						// 循环核算对象下的采样点
						for (Costunitsampledot x : sdl) {
							did = x.getId();
							if (did == null) {
								continue;
							}
							name = x.getName();
							if (name == null) {
								name = "";
							}
							if (pim.containsKey(name)) {
								// 方案下使用了采样点才添加
								ProgramIndicator pi = pim.get(name);
								IndicatorInfo ii = new IndicatorInfo();
								ii.setUnitid(unitid);
								ii.setSdid(did);
								ii.setBegindate(x.getBegintime());
								ii.setPvid(pvid);
								ii.setPid(x.getPid());
								ii.setTagnumber(x.getTagnumber());
								ii.setName(name);
								ii.setDatasource(x.getDatasource());
								ii.setSourceype(x.getSourceype());
								ii.setValrange(x.getValrange());
								ii.setSampleInterval(x.getSamplInterval());
								ii.setCtype(x.getCtype());
								ii.setTmsort(x.getTmsort());
								ii.setOperateUpLimit(pi.getOperateUpLimit());
								ii.setOperateLowLimit(pi.getOperateLowLimit());
								ii.setKeyUpLimit(pi.getKeyUpLimit());
								ii.setKeyLowLimit(pi.getKeyLowLimit());
								// 预案和接收人
								pid = pi.getId();
								if (pcpm.containsKey(pid)) {
									// 指标自身配置的
									ii.setPlanList(pcpm.get(pid));
								} else {
									// 使用默认的设置
									ii.setPlanList(mrl);
								}
								iil.add(ii);
							}
						}
					}
				}
			}
		}
		return iil;
	}

	@Override
	public Devicetypelibrary getDeviceType(String dt) {
		return entityService.queryObjectById(Devicetypelibrary.class, dt);
	}

	@Override
	public HashMap<String, ClassVo> getUnitClassId(List<String> unitList) {
		HashMap<String, ClassVo> rtn = new HashMap<String, ClassVo>();
		if (unitList != null) {
			String dt = DateTimeUtils.getDate();
			for (String id : unitList) {
				// 核算对象的最大版本
				String beginTime = this.getVersion(id, dt, "");
				// 成本分类
				List<Costclass> fll = this.costItemService.getCostClass(id, beginTime);
				if (fll != null) {
					// 得到产品对应的分类
					String iscp = "", flmc;
					for (Costclass x : fll) {
						iscp = x.getCctype();
						if (iscp == null) {
							iscp = "";
						}
						if ("产品".equals(iscp)) {
							ClassVo vo = new ClassVo();
							vo.setBeginTime(beginTime);
							vo.setClassId(x.getId());
							flmc = x.getCcname();
							if (flmc == null) {
								flmc = "";
							}
							vo.setClassName(flmc);
							rtn.put(id, vo);
							break;
						}
					}
				}
			}
		}
		return rtn;
	}

	@Override
	public Integer getUnitDoType(String unitid) {
		Integer rtn = 1;
		Costuint cu = unitMethodService.getCostuintById(unitid);
		if (cu != null) {
			String dt = cu.getUnittype();// 单元类型
			if (!StringUtils.isEmpty(dt)) {
				Devicetypelibrary dtl = getDeviceType(dt);
				if (dtl != null) {
					rtn = dtl.getFmluseclass();
					if (rtn == null) {
						rtn = 1;
					}
				}
			}
		}
		return rtn;
	}

	@Override
	public CostItemInfoVo getUnitWorkData(String unitcode, String queryDate, String programId) {
		CostItemInfoVo rtn = new CostItemInfoVo();
		Integer ut = getUnitDoType(unitcode);
		// 核算对象的最大版本
		String beginTime = this.getVersion(unitcode, queryDate, "");
		// 成本分类
		List<Costclass> fll = this.costItemService.getCostClass(unitcode, beginTime);
		if (fll != null) {
			// 得到产品对应的分类
			String iscp = "", flmc;
			HashMap<String, String> fm = new HashMap<String, String>();
			for (Costclass x : fll) {
				iscp = x.getCctype();
				if (iscp == null) {
					iscp = "";
				}
				flmc = x.getCcname();
				if (flmc == null) {
					flmc = "";
				}
				if (ut == 1) {
					if ("产品".equals(iscp)) {
						fm.put(x.getId(), flmc);
					}
				} else {
					if ("原料".equals(iscp)) {
						fm.put(x.getId(), flmc);
					}
				}
			}
			if (fm != null && fm.size() > 0) {
				// 成本项目
				List<Costitem> xml = this.costItemService.getItemData(unitcode, beginTime);
				if (xml != null) {
					String pid;
					List<Costitem> pp = new ArrayList<Costitem>();
					for (Costitem y : xml) {
						pid = y.getPid();
						if (fm.containsKey(pid)) {
							// 是产品
							pp.add(y);
						}
					}
					rtn.setItemList(pp);
				}
			}
		}
		return rtn;
	}

	@Override
	public CostItemInfoVo getFetchInfo(String unitid, String cxrq, String TENANT_ID) {
		CostItemInfoVo rtn = new CostItemInfoVo();
		// 核算对象的最大版本
		String beginTime = this.getVersion(unitid, cxrq, "");
		rtn.setBeginTime(beginTime);
		// 成本仪表
		List<Costinstrument> ybl = this.costItemService.getCostinstrument(unitid, beginTime);
		if (StringUtils.isNotEmpty(ybl)) {
			// 获取采集点名称
			Map<String, Costunitsampledot> sampledotMap = interfaceService.getSampledotMap(unitid, beginTime);
			if (StringUtils.isNotEmpty(sampledotMap)) {
				List<FetchRealTimeDataVo> pl = new ArrayList<FetchRealTimeDataVo>();
				HashMap<String, FetchRealTimeDataVo> pm = new HashMap<String, FetchRealTimeDataVo>();
				String ssyb = "";
				for (Costinstrument obj : ybl) {
					String dotid = obj.getDotid();
					if (StringUtils.isNotEmpty(dotid) && sampledotMap.containsKey(dotid)) {
						Costunitsampledot d = sampledotMap.get(dotid);
						ssyb = d.getDatasource();
						if (StringUtils.isEmpty(ssyb)) {
							continue;
						}
						FetchRealTimeDataVo cj = new FetchRealTimeDataVo();
						cj.setItemId(obj.getPid());
						cj.setInstrumentId(obj.getId());
						cj.setTagNumber(ssyb);
						cj.setYblx(obj.getInstrumentType());
						cj.setSjly(d.getSourceype());
						pl.add(cj);
						pm.put(obj.getId(), cj);
					}
				}
				rtn.setFiList(pl);
				rtn.setFiMap(pm);
			}
		}
		return rtn;
	}

	@Override
	public HashMap<String, ClassVo> getUnitProductClass(String unitId, String cxrq) {
		HashMap<String, ClassVo> rtn = new HashMap<String, ClassVo>();
		// 核算对象的最大版本
		String beginTime = this.getVersion(unitId, cxrq, "");
		// 成本分类
		List<Costclass> fll = this.costItemService.getCostClass(unitId, beginTime);
		if (fll != null) {
			// 得到产品对应的分类
			String iscp, flmc, flid;
			for (Costclass x : fll) {
				iscp = x.getCctype();
				if (iscp == null) {
					iscp = "";
				}
				if ("产品".equals(iscp)) {
					flmc = x.getCcname();
					if (flmc == null) {
						flmc = "";
					}
					flid = x.getId();
					ClassVo vo = new ClassVo();
					vo.setBeginTime(beginTime);
					vo.setClassId(flid);
					vo.setClassName(flmc);
					rtn.put(flid, vo);
				}
			}
		}
		return rtn;
	}

	@Override
	public List<Costindicator> getCostIndicator(String unitId, String cxrq) {
		// 核算对象的最大版本
		String beginTime = this.getVersion(unitId, cxrq, "");
		// 指标
		List<Costindicator> zbl = this.costItemService.getCostindicator(unitId, beginTime);
		return zbl;
	}

	@Override
	public List<Costclass> getCostClasses() {
		Where where = Where.create();
		where.eq(Costclass::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costclass::getUnitid);
		order.orderByAsc(Costclass::getTmsort);
		return this.entityService.queryList(Costclass.class, where, order);
	}

	@Override
	public List<Costitem> getCostItems() {
		Where where = Where.create();
		where.eq(Costitem::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costitem::getUnitid);
		order.orderByAsc(Costitem::getTmsort);
		return this.entityService.queryList(Costitem.class, where, order);
	}

	@Override
	public List<Costindicator> getCostIndicators() {
		Where where = Where.create();
		where.eq(Costindicator::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costindicator::getUnitid);
		order.orderByAsc(Costindicator::getTmsort);
		return this.entityService.queryList(Costindicator.class, where, order);
	}

	@Override
	public List<Costunitsampleclass> getSampleCalss() {
		Where where = Where.create();
		where.eq(Costunitsampleclass::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costunitsampleclass::getUnitid);
		order.orderByAsc(Costunitsampleclass::getTmsort);
		return this.entityService.queryList(Costunitsampleclass.class, where, order);
	}

	@Override
	public List<Costunitsampledot> getSampleDot() {
		Where where = Where.create();
		where.eq(Costunitsampledot::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costunitsampledot::getUnitid);
		order.orderByAsc(Costunitsampledot::getTmsort);
		return this.entityService.queryList(Costunitsampledot.class, where, order);
	}

	@Override
	public Costitem fetchItemInfo(String id) {
		return this.entityService.queryObjectById(Costitem.class, id);
	}

	@Override
	public Costunitsampledot fetchDotInfo(String id) {
		return this.entityService.queryObjectById(Costunitsampledot.class, id);
	}

}
