package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.OutSystemTagNumber;
import com.yunhesoft.leanCosting.unitConf.entity.po.OutSystemUnitCompare;
import com.yunhesoft.leanCosting.unitConf.service.IOutSystemService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;


@Service
public class OutSystemServiceImpl implements IOutSystemService {

	@Autowired
	private EntityService entityService;
	
	
	/**
	 * 外部系统仪表位号（查询）
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<OutSystemTagNumber> getOutSystemTagNumberList(MethodQueryDto queryDto) {
		List<OutSystemTagNumber> result = new ArrayList<OutSystemTagNumber>();
		try {
			String unitid = ""; // 核算对象ID
			String begintime = ""; // 版本日期
			Integer dataType = null; // 数据类型：0、控制指标；1、lims指标；
			if (StringUtils.isNotNull(queryDto)) {
				unitid = queryDto.getUnitid();
				begintime = queryDto.getBegintime();
				dataType = queryDto.getDataType();
			}
			// 检索条件
			Where where = Where.create();
			where.eq(OutSystemTagNumber::getActiveChk, "X"); //数据使用中

			if (StringUtils.isNotEmpty(unitid)) {
				where.eq(OutSystemTagNumber::getUnitid, unitid);
			}
			if (StringUtils.isNotEmpty(begintime)) {
				where.eq(OutSystemTagNumber::getBegintime, begintime);
			}
			if(dataType!=null) {
				where.eq(OutSystemTagNumber::getDataType, dataType);
			}
			// 排序
			Order order = Order.create();
			order.orderByAsc(OutSystemTagNumber::getDataType);
			order.orderByAsc(OutSystemTagNumber::getTmSort);
			List<OutSystemTagNumber> list = entityService.queryData(OutSystemTagNumber.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result.addAll(list);
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	

	/**
	 *	外部系统仪表位号（保存）
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	@Override
	public String saveDataOutSystemTagNumber(List<OutSystemTagNumber> addList,List<OutSystemTagNumber> updList,List<OutSystemTagNumber> delList) {
		String result = "";
		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
			if (entityService.insertBatch(addList) == 0) {
				result = "添加失败（外部系统仪表位号）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
			if (entityService.updateByIdBatch(updList) == 0) {
				result = "更新失败（外部系统仪表位号）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
			if (entityService.deleteByIdBatch(delList) == 0) {
				result = "删除失败（外部系统仪表位号）！";
			}
		}
		return result;
	}

	// ————————————————————————————————————————————————————————————————————————————————————————————

	
	/**
	 * 获取外部系统核算对象编码对照（装置）
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<OutSystemUnitCompare> getOutSystemUnitCompareList() {
		List<OutSystemUnitCompare> result = new ArrayList<OutSystemUnitCompare>();
		// 检索条件
		Where where = Where.create();
		where.eq(OutSystemUnitCompare::getTmUsed, 1); //数据使用中
		// 排序
		Order order = Order.create();
		order.orderByAsc(OutSystemUnitCompare::getOutPmtCode);
		order.orderByAsc(OutSystemUnitCompare::getTmSort);
		List<OutSystemUnitCompare> list = new ArrayList<OutSystemUnitCompare>();
		try {
			list = entityService.queryData(OutSystemUnitCompare.class, where, order, null);
		} catch (Exception e) {
			list = null;
		}
		if(list!=null) {
			if(list.size()>0) {
				result.addAll(list);
			}else { //初始化
				List<OutSystemUnitCompare> initList = this.initOutSystemUnitCompareData();
				if(StringUtils.isNotEmpty(initList)) {
					result = initList;
				}
			}
		}
		return result;
	}
	
	//初始化外部数据核算对象对比数据
	private List<OutSystemUnitCompare> initOutSystemUnitCompareData() {
		List<OutSystemUnitCompare> list = new ArrayList<OutSystemUnitCompare>();
		int tmSort = 0;
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT1","炼油生产一部","常减压装置Ⅰ","CDU1","CDU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT1","炼油生产一部","延迟焦化装置Ⅰ","CKU1","CKU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT1","炼油生产一部","干气分离装置","DGS1","DGS1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT1","炼油生产一部","轻烃分离装置","LHS1","LHS1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT1","炼油生产一部","常减压装置Ⅱ","CDU2","CDU2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT1","炼油生产一部","延迟焦化装置Ⅱ","CKU2","CKU2",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT2","炼油生产二部","蜡油加氢处理装置","HCU1","HCU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT2","炼油生产二部","催化裂化装置","FCC1","FCC1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT2","炼油生产二部","催化汽油加氢装置","CGH1","CGH1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT2","炼油生产二部","气分装置","GSU1","GSU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT2","炼油生产二部","烷基化装置","ALU1","ALU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT2","炼油生产二部","催化产品精制装置","CPR1","CPR1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT2","炼油生产二部","催化烟气脱硫装置","CFD1","CFD1",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT3","炼油生产三部","加氢裂化装置","HLU1","HLU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT3","炼油生产三部","柴油加氢装置Ⅰ","DHU1","DHU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT3","炼油生产三部","柴油加氢装置Ⅱ","DHU2","DHU2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT3","炼油生产三部","航煤加氢装置","AKH1","AKH1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT3","炼油生产三部","焦化石脑油加氢装置","NHU1","NHU1",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT4","炼油生产四部","石脑油加氢装置","NHU2","NHU2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT4","炼油生产四部","连续重整装置Ⅰ","RAU1","RAU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT4","炼油生产四部","连续重整装置Ⅱ","RAU2","RAU2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT4","炼油生产四部","氢气回收装置","HRU1","HRU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT4","炼油生产四部","芳烃联合装置（含芳烃抽提）","AHC1","AHC1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT4","炼油生产四部","6#含油污水预处理站","","",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","硫磺回收装置Ⅰ","SUU1","SUU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","硫磺回收装置Ⅱ","SUU2","SUU2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","硫磺回收装置Ⅲ","SUU3","SUU3",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","硫磺回收装置Ⅳ","SUU4","SUU4",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","硫磺回收装置Ⅴ","SUU5","SUU5",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","酸性水汽提装置Ⅰ","SWS1","SWS1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","酸性水汽提装置Ⅱ","SWS2","SWS2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","溶剂再生装置Ι","SRU1","SRU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","溶剂再生装置Ⅱ","SRU2","SRU2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT5","炼油生产五部","硫磺成型与包装仓库 硫磺散料堆取及贮存系统","SFU1","SFU1",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT6","化工生产一部","乙烯装置","ETU1","ETU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT6","化工生产一部","裂解汽油加氢","LQJ1","LQJ1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT6","化工生产一部","苯乙烯装置","STU1","STU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT6","化工生产一部","丁二烯装置","BUU1","BUU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT6","化工生产一部","化工1#消防水泵站","","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT6","化工生产一部","MTBE/丁烯-1装置","MTU1","MTU1",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT7","化工生产二部","高密度聚乙烯装置","HPU1","HPU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT7","化工生产二部","全密度聚乙烯装置","FDP1","FDP1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT7","化工生产二部","聚丙烯装置Ⅰ","PPU1","PPU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT7","化工生产二部","聚丙烯装置Ⅱ","PPU2","PPU2",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT8","POX运行部","石油焦制氢装置","HYU1","HYU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT8","POX运行部","氢气增压机组","HZY","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT8","POX运行部","原煤储运","YMCY","",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","污水处理场","SCU","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","全厂事故水池","SGSC","QCSGSC1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","炼油雨水收集池2","LYYS2","LYYSSJC2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","炼油雨水收集池3","LYYS3","LYYSSJC3",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","维修站","WXZ","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","炼油雨水收集池1","LYYS1","LYYSSJC1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","厂前区制冷站","CQZL","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","炼油第一循环水场","RTW1","LYXHSC1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","炼油1#消防水泵站","HFW_LY1","LYXFSBZ1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","炼油第二循环水场","RTW2","LYXHSC2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","炼油2#消防水泵站","HFW_LY2","LYXFSBZ2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","炼油第三循环水场","RTW","LYXHSC3",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","化工第一循环水场","HRTW1","HGXHSC1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","化工第二循环水场","HRTW2","HGXHSC2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","化工第三循环水场","HRTW","HGXHSC3",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","化工2#消防泵站","HFW_HG2","HGXFSBZ2",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","化工区雨水收集池机柜间","HYSSJ","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","动力中心","PUU","DLZX1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","余热回收站","YRHS","YRHSZ1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","空压站","KYZ","KYZ1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","空分装置","ASU","ASU1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","净水厂","JSC","JSC1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","除盐水站及凝液精制","CYSZ","CYS-NYJZ1",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","消防总站","XFZZ","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT9","公用工程部","消防分站","XFFZ","",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","原油罐组Ⅰ","TF_YY1","XNZZ31",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","原油罐组Ⅱ","TF_YY2","XNZZ32",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","原油罐组Ⅲ","TF_YY3","XNZZ33",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","原油罐组Ⅳ","TF_YY4","XNZZ34",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","原油罐组Ⅴ","TF_YY5","XNZZ35",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","原油末站","YYMZ","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","9#含油污水预处理站","HYWS9","HYWS9",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","4#厂区泡沫站","CQPM4","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","7#厂区泡沫站","CQPM7","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","重油中间罐组Ⅰ","TF_ZYZJ1","XNZZ56",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","重油中间罐组Ⅱ","TF_ZYZJ2","XNZZ57",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","污油罐组","TF_WY","XNZZ66",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","含硫油气处理设施","HLYQ","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","12#含油污水预处理站","HYWS12","HYWS12",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","轻油中间罐组Ι","TF_QYZJ1","XNZZ50",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","轻油中间罐组Ⅱ","TF_QYZJ2","XNZZ58",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","轻油中间罐组Ⅲ","TF_QYZJ3","XNZZ59",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","芳烃联合中间罐组Ⅰ","TF_FTLH1","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","芳烃联合中间罐组Ⅱ","TF_FTLH2","XNZZ61",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","芳烃油气处理设施","FTYQ","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","10#含油污水预处理站","HYWS10","HYWS10",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","3#厂区泡沫站","CQPM3","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","球罐组Ⅰ","TF_QG1","XNZZ62",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","球罐组Ⅱ","TF_QG2","XNZZ63",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","球罐组Ⅲ","TF_QG3","XNZZ64",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","汽油成品罐组","TF_QYCP","XNZZ67",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","汽油组分罐组","TF_QYZF","XNZZ65",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","柴油成品罐组","TF_CYCP","XNZZ68",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","航煤成品罐组","TF_HMCP","XNZZ69",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","对二甲苯罐组","TF_D2JB","XNZZ70",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","11#含油污水预处理站","HYWS11","HYWS11",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","8#含油污水预处理站","HYWS8","HYWS8",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","6#厂区泡沫站","CQPM6","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","5#厂区泡沫站","CQPM5","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","化工原料产品罐区","TF_HGYL","XNZZ01",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","炼油3#消防水泵站","HFW_LY3","LYXFSBZ3",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","液体产品汽车装卸站","TF_YTQC","XNZZ29",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","酸碱站","SJZ","XNZZ18",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","油品销售控制室","YPXS","YPXS",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","14#含油污水预处理站","HYWS14","HYWS14",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","化工中间罐区","TF_HGZJ","XNZZ71",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","液氨罐组","TF_YA","XNZZ72",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","HDPE包装厂房及仓库","HDPE_BZCK","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","FDPE包装厂房及仓库","FDPE_BZCK","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","PP包装厂房及仓库","PP_BZCK","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","炼油区外管","LYWG","LYWG",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","高架火炬系统","GJHJ","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","火炬气回收设施","HJHS","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","化学品库","HXPK","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","汽油在线调合控制系统","QYTK","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","汽油在线调合分析小屋","QYTH","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT10","储运生产部","油品移动控制系统","YPYK","",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT11","码头运行部","原油码头","YYMT","XNZZ09",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT11","码头运行部","产品码头","CPMT","XNZZ03",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT11","码头运行部","产品码头陆域","CPMTL","",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT11","码头运行部","码头库区","MTKQ","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT11","码头运行部","导热油加热炉","DRJL","",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT11","码头运行部","商储","SC","XNZZ02",1,++tmSort));
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT11","码头运行部","原油长输管线1#/3#阀室","YY13F","",1,++tmSort));
		
		list.add(new OutSystemUnitCompare(TMUID.getUID(),"PMT12","电力系统","电","DLXT","",1,++tmSort));
		
		if (entityService.insertBatch(list) == 0) {
			list.clear(); //执行失败清空列表
		}
		return list;
	}
	
}
