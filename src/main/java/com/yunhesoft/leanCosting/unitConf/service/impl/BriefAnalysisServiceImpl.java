package com.yunhesoft.leanCosting.unitConf.service.impl;


import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.dto.BriefAnalysisQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.BriefAnalysisSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.BriefAnalysisConf;
import com.yunhesoft.leanCosting.unitConf.entity.po.BriefAnalysisMeasureConf;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BriefAnalysisConfVo;
import com.yunhesoft.leanCosting.unitConf.service.IBriefAnalysisService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Update;
import com.yunhesoft.system.kernel.service.model.Where;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 简要分析设置相关服务接口实现类
 * <AUTHOR>
 * @date 2023-10-30
 */
@Service
public class BriefAnalysisServiceImpl implements IBriefAnalysisService {

	@Autowired
	private EntityService entityService;
	
	
	/**
	 * 获取简要分析设置数据（vo带附加属性）
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<BriefAnalysisConfVo> getBriefAnalysisConfVoList(BriefAnalysisQueryDto queryDto) {
		List<BriefAnalysisConfVo> result = new ArrayList<BriefAnalysisConfVo>();
		if(StringUtils.isNotNull(queryDto)) {
			String unitid = queryDto.getUnitid();
			String begintime = queryDto.getBegintime();
			if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)) {
				List<BriefAnalysisConf> list = this.getBriefAnalysisConfList(queryDto);
				if(StringUtils.isNotEmpty(list)) {
					//获取原因措施
					Map<String, List<BriefAnalysisMeasureConf>> measureConfMap = new HashMap<String, List<BriefAnalysisMeasureConf>>();
					BriefAnalysisQueryDto measureDto = new BriefAnalysisQueryDto();
					measureDto.setUnitid(unitid);
					measureDto.setBegintime(begintime);
					List<BriefAnalysisMeasureConf> measureConfList = this.getBriefAnalysisMeasureConfList(measureDto);
					if(StringUtils.isNotEmpty(measureConfList)) {
						measureConfMap = measureConfList.stream().collect(Collectors.groupingBy(BriefAnalysisMeasureConf::getAnalysisId, Collectors.toList()));
	    			}
					//遍历数据
					for (int i = 0; i < list.size(); i++) {
						BriefAnalysisConf obj = list.get(i);
						BriefAnalysisConfVo vo = new BriefAnalysisConfVo();
						BeanUtils.copyProperties(obj, vo); //赋予返回对象
						String id = vo.getId();
						if(StringUtils.isNotEmpty(id)&&StringUtils.isNotEmpty(measureConfMap)&&measureConfMap.containsKey(id)) {
							vo.setIsHasMeasure(1);
						}else {
							vo.setIsHasMeasure(0);
						}
						result.add(vo);
					}
				}
			}
		}
		return result;
	}
	
	
	/**
	 * 获取简要分析设置数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<BriefAnalysisConf> getBriefAnalysisConfList(BriefAnalysisQueryDto queryDto) {
		List<BriefAnalysisConf> result = new ArrayList<BriefAnalysisConf>();
		try {
			String unitid = ""; //核算对象id
			String begintime = ""; //版本日期 
			List<String> idList = null; //数据id列表
			List<String> goodsIdList = null; //物资id列表
			if (StringUtils.isNotNull(queryDto)) {
				unitid = queryDto.getUnitid();
				begintime = queryDto.getBegintime();
				idList = queryDto.getIdList();
				goodsIdList = queryDto.getGoodsIdList();
			}
			// 检索条件
			Where where = Where.create();
			where.eq(BriefAnalysisConf::getTmUsed, 1);
			if (StringUtils.isNotEmpty(unitid)) {
				where.eq(BriefAnalysisConf::getUnitid, unitid);
			}
			if (StringUtils.isNotEmpty(begintime)) {
				where.eq(BriefAnalysisConf::getBegintime, begintime);
			}
			if (StringUtils.isNotEmpty(idList)) {
				where.in(BriefAnalysisConf::getId, idList.toArray());
			}
			if (StringUtils.isNotEmpty(goodsIdList)) {
				where.in(BriefAnalysisConf::getGoodsId, goodsIdList.toArray());
			}			
			// 排序
			Order order = Order.create();
			order.orderByAsc(BriefAnalysisConf::getUnitid);
			order.orderByDesc(BriefAnalysisConf::getBegintime);
			order.orderByAsc(BriefAnalysisConf::getTmSort);
			List<BriefAnalysisConf> list = entityService.queryData(BriefAnalysisConf.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	

	/**
	 * 保存简要分析设置数据
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveBriefAnalysisConfData(BriefAnalysisSaveDto saveDto) {
		String result = "";
		List<BriefAnalysisConf> addList = new ArrayList<BriefAnalysisConf>();
       	List<BriefAnalysisConf> updList = new ArrayList<BriefAnalysisConf>();
		if (saveDto != null) {
			String unitid = saveDto.getUnitid();
			String begintime = saveDto.getBegintime();
			List<BriefAnalysisConfVo> saveList = saveDto.getAnalysisList();
            if (StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&StringUtils.isNotEmpty(saveList)) {
            	int maxNum = 0; //最大序号
            	Map<String, BriefAnalysisConf> dataMap = new HashMap<String, BriefAnalysisConf>();
            	BriefAnalysisQueryDto queryDto = new BriefAnalysisQueryDto();
            	queryDto.setUnitid(unitid);
            	queryDto.setBegintime(begintime);
            	List<BriefAnalysisConf> dataList = this.getBriefAnalysisConfList(queryDto);
            	if(StringUtils.isNotEmpty(dataList)) {
            		dataMap = dataList.stream().collect(Collectors.toMap(BriefAnalysisConf::getId,Function.identity()));
            		Integer maxSort = dataList.get(dataList.size()-1).getTmSort();
            		if(maxSort!=null) {
            			maxNum = maxSort;
            		}
            	}
            	for (int i = 0; i < saveList.size(); i++) {
            		BriefAnalysisConfVo saveObj = saveList.get(i);
            		Integer rowFlag = saveObj.getRowFlag()==null?0:saveObj.getRowFlag();
            		String id_save = saveObj.getId();
            		if(rowFlag==-1) { //删除
            			if(StringUtils.isNotEmpty(id_save)&&StringUtils.isNotEmpty(dataMap)&&dataMap.containsKey(id_save)) {
            				BriefAnalysisConf obj = dataMap.get(id_save);
                        	if(StringUtils.isNotNull(obj)) {
                        		obj.setTmUsed(0);
                    			updList.add(obj);
                        	}
            			}
            		}else { //新增或修改
            			if(StringUtils.isNotEmpty(id_save)&&StringUtils.isNotEmpty(dataMap)&&dataMap.containsKey(id_save)) { //修改
            				BriefAnalysisConf obj = dataMap.get(id_save);
    						BeanUtils.copyProperties(saveObj, obj); //赋予返回对象
    						updList.add(obj);
                		}else { //新增
                			maxNum += 1;
                			BriefAnalysisConf obj = new BriefAnalysisConf();
            				BeanUtils.copyProperties(saveObj, obj); //赋予返回对象
            				String id = obj.getId();
            				if(StringUtils.isEmpty(id)) {
            					id = TMUID.getUID();
            				}
            				obj.setId(id);
            				obj.setUnitid(unitid);
            				obj.setBegintime(begintime);
            				obj.setTmUsed(1);
            				obj.setTmSort(maxNum);
            				addList.add(obj);
                		}
            		}
				}
            }
        }
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(addList)) {
			if (entityService.insertBatch(addList) == 0) {
				result = "添加失败（简要分析设置）！";
			}
		}
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(updList)) {
			if (entityService.updateByIdBatch(updList) == 0) {
				result = "更新失败（简要分析设置）！";
			}
		}
		return result;
	}
	
	/**
	 * 查询措施
	 * @param analysisId	简要分析id
	 * @return
	 */
	@Override
	public List<BriefAnalysisMeasureConf> queryMeasureList(String analysisId) {
		Where where = Where.create().eq(BriefAnalysisMeasureConf::getTmUsed, 1).eq(BriefAnalysisMeasureConf::getAnalysisId, analysisId);
		Order order = Order.create().orderByAsc(BriefAnalysisMeasureConf::getTmSort).orderByAsc(BriefAnalysisMeasureConf::getId);
		return entityService.queryData(BriefAnalysisMeasureConf.class, where, order, null);
	}

	/**
	 * 保存措施
	 * @return
	 */
	@Override
	public List<BriefAnalysisMeasureConf> saveMeasureList(List<BriefAnalysisMeasureConf> list) {
		if (StringUtils.isEmpty(list)) {
			return null;
		}
		List<BriefAnalysisMeasureConf> updateList = new ArrayList<>();
		List<BriefAnalysisMeasureConf> insertList = new ArrayList<>();

		for (BriefAnalysisMeasureConf item : list) {
			item.setTmUsed(1);
			if (StringUtils.isEmpty(item.getId())) {
				//新增
				item.setId(TMUID.getUID());
				insertList.add(item);
			} else {
				//修改
				updateList.add(item);
			}
		}

		if (StringUtils.isNotEmpty(insertList)) {
			Integer maxSort = entityService.findMaxValue(BriefAnalysisMeasureConf.class, BriefAnalysisMeasureConf::getTmSort, Integer.class, Where.create().eq(BriefAnalysisMeasureConf::getTmUsed, 1).eq(BriefAnalysisMeasureConf::getAnalysisId, insertList.get(0).getAnalysisId()));
			if (maxSort == null) {
				maxSort = 0;
			}
			for (BriefAnalysisMeasureConf item : insertList) {
				item.setTmSort(++maxSort);
			}
			entityService.insertBatch(insertList);
		}
		if (StringUtils.isNotEmpty(updateList)) {
			entityService.updateBatch(updateList);
		}
		return null;
	}

	/**
	 * 批量删除
	 * @param idList
	 * @return
	 */
	@Override
	public String deleteMeasureByIdList(List<String> idList) {
		if (StringUtils.isEmpty(idList)) {
			return null;
		}
		Update update = Update.create(BriefAnalysisMeasureConf::getTmUsed, 0);
		Where where = Where.create().in(BriefAnalysisMeasureConf::getId, idList.toArray());
		entityService.rawUpdate(BriefAnalysisMeasureConf.class, update, where);
		return null;
	}

	
	/**
	 *	获取简要分析原因措施数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<BriefAnalysisMeasureConf> getBriefAnalysisMeasureConfList(BriefAnalysisQueryDto queryDto) {
		List<BriefAnalysisMeasureConf> result = new ArrayList<BriefAnalysisMeasureConf>();
		try {
			String unitid = ""; //核算对象id
			String begintime = ""; //版本日期 
			String analysisId = ""; //简要分析id
			if (StringUtils.isNotNull(queryDto)) {
				unitid = queryDto.getUnitid();
				begintime = queryDto.getBegintime();
				analysisId = queryDto.getAnalysisId();
			}

			// 检索条件
			Where where = Where.create();
			where.eq(BriefAnalysisMeasureConf::getTmUsed, 1);
			if (StringUtils.isNotEmpty(unitid)) {
				where.eq(BriefAnalysisMeasureConf::getUnitid, unitid);
			}
			if (StringUtils.isNotEmpty(begintime)) {
				where.eq(BriefAnalysisMeasureConf::getBegintime, begintime);
			}
			if (StringUtils.isNotEmpty(analysisId)) {
				where.eq(BriefAnalysisMeasureConf::getAnalysisId, analysisId);
			}
			
			// 排序
			Order order = Order.create();
			order.orderByAsc(BriefAnalysisMeasureConf::getUnitid);
			order.orderByDesc(BriefAnalysisMeasureConf::getBegintime);
			order.orderByAsc(BriefAnalysisMeasureConf::getAnalysisId);
			order.orderByAsc(BriefAnalysisMeasureConf::getTmSort);
			List<BriefAnalysisMeasureConf> list = entityService.queryData(BriefAnalysisMeasureConf.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	
	/**
	 *	同步更新简要分析设置的物资名称（项目、仪表、指标修改名称后调用）
	 * @param itemList  项目
	 * @param instrumentList  仪表
	 * @param indicatorList  指标
	 * @return
	 */
	@Override
	public String updateBriefAnalysisName(List<Costitem> itemList, List<Costinstrument> instrumentList, List<Costindicator> indicatorList) {
		String result = "";
		List<BriefAnalysisConf> updList = new ArrayList<BriefAnalysisConf>();
		List<String> goodsIdList = new ArrayList<String>();
		Map<String, Costitem> itemMap = new HashMap<String, Costitem>();
		Map<String, Costinstrument> instrumentMap = new HashMap<String, Costinstrument>();
		Map<String, Costindicator> indicatorMap = new HashMap<String, Costindicator>();
		//项目
		if(StringUtils.isNotEmpty(itemList)) {
			itemMap = itemList.stream().collect(Collectors.toMap(Costitem::getId, Function.identity()));
			List<String> idList = itemList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
			if(StringUtils.isNotEmpty(idList)) {
				goodsIdList.addAll(idList);
			}
		}
		//仪表
		if(StringUtils.isNotEmpty(instrumentList)) {
			instrumentMap = instrumentList.stream().collect(Collectors.toMap(Costinstrument::getId, Function.identity()));
			List<String> idList = instrumentList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
			if(StringUtils.isNotEmpty(idList)) {
				goodsIdList.addAll(idList);
			}
		}
		//指标
		if(StringUtils.isNotEmpty(indicatorList)) {
			indicatorMap = indicatorList.stream().collect(Collectors.toMap(Costindicator::getId, Function.identity()));
			List<String> idList = indicatorList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
			if(StringUtils.isNotEmpty(idList)) {
				goodsIdList.addAll(idList);
			}
		}
		//根据物资id列表，获取简要分析设置数据
		if(StringUtils.isNotEmpty(goodsIdList)) {
			BriefAnalysisQueryDto dto = new BriefAnalysisQueryDto();
			dto.setGoodsIdList(goodsIdList);
			List<BriefAnalysisConf> analysisList = this.getBriefAnalysisConfList(dto);
			if(StringUtils.isNotEmpty(analysisList)) {
				for (int i = 0; i < analysisList.size(); i++) {
					BriefAnalysisConf analysisObj = analysisList.get(i);
					String goodsId = analysisObj.getGoodsId();
					String goodsName = analysisObj.getGoodsName()==null?"":analysisObj.getGoodsName();
					String goodsUnit = analysisObj.getItemUnit()==null?"":analysisObj.getItemUnit();
					if(StringUtils.isNotEmpty(itemMap)&&itemMap.containsKey(goodsId)) { //项目
						Costitem obj = itemMap.get(goodsId);
						String newName = obj.getItemname()==null?"":obj.getItemname();
						String newUnit = obj.getItemunit()==null?"":obj.getItemunit();
						boolean isUpd = false;
						if(!newName.equals(goodsName)) {
							analysisObj.setGoodsName(newName);
							isUpd = true;
						}
						if(!newUnit.equals(goodsUnit)) {
							analysisObj.setItemUnit(newUnit);
							isUpd = true;
						}
						if(isUpd) {
							updList.add(analysisObj);
						}
					}else if(StringUtils.isNotEmpty(instrumentMap)&&instrumentMap.containsKey(goodsId)) { //仪表
						Costinstrument obj = instrumentMap.get(goodsId);
						String newName = obj.getName()==null?"":obj.getName();
						if(!newName.equals(goodsName)) {
							analysisObj.setGoodsName(newName);
							updList.add(analysisObj);
						}
					}else if(StringUtils.isNotEmpty(indicatorMap)&&indicatorMap.containsKey(goodsId)) { //指标
						Costindicator obj = indicatorMap.get(goodsId);
						String newName = obj.getCpname()==null?"":obj.getCpname();
						String newUnit = obj.getItemunit()==null?"":obj.getItemunit();
						boolean isUpd = false;
						if(!newName.equals(goodsName)) {
							analysisObj.setGoodsName(newName);
							isUpd = true;
						}
						if(!newUnit.equals(goodsUnit)) {
							analysisObj.setItemUnit(newUnit);
							isUpd = true;
						}
						if(isUpd) {
							updList.add(analysisObj);
						}
					}
				}
			}
		}
		if(StringUtils.isNotEmpty(updList)) {
			if (entityService.updateByIdBatchIncludeNull(updList, 500) == 0) {
				result = "同步更新失败（简要分析设置）！";
			}
		}
		return result;
	}
	

}
