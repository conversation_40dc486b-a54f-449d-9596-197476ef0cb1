package com.yunhesoft.leanCosting.unitConf.service;


import java.util.List;

import com.yunhesoft.leanCosting.unitConf.entity.dto.ElemLibQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.ElemLibSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.ElemLib;
import com.yunhesoft.leanCosting.unitConf.entity.vo.ElemLibTreeVo;


/**
 *	要素库设置相关服务接口
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface IElemLibService {
	
	//-------------------------------- 要素库设置 ↓ ----------------------------------
	
	/**
	 *	获取要素库树形数据
	 * @param queryDto
	 * @return
	 */
	public List<ElemLibTreeVo> getElemLibTreeList(ElemLibQueryDto queryDto);
	
	/**
	 *	保存要素库数据
	 * @param saveDto
	 * @return
	 */
	public String saveElemLibData(ElemLibSaveDto saveDto);
	
	/**
	 *	获取要素对象
	 * @param id
	 * @return
	 */
	public ElemLib getElemLibById(String id);
	
	/**
	 *	判断采集要素是否在采集点中使用
	 * @param elemId
	 * @return
	 */
	public Boolean isUseByElemId(String elemId);
	
	/**
	 *	保存要素排序数据
	 * @param saveDto
	 * @return
	 */
	public String saveElemLibSort(ElemLibSaveDto saveDto);
	
}
