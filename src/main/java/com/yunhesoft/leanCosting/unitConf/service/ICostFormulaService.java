package com.yunhesoft.leanCosting.unitConf.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.leanCosting.unitConf.entity.dto.CostFormulaQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostItemFormulaSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostFormulaVo;

public interface ICostFormulaService {

	/**
	 * 查询
	 * @param dto
	 * @return
	 */
	public CostFormulaVo getData(CostFormulaQueryDto dto);

	/**
	 * 保存数据
	 * @param dto
	 * @return
	 */
	public String saveData(CostItemFormulaSaveDto dto);

	/**
	 * 初始化项目公式
	 * @param costitem
	 * @return
	 */
	boolean initCostitem(List<Costitem> costitems);
	
	/**
	 * 初始化仪表公式
	 * @param costinstrument
	 * @return
	 */
	boolean initCostinstrument(List<Costinstrument> costinstruments);

	/**
	 * 初始化项目-消耗量
	 * @param costitemMap	Map<Costitem.ID,formula>
	 * @param objMap		Map<Costitem.ID,Map<String, Costinstrument>>
	 * @return
	 */
	public Map<String, List<CostItemFormula>> initCostItemHxl(Map<String,String> costitemMap,Map<String,Map<String, Costinstrument>> objMap);
	
	
	/**
	 * 初始化公式（租户内初始化[多租户模式下]）-如果有对应公式则不更新
	 * 更新公式范围全部核算对象下-核算项目公式
	 *  公式包括-项目公式，仪表公式
	 * @return
	 */
	public String initCostFormulaService();

}
