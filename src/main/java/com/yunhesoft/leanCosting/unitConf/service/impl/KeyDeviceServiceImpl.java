package com.yunhesoft.leanCosting.unitConf.service.impl;


import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.deviceStatus.entity.dto.ParamObj;
import com.yunhesoft.leanCosting.deviceStatus.service.IDeviceStatusService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostKeyDeviceQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostKeyDeviceSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.DeviceParamDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostDeviceStatusData;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostKeyDeviceConf;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostKeyDeviceConfVo;
import com.yunhesoft.leanCosting.unitConf.service.IKeyDeviceService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 简要分析设置相关服务接口实现类
 * <AUTHOR>
 * @date 2023-10-30
 */
@Service
public class KeyDeviceServiceImpl implements IKeyDeviceService {

	@Autowired
	private EntityService entityService;
	
	@Autowired
	private IUnitMethodService methodService;
	
	@Autowired
	private IDeviceStatusService statusService;
	
	
	/**
	 * 获取关键设备设置数据（vo带附加属性）
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<CostKeyDeviceConfVo> getCostKeyDeviceConfVoList(CostKeyDeviceQueryDto queryDto) {
		List<CostKeyDeviceConfVo> result = new ArrayList<CostKeyDeviceConfVo>();
		if(StringUtils.isNotNull(queryDto)) {
			String unitid = queryDto.getUnitid();
			String begintime = queryDto.getBegintime();
			if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)) {
				List<CostKeyDeviceConf> list = this.getKeyDeviceConfList(queryDto);
				if(StringUtils.isNotEmpty(list)) {
					//设备运行状态Map
					Map<String, List<CostDeviceStatusData>> statusMap = new HashMap<String, List<CostDeviceStatusData>>();
					List<String> idList = list.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
					if(StringUtils.isNotEmpty(idList)) {
						ParamObj param = new ParamObj();
						param.setDeviceIdList(idList);
						List<CostDeviceStatusData> statusList = statusService.getDeviceStatusDataList(param);
						if(StringUtils.isNotEmpty(statusList)) {
							statusMap = statusList.stream().collect(Collectors.groupingBy(CostDeviceStatusData::getDeviceId));
						}
					}
					for (int i = 0; i < list.size(); i++) {
						CostKeyDeviceConf obj = list.get(i);
						CostKeyDeviceConfVo vo = new CostKeyDeviceConfVo();
						BeanUtils.copyProperties(obj, vo); //赋予返回对象
						String id = vo.getId();
						if(StringUtils.isNotEmpty(id)&&StringUtils.isNotEmpty(statusMap)&&statusMap.containsKey(id)) {
							vo.setHasStartStop(true);
						}else {
							vo.setHasStartStop(false);
						}
						//检修周期小时
						int overhaulCycle = vo.getOverhaulCycle()==null?0:vo.getOverhaulCycle();
						vo.setOverhaulCycle(overhaulCycle);
						//设备启停状态
						int deviceSsState = vo.getDeviceSsState()==null?0:vo.getDeviceSsState();
						vo.setDeviceSsState(deviceSsState);
						result.add(vo);
					}
				}
			}
		}
		return result;
	}
	
	
	/**
	 * 获取关键设备设置数据（单表）
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<CostKeyDeviceConf> getKeyDeviceConfList(CostKeyDeviceQueryDto queryDto) {
		List<CostKeyDeviceConf> result = new ArrayList<CostKeyDeviceConf>();
		try {
			String unitid = ""; //核算对象id
			String begintime = ""; //版本日期 
			Integer isUseToRecordEvent = null; //用于记事
			String deviceName = ""; //设备名称
			Pagination<?> page = null;
			String searchName = null; //名称
			Integer statusNum = null; //启停状态
			if (StringUtils.isNotNull(queryDto)) {
				unitid = queryDto.getUnitid();
				begintime = queryDto.getBegintime();
				isUseToRecordEvent = queryDto.getIsUseToRecordEvent();
				deviceName = queryDto.getDeviceName();
				page = queryDto.getPage();
				searchName = queryDto.getSearchName();
				statusNum = queryDto.getStatusNum();
			}
			// 检索条件
			Where where = Where.create();
			where.eq(CostKeyDeviceConf::getTmUsed, 1);
			if (StringUtils.isNotEmpty(unitid)) {
				where.eq(CostKeyDeviceConf::getUnitid, unitid);
			}
			if (StringUtils.isNotEmpty(begintime)) {
				where.eq(CostKeyDeviceConf::getBegintime, begintime);
			}
			if (isUseToRecordEvent!=null) {
				if(isUseToRecordEvent==1) {
					where.eq(CostKeyDeviceConf::getIsUseToRecordEvent, isUseToRecordEvent);
				}else {
					where.and();
					where.lb();
					where.isEmpty(CostKeyDeviceConf::getIsUseToRecordEvent);
					where.or();
					where.eq(CostKeyDeviceConf::getIsUseToRecordEvent, 0);
					where.rb();
				}
			}
			if (StringUtils.isNotEmpty(deviceName)) {
				where.like(CostKeyDeviceConf::getDeviceName, deviceName.trim());
			}
			if (StringUtils.isNotEmpty(searchName)) {
				searchName = searchName.trim();
				where.and();
				where.lb();
				where.like(CostKeyDeviceConf::getDeviceName, searchName);
				where.or();
				where.like(CostKeyDeviceConf::getDeviceTagNo, searchName);
				where.rb();
			}
			if (statusNum!=null&&statusNum!=-1) { //启停状态
				if(statusNum==1) {
					where.eq(CostKeyDeviceConf::getDeviceSsState, 1);
				}else {
					where.and();
					where.lb();
					where.isEmpty(CostKeyDeviceConf::getDeviceSsState);
					where.or();
					where.eq(CostKeyDeviceConf::getDeviceSsState, 0);
					where.rb();
				}
			}
			// 排序
			Order order = Order.create();
			order.orderByAsc(CostKeyDeviceConf::getUnitid);
			order.orderByDesc(CostKeyDeviceConf::getBegintime);
			order.orderByAsc(CostKeyDeviceConf::getPid);
			order.orderByAsc(CostKeyDeviceConf::getTmSort);
			List<CostKeyDeviceConf> list = entityService.queryData(CostKeyDeviceConf.class, where, order, page);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	

	/**
	 * 保存简要分析设置数据
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveCostKeyDeviceData(CostKeyDeviceSaveDto saveDto) {
		String result = "";
		List<CostKeyDeviceConf> addList = new ArrayList<CostKeyDeviceConf>();
       	List<CostKeyDeviceConf> updList = new ArrayList<CostKeyDeviceConf>();
		if (saveDto != null) {
			String unitid = saveDto.getUnitid();
			String begintime = saveDto.getBegintime();
			List<CostKeyDeviceConfVo> saveList = saveDto.getKeyDeviceList();
            if (StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&StringUtils.isNotEmpty(saveList)) {
            	//保存记录的唯一ID
				List<String> saveIdList = new ArrayList<String>();
				for (int i = 0; i < saveList.size(); i++) {
					CostKeyDeviceConfVo saveObj = saveList.get(i);
					Integer rowFlag = saveObj.getRowFlag()==null?0:saveObj.getRowFlag();
					String id_save = saveObj.getId();
					if(rowFlag!=-1&&StringUtils.isNotEmpty(id_save)&&!saveIdList.contains(id_save)) {
						saveIdList.add(id_save);
					}
				}
				//获取已存在记录
            	HashMap<String, CostKeyDeviceConf> sameNameMap = new HashMap<String, CostKeyDeviceConf>(); //判断设备名称重复
            	HashMap<String, CostKeyDeviceConf> sameTagNoMap = new HashMap<String, CostKeyDeviceConf>(); //判断设备位号重复
            	HashMap<String, CostKeyDeviceConf> dataMap = new HashMap<String, CostKeyDeviceConf>();
    			HashMap<String, Integer> maxSortMap = new HashMap<String, Integer>();
            	CostKeyDeviceQueryDto queryDto = new CostKeyDeviceQueryDto();
            	queryDto.setUnitid(unitid);
            	queryDto.setBegintime(begintime);
            	List<CostKeyDeviceConf> dataList = this.getKeyDeviceConfList(queryDto);
            	if(StringUtils.isNotEmpty(dataList)) {
            		this.getCostKeyDeviceConfMap(dataList, dataMap, maxSortMap, sameNameMap, sameTagNoMap);
            	}
            	
            	for (int i = 0; i < saveList.size(); i++) {
            		CostKeyDeviceConfVo saveObj = saveList.get(i);
            		Integer rowFlag = saveObj.getRowFlag()==null?0:saveObj.getRowFlag();
            		String id_save = saveObj.getId();
            		if(rowFlag==-1) { //删除
            			if(StringUtils.isNotEmpty(id_save)&&StringUtils.isNotEmpty(dataMap)&&dataMap.containsKey(id_save)) {
            				CostKeyDeviceConf obj = dataMap.get(id_save);
                        	if(StringUtils.isNotNull(obj)) {
                        		//前台单条删除数据（判断是否有启停记录，有：不能删除）
                        		ParamObj param = new ParamObj();
        						param.setDeviceId(id_save);
        						List<CostDeviceStatusData> statusList = statusService.getDeviceStatusDataList(param);
                        		if(StringUtils.isNotEmpty(statusList)) {
                        			result = "设备名称【" + saveObj.getDeviceName() + "】已存在启停（或检修、修正）记录，不能进行删除操作！";
                        			return result;
                        		}else {
                        			obj.setTmUsed(0);
                        			updList.add(obj);
                        		}
                        	}
            			}
            		}else { //新增或修改
            			String deviceName_save = saveObj.getDeviceName()==null?"":saveObj.getDeviceName().trim();
            			String deviceStatus_save = saveObj.getDeviceStatus()==null?"":saveObj.getDeviceStatus().trim();
            			String deviceTagNo_save = saveObj.getDeviceTagNo()==null?"":saveObj.getDeviceTagNo().trim();
            			saveObj.setDeviceName(deviceName_save);
            			saveObj.setDeviceStatus(deviceStatus_save);
            			saveObj.setDeviceTagNo(deviceTagNo_save);
            			if(StringUtils.isNotEmpty(id_save)&&StringUtils.isNotEmpty(dataMap)&&dataMap.containsKey(id_save)) { //修改
            				if(StringUtils.isNotEmpty(sameNameMap)&&StringUtils.isNotEmpty(deviceName_save)&&sameNameMap.containsKey(deviceName_save)) {
								String sameId = sameNameMap.get(deviceName_save).getId();
								if (StringUtils.isNotEmpty(sameId) && !saveIdList.contains(sameId)) {
									result = "设备名称【" + deviceName_save + "】已存在，不能重复设置！";
									return result;
								}
							}
            				if(StringUtils.isNotEmpty(sameTagNoMap)&&StringUtils.isNotEmpty(deviceTagNo_save)&&sameTagNoMap.containsKey(deviceTagNo_save)) {
								String sameId = sameTagNoMap.get(deviceTagNo_save).getId();
								if (StringUtils.isNotEmpty(sameId) && !saveIdList.contains(sameId)) {
									result = "设备位号【" + deviceTagNo_save + "】已存在，不能重复设置！";
									return result;
								}
							}
            				CostKeyDeviceConf obj = dataMap.get(id_save);
    						//BeanUtils.copyProperties(saveObj, obj); //不能复制对象，影响隐藏属性数据
            				obj.setDeviceName(saveObj.getDeviceName());
            				obj.setDeviceTagNo(saveObj.getDeviceTagNo());
            				obj.setDeviceMatter(saveObj.getDeviceMatter());
            				obj.setOverhaulCycle(saveObj.getOverhaulCycle());
            				obj.setDeviceStatus(saveObj.getDeviceStatus());
            				obj.setIsUseToRecordEvent(saveObj.getIsUseToRecordEvent());
    						updList.add(obj);
                		}else { //新增
                			if(StringUtils.isNotEmpty(sameNameMap)&&StringUtils.isNotEmpty(deviceName_save)&&sameNameMap.containsKey(deviceName_save)) {
								result = "设备名称【" + deviceName_save + "】已存在，不能重复设置！";
								return result;
							}
                			if(StringUtils.isNotEmpty(sameTagNoMap)&&StringUtils.isNotEmpty(deviceTagNo_save)&&sameTagNoMap.containsKey(deviceTagNo_save)) {
								result = "设备位号【" + deviceTagNo_save + "】已存在，不能重复设置！";
								return result;
							}
                			CostKeyDeviceConf obj = new CostKeyDeviceConf();
            				BeanUtils.copyProperties(saveObj, obj); //赋予返回对象
            				String pid = obj.getPid();
            				if(StringUtils.isEmpty(pid)) {
            					pid = "nullStr";
            				}
            				int maxPx = 0;
            				if(StringUtils.isNotEmpty(maxSortMap)&&maxSortMap.containsKey(pid)) {
            					maxPx = maxSortMap.get(pid);
            				}
            				maxPx += 1;
            				maxSortMap.put(pid, maxPx);
            				
            				String id = obj.getId();
            				if(StringUtils.isEmpty(id)) {
            					id = TMUID.getUID();
            				}
            				obj.setId(id);
            				obj.setUnitid(unitid);
            				obj.setBegintime(begintime);
            				obj.setTmUsed(1);
            				obj.setTmSort(maxPx);
            				addList.add(obj);
                		}
            		}
				}
            }
        }
		result = this.saveDataCostKeyDevice(addList, updList, null);
		return result;
	}
	
	/**
	 *	保存关键设备数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	@Override
	public String saveDataCostKeyDevice(List<CostKeyDeviceConf> addList,List<CostKeyDeviceConf> updList,List<CostKeyDeviceConf> delList) {
		String result = "";
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(addList)) {
			if (entityService.insertBatch(addList, 500) == 0) {
				result = "添加失败（关键设备设置）！";
			}
		}
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(updList)) {
			if (entityService.updateByIdBatch(updList, 500) == 0) {
				result = "更新失败（关键设备设置）！";
			}
		}
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(delList)) {
			if (entityService.deleteByIdBatch(delList, 500) == 0) {
				result = "删除失败（关键设备设置）！";
			}
		}
		return result;
	}
	
	
	/**
	 *	获取关键设备数据Map
	 * @param dataList
	 * @param dataMap
	 * @param maxSortMap
	 * @param sameNameMap
	 * @param sameTagNoMap
	 */
	private void getCostKeyDeviceConfMap(List<CostKeyDeviceConf> dataList,HashMap<String, CostKeyDeviceConf> dataMap,
		HashMap<String, Integer> maxSortMap,HashMap<String, CostKeyDeviceConf> sameNameMap,HashMap<String, CostKeyDeviceConf> sameTagNoMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, CostKeyDeviceConf>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(sameNameMap==null) {
			sameNameMap = new HashMap<String, CostKeyDeviceConf>();
		}
		if(sameTagNoMap==null) {
			sameTagNoMap = new HashMap<String, CostKeyDeviceConf>();
		}
		if(StringUtils.isNotEmpty(dataList)) {
			for (int i = 0; i < dataList.size(); i++) {
				CostKeyDeviceConf dataObj = dataList.get(i);
				String id = dataObj.getId();
				if(!dataMap.containsKey(id)) {
					dataMap.put(id, dataObj);
				}
				String pid = dataObj.getPid();
				if(StringUtils.isEmpty(pid)) {
					pid = "nullStr";
				}
				int tmSort = dataObj.getTmSort()==null?0:dataObj.getTmSort();
				if(maxSortMap.containsKey(pid)) {
					int maxPx = maxSortMap.get(pid);
					if(tmSort>maxPx) {
						maxSortMap.put(pid, tmSort);
					}
				}else {
					maxSortMap.put(pid, tmSort);
				}
				String deviceName = dataObj.getDeviceName()==null?"":dataObj.getDeviceName().trim();
				if(StringUtils.isNotEmpty(deviceName)) {
					if(!sameNameMap.containsKey(deviceName)) {
						sameNameMap.put(deviceName, dataObj);
					}
				}
				String deviceTagNo = dataObj.getDeviceTagNo()==null?"":dataObj.getDeviceTagNo().trim();
				if(StringUtils.isNotEmpty(deviceTagNo)) {
					if(!sameTagNoMap.containsKey(deviceTagNo)) {
						sameTagNoMap.put(deviceTagNo, dataObj);
					}
				}
			}
		}
	}
	
	
	/**
	 *	获取用于记事的关键设备列表
	 * @param unitid  （必填项）
	 * @param begintime  （非必填）
	 * @return
	 */
	@Override
	public List<CostKeyDeviceConfVo> getUseToRecordEventList(String unitid, String begintime) {
		List<CostKeyDeviceConfVo> result = new ArrayList<CostKeyDeviceConfVo>();
		if(StringUtils.isNotEmpty(unitid)) {
			String maxVer = methodService.getMaxVersionCostunit(unitid, begintime);
			if(StringUtils.isNotEmpty(maxVer)) {
				CostKeyDeviceQueryDto queryDto = new CostKeyDeviceQueryDto();
				queryDto.setUnitid(unitid);
				queryDto.setBegintime(maxVer);
				queryDto.setIsUseToRecordEvent(1);
				List<CostKeyDeviceConf> list = this.getKeyDeviceConfList(queryDto);
				if(list==null) {
					result = null;
				}else {
					if(list.size()>0) {
						for (int i = 0; i < list.size(); i++) {
							CostKeyDeviceConf obj = list.get(i);
							CostKeyDeviceConfVo vo = new CostKeyDeviceConfVo();
							BeanUtils.copyProperties(obj, vo); //赋予返回对象
							result.add(vo);
						}
					}
				}
			}
		}
		return result;
	}


	@Override
	public List<CostKeyDeviceConf> getCostDeviceList(DeviceParamDto param) {
		List<CostKeyDeviceConf> result = new ArrayList<CostKeyDeviceConf>();
		if(StringUtils.isNotNull(param) && StringUtils.isNotEmpty(param.getUnitid())) {
			String begintime = param.getBegintime() == null ? DateTimeUtils.getNowDateStr() : param.getBegintime();
			CostKeyDeviceQueryDto queryDto = ObjUtils.copyTo(param, CostKeyDeviceQueryDto.class);
			queryDto.setBegintime(begintime);
			
			List<CostKeyDeviceConf> list = this.getKeyDeviceConfList(queryDto);
			if(StringUtils.isNotEmpty(list)) {
				for (int i = 0; i < list.size(); i++) {
					CostKeyDeviceConf obj = list.get(i);
					CostKeyDeviceConfVo vo = new CostKeyDeviceConfVo();
					BeanUtils.copyProperties(obj, vo); //赋予返回对象
					result.add(vo);
				}
			}
		}
		return result;
	}

}
