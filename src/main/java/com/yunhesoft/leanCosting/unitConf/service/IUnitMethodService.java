package com.yunhesoft.leanCosting.unitConf.service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costlibraryitem;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlan;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostMeteringUnit;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostStipulateTime;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostunitSampledotWidgetConfig;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitcycle;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitmanager;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitversion;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BeanVo;

/**
 * 核算方法服务接口
 * 
 * <AUTHOR>
 * @date 2023-07-14
 */
public interface IUnitMethodService {

	/**
	 * 核算对象（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costuint> getCostuintList(MethodQueryDto queryDto);

	/**
	 * 根据核算对象id（查询）
	 * 
	 * @param id
	 * @return
	 */
	public Costuint getCostuintById(String id);
	
	/**
	 * 核算对象（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostuintData(MethodSaveDto saveDto);
	
	/**
	 *	根据人员的采集点分类录入权限获取核算对象数据
	 * @param user
	 * @return
	 */
	public List<Costuint> getCostuintListByDotClassInputRight(SysUser user);
	
	/**
	 *	保存核算对象数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveCostuintData(List<Costuint> addList,List<Costuint> updList,List<Costuint> delList);
	
	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 * 核算对象的操作对象 （查询）
	 * 
	 * @param dto
	 * @return
	 */
	public List<Costunitoperator> getCostunitoperatorList(MethodQueryDto queryDto);
	
	/**
	 *	获取新的操作对象
	 * @param objid
	 * @param objType
	 * @param unitid
	 * @param tmsort
	 * @return
	 */
	public Costunitoperator getNewCostunitoperatorObj(String objid, String objType, String unitid, Integer tmsort);

	/**
	 * 核算对象的操作对象（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostunitoperatorData(MethodSaveDto saveDto);
	
	/**
	 *	保存核算对象的操作对象
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveCostunitoperatorData(List<Costunitoperator> addList,List<Costunitoperator> updList,List<Costunitoperator> delList);

	/**
	 * 核算对象的管理对象 （查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costunitmanager> getCostunitmanagerList(MethodQueryDto queryDto);

	/**
	 * 核算对象的管理对象（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostunitmanagerData(MethodSaveDto saveDto);

	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 * 核算对象周期（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costunitcycle> getCostunitcycleList(MethodQueryDto queryDto);

	/**
	 * 核算对象周期（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostunitcycleData(MethodSaveDto saveDto);

	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 * 核算单元版本（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costunitversion> getCostunitversionList(MethodQueryDto queryDto);

	/**
	 *	核算单元版本最大版本
	 * @param unitid
	 * @param begintime
	 * @return
	 */
	public String getMaxVersionCostunit(String unitid,String begintime);
	
	/**
	 * 核算单元版本（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostunitversionData(MethodSaveDto saveDto);

	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 * 核算对象的采集点分类（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costunitsampleclass> getCostunitsampleclassList(MethodQueryDto queryDto);

	
	/**
	 *	核算对象的采集点分类最大版本
	 * @param unitid
	 * @param begintime
	 * @return
	 */
	public String getMaxVersionByCostunitsampleclass(String unitid, String begintime);
	
	
	/**
	 * 核算对象的采集点分类（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostunitsampleclassData(MethodSaveDto saveDto);
	
	/**
	 *	保存采集点分类数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveSampleclassData(List<Costunitsampleclass> addList,List<Costunitsampleclass> updList,List<Costunitsampleclass> delList);

	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 * 核算对象采集点（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costunitsampledot> getCostunitsampledotList(MethodQueryDto queryDto);
	
	/**
	 * 核算对象下所有采集点（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costunitsampledot> getAllSampledotListByUnitid(String unitid, String begintime);
	
	/**
	 *	设置核算对象采集点默认属性
	 * @param obj
	 * @return
	 */
	public Costunitsampledot setSampledotAttribute(Costunitsampledot obj);
	
	/**
	 *	核算对象采集点最大版本
	 * @param unitid
	 * @param begintime
	 * @return
	 */
	public String getMaxVersionByCostunitsampledot(String unitid, String begintime);
	

	/**
	 * 核算对象采集点对象（查询）
	 * 
	 * @param id
	 * @return
	 */
	public Costunitsampledot getCostunitsampledotById(String id);

	/**
	 * 核算对象采集点（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostunitsampledotData(MethodSaveDto saveDto);
	
	/**
	 *	保存采集点数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveSampledotData(List<Costunitsampledot> addList,List<Costunitsampledot> updList,List<Costunitsampledot> delList);

	/**
	 *	保存采集点数据（是否更新空值）
	 * @param isUpdNull
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveSampledotDataByIsUpdNull(Boolean isUpdNull, List<Costunitsampledot> addList,List<Costunitsampledot> updList,List<Costunitsampledot> delList);
	
	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 *	成本项目分类
	 * @param id
	 * @return
	 */
	public Costclass getCostclassById(String id);
	
	/**
	 * 成本项目分类（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costclass> getCostclassList(MethodQueryDto queryDto);
	
	
	/**
	 *	成本项目分类类型（查询）
	 * @return
	 */
	public List<BeanVo> getCostClassTypeList();

	
	/**
	 *	成本项目分类最大版本
	 * @param unitid
	 * @param begintime
	 * @return
	 */
	public String getMaxVersionByCostunitcostclass(String unitid, String begintime);
	
	
	/**
	 * 成本项目分类（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostclassData(MethodSaveDto saveDto);
	
	/**
	 *	保存核算分类数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataCostclass(List<Costclass> addList, List<Costclass> updList, List<Costclass> delList);

	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 * 成本项目（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costitem> getCostitemList(MethodQueryDto queryDto);

	/**
	 *	成本项目最大版本
	 * @param unitid
	 * @param begintime
	 * @return
	 */
	public String getMaxVersionByCostunitcostitem(String unitid, String begintime);
	
	/**
	 * 成本项目（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostitemData(MethodSaveDto saveDto);
	
	/**
	 *	同步更新核算项目名称（项目库修改名称后调用）
	 * @param list
	 * @return
	 */
	public String updateCostItemName(List<Costlibraryitem> list);
	
	/**
	 *	从项目库为每个核算对象新增核算项目数据
	 * @param list
	 * @return
	 */
	public Boolean insertCostItemData(List<ProductScheduPlan> list);
	
	/**
	 *	保存核算项目数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataCostitem(List<Costitem> addList, List<Costitem> updList, List<Costitem> delList);
	
	/**
	 *	获取成本项目数量（根据核算对象id列表）
	 * @param unitidList
	 * @return
	 */
	public LinkedHashMap<String, Integer> getCostItemCountMapByUnitid(List<String> unitidList);
	
	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 * 成本项目仪表（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costinstrument> getCostinstrumentList(MethodQueryDto queryDto);

	/**
	 * 成本项目仪表（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostinstrumentData(MethodSaveDto saveDto);
	
	/**
	 *	保存仪表数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataCostinstrument(List<Costinstrument> addList, List<Costinstrument> updList, List<Costinstrument> delList);

	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 * 核算指标（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<Costindicator> getCostindicatorList(MethodQueryDto queryDto);

	/**
	 * 核算指标（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostindicatorData(MethodSaveDto saveDto);
	
	/**
	 *	保存核算指标数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataCostindicator(List<Costindicator> addList, List<Costindicator> updList, List<Costindicator> delList);

	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 *	成本项目公式
	 * @param unitid
	 * @param begintime
	 * @return
	 */
	public List<CostItemFormula> getCostItemFormulaListByRedis(String unitid, String begintime);
	
	/**
	 * 成本项目公式（查询）
	 * 
	 * @param queryDto
	 * @return
	 */
	public List<CostItemFormula> getCostItemFormulaList(MethodQueryDto queryDto);
	
	
	/**
	 * 批量检查
	 * @param unitcodes
	 * @param object
	 * @return
	 */
	public List<CostItemFormula> getCostItemFormulaList(List<String> unitcodes);

	/**
	 * 成本项目公式（保存）
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostItemFormulaData(MethodSaveDto saveDto);

	/**
	 * @category 根据传入的设置得到给定核算对象下的采集点ID
	 * @param unitid 核算对象ID
	 * @param config 配置内容
	 * @return
	 */
	public List<CostunitSampledotWidgetConfig> getUnitSampleDot(String unitid,
			List<CostunitSampledotWidgetConfig> config);

	/**
	 *	保存公式
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataCostItemFormula(List<CostItemFormula> addList, List<CostItemFormula> updList, List<CostItemFormula> delList);
	
	// ————————————————————————————————————————————————————————————————————————————————————————————

	/**
	 *	规定时间设置（从redis中获取）
	 * @param unitid
	 * @param begintime
	 * @return
	 */
	public List<CostStipulateTime> getCostStipulateTimeListByRedis(String unitid,String begintime);
	
	/**
	 * 规定时间设置（查询）
	 * @param queryDto
	 * @return
	 */
	public List<CostStipulateTime> getCostStipulateTimeList(MethodQueryDto queryDto);

	/**
	 *	根据仪表id和日期，获取仪表规定时间数据
	 * @param instrumentId 仪表id
	 * @param maxBegintime 小于等于此版本的最大日期
	 * @return
	 */
	public List<CostStipulateTime> getCostStipulateTimeListByInstrumentId(String instrumentId, String maxBegintime);
	
	/**
	 * 规定时间设置（保存）
	 * @param saveDto
	 * @return
	 */
	public String saveCostStipulateTimeData(MethodSaveDto saveDto);

	/**
	 *	保存规定时间数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataCostStipulateTime(List<CostStipulateTime> addList,List<CostStipulateTime> updList,List<CostStipulateTime> delList);
	
	/**
	 *	删除规定时间（父记录删除后）
	 * @param pid
	 * @return
	 */
	public String deleteCostStipulateTimeByPid(String pid);
	
	// ————————————————————————————————————————————————————————————————————————————————————————————
	
	/**
	 * 计量单位（查询）
	 * @param queryDto
	 * @return
	 */
	public List<CostMeteringUnit> getCostMeteringUnitList(MethodQueryDto queryDto);

	/**
	 * 计量单位（保存）
	 * @param saveDto
	 * @return
	 */
	public String saveCostMeteringUnitData(MethodSaveDto saveDto);
	
	/**
	 *	保存计量单位数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataCostMeteringUnit(List<CostMeteringUnit> addList,List<CostMeteringUnit> updList,List<CostMeteringUnit> delList);
	
	// ————————————————————————————————————————————————————————————————————————————————————————————
	
	/**
	 *	同步其他接口（采集点设置页面数据发生改变后）--不包含成本仪表的数据--王春阳
	 * @param addList
	 * @param updList
	 * @return
	 */
	public String synOtherInterfaceBySampledotChange(List<Costunitsampledot> addList,List<Costunitsampledot> updList);
	
	/**
	 *	获取同步r3db的采集点数据
	 * @param dotObj
	 * @param saveObj
	 * @param list
	 * @param hasMap
	 */
	public void getSynR3dbSampledotData(Costunitsampledot dotObj, Costunitsampledot saveObj, List<Costunitsampledot> list, HashMap<String, String> hasMap);
	
	/**
	 *	同步R3DB接口（采集点设置页面数据发生改变后、外部接口同步数据后）--包括控制指标和成本仪表的数据--关总
	 * @param list
	 * @param tokenStr
	 * @return
	 */
	public String synR3dbDataBySampledot(List<Costunitsampledot> list, String tokenStr);

	
	// ————————————————————————————————————————————————————————————————————————————————————————————
	
	/**
	 *	根据人员的录入权限过滤采集点数据
	 * @param unitid
	 * @param begintime
	 * @param user
	 * @return
	 */
	public List<Costunitsampledot> getSampledotListByInputRight(String unitid, String begintime, String ctype, SysUser user);
	
}
