package com.yunhesoft.leanCosting.unitConf.service;

import java.util.HashMap;

public interface ICostCopyByUnitTypeService {

	/**
	 *	根据核算单元类型复制采集点数据
	 * @param unittypeid_copy
	 * @param ctype
	 * @param newDotIdMap
	 * @return
	 */
	public String copySampledotByUnitTypeId(String unittypeid_copy,String ctype,HashMap<String, HashMap<String, String>> newDotIdMap);
	
	/**
	 *	根据核算单元类型复制核算项目数据
	 * @param unittypeid_copy
	 * @return
	 */
	public String copyCostItemByUnitTypeId(String unittypeid_copy);
	
}
