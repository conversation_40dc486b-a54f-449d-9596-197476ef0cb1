package com.yunhesoft.leanCosting.unitConf.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.system.org.entity.po.SysOrg;

/**
 * 核算对象操作
 * 
 * <AUTHOR>
 *
 */
public interface ICostuintService {

	/**
	 * 查询核算对象
	 * 
	 * @param dto
	 * @return
	 */
	public List<Costuint> getDatas(CostuintQueryDto dto);

	/**
	 * 查询全部仪表数据
	 * 
	 * @param dto
	 * @return
	 */
	public List<Costitem> getCostitemDatas(CostuintQueryDto costuintQueryDto);

	/**
	 * 仪表
	 * 
	 * @param dto
	 * @return
	 */
	public List<Costinstrument> getCostinstrumentDatas(CostuintQueryDto dto);

	/**
	 * 获得其他租户<核算对象ID,List<操作机构>>
	 * 
	 * @return
	 */
	Map<String, List<String>> getTenantUintMap(String tenantId);

	/**
	 * 获得租户内<核算对象ID,List<操作机构>>
	 * 
	 * @return
	 */
	public Map<String, List<String>> getUintMap();

	/**
	 * 通过核算对象查询操作机构
	 * 
	 * @param uintId
	 * @return Map<String, List<Object>> "sysOrg",List<SysOrg>
	 *         "sysEmp",List<SysEmployeeInfo> "sysPost",List<sysPost>
	 */
	public Map<String, Object> getUinToperator(String uintId);

	/**
	 * 通过核算对象查询管理机构
	 * 
	 * @param uintId
	 * @return
	 */
	public List<SysOrg> getUintManager(String uintId);

	/**
	 * 通过ID查对象
	 * @param orgCode
	 * @return
	 */
	public Costuint getUnitId(String uintId);
	
	/**
	 * TODO:接口
	 * 得到核算对象的涉及设备。
	 * @param uintId
	 * @return
	 */
	List<Costuint> getDeviceIds(String uintId);

}
