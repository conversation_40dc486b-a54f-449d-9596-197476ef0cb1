package com.yunhesoft.leanCosting.unitConf.service;

import java.util.List;

import com.yunhesoft.leanCosting.unitConf.entity.dto.paramDto;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampledotVo;

public interface CostitemService {
	 /**
	 * 查询数据
	 * 
	 * @param dto
	 * @return
	 */
	public List<CostitemVo> getDatas(paramDto dto);
	 /**
	 * 通过ID查询
	 * 
	 * @param id
	 * @return
	 */
	public CostitemVo getData(String id, String type);
	/**
	 * 保存数据
	 * 
	 * @param obj
	 * @return
	 */
	public CostitemVo save(CostitemVo obj);
	 /**
	 * 保存数据
	 * 
	 * @param list
	 * @return
	 */
	public String save(List<CostitemVo> list);
	 

	 /**
	 * 通过ID查询
	 * 
	 * @param id
	 * @return
	 */
	public SampledotVo getDot(String id, String type);
	/**
	 * 保存数据
	 * 
	 * @param obj
	 * @return
	 */
	public SampledotVo saveDot(SampledotVo obj);
	
	/**
	 * @category 获取采集点数据列表
	 * @param obj
	 * @return
	 */
	public List<SampledotVo> getDotList(SampledotVo obj);
	
	
	/**
	 * @category 根据核算对象、管理机构、版本及已设置仪表获取采集点数据列表
	 * @param obj
	 * @return
	 */
	public List<SampledotVo> showDotList(SampledotVo obj);
}
