package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.service.ICostDeviceTypeService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostStipulateTime;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BeanVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostCopyByUnitTypeService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.kernel.service.EntityService;

@Service
public class CostCopyByUnitTypeServiceImpl implements ICostCopyByUnitTypeService {

	@Autowired
	private EntityService entityService;
	
	@Autowired
	private IUnitMethodService methodService;
	
	@Autowired
	private ICostDeviceTypeService deviceTypeService;
	
	private String BEGINTIME = "2020-01-01";
	
	private String MARKLABEL = "___";
	
	//——————————————————————————————————————————   复制采集点 ↓ ————————————————————————————————————————————————————————————
	
	/**
	 *	根据核算单元类型复制采集点数据
	 * @param unittypeid_copy  复制核算单元类型ID
	 * @param ctype  采样点的采集类型                                     采集类型：1、成本仪表；2、控制指标；3、lims指标；
	 * @param newDotIdMap  新采集点idMap（unitid--<旧id,新id>）
	 * @return
	 */
	@Override
	public String copySampledotByUnitTypeId(String unittypeid_copy,String ctype,HashMap<String, HashMap<String, String>> newDotIdMap) {
		String result = "";
		if(newDotIdMap==null) {
			newDotIdMap = new HashMap<String, HashMap<String, String>>();
		}
		
		List<Costunitsampleclass> addClassList = new ArrayList<Costunitsampleclass>();
		List<Costunitsampledot> addDotList = new ArrayList<Costunitsampledot>();
		List<CostStipulateTime> add_costStipulateTime_list = new ArrayList<CostStipulateTime>(); //采集时间
		List<Costunitsampledot> synOtherList = new ArrayList<Costunitsampledot>(); //调用同步其他数据的接口
		
		if(StringUtils.isNotEmpty(unittypeid_copy)) {
			String begintime_copy = this.BEGINTIME; //由于单元类型没有初始化版本，所以使用默认版本日期
			String begintime = this.BEGINTIME;
			//获取此单元类型的所有核算对象
			MethodQueryDto dto = new MethodQueryDto();
			dto.setIsSelUnitOrgId(false);
			dto.setUnittype(unittypeid_copy);
			List<Costuint> unitList = methodService.getCostuintList(dto);
			if(StringUtils.isNotEmpty(unitList)) { //有核算对象数据
				List<String> unitidList = unitList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
				if(StringUtils.isNotEmpty(unitidList)) {
					if(StringUtils.isNotEmpty(ctype)&&"1".equals(ctype)) { //复制成本仪表，没有分类，直接复制即可
						//复制的采集点数据
						MethodQueryDto copyDotDto = new MethodQueryDto();
						copyDotDto.setUnitid(unittypeid_copy);
						copyDotDto.setBegintime(begintime_copy);
						copyDotDto.setCtype(ctype);
						List<Costunitsampledot> copyDotList = methodService.getCostunitsampledotList(copyDotDto);
						if(StringUtils.isNotEmpty(copyDotList)) { //有复制的采集点数据
							//已存在采集点（成本仪表）
							MethodQueryDto hasDotDto = new MethodQueryDto();
							hasDotDto.setUnitidList(unitidList);
							hasDotDto.setBegintime(begintime);
							hasDotDto.setCtype(ctype);
							List<Costunitsampledot> hasDotList = methodService.getCostunitsampledotList(hasDotDto);
							HashMap<String, LinkedHashMap<String, Costunitsampledot>> hasDotMap = new HashMap<String, LinkedHashMap<String,Costunitsampledot>>(); //采集点名称-对象Map
							HashMap<String, HashMap<String, Integer>> dotMaxPxMap = new HashMap<String, HashMap<String, Integer>>();
							if(StringUtils.isNotEmpty(hasDotList)) {
								hasDotMap = this.getDotNameMapByUnitid(hasDotList, null, dotMaxPxMap, null);
							}
							//遍历复制记录
							for (int i = 0; i < copyDotList.size(); i++) {
								Costunitsampledot copyDotObj = copyDotList.get(i);
								String copyDotId = copyDotObj.getId();
								String copyDotName = copyDotObj.getName();
								String copyPid = copyDotObj.getPid();
								if(StringUtils.isNotEmpty(copyDotName)) {
									copyDotName = copyDotName.trim();
									//遍历被复制记录赋值
									for (int j = 0; j < unitidList.size(); j++) {
										String unitid = unitidList.get(j);
										String newDotId = "";
										boolean isAdd = true;
										if(hasDotMap.containsKey(unitid)) {
											LinkedHashMap<String, Costunitsampledot> dotNameMap = hasDotMap.get(unitid);
											if(dotNameMap.containsKey(copyDotName)) { //存在同名数据，使用此纪录
												newDotId = dotNameMap.get(copyDotName).getId();
												isAdd = false;
											}
										}
										if(isAdd) {
											int maxPx = 0;
											if(dotMaxPxMap.containsKey(unitid)) {
												HashMap<String, Integer> maxPxMap = dotMaxPxMap.get(unitid);
												if(maxPxMap.containsKey(copyPid)) {
													maxPx = maxPxMap.get(copyPid);
												}
												maxPxMap.put(copyPid, maxPx+1);
											}else {
												HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
												maxPxMap.put(copyPid, maxPx+1);
												dotMaxPxMap.put(unitid, maxPxMap);
											}
											maxPx += 1;
											newDotId = TMUID.getUID();
											Costunitsampledot addDotObj = new Costunitsampledot();
											BeanUtils.copyProperties(copyDotObj, addDotObj); //赋予返回对象
											addDotObj.setId(newDotId);
											addDotObj.setUnitid(unitid);
											addDotObj.setBegintime(begintime);
											addDotObj.setTmsort(maxPx);
											addDotList.add(addDotObj);
										}
										if(newDotIdMap.containsKey(unitid)) {
											HashMap<String, String> dotIdMap = newDotIdMap.get(unitid);
											dotIdMap.put(copyDotId, newDotId);
										}else {
											HashMap<String, String> dotIdMap = new HashMap<String, String>();
											dotIdMap.put(copyDotId, newDotId);
											newDotIdMap.put(unitid, dotIdMap);
										}
									}
								}
							}
						}
					}else { //复制非成本仪表的采集点数据，需要判断是否新增分类，是否重名等
						
						//获取已存在的采集点分类数据（用于判断是否需要新增）
						MethodQueryDto hasClassDto = new MethodQueryDto();
						hasClassDto.setUnitidList(unitidList);
						hasClassDto.setBegintime(begintime);
						List<Costunitsampleclass> hasClassList = methodService.getCostunitsampleclassList(hasClassDto);
						HashMap<String, LinkedHashMap<String, Costunitsampleclass>> hasKeyClassMap = new HashMap<String, LinkedHashMap<String, Costunitsampleclass>>();
						HashMap<String, HashMap<String, String>> hasIdKeyMap = new HashMap<String, HashMap<String, String>>();
						HashMap<String, HashMap<String, String>> hasKeyIdMap = new HashMap<String, HashMap<String, String>>();
						HashMap<String, HashMap<String, Integer>> classMaxPxMap = new HashMap<String, HashMap<String,Integer>>();
						HashMap<String, LinkedHashMap<String, List<Costunitsampleclass>>> hasPidClassMap = new HashMap<String, LinkedHashMap<String, List<Costunitsampleclass>>>();
						if(StringUtils.isNotEmpty(hasClassList)) {
							this.getSampleClassMap(hasClassList, hasIdKeyMap, hasKeyIdMap, hasKeyClassMap, classMaxPxMap, hasPidClassMap);
						}
						
						//获取已存在采集点数据（用于判断是否需要新增）
						MethodQueryDto hasDotDto = new MethodQueryDto();
						hasDotDto.setUnitidList(unitidList);
						hasDotDto.setBegintime(begintime);
						hasDotDto.setCtypeNot("1");
						List<Costunitsampledot> hasDotList = methodService.getCostunitsampledotList(hasDotDto);
						HashMap<String, LinkedHashMap<String, Costunitsampledot>> hasKeyDotMap = new HashMap<String, LinkedHashMap<String,Costunitsampledot>>();
						HashMap<String, HashMap<String, Integer>> dotMaxPxMap = new HashMap<String, HashMap<String, Integer>>();
						HashMap<String, LinkedHashMap<String, List<Costunitsampledot>>> hasPidDotMap = new HashMap<String, LinkedHashMap<String, List<Costunitsampledot>>>();
						if(StringUtils.isNotEmpty(hasDotList)) {
							hasKeyDotMap = this.getDotNameMapByUnitid(hasDotList, hasIdKeyMap, dotMaxPxMap, hasPidDotMap); //如果传入分类Map，采集点名称前加上分类名称
						}
						
						//复制的采集点分类
						MethodQueryDto copyDto = new MethodQueryDto();
						copyDto.setUnitid(unittypeid_copy);
						copyDto.setBegintime(begintime_copy);
						List<Costunitsampleclass> copyClassList = methodService.getCostunitsampleclassList(copyDto);
						HashMap<String, LinkedHashMap<String, Costunitsampleclass>> copyKeyClassMap = new HashMap<String, LinkedHashMap<String, Costunitsampleclass>>();
						HashMap<String, HashMap<String, String>> copyIdKeyMap = new HashMap<String, HashMap<String, String>>();
						HashMap<String, HashMap<String, String>> copyKeyIdMap = new HashMap<String, HashMap<String, String>>();
						if(StringUtils.isNotEmpty(copyClassList)) {
							this.getSampleClassMap(copyClassList, copyIdKeyMap, copyKeyIdMap, copyKeyClassMap, null, null);
						}
						
						//复制的采集点数据
						MethodQueryDto copyDotDto = new MethodQueryDto();
						copyDotDto.setUnitid(unittypeid_copy);
						copyDotDto.setBegintime(begintime_copy);
						copyDotDto.setCtypeNot("1");
						HashMap<String, LinkedHashMap<String, Costunitsampledot>> copyDotMap = new HashMap<String, LinkedHashMap<String,Costunitsampledot>>();
						List<Costunitsampledot> copyDotList = methodService.getCostunitsampledotList(copyDotDto);
						if(StringUtils.isNotEmpty(copyDotList)) {
							copyDotMap = this.getDotNameMapByUnitid(copyDotList, copyIdKeyMap, null, null); //如果传入分类Map，采集点名称前加上分类名称
						}
						
						//复制的规定时间
						Map<String, List<CostStipulateTime>> copyCostStipulateTimeMap = new HashMap<String, List<CostStipulateTime>>();
						List<CostStipulateTime> copyCostStipulateTimeList_all = methodService.getCostStipulateTimeList(copyDto);
						if(StringUtils.isNotEmpty(copyCostStipulateTimeList_all)) {
							copyCostStipulateTimeMap = copyCostStipulateTimeList_all.stream().collect(Collectors.groupingBy(CostStipulateTime::getPid));
						}
						
						//遍历复制记录（分类）
						if(StringUtils.isNotEmpty(copyKeyClassMap)) {
							Iterator<Entry<String, LinkedHashMap<String, Costunitsampleclass>>> copyKeyClassMapIter = copyKeyClassMap.entrySet().iterator();
							while(copyKeyClassMapIter.hasNext()) {
								Entry<String, LinkedHashMap<String, Costunitsampleclass>> copyKeyClassMapEntry = copyKeyClassMapIter.next();
								LinkedHashMap<String, Costunitsampleclass> classMap = copyKeyClassMapEntry.getValue();
								if(StringUtils.isNotEmpty(classMap)) {
									Iterator<Entry<String, Costunitsampleclass>> classMapIter = classMap.entrySet().iterator();
									while(classMapIter.hasNext()) {
										Entry<String, Costunitsampleclass> classMapEntry = classMapIter.next();
										String classKey = classMapEntry.getKey();
										Costunitsampleclass classObj = classMapEntry.getValue();
										//遍历核算对象id列表（复制分类）
										for (int j = 0; j < unitidList.size(); j++) {
											String unitid = unitidList.get(j);
											String pid = "";
											String pidStr = "";
											if(hasKeyClassMap.containsKey(unitid)) {
												LinkedHashMap<String, Costunitsampleclass> keyClassMap = hasKeyClassMap.get(unitid);
												if(keyClassMap.containsKey(classKey)) {
													continue; //存在同名分类节点，不做任何处理
												}else { //新增分类
													//获取父级分类id
													String[] classKeyArr = classKey.split(this.MARKLABEL);
													if(classKeyArr.length==2) {
														pid = "root";
														pidStr = pid;
													}else if(classKeyArr.length>2) {
														pidStr = "";
														for (int i = 0; i < classKeyArr.length-1; i++) {
															pidStr += this.MARKLABEL + classKeyArr[i];
														}
														if(!"".equals(pidStr)) {
															pidStr = pidStr.substring(this.MARKLABEL.length());
															if(keyClassMap.containsKey(pidStr)) {
																pid = keyClassMap.get(pidStr).getId();
															}
														}
													}
													//分类下已存在采集点，不能再新增分类
													if(hasPidDotMap.containsKey(unitid)&&StringUtils.isNotEmpty(pid)&&!"root".equals(pid)) {
														LinkedHashMap<String, List<Costunitsampledot>> dotMap = hasPidDotMap.get(unitid);
														if(StringUtils.isNotEmpty(dotMap)&&dotMap.containsKey(pidStr)) {
															continue;
														}
													}
												}
											}else {
												pid = "root";
											}
											if(StringUtils.isNotEmpty(pid)) { //新增
												int maxPx = 0;
												if(classMaxPxMap.containsKey(unitid)) {
													HashMap<String, Integer> maxPxMap = classMaxPxMap.get(unitid);
													if(maxPxMap.containsKey(pid)) {
														maxPx = maxPxMap.get(pid);
													}
													maxPxMap.put(pid, maxPx+1);
												}else {
													HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
													maxPxMap.put(pid, maxPx+1);
													classMaxPxMap.put(unitid, maxPxMap);
												}
												maxPx += 1;
												Costunitsampleclass addClassObj = new Costunitsampleclass();
												BeanUtils.copyProperties(classObj, addClassObj); //赋予返回对象
												addClassObj.setId(TMUID.getUID());
												addClassObj.setUnitid(unitid);
												addClassObj.setBegintime(begintime);
												addClassObj.setPid(pid);
												addClassObj.setTmsort(maxPx);
												addClassList.add(addClassObj);
												if(hasKeyClassMap.containsKey(unitid)) {
													LinkedHashMap<String, Costunitsampleclass> keyClassMap = hasKeyClassMap.get(unitid);
													keyClassMap.put(classKey, addClassObj);
												}else {
													LinkedHashMap<String, Costunitsampleclass> keyClassMap = new LinkedHashMap<String, Costunitsampleclass>();
													keyClassMap.put(classKey, addClassObj);
													hasKeyClassMap.put(unitid, keyClassMap);
												}
											}
										}
									}
								}
							}
						}
						
						//遍历复制记录（采集点）
						if(StringUtils.isNotEmpty(copyDotMap)) {
							Iterator<Entry<String, LinkedHashMap<String, Costunitsampledot>>> copyDotMapIter = copyDotMap.entrySet().iterator();
							while(copyDotMapIter.hasNext()) {
								Entry<String, LinkedHashMap<String, Costunitsampledot>> copyDotMapEntry = copyDotMapIter.next();
								LinkedHashMap<String, Costunitsampledot> dotMap = copyDotMapEntry.getValue();
								if(StringUtils.isNotEmpty(dotMap)) {
									Iterator<Entry<String, Costunitsampledot>> dotMapIter = dotMap.entrySet().iterator();
									while(dotMapIter.hasNext()) {
										Entry<String, Costunitsampledot> dotMapEntry = dotMapIter.next();
										String copyDotKey = dotMapEntry.getKey();
										Costunitsampledot copyDotObj = dotMapEntry.getValue();
										//遍历核算对象id列表（复制采集点）
										for (int j = 0; j < unitidList.size(); j++) {
											String unitid = unitidList.get(j);
											if(hasKeyDotMap.containsKey(unitid)) {
												LinkedHashMap<String, Costunitsampledot> keyDotMap = hasKeyDotMap.get(unitid);
												if(keyDotMap.containsKey(copyDotKey)) {
													continue; //存在同名采集点节点，不做任何处理
												}
											}
											//新增采集点
											//获取父级分类id
											String pid = "";
											String[] dotKeyArr = copyDotKey.split(this.MARKLABEL);
											if(dotKeyArr.length>=2) {
												LinkedHashMap<String, Costunitsampleclass> keyClassMap = new LinkedHashMap<String, Costunitsampleclass>();
												if(hasKeyClassMap.containsKey(unitid)) {
													keyClassMap = hasKeyClassMap.get(unitid);
												}
												String pidStr = "";
												for (int i = 0; i < dotKeyArr.length-1; i++) {
													pidStr += this.MARKLABEL + dotKeyArr[i];
												}
												if(!"".equals(pidStr)) {
													pidStr = pidStr.substring(this.MARKLABEL.length());
													if(StringUtils.isNotEmpty(keyClassMap)&&keyClassMap.containsKey(pidStr)) {
														pid = keyClassMap.get(pidStr).getId();
													}
												}
												//分类下已存在子分类，不能再新增采集点
												if(hasPidClassMap.containsKey(unitid)&&StringUtils.isNotEmpty(pid)) {
													LinkedHashMap<String, List<Costunitsampleclass>> classMap = hasPidClassMap.get(unitid);
													if(StringUtils.isNotEmpty(classMap)&&classMap.containsKey(pidStr)) {
														continue;
													}
												}
											}
											if(StringUtils.isNotEmpty(pid)) {
												int maxPx = 0;
												if(dotMaxPxMap.containsKey(unitid)) {
													HashMap<String, Integer> maxPxMap = dotMaxPxMap.get(unitid);
													if(maxPxMap.containsKey(pid)) {
														maxPx = maxPxMap.get(pid);
													}
													maxPxMap.put(pid, maxPx+1);
												}else {
													HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
													maxPxMap.put(pid, maxPx+1);
													dotMaxPxMap.put(unitid, maxPxMap);
												}
												maxPx += 1;
												Costunitsampledot addDotObj = new Costunitsampledot();
												BeanUtils.copyProperties(copyDotObj, addDotObj); //赋予返回对象
												addDotObj.setId(TMUID.getUID());
												addDotObj.setUnitid(unitid);
												addDotObj.setBegintime(begintime);
												addDotObj.setPid(pid);
												addDotObj.setTmsort(maxPx);
												addDotList.add(addDotObj);
												
												synOtherList.add(addDotObj);
												//采集时间
												if(StringUtils.isNotEmpty(copyCostStipulateTimeMap)&&copyCostStipulateTimeMap.containsKey(copyDotObj.getId())) {
													List<CostStipulateTime> stipulateTimeList = copyCostStipulateTimeMap.get(copyDotObj.getId());
													if(StringUtils.isNotEmpty(stipulateTimeList)) {
														for (int m = 0; m < stipulateTimeList.size(); m++) {
															CostStipulateTime stipulateTimeObj = stipulateTimeList.get(m);
															stipulateTimeObj.setId(TMUID.getUID());
															stipulateTimeObj.setPid(addDotObj.getId());
															stipulateTimeObj.setUnitid(unitid);
															stipulateTimeObj.setBegintime(begintime);
															add_costStipulateTime_list.add(stipulateTimeObj);
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
		boolean isHasCopyData = false;
		if ("".equals(result) && StringUtils.isNotEmpty(addClassList)) {
			isHasCopyData = true;
			if (entityService.insertBatch(addClassList) == 0) {
				result = "添加失败（核算对象的采集点分类）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(addDotList)) {
			isHasCopyData = true;
			if (entityService.insertBatch(addDotList) == 0) {
				result = "添加失败（核算对象的采集点）！";
			}
		}
		//调用同步其他数据的接口
		if ("".equals(result) && StringUtils.isNotEmpty(synOtherList)) {
			methodService.synOtherInterfaceBySampledotChange(synOtherList, null);
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costStipulateTime_list)) {
			isHasCopyData = true;
			if (entityService.insertBatch(add_costStipulateTime_list) == 0) {
				result = "添加失败（采集时间）！";
			}
		}
		if ("".equals(result) && !isHasCopyData) {
			result = "没有可复制的采集点数据！";
		}
		return result;
	}
	
	//获取采集点数据Map
	private HashMap<String, LinkedHashMap<String, Costunitsampledot>> getDotNameMapByUnitid(List<Costunitsampledot> list, 
		HashMap<String, HashMap<String, String>> classIdKeyMap, HashMap<String, HashMap<String, Integer>> dotMaxPxMap,
		HashMap<String, LinkedHashMap<String, List<Costunitsampledot>>> pidDotMap) {
		HashMap<String, LinkedHashMap<String, Costunitsampledot>> map = new HashMap<String, LinkedHashMap<String, Costunitsampledot>>();
		if(dotMaxPxMap==null) {
			dotMaxPxMap = new HashMap<String, HashMap<String, Integer>>();
		}
		if(pidDotMap==null) {
			pidDotMap = new HashMap<String, LinkedHashMap<String,List<Costunitsampledot>>>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costunitsampledot obj = list.get(i);
				String unitid = obj.getUnitid();
				String pid = obj.getPid();
				String name = obj.getName();
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(StringUtils.isNotEmpty(pid)&&StringUtils.isNotEmpty(name)) {
					name = name.trim();
					if(StringUtils.isNotEmpty(classIdKeyMap)&&classIdKeyMap.containsKey(unitid)) { //如果传入分类Map，采集点名称前加上分类名称
						HashMap<String, String> idKeyMap = classIdKeyMap.get(unitid);
						if(StringUtils.isNotEmpty(idKeyMap)&&idKeyMap.containsKey(pid)) {
							String className = idKeyMap.get(pid);
							name = className + this.MARKLABEL + name;
							//按照分类分组
							if(pidDotMap.containsKey(unitid)) {
								LinkedHashMap<String, List<Costunitsampledot>> classNameMap = pidDotMap.get(unitid);
								if(classNameMap.containsKey(className)) {
									List<Costunitsampledot> sampledotList = classNameMap.get(className);
									sampledotList.add(obj);
								}else {
									List<Costunitsampledot> sampledotList = new ArrayList<Costunitsampledot>();
									sampledotList.add(obj);
									classNameMap.put(className, sampledotList);
								}
							}else {
								List<Costunitsampledot> sampledotList = new ArrayList<Costunitsampledot>();
								sampledotList.add(obj);
								LinkedHashMap<String, List<Costunitsampledot>> classNameMap = new LinkedHashMap<String, List<Costunitsampledot>>();
								classNameMap.put(className, sampledotList);
								pidDotMap.put(unitid, classNameMap);
							}
						}
					}
				}
				if(map.containsKey(unitid)) {
					LinkedHashMap<String, Costunitsampledot> dotNameMap = map.get(unitid);
					dotNameMap.put(name, obj);
				}else {
					LinkedHashMap<String, Costunitsampledot> dotNameMap = new LinkedHashMap<String, Costunitsampledot>();
					dotNameMap.put(name, obj);
					map.put(unitid, dotNameMap);
				}
				if(dotMaxPxMap.containsKey(unitid)) {
					HashMap<String, Integer> maxPxMap = dotMaxPxMap.get(unitid);
					if(maxPxMap.containsKey(pid)) {
						Integer maxPx = maxPxMap.get(pid);
						if(tmsort>maxPx) {
							maxPxMap.put(pid, tmsort);
						}
					}else {
						maxPxMap.put(pid, tmsort);
					}
				}else {
					HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
					maxPxMap.put(pid, tmsort);
					dotMaxPxMap.put(unitid, maxPxMap);
				}
			}
		}
		return map;
	}
	
	//获取分类Map
	private void getSampleClassMap(List<Costunitsampleclass> classList, HashMap<String, HashMap<String, String>> idKeyMap, 
		HashMap<String, HashMap<String, String>> keyIdMap, HashMap<String, LinkedHashMap<String, Costunitsampleclass>> keyClassMap,
		HashMap<String, HashMap<String, Integer>> classMaxPxMap, HashMap<String, LinkedHashMap<String, List<Costunitsampleclass>>> pidClassMap) {
		if(idKeyMap==null) {
			idKeyMap = new HashMap<String, HashMap<String, String>>();
		}
		if(keyIdMap==null) {
			keyIdMap = new HashMap<String, HashMap<String, String>>();
		}
		if(keyClassMap==null) {
			keyClassMap = new HashMap<String, LinkedHashMap<String, Costunitsampleclass>>();
		}
		if(classMaxPxMap==null) {
			classMaxPxMap = new HashMap<String, HashMap<String, Integer>>();
		}
		if(pidClassMap==null) {
			pidClassMap = new HashMap<String, LinkedHashMap<String,List<Costunitsampleclass>>>();
		}
		if(StringUtils.isNotEmpty(classList)) {
			Map<String, List<Costunitsampleclass>> classMap = classList.stream().collect(Collectors.groupingBy(Costunitsampleclass::getPid));
			for (int i = 0; i < classList.size(); i++) {
				Costunitsampleclass classObj = classList.get(i);
				String unitid = classObj.getUnitid();
				String id = classObj.getId();
				String name = classObj.getName();
				String pid = classObj.getPid();
				int tmsort = classObj.getTmsort()==null?0:classObj.getTmsort();
				if(StringUtils.isNotEmpty(pid)&&"root".equals(pid)) {
					String key = pid+this.MARKLABEL+name;
					
					if(idKeyMap.containsKey(unitid)) {
						HashMap<String, String> map = idKeyMap.get(unitid);
						map.put(id, key);
					}else {
						HashMap<String, String> map = new HashMap<String, String>();
						map.put(id, key);
						idKeyMap.put(unitid, map);
					}
					
					if(keyIdMap.containsKey(unitid)) {
						HashMap<String, String> map = keyIdMap.get(unitid);
						map.put(key, id);
					}else {
						HashMap<String, String> map = new HashMap<String, String>();
						map.put(key, id);
						keyIdMap.put(unitid, map);
					}
					
					if(keyClassMap.containsKey(unitid)) {
						LinkedHashMap<String, Costunitsampleclass> map = keyClassMap.get(unitid);
						map.put(key, classObj);
					}else {
						LinkedHashMap<String, Costunitsampleclass> map = new LinkedHashMap<String, Costunitsampleclass>();
						map.put(key, classObj);
						keyClassMap.put(unitid, map);
					}
					
					if(classMaxPxMap.containsKey(unitid)) {
						HashMap<String, Integer> maxPxMap = classMaxPxMap.get(unitid);
						if(maxPxMap.containsKey(pid)) {
							Integer maxPx = maxPxMap.get(pid);
							if(tmsort>maxPx) {
								maxPxMap.put(pid, tmsort);
							}
						}else {
							maxPxMap.put(pid, tmsort);
						}
					}else {
						HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
						maxPxMap.put(pid, tmsort);
						classMaxPxMap.put(unitid, maxPxMap);
					}
					
					//按照分类分组
					if(pidClassMap.containsKey(unitid)) {
						LinkedHashMap<String, List<Costunitsampleclass>> classNameMap = pidClassMap.get(unitid);
						if(classNameMap.containsKey(pid)) {
							List<Costunitsampleclass> sampleclassList = classNameMap.get(pid);
							sampleclassList.add(classObj);
						}else {
							List<Costunitsampleclass> sampleclassList = new ArrayList<Costunitsampleclass>();
							sampleclassList.add(classObj);
							classNameMap.put(pid, sampleclassList);
						}
					}else {
						List<Costunitsampleclass> sampleclassList = new ArrayList<Costunitsampleclass>();
						sampleclassList.add(classObj);
						LinkedHashMap<String, List<Costunitsampleclass>> classNameMap = new LinkedHashMap<String, List<Costunitsampleclass>>();
						classNameMap.put(pid, sampleclassList);
						pidClassMap.put(unitid, classNameMap);
					}
					
					this.getChildSampleClassMap(classMap, key, id, idKeyMap, keyIdMap, keyClassMap, classMaxPxMap, pidClassMap);
				}
			}
		}
	}
	
	//获取子级分类Map
	private void getChildSampleClassMap(Map<String, List<Costunitsampleclass>> classMap,String dataKey,String dataId,
		HashMap<String, HashMap<String, String>> idKeyMap, HashMap<String, HashMap<String, String>> keyIdMap, 
		HashMap<String, LinkedHashMap<String, Costunitsampleclass>> keyClassMap,HashMap<String, HashMap<String, Integer>> classMaxPxMap,
		HashMap<String, LinkedHashMap<String, List<Costunitsampleclass>>> pidClassMap) {
		if(StringUtils.isNotEmpty(classMap)&&classMap.containsKey(dataId)) {
			List<Costunitsampleclass> classList = classMap.get(dataId);
			for (int i = 0; i < classList.size(); i++) {
				Costunitsampleclass classObj = classList.get(i);
				String unitid = classObj.getUnitid();
				String id = classObj.getId();
				String name = classObj.getName();
				String pid = classObj.getPid();
				String key = dataKey + this.MARKLABEL + name;
				int tmsort = classObj.getTmsort()==null?0:classObj.getTmsort();
				
				if(idKeyMap.containsKey(unitid)) {
					HashMap<String, String> map = idKeyMap.get(unitid);
					map.put(id, key);
				}else {
					HashMap<String, String> map = new HashMap<String, String>();
					map.put(id, key);
					idKeyMap.put(unitid, map);
				}
				
				if(keyIdMap.containsKey(unitid)) {
					HashMap<String, String> map = keyIdMap.get(unitid);
					map.put(key, id);
				}else {
					HashMap<String, String> map = new HashMap<String, String>();
					map.put(key, id);
					keyIdMap.put(unitid, map);
				}
				
				if(keyClassMap.containsKey(unitid)) {
					LinkedHashMap<String, Costunitsampleclass> map = keyClassMap.get(unitid);
					map.put(key, classObj);
				}else {
					LinkedHashMap<String, Costunitsampleclass> map = new LinkedHashMap<String, Costunitsampleclass>();
					map.put(key, classObj);
					keyClassMap.put(unitid, map);
				}
				
				if(classMaxPxMap.containsKey(unitid)) {
					HashMap<String, Integer> maxPxMap = classMaxPxMap.get(unitid);
					if(maxPxMap.containsKey(pid)) {
						Integer maxPx = maxPxMap.get(pid);
						if(tmsort>maxPx) {
							maxPxMap.put(pid, tmsort);
						}
					}else {
						maxPxMap.put(pid, tmsort);
					}
				}else {
					HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
					maxPxMap.put(pid, tmsort);
					classMaxPxMap.put(unitid, maxPxMap);
				}
				
				//按照分类分组
				if(pidClassMap.containsKey(unitid)) {
					LinkedHashMap<String, List<Costunitsampleclass>> classNameMap = pidClassMap.get(unitid);
					if(classNameMap.containsKey(dataKey)) {
						List<Costunitsampleclass> sampleclassList = classNameMap.get(dataKey);
						sampleclassList.add(classObj);
					}else {
						List<Costunitsampleclass> sampleclassList = new ArrayList<Costunitsampleclass>();
						sampleclassList.add(classObj);
						classNameMap.put(dataKey, sampleclassList);
					}
				}else {
					List<Costunitsampleclass> sampleclassList = new ArrayList<Costunitsampleclass>();
					sampleclassList.add(classObj);
					LinkedHashMap<String, List<Costunitsampleclass>> classNameMap = new LinkedHashMap<String, List<Costunitsampleclass>>();
					classNameMap.put(dataKey, sampleclassList);
					pidClassMap.put(unitid, classNameMap);
				}
				
				this.getChildSampleClassMap(classMap, key, id, idKeyMap, keyIdMap, keyClassMap, classMaxPxMap, pidClassMap);
			}
		}
	}
	
	//——————————————————————————————————————————   复制采集点 ↑ ————————————————————————————————————————————————————————————
	
		
	
	//——————————————————————————————————————————   复制核算项目 ↓ ————————————————————————————————————————————————————————————
	
	/**
	 *	根据核算单元类型复制核算项目数据
	 * @param unittypeid_copy  复制核算单元类型ID
	 * @return
	 */
	@Override
	public String copyCostItemByUnitTypeId(String unittypeid_copy) {
		String result = "";
		
		List<Costclass> add_costclass_list = new ArrayList<Costclass>(); //成本分类
		List<Costitem> add_costitem_list = new ArrayList<Costitem>(); //成本项目
		List<Costinstrument> add_costinstrument_list = new ArrayList<Costinstrument>(); //成本仪表
		List<CostStipulateTime> add_costStipulateTime_list = new ArrayList<CostStipulateTime>(); //规定时间
		List<Costindicator> add_costindicator_list = new ArrayList<Costindicator>(); //核算指标
		List<CostItemFormula> add_costItemFormula_list = new ArrayList<CostItemFormula>(); //公式
		
		List<CostItemFormula> todoReplaceFormula_list = new ArrayList<CostItemFormula>(); //临时存放公式（替换公式前）
		HashMap<String, HashMap<String, String>> newDotIdMap = new HashMap<String, HashMap<String, String>>(); //新旧采集点id的Map
		
		if(StringUtils.isNotEmpty(unittypeid_copy)) {
			String begintime_copy = this.BEGINTIME; //由于单元类型没有初始化版本，所以使用默认版本日期
			String begintime = this.BEGINTIME;
			
			//复制采集点数据（采集类型：成本仪表）
			this.copySampledotByUnitTypeId(unittypeid_copy, "1", newDotIdMap);
			
			//获取此单元类型的所有核算对象
			MethodQueryDto dto = new MethodQueryDto();
			dto.setIsSelUnitOrgId(false);
			dto.setUnittype(unittypeid_copy);
			List<Costuint> unitList = methodService.getCostuintList(dto);
			if(StringUtils.isNotEmpty(unitList)) { //有核算对象数据
				Map<String, String> unitMap = unitList.stream().collect(Collectors.toMap(Costuint::getId, Costuint::getName, (key1, key2) -> key2));
				List<String> unitidList = unitList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
				if(StringUtils.isNotEmpty(unitidList)) {
					
					//复制记录检索条件
					MethodQueryDto copyDto = new MethodQueryDto();
					copyDto.setUnitid(unittypeid_copy);
					copyDto.setBegintime(begintime_copy);
					
					//复制的核算项目分类
					HashMap<String, LinkedHashMap<String, Costclass>> copyKeyClassMap = new HashMap<String, LinkedHashMap<String, Costclass>>();
					HashMap<String, HashMap<String, String>> copyClassIdKeyMap = new HashMap<String, HashMap<String, String>>();
					HashMap<String, HashMap<String, String>> copyClassKeyIdMap = new HashMap<String, HashMap<String, String>>();
					List<Costclass> copyCostClassList = methodService.getCostclassList(copyDto);
					if(StringUtils.isNotEmpty(copyCostClassList)) {
						this.getCostClassMap(copyCostClassList, copyClassIdKeyMap, copyClassKeyIdMap, copyKeyClassMap, null, null);
					}
					
					//复制的核算项目
					List<Costitem> copyCostItemList = methodService.getCostitemList(copyDto);
					HashMap<String, LinkedHashMap<String, Costitem>> copyKeyItemMap = new HashMap<String, LinkedHashMap<String, Costitem>>();
					HashMap<String, HashMap<String, String>> copyItemIdKeyMap = new HashMap<String, HashMap<String, String>>();
					if(StringUtils.isNotEmpty(copyCostItemList)) {
						copyKeyItemMap = this.getItemNameMapByUnitid(copyCostItemList, copyClassIdKeyMap, null, copyItemIdKeyMap, null); //如果传入分类Map，项目名称前加上分类名称
					}
					
					//复制的成本项目仪表
					List<Costinstrument> copyCostinstrumentList = methodService.getCostinstrumentList(copyDto);
					HashMap<String, LinkedHashMap<String, Costinstrument>> copyKeyInstrumentMap = new HashMap<String, LinkedHashMap<String, Costinstrument>>();
					if(StringUtils.isNotEmpty(copyCostinstrumentList)) {
						copyKeyInstrumentMap = this.getInstrumentNameMapByUnitid(copyCostinstrumentList, copyItemIdKeyMap, null);
					}
					
					//复制的规定时间
					Map<String, List<CostStipulateTime>> copyCostStipulateTimeMap = new HashMap<String, List<CostStipulateTime>>();
					List<CostStipulateTime> copyCostStipulateTimeList_all = methodService.getCostStipulateTimeList(copyDto);
					if(StringUtils.isNotEmpty(copyCostStipulateTimeList_all)) {
						copyCostStipulateTimeMap = copyCostStipulateTimeList_all.stream().collect(Collectors.groupingBy(CostStipulateTime::getPid));
					}
					
					//复制的指标数据
					List<Costindicator> copyCostindicatorList = methodService.getCostindicatorList(copyDto);
					
					//复制的公式
					List<CostItemFormula> copyCostItemFormulaList = methodService.getCostItemFormulaList(copyDto);
					Map<String, List<CostItemFormula>> copyCostItemFormulaMap = new HashMap<String, List<CostItemFormula>>();
					if(StringUtils.isNotEmpty(copyCostItemFormulaList)) {
						copyCostItemFormulaMap = copyCostItemFormulaList.stream().collect(Collectors.groupingBy(CostItemFormula::getPid));
					}
					
					//替换公式用
		        	HashMap<String, List<BeanVo>> replace_itemList_map = new HashMap<String, List<BeanVo>>(); //项目
		        	HashMap<String, List<BeanVo>> replace_instrumentList_map = new HashMap<String, List<BeanVo>>(); //仪表
		        	HashMap<String, List<BeanVo>> replace_indicatorList_map = new HashMap<String, List<BeanVo>>(); //指标
		        	
		        	//已存在记录查询
		        	MethodQueryDto hasDto = new MethodQueryDto();
		        	hasDto.setUnitidList(unitidList);
		        	hasDto.setBegintime(begintime);
		        	
		        	//存在的核算分类
					HashMap<String, LinkedHashMap<String, Costclass>> hasKeyClassMap = new HashMap<String, LinkedHashMap<String, Costclass>>();
					HashMap<String, LinkedHashMap<String, List<Costclass>>> hasPidClassMap = new HashMap<String, LinkedHashMap<String, List<Costclass>>>();
					HashMap<String, HashMap<String, String>> hasClassIdKeyMap = new HashMap<String, HashMap<String, String>>();
					HashMap<String, HashMap<String, String>> hasClassKeyIdMap = new HashMap<String, HashMap<String, String>>();
					HashMap<String, HashMap<String, Integer>> classMaxPxMap = new HashMap<String, HashMap<String,Integer>>();
					List<Costclass> hasCostClassList = methodService.getCostclassList(hasDto);
					if(StringUtils.isNotEmpty(hasCostClassList)) {
						this.getCostClassMap(hasCostClassList, hasClassIdKeyMap, hasClassKeyIdMap, hasKeyClassMap, classMaxPxMap, hasPidClassMap);
					}
					
					//存在的核算项目
					HashMap<String, LinkedHashMap<String, Costitem>> hasKeyItemMap = new HashMap<String, LinkedHashMap<String, Costitem>>();
					HashMap<String, LinkedHashMap<String, List<Costitem>>> hasPidItemMap = new HashMap<String, LinkedHashMap<String, List<Costitem>>>();
					HashMap<String, HashMap<String, String>> hasItemIdKeyMap = new HashMap<String, HashMap<String, String>>();
					HashMap<String, HashMap<String, Integer>> itemMaxPxMap = new HashMap<String, HashMap<String,Integer>>();
					List<Costitem> hasCostItemList = methodService.getCostitemList(hasDto);
					if(StringUtils.isNotEmpty(hasCostItemList)) {
						hasKeyItemMap = this.getItemNameMapByUnitid(hasCostItemList, hasClassIdKeyMap, itemMaxPxMap, hasItemIdKeyMap, hasPidItemMap); //如果传入分类Map，项目名称前加上分类名称
					}
					
					//存在的成本项目仪表
					HashMap<String, LinkedHashMap<String, Costinstrument>> hasKeyInstrumentMap = new HashMap<String, LinkedHashMap<String, Costinstrument>>();
					HashMap<String, HashMap<String, Integer>> instrumentMaxPxMap = new HashMap<String, HashMap<String,Integer>>();
					List<Costinstrument> hasCostinstrumentList = methodService.getCostinstrumentList(hasDto);
					if(StringUtils.isNotEmpty(hasCostinstrumentList)) {
						hasKeyInstrumentMap = this.getInstrumentNameMapByUnitid(hasCostinstrumentList, hasItemIdKeyMap, instrumentMaxPxMap);
					}
					
		        	//存在的指标数据
					List<Costindicator> hasCostindicatorList = methodService.getCostindicatorList(hasDto);
					HashMap<String, Integer> indicatorMaxPxMap = new HashMap<String, Integer>();
					HashMap<String, LinkedHashMap<String, Costindicator>> hasKeyIndicatorMap = this.getIndicatorNameMapByUnitid(hasCostindicatorList, indicatorMaxPxMap);
					
					//整理数据（核算项目分类、核算项目、核算项目仪表）
					this.getCopySaveData(add_costclass_list, add_costitem_list, add_costinstrument_list, add_costStipulateTime_list,
						todoReplaceFormula_list, replace_itemList_map, replace_instrumentList_map,
						unitidList, begintime, newDotIdMap,
						copyKeyClassMap, copyKeyItemMap, copyKeyInstrumentMap, copyCostStipulateTimeMap, copyCostItemFormulaMap,
						hasKeyClassMap, hasPidClassMap, classMaxPxMap,
						hasKeyItemMap, hasPidItemMap, itemMaxPxMap,
						hasKeyInstrumentMap, instrumentMaxPxMap);
					
					//整理数据（核算指标）
					if(StringUtils.isNotEmpty(copyCostindicatorList)) {
						for (int i = 0; i < copyCostindicatorList.size(); i++) {
							Costindicator copyIndicatorObj = copyCostindicatorList.get(i);
							String copy_indicator_id = copyIndicatorObj.getId();
							String copy_indicator_name = copyIndicatorObj.getCpname()==null?"":copyIndicatorObj.getCpname().trim();
							
							//遍历核算对象id列表（复制指标）
							for (int j = 0; j < unitidList.size(); j++) {
								String unitid = unitidList.get(j);
								if(hasKeyIndicatorMap.containsKey(unitid)) {
									LinkedHashMap<String, Costindicator> keyIndicatorMap = hasKeyIndicatorMap.get(unitid);
									if(keyIndicatorMap.containsKey(copy_indicator_name)) {
										continue; //存在同名指标节点，不做任何处理
									}
								}
								//新增指标
								int maxPx = 0;
								if(indicatorMaxPxMap.containsKey(unitid)) {
									maxPx = indicatorMaxPxMap.get(unitid);
								}
								maxPx += 1;
								indicatorMaxPxMap.put(unitid, maxPx);
								Costindicator addIndicatorObj = new Costindicator();
								BeanUtils.copyProperties(copyIndicatorObj, addIndicatorObj); //赋予返回对象
								String new_indicator_id = TMUID.getUID();
								addIndicatorObj.setId(new_indicator_id);
								addIndicatorObj.setUnitid(unitid);
								addIndicatorObj.setBegintime(begintime);
								addIndicatorObj.setTmsort(maxPx);
								add_costindicator_list.add(addIndicatorObj);
								//替换公式用
								BeanVo indicatorBean = new BeanVo();
								indicatorBean.setCopyId(copy_indicator_id);
								indicatorBean.setNewId(new_indicator_id);
								if(replace_indicatorList_map.containsKey(unitid)) {
									List<BeanVo> beanList = replace_indicatorList_map.get(unitid);
									beanList.add(indicatorBean);
								}else {
									List<BeanVo> beanList = new ArrayList<BeanVo>();
									beanList.add(indicatorBean);
									replace_indicatorList_map.put(unitid, beanList);
								}
								//指标公式
								if(StringUtils.isNotEmpty(copyCostItemFormulaMap)&&copyCostItemFormulaMap.containsKey(copy_indicator_id)) {
									List<CostItemFormula> formulaList = copyCostItemFormulaMap.get(copy_indicator_id);
									if(StringUtils.isNotEmpty(formulaList)) {
										for (int m = 0; m < formulaList.size(); m++) {
											CostItemFormula formulaObj = formulaList.get(m);
											formulaObj.setId(TMUID.getUID());
											formulaObj.setPid(new_indicator_id);
											formulaObj.setUnitId(unitid);
											formulaObj.setBegintime(begintime);
											todoReplaceFormula_list.add(formulaObj);
										}
									}
								}
							}
						}
					}
					
					//有公式--替换公式
					if(StringUtils.isNotEmpty(todoReplaceFormula_list)) {
						Devicetypelibrary copyUnitTypeObj = deviceTypeService.getBean(unittypeid_copy);
						if(copyUnitTypeObj!=null) {
							for (int i = 0; i < todoReplaceFormula_list.size(); i++) {
								CostItemFormula formulaObj = todoReplaceFormula_list.get(i);
								String formula = formulaObj.getFormula();
								String cFormula = formulaObj.getCFormula();
								if(StringUtils.isNotEmpty(formula)&&StringUtils.isNotEmpty(cFormula)) {
									//替换公式中的核算对象ID、名称
									String copyCostId = copyUnitTypeObj.getId();
									String copyCostName = copyUnitTypeObj.getDtname();
									String newCostId = formulaObj.getUnitId();
									if(StringUtils.isNotEmpty(newCostId)&&StringUtils.isNotEmpty(unitMap)&&unitMap.containsKey(newCostId)) {
										String newCostName = unitMap.get(newCostId);
										formula = formula.replaceAll(copyCostId+".", newCostId+".");
										cFormula = cFormula.replaceAll(copyCostName+".", newCostName+".");
										//替换公式中的项目ID（名称未变化不需要替换）
										if(StringUtils.isNotEmpty(replace_itemList_map)&&replace_itemList_map.containsKey(newCostId)) {
											List<BeanVo> replace_itemList = replace_itemList_map.get(newCostId);
											if(StringUtils.isNotEmpty(replace_itemList)) {
												for (int j = 0; j < replace_itemList.size(); j++) {
													BeanVo replace_itemObj = replace_itemList.get(j);
													String copyItemId = replace_itemObj.getCopyId();
													String newItemId = replace_itemObj.getNewId();
													formula = formula.replaceAll("."+copyItemId+".", "."+newItemId+".");
												}
											}
										}
										//替换公式中的仪表ID（名称未变化不需要替换）
										if(StringUtils.isNotEmpty(replace_instrumentList_map)&&replace_instrumentList_map.containsKey(newCostId)) {
											List<BeanVo> replace_instrumentList = replace_instrumentList_map.get(newCostId);
											if(StringUtils.isNotEmpty(replace_instrumentList)) {
												for (int j = 0; j < replace_instrumentList.size(); j++) {
													BeanVo replace_instrumentObj = replace_instrumentList.get(j);
													String copyInstrumentId = replace_instrumentObj.getCopyId();
													String newInstrumentId = replace_instrumentObj.getNewId();
													formula = formula.replaceAll("."+copyInstrumentId+".", "."+newInstrumentId+".");
												}
											}
										}
										//替换公式中的指标ID（名称未变化不需要替换）
										if(StringUtils.isNotEmpty(replace_indicatorList_map)&&replace_indicatorList_map.containsKey(newCostId)) {
											List<BeanVo> replace_indicatorList = replace_indicatorList_map.get(newCostId);
											if(StringUtils.isNotEmpty(replace_indicatorList)) {
												for (int j = 0; j < replace_indicatorList.size(); j++) {
													BeanVo replace_indicatorObj = replace_indicatorList.get(j);
													String copyIndicatorId = replace_indicatorObj.getCopyId();
													String newIndicatorId = replace_indicatorObj.getNewId();
													formula = formula.replaceAll(copyIndicatorId, newIndicatorId);
												}
											}
										}
									}
								}
								formulaObj.setFormula(formula);
								formulaObj.setCFormula(cFormula);
								add_costItemFormula_list.add(formulaObj);
							}
						}
					}
				}
			}
		}
		//保存数据
		boolean isHasCopyData = false;
		if ("".equals(result) && StringUtils.isNotEmpty(add_costclass_list)) {
			isHasCopyData = true;
			if (entityService.insertBatch(add_costclass_list) == 0) {
				result = "添加失败（核算分类）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costitem_list)) {
			isHasCopyData = true;
			if (entityService.insertBatch(add_costitem_list) == 0) {
				result = "添加失败（核算项目）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costinstrument_list)) {
			isHasCopyData = true;
			if (entityService.insertBatch(add_costinstrument_list) == 0) {
				result = "添加失败（核算仪表）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costStipulateTime_list)) {
			isHasCopyData = true;
			if (entityService.insertBatch(add_costStipulateTime_list) == 0) {
				result = "添加失败（核算仪表的规定时间）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costindicator_list)) {
			isHasCopyData = true;
			if (entityService.insertBatch(add_costindicator_list) == 0) {
				result = "添加失败（核算指标）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costItemFormula_list)) {
			isHasCopyData = true;
			if (entityService.insertBatch(add_costItemFormula_list) == 0) {
				result = "添加失败（核算公式）！";
			}
		}
		if ("".equals(result) && !isHasCopyData) {
			result = "没有可复制的核算项目数据！";
		}
		return result;
	}
	
	//获取分类Map
	private void getCostClassMap(List<Costclass> classList, HashMap<String, HashMap<String, String>> idKeyMap, 
		HashMap<String, HashMap<String, String>> keyIdMap, HashMap<String, LinkedHashMap<String, Costclass>> keyClassMap,
		HashMap<String, HashMap<String, Integer>> classMaxPxMap, HashMap<String, LinkedHashMap<String, List<Costclass>>> pidClassMap) {
		if(idKeyMap==null) {
			idKeyMap = new HashMap<String, HashMap<String, String>>();
		}
		if(keyIdMap==null) {
			keyIdMap = new HashMap<String, HashMap<String, String>>();
		}
		if(keyClassMap==null) {
			keyClassMap = new HashMap<String, LinkedHashMap<String, Costclass>>();
		}
		if(classMaxPxMap==null) {
			classMaxPxMap = new HashMap<String, HashMap<String, Integer>>();
		}
		if(pidClassMap==null) {
			pidClassMap = new HashMap<String, LinkedHashMap<String, List<Costclass>>>();
		}
		if(StringUtils.isNotEmpty(classList)) {
			Map<String, List<Costclass>> classMap = classList.stream().collect(Collectors.groupingBy(Costclass::getPid));
			for (int i = 0; i < classList.size(); i++) {
				Costclass classObj = classList.get(i);
				String unitid = classObj.getUnitid();
				String id = classObj.getId();
				String name = classObj.getCcname();
				String pid = classObj.getPid();
				int tmsort = classObj.getTmsort()==null?0:classObj.getTmsort();
				if(StringUtils.isNotEmpty(pid)&&"root".equals(pid)) {
					String key = pid+this.MARKLABEL+name;
					
					if(idKeyMap.containsKey(unitid)) {
						HashMap<String, String> map = idKeyMap.get(unitid);
						map.put(id, key);
					}else {
						HashMap<String, String> map = new HashMap<String, String>();
						map.put(id, key);
						idKeyMap.put(unitid, map);
					}
					
					if(keyIdMap.containsKey(unitid)) {
						HashMap<String, String> map = keyIdMap.get(unitid);
						map.put(key, id);
					}else {
						HashMap<String, String> map = new HashMap<String, String>();
						map.put(key, id);
						keyIdMap.put(unitid, map);
					}
					
					if(keyClassMap.containsKey(unitid)) {
						LinkedHashMap<String, Costclass> map = keyClassMap.get(unitid);
						map.put(key, classObj);
					}else {
						LinkedHashMap<String, Costclass> map = new LinkedHashMap<String, Costclass>();
						map.put(key, classObj);
						keyClassMap.put(unitid, map);
					}
					
					if(classMaxPxMap.containsKey(unitid)) {
						HashMap<String, Integer> maxPxMap = classMaxPxMap.get(unitid);
						if(maxPxMap.containsKey(pid)) {
							Integer maxPx = maxPxMap.get(pid);
							if(tmsort>maxPx) {
								maxPxMap.put(pid, tmsort);
							}
						}else {
							maxPxMap.put(pid, tmsort);
						}
					}else {
						HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
						maxPxMap.put(pid, tmsort);
						classMaxPxMap.put(unitid, maxPxMap);
					}
					
					//按照分类分组
					if(pidClassMap.containsKey(unitid)) {
						LinkedHashMap<String, List<Costclass>> classNameMap = pidClassMap.get(unitid);
						if(classNameMap.containsKey(pid)) {
							List<Costclass> dataList = classNameMap.get(pid);
							dataList.add(classObj);
						}else {
							List<Costclass> dataList = new ArrayList<Costclass>();
							dataList.add(classObj);
							classNameMap.put(pid, dataList);
						}
					}else {
						List<Costclass> dataList = new ArrayList<Costclass>();
						dataList.add(classObj);
						LinkedHashMap<String, List<Costclass>> classNameMap = new LinkedHashMap<String, List<Costclass>>();
						classNameMap.put(pid, dataList);
						pidClassMap.put(unitid, classNameMap);
					}
					
					this.getChildCostClassMap(classMap, key, id, idKeyMap, keyIdMap, keyClassMap, classMaxPxMap, pidClassMap);
				}
			}
		}
	}
	
	//获取子级分类Map
	private void getChildCostClassMap(Map<String, List<Costclass>> classMap,String dataKey,String dataId,
		HashMap<String, HashMap<String, String>> idKeyMap, HashMap<String, HashMap<String, String>> keyIdMap, 
		HashMap<String, LinkedHashMap<String, Costclass>> keyClassMap,HashMap<String, HashMap<String, Integer>> classMaxPxMap,
		HashMap<String, LinkedHashMap<String, List<Costclass>>> pidClassMap) {
		if(StringUtils.isNotEmpty(classMap)&&classMap.containsKey(dataId)) {
			List<Costclass> classList = classMap.get(dataId);
			for (int i = 0; i < classList.size(); i++) {
				Costclass classObj = classList.get(i);
				String unitid = classObj.getUnitid();
				String id = classObj.getId();
				String name = classObj.getCcname();
				String pid = classObj.getPid();
				String key = dataKey + this.MARKLABEL + name;
				int tmsort = classObj.getTmsort()==null?0:classObj.getTmsort();
				
				if(idKeyMap.containsKey(unitid)) {
					HashMap<String, String> map = idKeyMap.get(unitid);
					map.put(id, key);
				}else {
					HashMap<String, String> map = new HashMap<String, String>();
					map.put(id, key);
					idKeyMap.put(unitid, map);
				}
				
				if(keyIdMap.containsKey(unitid)) {
					HashMap<String, String> map = keyIdMap.get(unitid);
					map.put(key, id);
				}else {
					HashMap<String, String> map = new HashMap<String, String>();
					map.put(key, id);
					keyIdMap.put(unitid, map);
				}
				
				if(keyClassMap.containsKey(unitid)) {
					LinkedHashMap<String, Costclass> map = keyClassMap.get(unitid);
					map.put(key, classObj);
				}else {
					LinkedHashMap<String, Costclass> map = new LinkedHashMap<String, Costclass>();
					map.put(key, classObj);
					keyClassMap.put(unitid, map);
				}
				
				if(classMaxPxMap.containsKey(unitid)) {
					HashMap<String, Integer> maxPxMap = classMaxPxMap.get(unitid);
					if(maxPxMap.containsKey(pid)) {
						Integer maxPx = maxPxMap.get(pid);
						if(tmsort>maxPx) {
							maxPxMap.put(pid, tmsort);
						}
					}else {
						maxPxMap.put(pid, tmsort);
					}
				}else {
					HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
					maxPxMap.put(pid, tmsort);
					classMaxPxMap.put(unitid, maxPxMap);
				}
				
				//按照分类分组
				if(pidClassMap.containsKey(unitid)) {
					LinkedHashMap<String, List<Costclass>> classNameMap = pidClassMap.get(unitid);
					if(classNameMap.containsKey(dataKey)) {
						List<Costclass> dataList = classNameMap.get(dataKey);
						dataList.add(classObj);
					}else {
						List<Costclass> dataList = new ArrayList<Costclass>();
						dataList.add(classObj);
						classNameMap.put(dataKey, dataList);
					}
				}else {
					List<Costclass> dataList = new ArrayList<Costclass>();
					dataList.add(classObj);
					LinkedHashMap<String, List<Costclass>> classNameMap = new LinkedHashMap<String, List<Costclass>>();
					classNameMap.put(dataKey, dataList);
					pidClassMap.put(unitid, classNameMap);
				}
				
				this.getChildCostClassMap(classMap, key, id, idKeyMap, keyIdMap, keyClassMap, classMaxPxMap, pidClassMap);
			}
		}
	}
	
	//获取项目数据Map
	private HashMap<String, LinkedHashMap<String, Costitem>> getItemNameMapByUnitid(List<Costitem> list, 
		HashMap<String, HashMap<String, String>> classIdKeyMap, HashMap<String, HashMap<String, Integer>> itemMaxPxMap,
		HashMap<String, HashMap<String, String>> itemIdKeyMap, HashMap<String, LinkedHashMap<String, List<Costitem>>> pidItemMap) {
		HashMap<String, LinkedHashMap<String, Costitem>> map = new HashMap<String, LinkedHashMap<String, Costitem>>();
		if(itemMaxPxMap==null) {
			itemMaxPxMap = new HashMap<String, HashMap<String, Integer>>();
		}
		if(itemIdKeyMap==null) {
			itemIdKeyMap = new HashMap<String, HashMap<String, String>>();
		}
		if(pidItemMap==null) {
			pidItemMap = new HashMap<String, LinkedHashMap<String, List<Costitem>>>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costitem obj = list.get(i);
				String unitid = obj.getUnitid();
				String pid = obj.getPid();
				String id = obj.getId();
				String name = obj.getItemname();
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(StringUtils.isNotEmpty(pid)&&StringUtils.isNotEmpty(name)) {
					name = name.trim();
					if(StringUtils.isNotEmpty(classIdKeyMap)&&classIdKeyMap.containsKey(unitid)) { //如果传入分类Map，项目名称前加上分类名称
						HashMap<String, String> idKeyMap = classIdKeyMap.get(unitid);
						if(StringUtils.isNotEmpty(idKeyMap)&&idKeyMap.containsKey(pid)) {
							String className = idKeyMap.get(pid);
							name = className + this.MARKLABEL + name;
							//按照分类分组
							if(pidItemMap.containsKey(unitid)) {
								LinkedHashMap<String, List<Costitem>> classNameMap = pidItemMap.get(unitid);
								if(classNameMap.containsKey(className)) {
									List<Costitem> itemList = classNameMap.get(className);
									itemList.add(obj);
								}else {
									List<Costitem> itemList = new ArrayList<Costitem>();
									itemList.add(obj);
									classNameMap.put(className, itemList);
								}
							}else {
								List<Costitem> itemList = new ArrayList<Costitem>();
								itemList.add(obj);
								LinkedHashMap<String, List<Costitem>> classNameMap = new LinkedHashMap<String, List<Costitem>>();
								classNameMap.put(className, itemList);
								pidItemMap.put(unitid, classNameMap);
							}
						}
					}
				}
				if(map.containsKey(unitid)) {
					LinkedHashMap<String, Costitem> nameMap = map.get(unitid);
					nameMap.put(name, obj);
				}else {
					LinkedHashMap<String, Costitem> nameMap = new LinkedHashMap<String, Costitem>();
					nameMap.put(name, obj);
					map.put(unitid, nameMap);
				}
				if(itemMaxPxMap.containsKey(unitid)) {
					HashMap<String, Integer> maxPxMap = itemMaxPxMap.get(unitid);
					if(maxPxMap.containsKey(pid)) {
						Integer maxPx = maxPxMap.get(pid);
						if(tmsort>maxPx) {
							maxPxMap.put(pid, tmsort);
						}
					}else {
						maxPxMap.put(pid, tmsort);
					}
				}else {
					HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
					maxPxMap.put(pid, tmsort);
					itemMaxPxMap.put(unitid, maxPxMap);
				}
				if(itemIdKeyMap.containsKey(unitid)) {
					HashMap<String, String> dataMap = itemIdKeyMap.get(unitid);
					dataMap.put(id, name);
				}else {
					HashMap<String, String> dataMap = new HashMap<String, String>();
					dataMap.put(id, name);
					itemIdKeyMap.put(unitid, dataMap);
				}
			}
		}
		return map;
	}
	
	//获取仪表数据Map
	private HashMap<String, LinkedHashMap<String, Costinstrument>> getInstrumentNameMapByUnitid(List<Costinstrument> list, 
		HashMap<String, HashMap<String, String>> itemIdKeyMap, HashMap<String, HashMap<String, Integer>> instrumentMaxPxMap) {
		HashMap<String, LinkedHashMap<String, Costinstrument>> map = new HashMap<String, LinkedHashMap<String, Costinstrument>>();
		if(instrumentMaxPxMap==null) {
			instrumentMaxPxMap = new HashMap<String, HashMap<String, Integer>>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costinstrument obj = list.get(i);
				String unitid = obj.getUnitid();
				String pid = obj.getPid();
				String name = obj.getName();
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(StringUtils.isNotEmpty(pid)&&StringUtils.isNotEmpty(name)) {
					name = name.trim();
					if(StringUtils.isNotEmpty(itemIdKeyMap)&&itemIdKeyMap.containsKey(unitid)) { //如果传入分类Map，采集点名称前加上分类名称
						HashMap<String, String> idKeyMap = itemIdKeyMap.get(unitid);
						if(StringUtils.isNotEmpty(idKeyMap)&&idKeyMap.containsKey(pid)) {
							String itemName = idKeyMap.get(pid);
							name = itemName + this.MARKLABEL + name;
						}
					}
				}
				if(map.containsKey(unitid)) {
					LinkedHashMap<String, Costinstrument> nameMap = map.get(unitid);
					nameMap.put(name, obj);
				}else {
					LinkedHashMap<String, Costinstrument> nameMap = new LinkedHashMap<String, Costinstrument>();
					nameMap.put(name, obj);
					map.put(unitid, nameMap);
				}
				if(instrumentMaxPxMap.containsKey(unitid)) {
					HashMap<String, Integer> maxPxMap = instrumentMaxPxMap.get(unitid);
					if(maxPxMap.containsKey(pid)) {
						Integer maxPx = maxPxMap.get(pid);
						if(tmsort>maxPx) {
							maxPxMap.put(pid, tmsort);
						}
					}else {
						maxPxMap.put(pid, tmsort);
					}
				}else {
					HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
					maxPxMap.put(pid, tmsort);
					instrumentMaxPxMap.put(unitid, maxPxMap);
				}
			}
		}
		return map;
	}
	
	//获取指标数据Map
	private HashMap<String, LinkedHashMap<String, Costindicator>> getIndicatorNameMapByUnitid(List<Costindicator> list, 
		HashMap<String, Integer> indicatorMaxPxMap) {
		HashMap<String, LinkedHashMap<String, Costindicator>> map = new HashMap<String, LinkedHashMap<String, Costindicator>>();
		if(indicatorMaxPxMap==null) {
			indicatorMaxPxMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costindicator obj = list.get(i);
				String unitid = obj.getUnitid();
				String name = obj.getCpname()==null?"":obj.getCpname().trim();
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(map.containsKey(unitid)) {
					LinkedHashMap<String, Costindicator> nameMap = map.get(unitid);
					nameMap.put(name, obj);
				}else {
					LinkedHashMap<String, Costindicator> nameMap = new LinkedHashMap<String, Costindicator>();
					nameMap.put(name, obj);
					map.put(unitid, nameMap);
				}
				if(indicatorMaxPxMap.containsKey(unitid)) {
					Integer maxPx = indicatorMaxPxMap.get(unitid);
					if(tmsort>maxPx) {
						indicatorMaxPxMap.put(unitid, tmsort);
					}
				}else {
					indicatorMaxPxMap.put(unitid, tmsort);
				}
			}
		}
		return map;
	}
	
	//整理数据（核算项目分类、核算项目、核算项目仪表）
	private void getCopySaveData(List<Costclass> add_costclass_list,List<Costitem> add_costitem_list,List<Costinstrument> add_costinstrument_list,List<CostStipulateTime> add_costStipulateTime_list,
		List<CostItemFormula> todoReplaceFormula_list, HashMap<String, List<BeanVo>> replace_itemList_map, HashMap<String, List<BeanVo>> replace_instrumentList_map,
		
		List<String> unitidList,String begintime, HashMap<String, HashMap<String, String>> newDotIdMap,
		
		HashMap<String, LinkedHashMap<String, Costclass>> copyKeyClassMap,
		HashMap<String, LinkedHashMap<String, Costitem>> copyKeyItemMap,
		HashMap<String, LinkedHashMap<String, Costinstrument>> copyKeyInstrumentMap,
		Map<String, List<CostStipulateTime>> copyCostStipulateTimeMap,
		Map<String, List<CostItemFormula>> copyCostItemFormulaMap,
		
		HashMap<String, LinkedHashMap<String, Costclass>> hasKeyClassMap, HashMap<String, LinkedHashMap<String, List<Costclass>>> hasPidClassMap, HashMap<String, HashMap<String, Integer>> classMaxPxMap,
		HashMap<String, LinkedHashMap<String, Costitem>> hasKeyItemMap, HashMap<String, LinkedHashMap<String, List<Costitem>>> hasPidItemMap, HashMap<String, HashMap<String, Integer>> itemMaxPxMap,
		HashMap<String, LinkedHashMap<String, Costinstrument>> hasKeyInstrumentMap, HashMap<String, HashMap<String, Integer>> instrumentMaxPxMap) {
		
		//遍历复制记录（分类）
		if(StringUtils.isNotEmpty(copyKeyClassMap)) {
			Iterator<Entry<String, LinkedHashMap<String, Costclass>>> copyKeyClassMapIter = copyKeyClassMap.entrySet().iterator();
			while(copyKeyClassMapIter.hasNext()) {
				Entry<String, LinkedHashMap<String, Costclass>> copyKeyClassMapEntry = copyKeyClassMapIter.next();
				LinkedHashMap<String, Costclass> copyClassMap = copyKeyClassMapEntry.getValue();
				if(StringUtils.isNotEmpty(copyClassMap)) {
					Iterator<Entry<String, Costclass>> copyClassMapIter = copyClassMap.entrySet().iterator();
					while(copyClassMapIter.hasNext()) {
						Entry<String, Costclass> copyClassMapEntry = copyClassMapIter.next();
						String copyClassKey = copyClassMapEntry.getKey();
						Costclass copyClassObj = copyClassMapEntry.getValue();
						//遍历核算对象id列表（复制分类）
						for (int j = 0; j < unitidList.size(); j++) {
							String unitid = unitidList.get(j);
							String pid = "";
							String pidStr = "";
							if(hasKeyClassMap.containsKey(unitid)) {
								LinkedHashMap<String, Costclass> hasClassMap = hasKeyClassMap.get(unitid);
								if(hasClassMap.containsKey(copyClassKey)) {
									continue; //存在同名分类节点，不做任何处理
								}else { //新增分类
									//获取父级分类id
									String[] classKeyArr = copyClassKey.split(this.MARKLABEL);
									if(classKeyArr.length==2) {
										pid = "root";
										pidStr = pid;
									}else if(classKeyArr.length>2) {
										pidStr = "";
										for (int i = 0; i < classKeyArr.length-1; i++) {
											pidStr += this.MARKLABEL + classKeyArr[i];
										}
										if(!"".equals(pidStr)) {
											pidStr = pidStr.substring(this.MARKLABEL.length());
											if(hasClassMap.containsKey(pidStr)) {
												pid = hasClassMap.get(pidStr).getId();
											}
										}
									}
									//分类下已存在项目，不能再新增分类
									if(hasPidItemMap.containsKey(unitid)&&StringUtils.isNotEmpty(pid)&&!"root".equals(pid)) {
										LinkedHashMap<String, List<Costitem>> hasItemMap = hasPidItemMap.get(unitid);
										if(StringUtils.isNotEmpty(hasItemMap)&&hasItemMap.containsKey(pidStr)) {
											continue;
										}
									}
								}
							}else {
								pid = "root";
							}
							if(StringUtils.isNotEmpty(pid)) { //新增
								int maxPx = 0;
								if(classMaxPxMap.containsKey(unitid)) {
									HashMap<String, Integer> maxPxMap = classMaxPxMap.get(unitid);
									if(maxPxMap.containsKey(pid)) {
										maxPx = maxPxMap.get(pid);
									}
									maxPxMap.put(pid, maxPx+1);
								}else {
									HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
									maxPxMap.put(pid, maxPx+1);
									classMaxPxMap.put(unitid, maxPxMap);
								}
								maxPx += 1;
								Costclass addClassObj = new Costclass();
								BeanUtils.copyProperties(copyClassObj, addClassObj); //赋予返回对象
								addClassObj.setId(TMUID.getUID());
								addClassObj.setUnitid(unitid);
								addClassObj.setBegintime(begintime);
								addClassObj.setPid(pid);
								addClassObj.setTmsort(maxPx);
								add_costclass_list.add(addClassObj);
								if(hasKeyClassMap.containsKey(unitid)) {
									LinkedHashMap<String, Costclass> keyClassMap = hasKeyClassMap.get(unitid);
									keyClassMap.put(copyClassKey, addClassObj);
								}else {
									LinkedHashMap<String, Costclass> keyClassMap = new LinkedHashMap<String, Costclass>();
									keyClassMap.put(copyClassKey, addClassObj);
									hasKeyClassMap.put(unitid, keyClassMap);
								}
							}
						}
					}
				}
			}
		}
		
		//遍历复制记录（项目）
		if(StringUtils.isNotEmpty(copyKeyItemMap)) {
			Iterator<Entry<String, LinkedHashMap<String, Costitem>>> copyKeyItemMapIter = copyKeyItemMap.entrySet().iterator();
			while(copyKeyItemMapIter.hasNext()) {
				Entry<String, LinkedHashMap<String, Costitem>> copyKeyItemMapEntry = copyKeyItemMapIter.next();
				LinkedHashMap<String, Costitem> copyItemMap = copyKeyItemMapEntry.getValue();
				if(StringUtils.isNotEmpty(copyItemMap)) {
					Iterator<Entry<String, Costitem>> copyItemMapIter = copyItemMap.entrySet().iterator();
					while(copyItemMapIter.hasNext()) {
						Entry<String, Costitem> copyItemMapEntry = copyItemMapIter.next();
						String copyItemKey = copyItemMapEntry.getKey();
						Costitem copyItemObj = copyItemMapEntry.getValue();
						//遍历核算对象id列表（复制项目）
						for (int j = 0; j < unitidList.size(); j++) {
							String unitid = unitidList.get(j);
							if(hasKeyItemMap.containsKey(unitid)) {
								LinkedHashMap<String, Costitem> keyItemMap = hasKeyItemMap.get(unitid);
								if(keyItemMap.containsKey(copyItemKey)) {
									continue; //存在同名项目节点，不做任何处理
								}
							}
							//新增项目
							//获取父级分类id
							String pid = "";
							String[] itemKeyArr = copyItemKey.split(this.MARKLABEL);
							if(itemKeyArr.length>=2) {
								String pidStr = "";
								for (int i = 0; i < itemKeyArr.length-1; i++) {
									pidStr += this.MARKLABEL + itemKeyArr[i];
								}
								if(!"".equals(pidStr)) {
									pidStr = pidStr.substring(this.MARKLABEL.length());
									LinkedHashMap<String, Costclass> keyClassMap = new LinkedHashMap<String, Costclass>();
									if(hasKeyClassMap.containsKey(unitid)) {
										keyClassMap = hasKeyClassMap.get(unitid);
									}
									if(StringUtils.isNotEmpty(keyClassMap)&&keyClassMap.containsKey(pidStr)) {
										pid = keyClassMap.get(pidStr).getId();
									}
								}
								//分类下已存在子分类，不能再新增项目
								if(hasPidClassMap.containsKey(unitid)&&StringUtils.isNotEmpty(pid)) {
									LinkedHashMap<String, List<Costclass>> hasClassMap = hasPidClassMap.get(unitid);
									if(StringUtils.isNotEmpty(hasClassMap)&&hasClassMap.containsKey(pidStr)) {
										continue;
									}
								}
							}
							if(StringUtils.isNotEmpty(pid)) {
								int maxPx = 0;
								if(itemMaxPxMap.containsKey(unitid)) {
									HashMap<String, Integer> maxPxMap = itemMaxPxMap.get(unitid);
									if(maxPxMap.containsKey(pid)) {
										maxPx = maxPxMap.get(pid);
									}
									maxPxMap.put(pid, maxPx+1);
								}else {
									HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
									maxPxMap.put(pid, maxPx+1);
									itemMaxPxMap.put(unitid, maxPxMap);
								}
								maxPx += 1;
								Costitem addItemObj = new Costitem();
								BeanUtils.copyProperties(copyItemObj, addItemObj); //赋予返回对象
								addItemObj.setId(TMUID.getUID());
								addItemObj.setUnitid(unitid);
								addItemObj.setBegintime(begintime);
								addItemObj.setPid(pid);
								addItemObj.setTmsort(maxPx);
								add_costitem_list.add(addItemObj);
								if(hasKeyItemMap.containsKey(unitid)) {
									LinkedHashMap<String, Costitem> keyItemMap = hasKeyItemMap.get(unitid);
									keyItemMap.put(copyItemKey, addItemObj);
								}else {
									LinkedHashMap<String, Costitem> keyItemMap = new LinkedHashMap<String, Costitem>();
									keyItemMap.put(copyItemKey, addItemObj);
									hasKeyItemMap.put(unitid, keyItemMap);
								}
								//替换公式用
								BeanVo itemBean = new BeanVo();
								itemBean.setCopyId(copyItemObj.getId());
								itemBean.setNewId(addItemObj.getId());
								if(replace_itemList_map.containsKey(unitid)) {
									List<BeanVo> beanList = replace_itemList_map.get(unitid);
									beanList.add(itemBean);
								}else {
									List<BeanVo> beanList = new ArrayList<BeanVo>();
									beanList.add(itemBean);
									replace_itemList_map.put(unitid, beanList);
								}
								//核算项目公式
								if(StringUtils.isNotEmpty(copyCostItemFormulaMap)&&copyCostItemFormulaMap.containsKey(copyItemObj.getId())) {
									List<CostItemFormula> formulaList = copyCostItemFormulaMap.get(copyItemObj.getId());
									if(StringUtils.isNotEmpty(formulaList)) {
										for (int m = 0; m < formulaList.size(); m++) {
											CostItemFormula formulaObj = formulaList.get(m);
											formulaObj.setId(TMUID.getUID());
											formulaObj.setPid(addItemObj.getId());
											formulaObj.setUnitId(unitid);
											formulaObj.setBegintime(begintime);
											todoReplaceFormula_list.add(formulaObj);
										}
									}
								}
							}
						}
					}
				}
			}
		}
		
		//遍历复制记录（仪表）
		if(StringUtils.isNotEmpty(copyKeyInstrumentMap)) {
			Iterator<Entry<String, LinkedHashMap<String, Costinstrument>>> copyKeyInstrumentMapIter = copyKeyInstrumentMap.entrySet().iterator();
			while(copyKeyInstrumentMapIter.hasNext()) {
				Entry<String, LinkedHashMap<String, Costinstrument>> copyKeyInstrumentMapEntry = copyKeyInstrumentMapIter.next();
				LinkedHashMap<String, Costinstrument> copyInstrumentMap = copyKeyInstrumentMapEntry.getValue();
				if(StringUtils.isNotEmpty(copyInstrumentMap)) {
					Iterator<Entry<String, Costinstrument>> copyInstrumentMapIter = copyInstrumentMap.entrySet().iterator();
					while(copyInstrumentMapIter.hasNext()) {
						Entry<String, Costinstrument> copyInstrumentMapEntry = copyInstrumentMapIter.next();
						String copyInstrumentKey = copyInstrumentMapEntry.getKey();
						Costinstrument copyInstrumentObj = copyInstrumentMapEntry.getValue();
						//遍历核算对象id列表（复制仪表）
						for (int j = 0; j < unitidList.size(); j++) {
							String unitid = unitidList.get(j);
							if(hasKeyInstrumentMap.containsKey(unitid)) {
								LinkedHashMap<String, Costinstrument> keyInstrumentMap = hasKeyInstrumentMap.get(unitid);
								if(keyInstrumentMap.containsKey(copyInstrumentKey)) {
									continue; //存在同名仪表节点，不做任何处理
								}
							}
							//新增仪表
							//获取父级分类id
							String pid = "";
							String[] instrumentKeyArr = copyInstrumentKey.split(this.MARKLABEL);
							if(instrumentKeyArr.length>=2) {
								String pidStr = "";
								for (int i = 0; i < instrumentKeyArr.length-1; i++) {
									pidStr += this.MARKLABEL + instrumentKeyArr[i];
								}
								if(!"".equals(pidStr)) {
									pidStr = pidStr.substring(this.MARKLABEL.length());
									LinkedHashMap<String, Costitem> keyItemMap = new LinkedHashMap<String, Costitem>();
									if(hasKeyItemMap.containsKey(unitid)) {
										keyItemMap = hasKeyItemMap.get(unitid);
									}
									if(StringUtils.isNotEmpty(keyItemMap)&&keyItemMap.containsKey(pidStr)) {
										pid = keyItemMap.get(pidStr).getId();
									}
								}
							}
							if(StringUtils.isNotEmpty(pid)) {
								int maxPx = 0;
								if(instrumentMaxPxMap.containsKey(unitid)) {
									HashMap<String, Integer> maxPxMap = instrumentMaxPxMap.get(unitid);
									if(maxPxMap.containsKey(pid)) {
										maxPx = maxPxMap.get(pid);
									}
									maxPxMap.put(pid, maxPx+1);
								}else {
									HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
									maxPxMap.put(pid, maxPx+1);
									instrumentMaxPxMap.put(unitid, maxPxMap);
								}
								maxPx += 1;
								String newDotId = ""; //新采集点ID（仪表id）
								if(StringUtils.isNotEmpty(newDotIdMap)&&newDotIdMap.containsKey(unitid)) {
									HashMap<String, String> dotIdMap = newDotIdMap.get(unitid);
									String oldDotid = copyInstrumentObj.getDotid();
									if(StringUtils.isNotEmpty(dotIdMap)&&StringUtils.isNotEmpty(oldDotid)&&dotIdMap.containsKey(oldDotid)) {
										newDotId = dotIdMap.get(oldDotid);
									}
								}
								Costinstrument addInstrumentObj = new Costinstrument();
								BeanUtils.copyProperties(copyInstrumentObj, addInstrumentObj); //赋予返回对象
								addInstrumentObj.setId(TMUID.getUID());
								addInstrumentObj.setUnitid(unitid);
								addInstrumentObj.setBegintime(begintime);
								addInstrumentObj.setPid(pid);
								addInstrumentObj.setTmsort(maxPx);
								addInstrumentObj.setDotid(newDotId);
								add_costinstrument_list.add(addInstrumentObj);
								//替换公式用
								BeanVo instrumentBean = new BeanVo();
								instrumentBean.setCopyId(copyInstrumentObj.getId());
								instrumentBean.setNewId(addInstrumentObj.getId());
								if(replace_instrumentList_map.containsKey(unitid)) {
									List<BeanVo> beanList = replace_instrumentList_map.get(unitid);
									beanList.add(instrumentBean);
								}else {
									List<BeanVo> beanList = new ArrayList<BeanVo>();
									beanList.add(instrumentBean);
									replace_instrumentList_map.put(unitid, beanList);
								}
								//项目仪表公式
								if(StringUtils.isNotEmpty(copyCostItemFormulaMap)&&copyCostItemFormulaMap.containsKey(copyInstrumentObj.getId())) {
									List<CostItemFormula> formulaList = copyCostItemFormulaMap.get(copyInstrumentObj.getId());
									if(StringUtils.isNotEmpty(formulaList)) {
										for (int m = 0; m < formulaList.size(); m++) {
											CostItemFormula formulaObj = formulaList.get(m);
											formulaObj.setId(TMUID.getUID());
											formulaObj.setPid(addInstrumentObj.getId());
											formulaObj.setUnitId(unitid);
											formulaObj.setBegintime(begintime);
											todoReplaceFormula_list.add(formulaObj);
										}
									}
								}
								//规定时间
								if(StringUtils.isNotEmpty(copyCostStipulateTimeMap)&&copyCostStipulateTimeMap.containsKey(copyInstrumentObj.getId())) {
									List<CostStipulateTime> stipulateTimeList = copyCostStipulateTimeMap.get(copyInstrumentObj.getId());
									if(StringUtils.isNotEmpty(stipulateTimeList)) {
										for (int m = 0; m < stipulateTimeList.size(); m++) {
											CostStipulateTime stipulateTimeObj = stipulateTimeList.get(m);
											stipulateTimeObj.setId(TMUID.getUID());
											stipulateTimeObj.setPid(addInstrumentObj.getId());
											stipulateTimeObj.setUnitid(unitid);
											stipulateTimeObj.setBegintime(begintime);
											add_costStipulateTime_list.add(stipulateTimeObj);
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	
	//——————————————————————————————————————————   复制核算项目 ↑ ————————————————————————————————————————————————————————————
	
}
