package com.yunhesoft.leanCosting.unitConf.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostFormulaQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostItemFormulaSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostFormulaVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostFormulaService;
import com.yunhesoft.leanCosting.unitConf.service.impl.CostFormulaForMTM;
import com.yunhesoft.outInterface.util.PortAffairsFormulaForMTM;
import com.yunhesoft.system.tools.classExec.entry.dto.MtmFormulaOperateDto;
import com.yunhesoft.system.tools.classExec.entry.dto.MtmFormulaTreeDto;
import com.yunhesoft.system.tools.classExec.entry.dto.MtmFormulaValueDto;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaApiVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/costFormula")
@Api(tags = "核算公式")
public class CostFormulaController {

	@Autowired
	private ICostFormulaService costFormulaService;
	@Autowired
	private CostFormulaForMTM formula;
	@Autowired
	private PortAffairsFormulaForMTM portAffairsFormula;
	
	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获取数据")
	public Res<?> getData(@RequestBody CostFormulaQueryDto dto) {
		Res<CostFormulaVo> res = new Res<CostFormulaVo>();
//		dto.setBegintime(DateTimeUtils.getNowDateStr());//不需要前台传值
		CostFormulaVo obj = costFormulaService.getData(dto);
		res.setResult(obj);
		return res;
	}
	
	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> saveData(@RequestBody CostItemFormulaSaveDto dto) {
		String message = costFormulaService.saveData(dto);
		Res<String> res = new Res<String>();
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}
	
	@RequestMapping(value = "/getFormulaTree", method = RequestMethod.POST)
	@ApiOperation("获取核算公式树形")
	public Res<MtmFormulaApiVo> getFormulaTree(@RequestBody MtmFormulaTreeDto dto) {
		Res<MtmFormulaApiVo> res = new Res<MtmFormulaApiVo>();
		MtmFormulaApiVo vo = new MtmFormulaApiVo();
		vo.setTreeList(formula.getFormulaTree(dto.getPId(), dto.getIsRootLoad()==null?false:dto.getIsRootLoad().booleanValue()));//获取公式树形
		res.ok(vo);
		return res;
	}
	@RequestMapping(value = "/getFormulaValue", method = RequestMethod.POST)
	@ApiOperation("解析核算公式")
	public Res<MtmFormulaApiVo> getFormulaValue(@RequestBody MtmFormulaValueDto dto) {
		Res<MtmFormulaApiVo> res = new Res<MtmFormulaApiVo>();
		MtmFormulaApiVo vo = new MtmFormulaApiVo();
		vo.setValueList(formula.getFormulaValue(dto.getStartDt(), dto.getEndDt(), dto.getFormulaTextList(), dto.getFormulaTextObjList()));//获取公式解析值
		res.ok(vo);
		return res;
	}
	
	@RequestMapping(value = "/getPortAffairsFormulaGetJsonData", method = RequestMethod.POST)
	@ApiOperation("获取港务系统json数据")
	public Res<MtmFormulaApiVo> getPortAffairsFormulaGetJsonData(@RequestBody MtmFormulaOperateDto dto) {
		Res<MtmFormulaApiVo> res = new Res<MtmFormulaApiVo>();
		MtmFormulaApiVo vo = new MtmFormulaApiVo();
		portAffairsFormula.getJsonData(dto.getStartDt(), dto.getEndDt(), dto.getQueryList());
		vo.setValueList(dto.getQueryList());//获取json数据
		res.ok(vo);
		return res;
	}
	
	@RequestMapping(value = "/getPortAffairsFormulaSaveJsonData", method = RequestMethod.POST)
	@ApiOperation("保存港务系统json数据")
	public Res<MtmFormulaApiVo> getPortAffairsFormulaSaveJsonData(@RequestBody MtmFormulaOperateDto dto) {
		Res<MtmFormulaApiVo> res = new Res<MtmFormulaApiVo>();
		MtmFormulaApiVo vo = new MtmFormulaApiVo();
		vo.setResult(portAffairsFormula.saveJsonData(dto.getSaveList()));//保存json数据
		res.ok(vo);
		return res;
	}
	
	@RequestMapping(value = "/getPortAffairsFormulaInit", method = RequestMethod.POST)
	@ApiOperation("港务系统公式初始化")
	public Res<MtmFormulaApiVo> getPortAffairsFormulaInit(@RequestBody MtmFormulaOperateDto dto) {
		Res<MtmFormulaApiVo> res = new Res<MtmFormulaApiVo>();
		MtmFormulaApiVo vo = new MtmFormulaApiVo();
		portAffairsFormula.init();//初始化数据
		vo.setResult(true);//默认成功
		res.ok(vo);
		return res;
	}
}
