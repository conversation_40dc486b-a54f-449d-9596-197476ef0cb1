package com.yunhesoft.leanCosting.unitConf.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostunitSampledotWidgetConfig;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostunitSampledotWidgetData;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostunitSampledotWidgetTemplate;
import com.yunhesoft.leanCosting.unitConf.service.ICostunitSampledotWidgetService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "简流表单组件")
@RestController
@RequestMapping("/leanCosting/unitConf/widget/sampledot")
public class CostunitSampledotWidgetController extends BaseRestController {

	@Autowired
	private ICostunitSampledotWidgetService srv;


	@ApiOperation(value = "查询模板列表")
	@RequestMapping(value = "queryTemplateList", method = { RequestMethod.POST })
	public Res<?> queryTemplateList(@RequestBody(required = false) CostunitSampledotWidgetTemplate t) {
		return Res.OK(srv.queryTemplateList(t==null?null:t.getName()));
	}

	@ApiOperation(value = "保存模板")
	@RequestMapping(value = "saveTemplateList", method = { RequestMethod.POST })
	public Res<?> saveTemplateList(@RequestBody List<CostunitSampledotWidgetTemplate> list) {
		return Res.OK(srv.saveTemplateList(list));
	}

	@ApiOperation(value = "批量删除模板")
	@RequestMapping(value = "deleteTemplateByIdList", method = { RequestMethod.POST })
	public Res<?> deleteTemplateByIdList(@RequestBody List<String> idList) {
		return Res.OK(srv.deleteTemplateByIdList(idList));
	}

	@ApiOperation(value = "根据表单id查询设置")
	@RequestMapping(value = "queryConfigListByTemplateId", method = { RequestMethod.GET })
	public Res<?> queryConfigListByTemplateId(@RequestParam String templateId) {
		return Res.OK(srv.queryConfigListByTemplateId(templateId));
	}

	@ApiOperation(value = "保存设置")
	@RequestMapping(value = "saveConfigList", method = { RequestMethod.POST })
	public Res<?> saveConfigList(@RequestBody List<CostunitSampledotWidgetConfig> list) {
		return Res.OK(srv.saveConfigList(list));
	}

	@ApiOperation(value = "批量删除设置")
	@RequestMapping(value = "deleteConfigByIdList", method = { RequestMethod.POST })
	public Res<?> deleteConfigByIdList(@RequestBody List<String> idList) {
		return Res.OK(srv.deleteConfigByIdList(idList));
	}

	@ApiOperation(value = "查询实时仪表组件录入数据")
	@RequestMapping(value = "getSampledotWidgetData", method = { RequestMethod.POST })
	public Res<?> getSampledotWidgetData(@RequestBody CostunitSampledotWidgetData dto) {
		return Res.OK(srv.getSampledotWidgetData(dto.getTemplateId(), dto.getDataId(), dto.getCompId(), dto.getUnitId(), dto.getStartTime(), dto.getEndTime()));
	}

	@ApiOperation(value = "保存实时仪表组件录入数据")
	@RequestMapping(value = "saveSampledotWidgetData", method = { RequestMethod.POST })
	public Res<?> saveSampledotWidgetData(@RequestBody CostunitSampledotWidgetData dto) {
		return Res.OK(srv.saveSampledotWidgetData(dto));
	}

	@ApiOperation(value = "查询实时仪表组件表头及数据")
	@RequestMapping(value = "querySampledotWidgetColumnAndData", method = { RequestMethod.POST })
	public Res<?> querySampledotWidgetColumnAndData(@RequestBody CostunitSampledotWidgetData dto) {
		return Res.OK(srv.querySampledotWidgetColumnAndData(dto.getTemplateId(), dto.getDataId(), dto.getCompId(), dto.getUnitId(), dto.getStartTime(), dto.getEndTime()));
	}


	@ApiOperation(value = "查询实时仪表组件缓存数据")
	@RequestMapping(value = "querySampledotWidgetDataTemp", method = { RequestMethod.POST })
	public Res<?> querySampledotWidgetDataTemp(@RequestBody CostunitSampledotWidgetData dto) {
		return Res.OK(srv.querySampledotWidgetDataTemp(dto.getDataId(), dto.getUnitId(), dto.getStartTime(), dto.getEndTime()));

	}

}
