package com.yunhesoft.leanCosting.unitConf.controller;


import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.unitConf.entity.dto.BriefAnalysisQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.BriefAnalysisSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.BriefAnalysisMeasureConf;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BriefAnalysisConfVo;
import com.yunhesoft.leanCosting.unitConf.service.IBriefAnalysisService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/leanCosting/briefAnalysisConf")
@Api(tags = "简要分析设置")
public class BriefAnalysisController extends BaseRestController {

	
	@Autowired
	private IBriefAnalysisService briefAnalysisService;

	
	/**
	 *	获取简要分析设置数据
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/getBriefAnalysisConfVoList", method = RequestMethod.POST)
	@ApiOperation("获取方案分类项目树形数据")
	public Res<?> getBriefAnalysisConfVoList(@RequestBody BriefAnalysisQueryDto queryDto) {
		Res<List<BriefAnalysisConfVo>> res = new Res<List<BriefAnalysisConfVo>>();
		List<BriefAnalysisConfVo> list = briefAnalysisService.getBriefAnalysisConfVoList(queryDto);
		res.setResult(list);
		return res;
	}
	
	
	/**
	 *	保存简要分析设置数据
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveBriefAnalysisConfData", method = RequestMethod.POST)
	@ApiOperation("保存简要分析设置数据")
	public Res<?> saveBriefAnalysisConfData(@RequestBody BriefAnalysisSaveDto saveDto) {
		return Res.OK(briefAnalysisService.saveBriefAnalysisConfData(saveDto));
	}



	/**
	 *	查询措施
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/queryMeasureList", method = RequestMethod.POST)
	@ApiOperation("查询措施")
	public Res<?> queryMeasureList(@RequestBody BriefAnalysisQueryDto dto) {
		return Res.OK(briefAnalysisService.queryMeasureList(dto.getAnalysisId()));
	}
	/**
	 *	查询措施
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/saveMeasureList", method = RequestMethod.POST)
	@ApiOperation("保存措施")
	public Res<?> saveMeasureList(@RequestBody List<BriefAnalysisMeasureConf> list) {
		return Res.OK(briefAnalysisService.saveMeasureList(list));
	}
	/**
	 *	查询措施
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/deleteMeasureByIdList", method = RequestMethod.POST)
	@ApiOperation("删除措施")
	public Res<?> deleteMeasureByIdList(@RequestBody List<String> idLIst) {
		return Res.OK(briefAnalysisService.deleteMeasureByIdList(idLIst));
	}

	
}
