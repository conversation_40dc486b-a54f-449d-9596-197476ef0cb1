package com.yunhesoft.leanCosting.unitConf.entity.vo;

import com.yunhesoft.system.kernel.utils.excel.ExcelExt;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class CostuintExcelVo {

	
	 /** 名称 */
	@Excel(name = "名称", width = 30, orderNum = "1")
	@ExcelExt(align = "left") // 导出列居左对齐
    private String name;
    
    /** 单元类型 */
	 @Excel(name = "单元类型", width = 30, orderNum = "1")
    @ExcelExt(align = "left") // 导出列居左对齐
    private String unittypeName;
    
    /** 维护机构 */
    @Excel(name = "维护机构", width = 30, orderNum = "1")
    @ExcelExt(align = "left") // 导出列居左对齐
    private String orgName;
    
    /** 注释 */
    @Excel(name = "注释", width = 30, orderNum = "1")
    @ExcelExt(align = "left") // 导出列居左对齐
    private String memo;
}
