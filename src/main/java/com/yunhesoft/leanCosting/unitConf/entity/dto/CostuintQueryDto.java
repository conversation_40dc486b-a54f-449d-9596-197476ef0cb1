package com.yunhesoft.leanCosting.unitConf.entity.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CostuintQueryDto {

	private String id;
	
	private List<String> ids;

	private List<String> unittypeIds;
	
	/** 父对象ID */
    private String pid;
    
    /** 名称 */
    private String name;
    
    /** 单元类型ID */
    private String unittype;
    
    /** 生效日期 */
	private String begintime;
	
	/**维护机构*/
	private List<String> orgIds;
	
	private String tenantId;//租户ID  如果没有查询当前租户

	/** 用于移动端录入 */
    private Integer mobileInput;
	
	//TODO:2024-02-27添加
	/** 生产活动 */
    private Integer productive;
    
    @ApiModelProperty(value = "分析模型")
    private String analysisTemplate;
    
    private Integer productiveType;
    
    /** 调度台体现 */
    private Integer dispatchDesk;
    
    /** 使用核算 */
    private Integer useAccounting;
    
	/** 按时录入 */
	private Integer isOntime;
    
    /** 设备ID */
    private List<String> deviceIds;
	
	
	private String unitid;
	
	private String startTime;
	
	private String endTime;
	
	private boolean checked;
	
}
