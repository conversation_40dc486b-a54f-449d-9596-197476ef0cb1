package com.yunhesoft.leanCosting.unitConf.entity.dto;


import java.util.List;

import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampledotVo;

import lombok.Data;

@Data
public class paramDto {

	/** 操作类型 del save */
	private String op;
	
	/** 节点类型 */
	private String type;
	
	/** 版本日期 */
	private String version;
	
	/** 核算单元CODE */
	private String unitcode;
	
	/** ID */
    private String id;
    
    /** ID */
    private String pid;
	
    /** 名称 */
    private String name;
    
    /** 采集类型：1、成本仪表；2、控制指标；3、lims指标；0、无； */
    private String ctype;
    
    /** 来源类型：1、influxdb；2、数据源；3、手工填写； */
	private String sourceype;
    
    /** 项目对象 */
    private CostitemVo item;
    
    /** 仪表采集点对象 */
    private List<SampledotVo> dotList;
    
    /** 核算单元编码（被复制的） */
	private String unitcode_copy;
	
	/** 用途：控制指标是用于总平稳率计算，LIMS指标是用于总合格率计算，默认勾选 */
	private Integer useTo;
	
	/** 仪表位号 */
	private String tagnumber;
	
	/** 分类类型 */
	private String classType;
	
	/** 分类描述 */
	private String memo;
	
	//台账数据源参数部分
    private Integer mode;		//模式 1核算单元 2机构
//    private String unitCode;	//核算单元代码
    private String useStatus;	//使用情况 1显示 2不显示
    private String tagName;		//仪表名称
    /** 数据源别名 */
    private String tdsAlias;
    /** 配置编码 */
    private String confCode;
    /** 显示 */
    private String showMark;
    /** 机构CODE */
	private String orgCode;
	/** 数据来源 */
	private String datasource;
	/** 外部数据采集点id */
	private String outSysDotId;
	/** 是否用于记事 */
	private Integer isUseToRecordEvent;
	/** 数据是否回写到influxdb */
	private Integer isWriteBackInfluxdb;
	
	private List<String> list; //数据ID列表
		
}
