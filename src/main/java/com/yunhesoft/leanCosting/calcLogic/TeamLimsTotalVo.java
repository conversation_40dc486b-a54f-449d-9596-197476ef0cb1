package com.yunhesoft.leanCosting.calcLogic;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TeamLimsTotalVo {

	@ApiModelProperty(name = "核算对象id")
	private String unitId;

	@ApiModelProperty(name = "机构代码，班组id")
	private String orgCode;

	@ApiModelProperty(name = "采样均值")
	private Double fetchValue;

	@ApiModelProperty(name = "总点数")
	private int pointCount;

	@ApiModelProperty(name = "超限点数")
	private int overCount;

	@ApiModelProperty(name = "公平化点数")
	private int delCount;

	@ApiModelProperty(name = "平稳率")
	private Double rate;
}
