package com.yunhesoft.leanCosting.calcLogic;

import lombok.Data;

@Data
public class FetchRealTimeDataVo {

	/** 项目ID */
	private String itemId;

	/** 项目名称 */
	private String itemName;

	/** 仪表ID */
	private String instrumentId;

	/** 仪表名称 */
	private String instrumentName;

	/** 实时仪表 */
	private String tagNumber;

	/** 仪表类型 */
	private Integer yblx;

	/** 实时数据库类型： 1、influxdb；2、数据源；3、手工填写 */
	private String sjly;

	/** 采集值对应的时间 */
	private String time;

	/** 采集值 */
	private String value;
	

	/** 上一次的采集值，仅是为了特殊要求 */
	private String lastValue;

}
