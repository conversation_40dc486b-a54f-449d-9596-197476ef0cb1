package com.yunhesoft.leanCosting.calcLogic;

import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInstrumentData;

public interface ICalcTeamProjectLogic {

	/**
	 * @category 计算批次数据
	 * @param reportinfo
	 * @return
	 */
	public String calcPCData(TeamReportInputDto reportinfo);

	/**
	 * @category 计算批次仪表表数
	 * @param reportinfo
	 * @return
	 */
	public List<CostBatchInstrumentData> calcYbbs(TeamReportInputDto reportinfo);

	/**
	 * @category 计算班组数据
	 * @param reportinfo
	 * @return
	 */
	public String calcTeamDayProgramReport(TeamReportInputDto reportinfo);

	/**
	 * @category 自动计算装置日报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcDeviceDayReportAuto(TeamReportInputDto reportinfo);

	/**
	 * @category 计算装置日报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcDeviceDayReport(TeamReportInputDto reportinfo);

	/**
	 * @category 自动计算装置月报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcDeviceMonthReportAuto(TeamReportInputDto reportinfo);

	/**
	 * @category 计算装置月报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcDeviceMonthReport(TeamReportInputDto reportinfo);

	/**
	 * @category 自动计算班组月报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcTeamMonthReportAuto(TeamReportInputDto reportinfo);

	/**
	 * @category 计算班组月报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcTeamMonthReport(TeamReportInputDto reportinfo);

	/**
	 * @category 自动计算周报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcWeekReportAuto(TeamReportInputDto reportinfo);

	/**
	 * @category 计算周报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcWeekReport(TeamReportInputDto reportinfo);

	/**
	 * @category 自动计算季报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcQuarterReportAuto(TeamReportInputDto reportinfo);

	/**
	 * @category 计算季报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcQuarterReport(TeamReportInputDto reportinfo);

	/**
	 * @category 自动计算年报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcYearReportAuto(TeamReportInputDto reportinfo);

	/**
	 * @category 计算年报数据
	 * @param reportinfo
	 * @return
	 */
	public String calcYearReport(TeamReportInputDto reportinfo);

	/**
	 * @category 计算分时段报表
	 * @param reportinfo
	 * @return
	 */
	public String calcPeriodReport(TeamReportInputDto reportinfo);

	/**
	 * @category 计算批次数据
	 * @param reportinfo
	 * @return
	 */
	public String calcBatchProgramData(TeamReportInputDto reportinfo);

}
