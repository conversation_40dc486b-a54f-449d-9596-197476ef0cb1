package com.yunhesoft.leanCosting.calcLogic;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchOnDuty;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamParamData;
import com.yunhesoft.leanCosting.priceManage.entity.po.ItemPriceChangeTask;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

/**
 * 交接班与批次计算的数据库操作
 * 
 * <AUTHOR>
 *
 */
@Service
public class OPShiftDataImpl implements IOPShiftData {

	@Autowired
	EntityService entityService;

	@Override
	public boolean deleteTeamDayCalcInfo(String id) {
		Where where = Where.create();
		where.eq(CostTeamInfo::getId, id);
		int bs = entityService.delete(CostTeamInfo.class, where);
		if (bs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean insertTeamDayCalcInfo(CostTeamInfo data) {
		boolean rtn = false;
		int bs = entityService.insert(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean insertTeamInfo(List<CostTeamInfo> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean deleteTeamDayItemData(String id) {
		Where where = Where.create();
		where.eq(CostTeamItemData::getPid, id);
		int bs = entityService.delete(CostTeamItemData.class, where);
		if (bs < 0) {

			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean insertTeamDayItemData(List<CostTeamItemData> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean deleteTeamDayYbData(String id) {
		Where where = Where.create();
		where.eq(CostTeamInstrumentData::getPid, id);
		int bs = entityService.delete(CostTeamInstrumentData.class, where);
		if (bs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean insertTeamDayYbData(List<CostTeamInstrumentData> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean deleteTeamDayParamData(String id) {
		Where where = Where.create();
		where.eq(CostTeamParamData::getPid, id);
		int bs = entityService.delete(CostTeamParamData.class, where);
		if (bs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean insertTeamDayParamData(List<CostTeamParamData> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean deleteSummaryInfo(String id) {
		Where where = Where.create();
		where.eq(CostSummaryInfo::getId, id);
		int bs = entityService.delete(CostSummaryInfo.class, where);
		if (bs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean deleteSummaryItemData(String pid) {
		Where where = Where.create();
		where.eq(CostSummaryItemData::getPid, pid);
		int rs = entityService.delete(CostSummaryItemData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean deleteSummaryParamData(String pid) {
		Where where = Where.create();
		where.eq(CostSummaryParamData::getPid, pid);
		int rs = entityService.delete(CostSummaryParamData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean insertSummaryCalcInfo(CostSummaryInfo data) {
		boolean rtn = false;
		int bs = entityService.insert(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean insertSummaryItemData(List<CostSummaryItemData> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean insertSummaryParamData(List<CostSummaryParamData> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public List<CostBatchOnDuty> getBatchOnDuty(String unitid, String shiftid, String teamid, String tbrq) {
		Where where = Where.create();
		where.eq(CostBatchOnDuty::getUnitId, unitid);
		where.eq(CostBatchOnDuty::getShiftId, shiftid);
		where.eq(CostBatchOnDuty::getTeamId, teamid);
		where.eq(CostBatchOnDuty::getWriteDay, tbrq);
		Order order = Order.create();
		order.orderByAsc(CostBatchOnDuty::getBeginTime);
		return entityService.queryList(CostBatchOnDuty.class, where, order);
	}

	@Override
	public List<CostTeamInfo> getTeamInfo(String unitid, String shiftid, String teamid, String tbrq) {
		Where where = Where.create();
		where.eq(CostTeamInfo::getUnitId, unitid);
		where.eq(CostTeamInfo::getShiftId, shiftid);
		where.eq(CostTeamInfo::getTeamId, teamid);
		where.eq(CostTeamInfo::getWriteDay, tbrq);
		return entityService.queryList(CostTeamInfo.class, where, null);
	}

	@Override
	public List<CostTeamInfo> getReportInfo(String unitid, String tbrq) {
		Where where = Where.create();
		where.eq(CostTeamInfo::getUnitId, unitid);
		where.eq(CostTeamInfo::getWriteDay, tbrq);
		return entityService.queryList(CostTeamInfo.class, where, null);
	}

	@Override
	public boolean deleteTeamInfo(String unitid, String shiftid, String teamid, String tbrq) {
		Where where = Where.create();
		where.eq(CostTeamInfo::getUnitId, unitid);
		where.eq(CostTeamInfo::getWriteDay, tbrq);
		where.eq(CostTeamInfo::getTeamId, teamid);
		where.eq(CostTeamInfo::getShiftId, shiftid);
		int rs = entityService.delete(CostTeamInfo.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean deleteTeamInstrumentData(String unitid, String shiftid, String teamid, String tbrq) {
		Where where = Where.create();
		where.eq(CostTeamInstrumentData::getUnitId, unitid);
		where.eq(CostTeamInstrumentData::getWriteDay, tbrq);
		where.eq(CostTeamInstrumentData::getTeamId, teamid);
		where.eq(CostTeamInstrumentData::getShiftId, shiftid);
		int rs = entityService.delete(CostTeamInstrumentData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	};

	@Override
	public boolean deleteTeamItemData(String unitid, String shiftid, String teamid, String tbrq) {
		Where where = Where.create();
		where.eq(CostTeamItemData::getUnitId, unitid);
		where.eq(CostTeamItemData::getWriteDay, tbrq);
		where.eq(CostTeamItemData::getTeamId, teamid);
		where.eq(CostTeamItemData::getShiftId, shiftid);
		int rs = entityService.delete(CostTeamItemData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean deleteTeamParamData(String unitid, String shiftid, String teamid, String tbrq) {
		Where where = Where.create();
		where.eq(CostTeamParamData::getUnitId, unitid);
		where.eq(CostTeamParamData::getWriteDay, tbrq);
		where.eq(CostTeamParamData::getTeamId, teamid);
		where.eq(CostTeamParamData::getShiftId, shiftid);
		int rs = entityService.delete(CostTeamParamData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean deleteOnDuty(String id) {
		Where where = Where.create();
		where.eq(CostBatchOnDuty::getId, id);
		int rs = entityService.delete(CostBatchOnDuty.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean deleteBatchCalcInfo(String id) {
		Where where = Where.create();
		where.eq(CostBatchInfo::getId, id);
		int rs = entityService.delete(CostBatchInfo.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean deleteInstrumentData(String pid) {
		Where where = Where.create();
		where.eq(CostBatchInstrumentData::getPid, pid);
		int rs = entityService.delete(CostBatchInstrumentData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean deleteItemData(String pid) {
		Where where = Where.create();
		where.eq(CostBatchItemData::getPid, pid);
		int rs = entityService.delete(CostBatchItemData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean deleteParamData(String pid) {
		Where where = Where.create();
		where.eq(CostBatchParamData::getPid, pid);
		int rs = entityService.delete(CostBatchParamData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public List<CostBatchInfo> getBatchInfo(String unitid, String kssj, String jzsj) {
		Where where = Where.create();
		where.eq(CostBatchInfo::getUnitId, unitid);
		where.ge(CostBatchInfo::getBeginTime, kssj);
		where.le(CostBatchInfo::getEndTime, jzsj);
		return entityService.queryList(CostBatchInfo.class, where, null);
	}

	@Override
	public boolean insertDutyInfo(CostBatchOnDuty data) {
		boolean rtn = false;
		int bs = entityService.insert(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean insertBatchCalcInfo(CostBatchInfo data) {
		boolean rtn = false;
		int bs = entityService.insert(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean insertBatchInfo(List<CostBatchInfo> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean insertInstrumentData(List<CostBatchInstrumentData> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean insertItemData(List<CostBatchItemData> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean insertParamData(List<CostBatchParamData> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public boolean updateBatchYbbs(List<CostBatchInstrumentData> ybbs) {
		int rs = entityService.updateByIdBatch(ybbs);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean delSumInfo(String unitid, String reportno, String faid, String teamid) {
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, unitid);
		where.eq(CostSummaryInfo::getReportNo, reportno);
		where.eq(CostSummaryInfo::getProgramId, faid);
		if (reportno.length() != 10) {
			where.eq(CostSummaryInfo::getTeamId, teamid);
		}
		int rs = entityService.delete(CostSummaryInfo.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean delSumItem(String unitid, String reportno, String faid, String teamid) {
		Where where = Where.create();
		where.eq(CostSummaryItemData::getUnitId, unitid);
		where.eq(CostSummaryItemData::getWriteDay, reportno);
		where.eq(CostSummaryItemData::getProgramId, faid);
		if (reportno.length() != 10) {
			where.eq(CostSummaryItemData::getTeamId, teamid);
		}
		int rs = entityService.delete(CostSummaryItemData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public boolean delSumParam(String unitid, String reportno, String faid, String teamid) {
		Where where = Where.create();
		where.eq(CostSummaryParamData::getUnitId, unitid);
		where.eq(CostSummaryParamData::getWriteDay, reportno);
		where.eq(CostSummaryParamData::getProgramId, faid);
		if (reportno.length() != 10) {
			where.eq(CostSummaryParamData::getTeamId, teamid);
		}
		int rs = entityService.delete(CostSummaryParamData.class, where);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

	@Override
	public List<ItemPriceChangeTask> getPriceCalcTask(String TENANT_ID) {
		Where where = Where.create();
		where.eq(ItemPriceChangeTask::getCalcStatus, 0);
		where.or();
		where.eq(ItemPriceChangeTask::getCalcStatus, 1);
		Order order = Order.create();
		order.orderByAsc(ItemPriceChangeTask::getBeginTime);
		if (StringUtils.isEmpty(TENANT_ID) || "0".equals(TENANT_ID)) {
			return entityService.queryList(ItemPriceChangeTask.class, where, order);
		} else {
			return entityService.rawQueryListByWhereWithTenant(TENANT_ID, ItemPriceChangeTask.class, where, order);
		}
	}

	@Override
	public Boolean insertPriceCalcTask(List<ItemPriceChangeTask> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	@Override
	public Boolean updatePriceCalcTask(List<ItemPriceChangeTask> data) {
		int rs = entityService.updateByIdBatch(data);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

}
