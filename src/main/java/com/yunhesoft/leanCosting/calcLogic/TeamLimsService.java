package com.yunhesoft.leanCosting.calcLogic;

import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.leanCosting.samplePlan.entity.po.ProdLimsResult;
import com.yunhesoft.leanCosting.samplePlan.entity.po.TeamLimsResult;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class TeamLimsService {

	@Autowired
	private EntityService dao;

	@Autowired
	private UnitItemInfoService uisrv;

	public void test() {
		Pagination<ProdLimsResult> page = new Pagination<>();
		page.setPage(1);
		page.setLimit(50);
		List<ProdLimsResult> prodLimsResults = dao.queryData(ProdLimsResult.class, Where.create(), null, page);
		this.addLimsDataToTeamResult(prodLimsResults);
	}

	/**
	 * 添加lims采集结果到班组结果表中
	 * 
	 * @param limsList lims新增采集结果
	 * @return
	 */
	public String addLimsDataToTeamResult(List<ProdLimsResult> limsList) {

		if (ObjectUtils.isEmpty(limsList)) {
			return "传入数据为空";
		}

		List<Costunitsampledot> dotList = dao.queryData(Costunitsampledot.class,
				Where.create().eq(Costunitsampledot::getTmused, 1).eq(Costunitsampledot::getCtype, "3"), null, null);
		if (ObjectUtils.isEmpty(dotList)) {
			return null;
		}

		Map<String, List<Costunitsampledot>> dotGroup = new HashMap<>();
		for (Costunitsampledot dot : dotList) {
			String processUnitName = Optional.ofNullable(dot.getProcessUnitName()).orElse("");
			String productName = Optional.ofNullable(dot.getProductName()).orElse("");
			String samplingPoint = Optional.ofNullable(dot.getSamplingPoint()).orElse("");
			String analysisName = Optional.ofNullable(dot.getAnalysisName()).orElse("");
			String analysisSubName = Optional.ofNullable(dot.getAnalysisSubName()).orElse("");
			if (StringUtils.isAllEmpty(processUnitName, productName, samplingPoint, analysisName, analysisSubName)) {
				// 全空跳过
				continue;
			}
			String key;
			if ("".equals(productName)) {
				key = processUnitName + "_" + productName + "_" + samplingPoint + "_" + analysisName + "_"
						+ analysisSubName;
			} else {
				key = processUnitName + "_" + samplingPoint + "_" + analysisName + "_" + analysisSubName;
			}
			dotGroup.computeIfAbsent(key, k -> new ArrayList<>()).add(dot);
		}

		List<TeamLimsResult> insertList = new ArrayList<>();

		Map<String, ShiftForeignVo> shiftMap = new HashMap<>();

		for (ProdLimsResult lims : limsList) {
			String processunit = Optional.ofNullable(lims.getProcessunit()).orElse("");
			String productName = Optional.ofNullable(lims.getProductName()).orElse("");
			String samplingPoint = Optional.ofNullable(lims.getSamplingPoint()).orElse("");
			String analysisName = Optional.ofNullable(lims.getAnalysisName()).orElse("");
			String itemName = Optional.ofNullable(lims.getItemName()).orElse("");
			String hyid = lims.getId();
			String hyjg = lims.getResultInSpec();
			if (hyjg == null) {
				hyjg = "t";
			}
			Integer oc = 0;
			if ("f".equalsIgnoreCase(hyjg)) {
				oc = 1;
			}
			if (StringUtils.isAllEmpty(processunit, productName, samplingPoint, analysisName, itemName)) {
				// 全空跳过
				continue;
			}
			String key = processunit + "_" + productName + "_" + samplingPoint + "_" + analysisName + "_" + itemName;
			List<Costunitsampledot> dots = dotGroup.get(key);
			if (ObjectUtils.isEmpty(dots)) {
				// 5个对照不存在对应采集点，再用4个对照
				key = processunit + "_" + samplingPoint + "_" + analysisName + "_" + itemName;
				dots = dotGroup.get(key);
				if (ObjectUtils.isEmpty(dots)) {
					continue;// 都没有，跳过
				}
			}
			String sampledDate = lims.getSampledDate();
			for (Costunitsampledot dot : dots) {
				String unitid = dot.getUnitid();
				ShiftForeignVo shiftInfo = shiftMap.computeIfAbsent(unitid + "_" + sampledDate,
						k -> uisrv.getShiftInfo(unitid, sampledDate));
				if (shiftInfo == null) {
					// 找不到班次跳过
					continue;
				}

				TeamLimsResult r = new TeamLimsResult();
				r.setId(TMUID.getUID());
				r.setUnitId(unitid);
				r.setTeamId(shiftInfo.getOrgCode());
				r.setShiftId(shiftInfo.getShiftClassCode());
				r.setDotId(dot.getId());
				r.setSampleId(hyid);
				r.setPointCount(1);
				r.setOverCount(oc);
				r.setSampleDate(sampledDate);
				r.setFormattedEntry(lims.getFormattedEntry());
				r.setResultInSpec(hyjg);
				r.setShiftBeginTime(shiftInfo.getSbsj());
				r.setShiftEndTime(shiftInfo.getXbsj());
				r.setShiftDay(shiftInfo.getTbrq());

				insertList.add(r);
			}

		}

		int res = 1;
		if (ObjectUtils.isNotEmpty(insertList)) {
			res = dao.insertBatch(insertList);
		}

		return res <= 0 ? "保存失败" : null;
	}

	/**
	 * 获取班组lims统计结果
	 * 
	 * @param unitId
	 * @param startDt
	 * @param endDt
	 * @return
	 */
	public HashMap<String, List<TeamLimsTotalVo>> getTeamLimsTotal(String unitId, String startDt, String endDt,
			HashMap<String, Costunitsampledot> dotm) {
		if (StringUtils.isAnyEmpty(unitId, startDt, endDt)) {
			return null;
		}
		Where where = Where.create();
		where.eq(TeamLimsResult::getUnitId, unitId);
		where.between(TeamLimsResult::getShiftDay, startDt, endDt);
		List<TeamLimsResult> list = dao.queryData(TeamLimsResult.class, where, null, null);
		if (ObjectUtils.isEmpty(list)) {
			return null;
		}
		Double cjz;
		Integer zhgl, pointCount, overCount, delCount;
		String ybid, teamId, sval;
		HashMap<String, List<TeamLimsTotalVo>> rtn = new HashMap<String, List<TeamLimsTotalVo>>();
		HashMap<String, HashMap<String, TeamLimsTotalVo>> valm = new HashMap<String, HashMap<String, TeamLimsTotalVo>>();
		LinkedHashMap<String, TeamLimsTotalVo> map = new LinkedHashMap<>();
		for (TeamLimsResult team : list) {
			ybid = team.getDotId();
			zhgl = 0;
			if (dotm.containsKey(ybid)) {
				Costunitsampledot di = dotm.get(ybid);
				if (di != null) {
					zhgl = di.getUseTo();
					if (zhgl == null) {
						zhgl = 1;
					}
				}
			}
			teamId = team.getTeamId();
			pointCount = team.getPointCount();
			overCount = team.getOverCount();
			delCount = team.getDelCount();
			sval = team.getFormattedEntry();
			if (Coms.judgeTrimDouble(sval)) {
				cjz = Double.parseDouble(sval);
			} else {
				cjz = 0.0;
			}
			if (valm.containsKey(ybid)) {
				HashMap<String, TeamLimsTotalVo> mm = valm.get(ybid);
				if (mm != null) {
					if (mm.containsKey(teamId)) {
						TeamLimsTotalVo vo = mm.get(teamId);
						vo.setPointCount(vo.getPointCount() + pointCount);
						vo.setOverCount(vo.getOverCount() + overCount);
						vo.setDelCount(vo.getDelCount() + delCount);
						vo.setFetchValue(vo.getFetchValue() + cjz);
					} else {
						TeamLimsTotalVo vo = new TeamLimsTotalVo();
						vo.setUnitId(unitId);
						vo.setOrgCode(teamId);
						vo.setPointCount(pointCount);
						vo.setOverCount(overCount);
						vo.setDelCount(delCount);
						vo.setFetchValue(cjz);
						mm.put(teamId, vo);
					}
				}
			} else {
				TeamLimsTotalVo vo = new TeamLimsTotalVo();
				vo.setUnitId(unitId);
				vo.setOrgCode(teamId);
				vo.setPointCount(pointCount);
				vo.setOverCount(overCount);
				vo.setDelCount(delCount);
				vo.setFetchValue(cjz);
				HashMap<String, TeamLimsTotalVo> mm = new HashMap<String, TeamLimsTotalVo>();
				mm.put(teamId, vo);
				valm.put(ybid, mm);
			}
			if (zhgl == 1) {
				// 只要用于总合格率计算的指标
				TeamLimsTotalVo vo = map.computeIfAbsent(teamId, k -> {
					TeamLimsTotalVo bean = new TeamLimsTotalVo();
					bean.setUnitId(team.getUnitId());
					bean.setOrgCode(team.getTeamId());
					return bean;
				});
				vo.setPointCount(vo.getPointCount() + pointCount);
				vo.setOverCount(vo.getOverCount() + overCount);
				vo.setDelCount(vo.getDelCount() + delCount);
			}
		}
		// 普通采集点
		for (Map.Entry<String, HashMap<String, TeamLimsTotalVo>> xx : valm.entrySet()) {
			ybid = xx.getKey();
			HashMap<String, TeamLimsTotalVo> mm = xx.getValue();
			if (mm != null) {
				for (Map.Entry<String, TeamLimsTotalVo> yy : mm.entrySet()) {
					TeamLimsTotalVo vo = yy.getValue();
					if (rtn.containsKey(ybid)) {
						rtn.get(ybid).add(vo);
					} else {
						List<TeamLimsTotalVo> rl = new ArrayList<>();
						rl.add(vo);
						rtn.put(ybid, rl);
					}
				}
			}
		}
		// 总合格率
		List<TeamLimsTotalVo> result = new ArrayList<>();
		for (Map.Entry<String, TeamLimsTotalVo> entry : map.entrySet()) {
			TeamLimsTotalVo vo = entry.getValue();
			pointCount = vo.getPointCount();
			overCount = vo.getOverCount();
			delCount = vo.getDelCount();
			double rate;
			int cxd = overCount - delCount;
			// if(总点数=0,1,1-(超点，抛超点)/总点数)
			if (pointCount == 0) {
				rate = 1.0;
			} else {
				if (cxd <= 0) {
					rate = 1.0;
				} else {
					rate = 1.0 - cxd / (pointCount+0.0);
				}
			}
			vo.setOverCount(cxd);
			vo.setRate(rate);
			result.add(vo);
		}
		rtn.put("TotalStableQualificationRate", result);
		return rtn;
	}
}
