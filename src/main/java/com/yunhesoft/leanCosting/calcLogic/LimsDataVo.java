package com.yunhesoft.leanCosting.calcLogic;

import lombok.Data;

@Data
public class LimsDataVo {

	/** 装置 */
	private String processUnit;

	/** 产品 */
	private String productName;

	/** 采样点 */
	private String samplingPoint;

	/** 分析项 */
	private String analysisName;

	/** 分析分项 */
	private String itemName;

	/** 仪表ID */
	private String dotId;

	/** 仪表名称 */
	private String instrumentName;

	/** 实时仪表 */
	private String tagNumber;

	/** 采集值对应的时间 */
	private String time;

	/** 采集值 */
	private String value;

}
