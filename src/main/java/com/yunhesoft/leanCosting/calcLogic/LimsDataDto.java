package com.yunhesoft.leanCosting.calcLogic;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import com.yunhesoft.leanCosting.unitConf.entity.po.CostStipulateTime;

import lombok.Data;

@Data
public class LimsDataDto {

	/** 核算单元ID */
	private String unitId;

	/** 方案ID */
	private String programId;

	/** 开始时间 */
	private String kssj;

	/** 截止时间 */
	private String jzsj;

	/** 填表日期 */
	private String tbrq;

	/** 班次ID */
	private String shiftId;

	/** 班次名称 */
	private String shiftName;

	/** 班组ID */
	private String teamId;

	/** 班组名称 */
	private String teamName;

	/** 待采集的LIMS仪表 */
	private List<LimsDataVo> limsyb;

	/** 核算对象的提前时间 */
	private Integer tqsj;

	/** 检索过规定时间的配置 */
	private Integer fixTime;

	/** 错误信息 */
	private String errInfo;

	/** 租户ID */
	private String TENANT_ID;

	/** 规定时间的配置 */
	private HashMap<String, HashMap<String, CostStipulateTime>> fixTimeConfig = new HashMap<String, HashMap<String, CostStipulateTime>>();

	/** 仪表的规定时间 */
	private HashMap<String, Date> iTimePoint = new HashMap<String, Date>();

	/** 采集仪表信息MAP */
	private HashMap<String, FetchRealTimeDataVo> pm = new HashMap<String, FetchRealTimeDataVo>();

	/** 采集数据MAP */
	private HashMap<String, List<FetchRealTimeDataVo>> dm = new HashMap<String, List<FetchRealTimeDataVo>>();

	/** 采集仪表信息List */
	private List<FetchRealTimeDataVo> pl = new ArrayList<FetchRealTimeDataVo>();

}
