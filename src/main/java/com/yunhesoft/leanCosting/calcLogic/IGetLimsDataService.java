package com.yunhesoft.leanCosting.calcLogic;

import java.util.HashMap;
import java.util.List;

import com.yunhesoft.leanCosting.deliverGoods.entity.po.ItemStockDayLedger;
import com.yunhesoft.leanCosting.deliverGoods.entity.po.ShiftItemEntryQty;
import com.yunhesoft.leanCosting.samplePlan.entity.po.ProdLimsResult;

public interface IGetLimsDataService {

	/**
	 * @category 保存LIMS数据
	 * @param data
	 * @return
	 */
	public String saveLimsData(List<ProdLimsResult> updata, List<ProdLimsResult> indata);

	/**
	 * @category 获取LIMS数据
	 * @param dto
	 * @return
	 */
	public HashMap<String, List<LimsDataVo>> getLimsData(LimsDataDto dto);

	/**
	 * @category 保存仓储库存量同步数据
	 * @param indata
	 * @return
	 */
	public String saveItemStockDayLedgerData(List<ItemStockDayLedger> indata);

	/**
	 * @category 保存仓储班次入库量
	 * @param indata
	 * @return
	 */
	public String saveShiftItemEntryQtyData(List<ShiftItemEntryQty> indata);

}
