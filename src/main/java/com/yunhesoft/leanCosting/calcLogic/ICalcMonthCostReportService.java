package com.yunhesoft.leanCosting.calcLogic;

import com.yunhesoft.leanCosting.costReport.entity.dto.AutoCreateMonthParamDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.AutoCreateShiftTaskDto;

public interface ICalcMonthCostReportService {

	/**
	 * @category 调度调用的班组月汇总表自动计算
	 * @return
	 */
	public String autoTeamCalc(AutoCreateMonthParamDto dto);

	/**
	 * @category 调度调用的装置月汇总表自动计算
	 * @return
	 */
	public String autoDeviceCalc(AutoCreateMonthParamDto dto);

	/**
	 * @category 生成交接班数据（以批次的形式），仅是一个班次的交接班
	 * @param dto
	 * @return
	 */
	public String autoCreateShiftData(AutoCreateShiftTaskDto dto);

	/**
	 * @category 交接班计算任务：只执行交接班内自动生成计算任务
	 * @param dto
	 * @return
	 */
	public String calcCreateShiftTask(AutoCreateShiftTaskDto dto);

	/**
	 * @category 价格导入和费用导入的计算任务
	 * @param dto
	 * @return
	 */
	public String calcPriceChangeTask(AutoCreateShiftTaskDto dto);

	/**
	 * @category 删除错误的costbatchinstrumentdata数据
	 * @return
	 */
	public String delBatchErrData();

}
