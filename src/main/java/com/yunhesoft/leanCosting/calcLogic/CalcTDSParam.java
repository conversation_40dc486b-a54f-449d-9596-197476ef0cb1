package com.yunhesoft.leanCosting.calcLogic;

/**
 * 数据源解析
 */

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.script.ScriptException;

import org.hibernate.HibernateException;

import com.yunhesoft.core.common.script.ScriptEngineUtils;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.model.TInPara;
import com.yunhesoft.system.tools.eval.CompiledScriptEngine;
import com.yunhesoft.system.tools.eval.model.CustomFun;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class CalcTDSParam {
	/** 预编译脚本解析引擎 */
	private static ConcurrentHashMap<String, CompiledScriptEngine> evalMap = new ConcurrentHashMap<String, CompiledScriptEngine>();

	// 变量
	private String Month;// 月份
	private String Orgid;// 机构代码
	private String Kssj;
	private String Jzsj;
	// List
	private List<String> dsList = new ArrayList<String>();// 已经检索过的数据源，用于确认数据源是否已初始化
	private List<String> errDs = new ArrayList<String>();

	// HashMap
	private HashMap<String, String> TIDSM = new HashMap<String, String>();
	private HashMap<String, IDataSource> ATDSM = new HashMap<String, IDataSource>();// 已经检索过的数据源，用于确认数据源是否已初始化
	private HashMap<String, IDataSource> mapTds = new HashMap<String, IDataSource>();
	private HashMap<String, IDataSource> mapTdsData = new HashMap<String, IDataSource>();
	// 对象
	private PublicMethods pm = new PublicMethods();

	public CalcTDSParam(String orgid, String yf, String kssj, String jzsj) {
		this.Orgid = orgid;
		this.Month = yf;
		this.Kssj = kssj;
		this.Jzsj = jzsj;
	}

	/**
	 * @category 获取指定装置下指定的数据源结果信息
	 * @throws HibernateException
	 * @throws Exception
	 */
	private String getDs(String tdsname, TdsFormulaParamDTO p) throws HibernateException, Exception {// 获取所有统计公式包含数据源设置的记录
		String info = "", pn, lkey;
		String unitid = p.getUnitId();
		if (unitid == null) {
			unitid = "";
		}
		IDataSource ids = null;
		StringBuffer sb = new StringBuffer();
		if (this.mapTds.containsKey(tdsname)) {
			ids = this.mapTds.get(tdsname);
		} else {
			TDataSourceManager tdsm = new TDataSourceManager();
			ids = tdsm.getDataSource(tdsname);
			this.mapTds.put(tdsname, ids);
		}

		if (ids != null) {
			// 无论是否自动加载，替换输入参数
			List<TInPara> paralist = ids.getInParaList();
			sb.append(tdsname);
			for (TInPara para : paralist) {
				pn = para.getParaAlias();
				sb.append(pn);
				sb.append(":");
				if ("yf".equals(para.getParaAlias())) {
					para.setValue(this.Month);
					sb.append(this.Month);
					sb.append(";");
				} else if ("unitid".equals(para.getParaAlias())) {
					para.setValue(unitid);
					sb.append(unitid);
					sb.append(";");
				} else if ("tbrq".equals(para.getParaAlias())) {
					para.setValue(p.getTbrq());
					sb.append(p.getTbrq());
					sb.append(";");
				} else if ("ksrq".equals(para.getParaAlias()) || "kssj".equals(para.getParaAlias())) {
					para.setValue(this.Kssj);
					sb.append(this.Kssj);
					sb.append(";");
				} else if ("jzrq".equals(para.getParaAlias()) || "jzsj".equals(para.getParaAlias())) {
					para.setValue(this.Jzsj);
					sb.append(this.Jzsj);
					sb.append(";");
				} else if ("orgid".equals(para.getParaAlias())) {
					// 针对数据源支持车间代码的替换
					para.setValue(this.Orgid);
					sb.append(this.Orgid);
					sb.append(";");
				}
			}
			if (sb != null && sb.length() > 0) {
				lkey = sb.toString();
			} else {
				lkey = (new StringBuffer(unitid)).append(",").append(tdsname).toString();
			}
			try {
				boolean toload = false;
				if (this.TIDSM.containsKey(tdsname)) {// 数据源已加载过
					String ltj = this.TIDSM.get(tdsname);
					if (ltj.equals(lkey)) {
						toload = false;
					} else {
						toload = true;
						this.TIDSM.put(tdsname, lkey);
					}
				} else {// 新的数据源，肯定要加载
					toload = true;
					this.TIDSM.put(tdsname, lkey);
				}
				if (toload) {// 解析对象里数据源需要重新加载
					if (this.ATDSM.containsKey(lkey)) {// 关键条件满足
						this.mapTdsData.put(tdsname, this.ATDSM.get(lkey));
					} else {
						TDataSourceManager tdsm = new TDataSourceManager();
						IDataSource ids1 = tdsm.getDataSource(tdsname);
						ids1.setInPara(paralist);
						ids1.load();
						this.ATDSM.put(lkey, ids1);
						this.mapTdsData.put(tdsname, ids1);
					}
				}
			} catch (Exception e) {
			}
		} else {// 数据源不存在
			errDs.add(tdsname);
		}
		return info;
	}

	/**
	 * @category 检索数据源
	 * @param zzdm
	 * @param gs
	 * @return
	 */
	public void TDSRetrieve(List<String> tdsl, TdsFormulaParamDTO p) {
		for (String ds : tdsl) {
			// 公式含有数据源
			ds = ds.substring(1);
			if (!dsList.contains(ds)) {
				// 未初始化的数据源需要解析
				dsList.add(ds);
				try {
					getDs(ds, p);
				} catch (Exception e) {
					log.error("", e);
				}
			}
		}
	}

	/**
	 * @category 公式脚本解析
	 * @param 替换完变量的数据源公式或四则运算
	 * @param tdsAlias         如果是数据源公式，传入数据源别名
	 * @param data             传入数据源数据
	 * @return
	 * @throws ScriptException
	 */

	private Object eval(String s, String tdsAlias, IDataSource data) throws ScriptException {
		Object obj = null;
		if (s != null && s.trim().length() > 0) {
			s = s.trim();
			obj = ScriptEngineUtils.evalConst(s);// 首先进行常量的计算
			if (obj != null) {
				return obj;
			}
			// 判断是否为数据源解析
			if (tdsAlias == null || "".equals(tdsAlias)) {// 数据源解析
				return "0";
			} else {
				// 数据源解析
				String evalKey = ScriptEngineUtils.getMd5(s);
				CompiledScriptEngine cse = evalMap.get(evalKey);
				if (cse == null) {
					String calScript = ScriptEngineUtils.replaceFunctionWithOutIIF(s);// 计算脚本
					calScript = ScriptEngineUtils.clearScript(calScript);// 去掉 $ 符号
					calScript = CustomFun.getCuntomFunScript() + " " + calScript;
					cse = new CompiledScriptEngine(calScript);
					if (cse.getScriptException() == null) {
						evalMap.put(evalKey, cse);
					} else {// 脚本编译错误
						return null;
					}
				}
				Map<String, Object> paramMap = new HashMap<String, Object>();
				paramMap.put(tdsAlias, data);
				try {
					if (paramMap != null && paramMap.size() > 0) {
						obj = cse.eval(paramMap);
					} else {
						obj = cse.eval();
					}
				} catch (ScriptException e) {
					log.error("", e);
				}
			}
		}
		return obj;
	}

	/*
	 * 替换变量信息
	 */
	private String replaceVar(String str, TdsFormulaParamDTO p) {
		str = str.replaceAll("@unitid", p.getUnitId());// 核算单元ID
		str = str.replaceAll("@unitname", p.getUnitName());// 核算单元名称
		str = str.replaceAll("@orgid", Orgid);// 用户机构ID
		str = str.replaceAll("@xmid", p.getXmid());// 项目ID
		str = str.replaceAll("@xmmc", p.getXmmc());// 项目名称
		str = str.replaceAll("@ybid", p.getYbid());// 仪表ID
		str = str.replaceAll("@ybmc", p.getYbmc());// 仪表名称
		str = str.replace("@bcdm", p.getShiftId());
		str = str.replace("@bzdm", p.getTeamId());
		return str;
	}

	/*
	 * 替换表达式中的数据源公式
	 */
	public String replaceDsFormula(String tdsformula, TdsFormulaParamDTO p) {
		String rtn = "0";
		if (tdsformula.indexOf("$") == -1)// 无数据源内容
			return tdsformula;

		String str = this.replaceVar(tdsformula, p);// 替换@变量;
		String dsstr = Coms.getBl(str, "(?<=\\$)[a-zA-Z_]+[\\w]*(?=\\.)").get(0);
		if (errDs.contains(dsstr)) {// 数据源不存在
			rtn = "0";
		} else {
			if (this.mapTdsData.containsKey(dsstr)) {
				IDataSource data = this.mapTdsData.get(dsstr);
				try {
					rtn = (this.eval(str, dsstr, data) + "").trim();
				} catch (ScriptException e1) {
					rtn = "0";
					log.error("", e1);
				}
				if (rtn != null && rtn.startsWith("s")) {
					// 字符串型的返回值
				} else {
					if ("".equals(rtn)) {// 如果结果为空字符，返回0
						rtn = "0";
					} else if (!Coms.judgeDouble(rtn)) {// 不为空字符且非数值结果，不输出错误日志
						if (Coms.isFind(rtn, "^-?\\d+\\.?\\d+[E][-]?\\d+$")) {// 如果结果为科学计数法，对数据进行格式化
							try {
								rtn = Coms.formatNumber(Double.parseDouble(rtn), "################.################")
										+ "";
							} catch (Exception e) {
								rtn = "0";
							}
						}
					}
					rtn = this.pm.judgeResult(rtn);
				}
			}
		}

		return rtn;// 双引号内容替换
	}

}
