package com.yunhesoft.leanCosting.calcLogic;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.costReport.entity.dto.AutoCreateMonthParamDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.AutoCreateShiftTaskDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchOnDuty;
import com.yunhesoft.leanCosting.costReport.entity.po.ShiftCostCalcTask;
import com.yunhesoft.leanCosting.priceManage.entity.po.ItemPriceChangeTask;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlanStart;
import com.yunhesoft.leanCosting.productSchedu.service.IZzRunStateService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class CalcMonthCostReportServiceImpl implements ICalcMonthCostReportService {

	@Autowired
	private ICalcTeamProjectLogic ictpl;

	@Autowired
	private IAutoShiftCalcLogic iascl;

	@Autowired
	private IGetPeriodForReportService igpfrs;

	@Autowired
	private EntityService entityService;

	@Autowired
	private IShiftService shiftService;;

	@Autowired
	private UnitItemInfoService uiis;

	@Autowired
	private IZzRunStateService runStateService; // 运行状态

	@Autowired
	private IUnitMethodService unitMeth;

	@Autowired
	private IOPShiftData iopsd;

	@Override
	public String autoTeamCalc(AutoCreateMonthParamDto dto) {
		String rtn = "";
		Integer xd = 0;
		String xdyf = dto.getDIFMonth();
		if (Coms.judgeInt(xdyf)) {
			xd = Integer.parseInt(xdyf);
		} else {
			xd = -1;// 默认计算上个月
		}
		String yf = DateTimeUtils.formatDateTime(DateTimeUtils.doMonth(DateTimeUtils.getNDT(), xd)).substring(0, 7);
		String TENANT_ID = dto.getTENANT_ID();
		if (StringUtils.isEmpty(TENANT_ID)) {
			TENANT_ID = "0";
		}
		List<Costuint> ul = this.getAllUnit(TENANT_ID);
		String currq = DateTimeUtils.getNowDateStr();
		if (ul != null) {
			int count = ul.size();
			String unitid, ksrq, jzrq, faid, kssj, jzsj;
			for (int i = 0; count > i; i++) {
				Costuint x = ul.get(i);
				unitid = x.getId();
				HashMap<String, String> tjsd = igpfrs.getReportPeriod(unitid, yf, "4");
				ksrq = tjsd.get("ksrq");// 统计日期
				jzrq = tjsd.get("jzrq");// 统计日期
				// 根据统计日期得到当班的开始和结束时间
				HashMap<String, String> qhm = this.uiis.getStartEndTime(unitid, ksrq, jzrq, 2);
				HashMap<String, String> alfm = new HashMap<String, String>();
				if (qhm != null && qhm.size() > 0) {
					// 班组录入过数据，这时才能知道开始时间和结束时间
					kssj = qhm.get("kssj");
					if (StringUtils.isEmpty(kssj)) {
						kssj = ksrq;
					}
					jzsj = qhm.get("jzsj");
					if (StringUtils.isEmpty(jzsj)) {
						jzsj = ksrq;
					}
					LinkedHashMap<String, List<ProductScheduPlanStart>> faMap = this.runStateService
							.getUnitProgByksrqjzrq(unitid, kssj, jzsj);
					if (faMap != null && faMap.size() > 0) {
						List<ProductScheduPlanStart> fal = faMap.get(unitid);
						if (fal != null) {
							int facount = fal.size();
							for (int h = 0; facount > h; h++) {
								ProductScheduPlanStart y = fal.get(h);
								faid = y.getProgramid();
								if (alfm.containsKey(faid)) {
									continue;// 方案已经计算过
								} else {
									// 计算方案数据
									alfm.put(faid, "1");
									TeamReportInputDto reportinfo = new TeamReportInputDto();
									reportinfo.setWriteDay(yf);
									reportinfo.setUnitId(unitid);
									reportinfo.setProgramId(faid);
									reportinfo.setBegintime(kssj);
									reportinfo.setEndtime(jzsj);
									reportinfo.setSummaryDay(currq);
									rtn = this.ictpl.calcTeamMonthReportAuto(reportinfo);
								}
							}
						}
					}
					if (!alfm.containsKey("0")) {
						// 默认要计算汇总数据
						TeamReportInputDto reportinfo = new TeamReportInputDto();
						reportinfo.setWriteDay(yf);
						reportinfo.setUnitId(unitid);
						reportinfo.setProgramId("0");
						reportinfo.setBegintime(kssj);
						reportinfo.setEndtime(jzsj);
						reportinfo.setSummaryDay(currq);
						rtn = this.ictpl.calcTeamMonthReportAuto(reportinfo);
					}
				}
			}
		}
		return rtn;
	}

	@Override
	public String autoDeviceCalc(AutoCreateMonthParamDto dto) {
		String rtn = "";
		Integer xd = 0;
		String xdyf = dto.getDIFMonth();
		if (Coms.judgeInt(xdyf)) {
			xd = Integer.parseInt(xdyf);
		} else {
			xd = -1;// 默认计算上个月
		}
		String yf = DateTimeUtils.formatDateTime(DateTimeUtils.doMonth(DateTimeUtils.getNDT(), xd)).substring(0, 7);
		String TENANT_ID = dto.getTENANT_ID();
		if (StringUtils.isEmpty(TENANT_ID)) {
			TENANT_ID = "0";
		}
		List<Costuint> ul = this.getAllUnit(TENANT_ID);
		String currq = DateTimeUtils.getNowDateStr();
		if (ul != null) {
			int count = ul.size();
			String unitid, ksrq, jzrq, faid, kssj, jzsj;
			for (int i = 0; count > i; i++) {
				Costuint x = ul.get(i);
				unitid = x.getId();
				HashMap<String, String> tjsd = igpfrs.getReportPeriod(unitid, yf, "4");
				ksrq = tjsd.get("ksrq");// 统计日期
				jzrq = tjsd.get("jzrq");// 统计日期
				// 根据统计日期得到当班的开始和结束时间
				HashMap<String, String> qhm = this.uiis.getStartEndTime(unitid, ksrq, jzrq, 2);
				HashMap<String, String> alfm = new HashMap<String, String>();
				if (qhm != null && qhm.size() > 0) {
					// 班组录入过数据，这时才能知道开始时间和结束时间
					kssj = qhm.get("kssj");
					if (StringUtils.isEmpty(kssj)) {
						kssj = ksrq;
					}
					jzsj = qhm.get("jzsj");
					if (StringUtils.isEmpty(jzsj)) {
						jzsj = ksrq;
					}
					LinkedHashMap<String, List<ProductScheduPlanStart>> faMap = this.runStateService
							.getUnitProgByksrqjzrq(unitid, kssj, jzsj);
					if (faMap != null && faMap.size() > 0) {
						List<ProductScheduPlanStart> fal = faMap.get(unitid);
						if (fal != null) {
							int facount = fal.size();
							for (int h = 0; facount > h; h++) {
								ProductScheduPlanStart y = fal.get(h);
								faid = y.getProgramid();
								if (alfm.containsKey(faid)) {
									continue;// 方案已经计算过
								} else {
									// 计算方案数据
									alfm.put(faid, "1");
									TeamReportInputDto reportinfo = new TeamReportInputDto();
									reportinfo.setWriteDay(yf);
									reportinfo.setUnitId(unitid);
									reportinfo.setProgramId(faid);
									reportinfo.setBegintime(ksrq);
									reportinfo.setEndtime(jzrq);
									reportinfo.setSummaryDay(currq);
									rtn = this.ictpl.calcDeviceMonthReportAuto(reportinfo);
								}
							}
						}
					}
				}

				if (!alfm.containsKey("0")) {
					// 默认要计算汇总数据
					TeamReportInputDto reportinfo = new TeamReportInputDto();
					reportinfo.setWriteDay(yf);
					reportinfo.setUnitId(unitid);
					reportinfo.setProgramId("0");
					reportinfo.setBegintime(ksrq);
					reportinfo.setEndtime(jzrq);
					reportinfo.setSummaryDay(currq);
					rtn = this.ictpl.calcDeviceMonthReportAuto(reportinfo);
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 得到租户内全部在用的核算对象
	 * @return
	 */
	private List<Costuint> getAllUnit(String TENANT_ID) {
		Where where = Where.create();
		where.eq(Costuint::getTmused, 1);
		if (StringUtils.isEmpty(TENANT_ID) || "0".equals(TENANT_ID)) {
			return entityService.queryList(Costuint.class, where);
		} else {
			return entityService.rawQueryListByWhereWithTenant(TENANT_ID, Costuint.class, where);
		}
	}

	/**
	 * @category 得到租户内的交接班计算任务
	 * @param TENANT_ID
	 * @return
	 */
	private List<ShiftCostCalcTask> getTask(String TENANT_ID) {
		Where where = Where.create();
		where.eq(ShiftCostCalcTask::getCalcStatus, 0);
		where.or();
		where.eq(ShiftCostCalcTask::getCalcStatus, 1);
		if (StringUtils.isEmpty(TENANT_ID) || "0".equals(TENANT_ID)) {
			return entityService.queryList(ShiftCostCalcTask.class, where);
		} else {
			return entityService.rawQueryListByWhereWithTenant(TENANT_ID, ShiftCostCalcTask.class, where);
		}
	}

	@Override
	public String autoCreateShiftData(AutoCreateShiftTaskDto dto) {
		String rtn = "";
		String ulist = dto.getUnitList();
		List<String> ull = new ArrayList<String>();
		if (StringUtils.isNotEmpty(ulist)) {
			String[] ual = ulist.split(",");
			Collections.addAll(ull, ual);
		}
		String TENANT_ID = dto.getTENANT_ID();
		if (StringUtils.isEmpty(TENANT_ID)) {
			TENANT_ID = "0";
		}
		Integer xd = 0, ubs;
		String xdyf = dto.getDIFShift();
		if (Coms.judgeInt(xdyf)) {
			xd = Integer.parseInt(xdyf);
		} else {
			xd = 0;// 默认当班
		}
		String subs = dto.getUseList();
		if (Coms.judgeInt(subs)) {
			ubs = Integer.parseInt(subs);
		} else {
			ubs = 0;// 默认是只计算传入的核算对象
		}
		// 给定时间
		String curtime = dto.getGdsj();
		if (StringUtils.isEmpty(curtime)) {
			curtime = DateTimeUtils.getNowDateTimeStr();// 没有给定时间使用当前时间
		} else {
			xd = 0;// 给定时间时，就是算当班
		}

		List<Costuint> ul = this.getAllUnit(TENANT_ID);
		if (ul != null) {
			String pid;
			int count = ul.size();// 整理上下级关系
			ConcurrentHashMap<String, List<Costuint>> dxgxm = new ConcurrentHashMap<String, List<Costuint>>();
			for (int i = 0; count > i; i++) {
				Costuint x = ul.get(i);
				pid = x.getPid();
				if (StringUtils.isEmpty(pid)) {
					pid = "root";
				}
				if (dxgxm.containsKey(pid)) {
					dxgxm.get(pid).add(x);
				} else {
					List<Costuint> bl = new ArrayList<Costuint>();
					bl.add(x);
					dxgxm.put(pid, bl);
				}
			}
			if (dxgxm.containsKey("root")) {
				// 从根对象开始
				List<Costuint> dxl = dxgxm.get("root");
				int xcount = dxl.size();
				for (int i = 0; xcount > i; i++) {
					Costuint x = dxl.get(i);
					this.calc(x, xd, ubs, ull, dxgxm, TENANT_ID, curtime);
				}
			}
		}
		return rtn;
	}

	private String calc(Costuint x, Integer xd, Integer ubs, List<String> ull,
			ConcurrentHashMap<String, List<Costuint>> dxgxm, String TENANT_ID, String curtime) {
		String rtn = "";
		String unitid = x.getId();
		if (dxgxm.containsKey(unitid)) {
			// 有子对象,先计算子对象
			List<Costuint> dxl = dxgxm.get(unitid);
			if (dxl != null) {
				int count = dxl.size();
				for (int i = 0; count > i; i++) {
					Costuint zdx = dxl.get(i);
					this.calc(zdx, xd, ubs, ull, dxgxm, TENANT_ID, curtime);// 递归
				}
			}
		}
		this.dxcalc(x, xd, ubs, ull, TENANT_ID, curtime);// 计算对象自身
		return rtn;
	}

	/**
	 * @category 计算核算对象的交接班或日报
	 * @return
	 */
	private String dxcalc(Costuint x, Integer xd, Integer ubs, List<String> ull, String TENANT_ID, String curtime) {
		String rtn = "";
		String unitid = x.getId();
		boolean canc;
		String unitname, sbsj, xbsj, bzdm, bcdm, bcmc, bzmc, tbrq, tjrq, faid, famc;
		if (ubs == 0) {
			// 只计算传入的核算对象
			if (ull.contains(unitid)) {
				canc = true;
			} else {
				canc = false;
			}
		} else {
			// 不计算传入的核算对象
			if (ull.contains(unitid)) {
				canc = false;
			} else {
				canc = true;
			}
		}
		if (canc) {
			unitname = x.getName();
			// 当前核算对象要计算，根据当前时间推倒班
			try {
				ShiftForeignVo si = this.uiis.getShiftInfo(unitid, curtime);
				if (si != null) {
					// 倒班信息不是空，计算交接班
					if (xd == -1) {
						// 取上班
						ShiftForeignVo psi = this.shiftService.getPShiftByDateTime(si.getSbsj(), si.getOrgCode());
						bzdm = psi.getOrgCode();
						bzmc = psi.getOrgName();
						bcdm = psi.getShiftClassCode();
						bcmc = psi.getShiftClassName();
						sbsj = psi.getSbsj();
						xbsj = psi.getXbsj();
						tbrq = psi.getTbrq();
						tjrq = psi.getTjsj();
					} else {
						// 取当班
						bzdm = si.getOrgCode();
						bzmc = si.getOrgName();
						bcdm = si.getShiftClassCode();
						bcmc = si.getShiftClassName();
						sbsj = si.getSbsj();
						xbsj = si.getXbsj();
						tbrq = si.getTbrq();
						tjrq = si.getTjsj();
					}
					this.iascl.deleteBatchData(unitid, bcdm, bzdm, tbrq);// 先清除旧的批次数据
					LinkedHashMap<String, List<ProductScheduPlanStart>> faMap = this.runStateService
							.getUnitProgByksrqjzrq(unitid, sbsj, xbsj);
					if (faMap != null && faMap.size() > 0) {
						// 有方案切换的交接班
						List<ProductScheduPlanStart> fal = faMap.get(unitid);
						if (fal != null) {
							int facount = fal.size();
							// 每一次切换的数据
							for (int h = 0; facount > h; h++) {
								ProductScheduPlanStart y = fal.get(h);
								faid = y.getProgramid();
								if (faid != null) {
									// 计算方案数据
									famc = y.getProgramname();
									this.iascl.calcProgramData(unitid, bcdm, bcmc, bzdm, bzmc, sbsj, xbsj,
											y.getStartdatetime(), y.getEnddatetime(), faid, tbrq, tjrq, TENANT_ID,
											TMUID.getUID(), unitname, famc, true);
								}
							}
							// 交接班计算
							TeamReportInputDto reportinfo = new TeamReportInputDto();
							reportinfo.setWriteDay(tbrq);
							reportinfo.setUnitId(unitid);
							reportinfo.setShiftId(bcdm);
							reportinfo.setTeamId(bzdm);
							reportinfo.setIsBatchCalc(true);
							this.ictpl.calcBatchProgramData(reportinfo);
							// 计算日报和周报：与调度的逻辑相同
							this.iascl.calcTask(unitid, bcdm, bzdm, tbrq);
						}
					} else {
						// 直接把批次的数据变成交接班
						this.iascl.calcProgramData(unitid, bcdm, bcmc, bzdm, bzmc, sbsj, xbsj, sbsj, xbsj, "0", tbrq,
								tjrq, TENANT_ID, TMUID.getUID(), unitname, null, false);
					}
				} else {
					// 没有倒班的仅计算日报
					tjrq = DateTimeUtils.formatDate(DateTimeUtils.doDate(DateTimeUtils.getND(), xd), "yyyy-MM-dd");
					this.iascl.calcDayReport(unitid, unitname, tjrq, "0", "", TENANT_ID, TMUID.getUID());
				}
			} catch (Exception e) {
				// 代理服务器返回异常，为错误赋值并结束采集
				log.error("交接班自动计算", "推倒班发生错误，核算对象ID：" + unitid);
			}
		}
		return rtn;
	}

	@Override
	public String calcCreateShiftTask(AutoCreateShiftTaskDto dto) {
		String rtn = "";
		String TENANT_ID = dto.getTENANT_ID();
		if (StringUtils.isEmpty(TENANT_ID)) {
			TENANT_ID = "0";
		}
		List<ShiftCostCalcTask> tl = this.getTask(TENANT_ID);
		if (tl != null) {
			try {
				int count = tl.size();
				for (int i = 0; count > i; i++) {
					ShiftCostCalcTask t = tl.get(i);
					// 计算任务
					t.setCalcBeginTime(DateTimeUtils.getDTStr());
					String err = this.iascl.calcTask(t.getUnitId(), t.getShiftId(), t.getTeamId(), t.getWriteDay());
					if ("".equals(err)) {
						t.setCalcStatus(2);
						t.setCalcEndTime(DateTimeUtils.getDTStr());
					} else {
						t.setCalcStatus(3);
					}
				}
				this.entityService.updateBatch(tl);
			} catch (Exception e) {
				rtn = "err";
			}
		}
		return rtn;
	}

	@Override
	public String calcPriceChangeTask(AutoCreateShiftTaskDto dto) {
		String rtn = "";
		log.info("价格导入计算开始", "统计时段1" + DateTimeUtils.getDTStr());
		// 租户
		String TENANT_ID = dto.getTENANT_ID();
		if (StringUtils.isEmpty(TENANT_ID)) {
			TENANT_ID = "0";
		}
		// 给定的核算对象ID
		String ulist = dto.getUnitList();
		List<String> ull = new ArrayList<String>();
		if (StringUtils.isNotEmpty(ulist)) {
			String[] ual = ulist.split(",");
			Collections.addAll(ull, ual);
		}
		// 如何使用给定核算对象
		Integer ubs;
		String subs = dto.getUseList();
		if (Coms.judgeInt(subs)) {
			ubs = Integer.parseInt(subs);
		} else {
			ubs = 0;// 默认是只计算传入的核算对象
		}
		List<ItemPriceChangeTask> tl = new ArrayList<ItemPriceChangeTask>();
		// 开始日期
		String ksrq = dto.getDIFShift();
		if (StringUtils.isEmpty(ksrq)) {
			// 未给定开始日期时，检索数据库的任务
			tl = this.iopsd.getPriceCalcTask(TENANT_ID);
		} else {
			// 给定时间，用给定时间做成调度
			String jzrq = dto.getGdsj();
			if (StringUtils.isEmpty(jzrq)) {
				jzrq = DateTimeUtils.getNowDateStr();// 未给定截止日期使用当前日期
			}
			ItemPriceChangeTask b = new ItemPriceChangeTask();
			b.setBeginTime(ksrq);
			b.setEndTime(jzrq);
			b.setCalcStatus(-1);
			tl.add(b);
		}
		// 开始计算
		List<Costuint> ul = this.getAllUnit(TENANT_ID);
		if (ul != null && tl != null) {
			String pid;
			int count = ul.size();// 整理上下级关系
			ConcurrentHashMap<String, List<Costuint>> dxgxm = new ConcurrentHashMap<String, List<Costuint>>();
			for (int i = 0; count > i; i++) {
				Costuint x = ul.get(i);
				pid = x.getPid();
				if (StringUtils.isEmpty(pid)) {
					pid = "root";
				}
				if (dxgxm.containsKey(pid)) {
					dxgxm.get(pid).add(x);
				} else {
					List<Costuint> bl = new ArrayList<Costuint>();
					bl.add(x);
					dxgxm.put(pid, bl);
				}
			}
			Integer bs = 0;
			count = tl.size();
			for (int t = 0; count > t; t++) {
				ItemPriceChangeTask b = tl.get(t);
				bs = b.getCalcStatus();
				if (bs == null) {
					bs = 0;
				}
				if (bs >= 0) {
					b.setCalcBeginTime(DateTimeUtils.getNowDateTimeStr());
				}
				log.info("价格导入计算任务", "统计时段2" + DateTimeUtils.getDTStr());
				if (dxgxm.containsKey("root")) {
					// 从根对象开始
					List<Costuint> dxl = dxgxm.get("root");
					int xcount = dxl.size();
					for (int dx = 0; xcount > dx; dx++) {
						Costuint x = dxl.get(dx);
						this.calc(x, ubs, ull, b, dxgxm, TENANT_ID);
					}
				}
				log.info("价格导入计算任务", "统计时段3" + DateTimeUtils.getDTStr());
				if (bs >= 0) {
					b.setCalcStatus(2);
					b.setCalcEndTime(DateTimeUtils.getNowDateTimeStr());
				}
			}
			if (bs >= 0) {
				// 更新任务状态
				this.iopsd.updatePriceCalcTask(tl);
			}
		}
		log.info("价格导入计算结束", "统计时段4" + DateTimeUtils.getDTStr());
		return rtn;
	}

	private String calc(Costuint x, Integer ubs, List<String> ull, ItemPriceChangeTask t,
			ConcurrentHashMap<String, List<Costuint>> dxgxm, String TENANT_ID) {
		String rtn = "";
		String unitid = x.getId();
		if (dxgxm.containsKey(unitid)) {
			// 有子对象,先计算子对象
			List<Costuint> dxl = dxgxm.get(unitid);
			if (dxl != null) {
				int count = dxl.size();
				for (int i = 0; count > i; i++) {
					Costuint zdx = dxl.get(i);
					this.calc(zdx, ubs, ull, t, dxgxm, TENANT_ID);// 递归
				}
			}
		}
		this.dxcalc(x, ubs, ull, t, TENANT_ID);// 计算对象自身
		return rtn;
	}

	/**
	 * @category 计算核算对象的交接班或日报
	 * @return
	 */
	private String dxcalc(Costuint x, Integer ubs, List<String> ull, ItemPriceChangeTask t, String TENANT_ID) {
		String rtn = "", msg;
		String unitid = x.getId();
		boolean canc;
		if (ubs == 0) {
			// 只计算传入的核算对象
			if (ull.contains(unitid)) {
				canc = true;
			} else {
				canc = false;
			}
		} else {
			// 不计算传入的核算对象
			if (ull.contains(unitid)) {
				canc = false;
			} else {
				canc = true;
			}
		}
		if (canc) {
			String unitname = x.getName();
			msg = "价格导入计算任务" + unitname;
			log.info(msg, "统计时段1" + DateTimeUtils.getDTStr());
			// 当前核算对象要计算，根据当前时间推倒班
			try {
				String ksrq = t.getBeginTime();
				String jzrq = t.getEndTime();
				// 推倒班
				log.info(msg, "统计时段2" + DateTimeUtils.getDTStr());
				MethodQueryDto qdto = new MethodQueryDto();
				qdto.setUnitid(unitid);// 核算对象ID
				qdto.setObjType("org");
				List<Costunitoperator> listCzjg = unitMeth.getCostunitoperatorList(qdto);
				List<String> listOrg = new ArrayList<String>();
				if (StringUtils.isNotEmpty(listCzjg)) {
					for (Costunitoperator temp : listCzjg) {
						listOrg.add(temp.getObjid());
					}
				}
				String nowDt = DateTimeUtils.getNowDateTimeStr();
				log.info(msg, "统计时段3" + DateTimeUtils.getDTStr());
				List<TeamReportInputDto> dbl = new ArrayList<TeamReportInputDto>();
				HashMap<String, TeamReportInputDto> rchm = new HashMap<String, TeamReportInputDto>();
				HashMap<String, TeamReportInputDto> zchm = new HashMap<String, TeamReportInputDto>();
				HashMap<String, HashMap<String, String>> zmm = new HashMap<String, HashMap<String, String>>();
				HashMap<String, List<ProductScheduPlanStart>> dbfam = new HashMap<String, List<ProductScheduPlanStart>>();
				List<ShiftForeignVo> listShift = new ArrayList<ShiftForeignVo>();
				if (listOrg != null && listOrg.size() > 0) {
					String sbsj, xbsj, bzdm, bcdm, tbrq, tjrq, faid, key, kssj, jzsj, zq;
					log.info(msg, "统计时段4" + DateTimeUtils.getDTStr());
					listShift = shiftService.getShiftDataByksrqjzrq(listOrg, ksrq, jzrq);
					if (listShift != null) {
						// 有班次
						for (ShiftForeignVo yn : listShift) {
							sbsj = yn.getSbsj();
							if (DateTimeUtils.bjDate(DateTimeUtils.parseDate(nowDt),
									DateTimeUtils.parseDate(sbsj)) <= 0) {
								// 当前时间比批次结束时间晚，使用当前时间
								continue;
							}
							xbsj = yn.getXbsj();
							bzdm = yn.getOrgCode();
							bcdm = yn.getShiftClassCode();
							tbrq = yn.getTbrq();
							tjrq = yn.getTjsj();
							// 判定有无数据
							List<CostBatchOnDuty> dl = this.iopsd.getBatchOnDuty(unitid, bcdm, bzdm, tbrq);
							if (dl == null || dl.size() == 0) {
								// 当班未录入过，此时要采集数据
								// 班次内的切换
								key = (new StringBuffer(unitid).append(".").append(tbrq).append(".").append(bzdm)
										.append(".").append(bcdm)).toString();
								LinkedHashMap<String, List<ProductScheduPlanStart>> faMap = this.runStateService
										.getUnitProgByksrqjzrq(unitid, sbsj, xbsj);
								if (faMap != null && faMap.size() > 0) {
									dbfam.put(key, faMap.get(unitid));// 有方案切换
								} else {
									// 没有方案切换补上默认方案
									List<ProductScheduPlanStart> ll = new ArrayList<ProductScheduPlanStart>();
									ProductScheduPlanStart b = new ProductScheduPlanStart();
									b.setProgramid("0");
									b.setStartdatetime(sbsj);
									b.setEnddatetime(xbsj);
									dbfam.put(key, ll);
								}
							}
							// 交接班计算
							TeamReportInputDto reportinfo = new TeamReportInputDto();
							reportinfo.setWriteDay(tbrq);
							reportinfo.setUnitId(unitid);
							reportinfo.setShiftId(bcdm);
							reportinfo.setTeamId(bzdm);
							reportinfo.setSummaryDay(tjrq);
							reportinfo.setShiftBegintime(sbsj);
							reportinfo.setShiftEndtime(xbsj);
							reportinfo.setIsBatchCalc(true);
							dbl.add(reportinfo);
							// 日报
							key = (new StringBuffer(unitid).append(".").append(tjrq)).toString();
							if (!rchm.containsKey(key)) {
								TeamReportInputDto rbinfo = new TeamReportInputDto();
								rbinfo.setUnitId(unitid);
								rbinfo.setWriteDay(tjrq);
								rbinfo.setSummaryDay(tjrq);
								rbinfo.setOrgId("");
								rchm.put(key, rbinfo);
							}
							log.info(msg, "统计时段5" + DateTimeUtils.getDTStr());
							// 周报
							HashMap<String, String> tjsd = null;
							if (zmm.containsKey(tjrq)) {
								tjsd = zmm.get(tjrq);
							} else {
								tjsd = igpfrs.getReportPeriod(unitid, tjrq, "3");
								zmm.put(tjrq, tjsd);
							}
							log.info(msg, "统计时段6" + DateTimeUtils.getDTStr());
							if (tjsd != null) {
								String yf = tjsd.get("yf");
								String zs = tjsd.get("zs");
								kssj = tjsd.get("ksrq");
								jzsj = tjsd.get("jzrq");
								zq = yf + "-" + zs;
								key = (new StringBuffer(unitid).append(".").append(zq)).toString();
								if (!zchm.containsKey(key)) {
									TeamReportInputDto zbinfo = new TeamReportInputDto();
									zbinfo.setWriteDay(zq);
									zbinfo.setBnid(yf);
									zbinfo.setBatchNo(zs);
									zbinfo.setUnitId(unitid);
									zbinfo.setBegintime(kssj);
									zbinfo.setEndtime(jzsj);
									zchm.put(key, zbinfo);
								}
							}
						}
					}
					log.info(msg, "统计时段7" + DateTimeUtils.getDTStr());
					// 交接班计算
					for (TeamReportInputDto db : dbl) {
						bzdm = db.getTeamId();
						bcdm = db.getShiftId();
						tbrq = db.getWriteDay();
						key = (new StringBuffer(unitid).append(".").append(tbrq).append(".").append(bzdm).append(".")
								.append(bcdm)).toString();
						if (dbfam.containsKey(key)) {
							List<ProductScheduPlanStart> dd = dbfam.get(key);
							if (dd != null) {
								for (ProductScheduPlanStart yl : dd) {
									faid = yl.getProgramid();
									// 获取方案数据
									log.info(msg, "获取方案仪表数据1" + DateTimeUtils.getDTStr());
									if (faid != null) {
										this.iascl.calcProgramData(unitid, bcdm, "", bzdm, "", db.getShiftBegintime(),
												db.getShiftEndtime(), yl.getStartdatetime(), yl.getEnddatetime(), faid,
												tbrq, db.getSummaryDay(), "0", TMUID.getUID(), "", "", true);
									}
									log.info(msg, "获取方案仪表数据2" + DateTimeUtils.getDTStr());
								}
							}
						}
						// 交接班计算
						log.info(msg, "交接班计算1" + DateTimeUtils.getDTStr());
						this.ictpl.calcBatchProgramData(db);
						log.info(msg, "交接班计算2" + DateTimeUtils.getDTStr());
					}
					// 日报计算
					for (Entry<String, TeamReportInputDto> cyn : rchm.entrySet()) {
						TeamReportInputDto yn = cyn.getValue();
						log.info(msg, "日报计算1" + DateTimeUtils.getDTStr());
						this.iascl.checkDayData(yn);// 整理日报数据，去除无效数据
						log.info(msg, "日报计算2" + DateTimeUtils.getDTStr());
					}
					// 周报计算
					for (Entry<String, TeamReportInputDto> cyn : zchm.entrySet()) {
						TeamReportInputDto yn = cyn.getValue();
						log.info(msg, "周报计算1" + DateTimeUtils.getDTStr());
						this.iascl.checkWeekData(yn);// 整理周报数据，去除无效数据
						log.info(msg, "周报计算2" + DateTimeUtils.getDTStr());
					}
				}
			} catch (Exception e) {
				// 代理服务器返回异常，为错误赋值并结束采集
				log.error("交接班自动计算", "推倒班发生错误，核算对象ID：" + unitid);
			}
			log.info(msg, "统计时段8" + DateTimeUtils.getDTStr());
		}
		return rtn;
	}

	@Override
	public String delBatchErrData() {
		String rtn = "";
		log.info("删除交接班错误数据开始", "统计时段1" + DateTimeUtils.getDTStr());
		entityService.rawDelete(
				"delete from costbatchinstrumentdata where pid not in (select id from costbatchonduty) and endtime>='2024-05-16'");
		log.info("删除交接班错误数据结束", "统计时段4" + DateTimeUtils.getDTStr());
		return rtn;
	}
}
