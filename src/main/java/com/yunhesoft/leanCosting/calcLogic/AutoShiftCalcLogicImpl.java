package com.yunhesoft.leanCosting.calcLogic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yunhesoft.core.common.aviator.AviatorResult;
import com.yunhesoft.core.common.aviator.AviatorUtils;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchOnDuty;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamParamData;
import com.yunhesoft.leanCosting.costReport.entity.vo.InstructmentDataVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.ItemConsumption;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.costReport.service.GetItemConsumptionService;
import com.yunhesoft.leanCosting.costReport.service.IGetShiftDataService;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.CostProgStartTimeDto;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlanStart;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ProductUnitProgShift;
import com.yunhesoft.leanCosting.productSchedu.service.IZzRunStateService;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramLibraryCostItem;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemInfoVo;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class AutoShiftCalcLogicImpl implements IAutoShiftCalcLogic {

	@Autowired
	ICalcTeamProjectLogic ictpl;

	@Autowired
	GetItemConsumptionService gics;

	@Autowired
	UnitItemInfoService unitItemInfoService;

	@Autowired
	IGetShiftDataService igsds;

	@Autowired
	EntityService entityService;

	@Autowired
	IGetPeriodForReportService gettjsd;

	@Autowired
	IShiftService shift;

	@Autowired
	IUnitMethodService unitMeth;

	@Autowired
	GetCostItemInfoService gcii;

	@Autowired
	IOPShiftData iopsd;

	@Autowired
	private IZzRunStateService runStateService; // 运行状态

	private PublicMethods pm = new PublicMethods();
	private String _zbbl = "(\\w+\\.\\w+\\.?(\\w+)?)|(\\w+\\.([\\u4E00-\\u9FA5A-Za-z0-9_])+\\.\\w+)|([\\u4E00-\\u9FA5A-Za-z0-9_]+)";
	private ConcurrentHashMap<String, Double> p_gzscm = new ConcurrentHashMap<String, Double>();// 工作时长，key是计算传入的uid,value是对应的时长
	private ConcurrentHashMap<String, ConcurrentHashMap<String, Costinstrument>> p_ybm = new ConcurrentHashMap<String, ConcurrentHashMap<String, Costinstrument>>();
	private ConcurrentHashMap<String, ConcurrentHashMap<String, Double>> p_itemjh = new ConcurrentHashMap<String, ConcurrentHashMap<String, Double>>();
	private ConcurrentHashMap<String, HashMap<String, Costitem>> p_im = new ConcurrentHashMap<String, HashMap<String, Costitem>>();
	private ConcurrentHashMap<String, ConcurrentHashMap<String, List<String>>> p_flxm = new ConcurrentHashMap<String, ConcurrentHashMap<String, List<String>>>();
	private ConcurrentHashMap<String, ConcurrentHashMap<String, Costindicator>> p_csm = new ConcurrentHashMap<String, ConcurrentHashMap<String, Costindicator>>();
	private ConcurrentHashMap<String, ConcurrentHashMap<String, CostItemFormula>> p_gm = new ConcurrentHashMap<String, ConcurrentHashMap<String, CostItemFormula>>();
	private ConcurrentHashMap<String, ConcurrentHashMap<String, String>> p_alrm = new ConcurrentHashMap<String, ConcurrentHashMap<String, String>>();
	private ConcurrentHashMap<String, ConcurrentHashMap<String, Double>> p_subitem = new ConcurrentHashMap<String, ConcurrentHashMap<String, Double>>();
	private ConcurrentHashMap<String, ConcurrentHashMap<String, String>> p_valm = new ConcurrentHashMap<String, ConcurrentHashMap<String, String>>();
	private ConcurrentHashMap<String, CalcTDSParam> p_ctspm = new ConcurrentHashMap<String, CalcTDSParam>();
	private ConcurrentHashMap<String, String> p_infom = new ConcurrentHashMap<String, String>();
	private ConcurrentHashMap<String, CostBatchOnDuty> p_dutyinfo = new ConcurrentHashMap<String, CostBatchOnDuty>();
	private ConcurrentHashMap<String, List<?>> p_ybdm = new ConcurrentHashMap<String, List<?>>();
	private ConcurrentHashMap<String, List<?>> p_xmdm = new ConcurrentHashMap<String, List<?>>();
	private ConcurrentHashMap<String, List<?>> p_csdm = new ConcurrentHashMap<String, List<?>>();

	@Override
	public void deleteBatchData(String unitid, String shiftid, String teamid, String writeday) {
		// 得到当班期间各批次信息
		List<CostBatchOnDuty> bl = this.iopsd.getBatchOnDuty(unitid, shiftid, teamid, writeday);
		if (bl != null) {
			int count = bl.size();
			String id;
			// 开始删除数据
			for (int i = 0; count > i; i++) {
				CostBatchOnDuty x = bl.get(i);
				id = x.getId();
				if (this.iopsd.deleteInstrumentData(id)) {
					this.iopsd.deleteOnDuty(id);
				}
			}
		}
	}

	@Override
	public String calcProgramData(String unitid, String shiftid, String shiftname, String teamid, String teamname,
			String sbsj, String xbsj, String fakssj, String fajzsj, String faid, String writeday, String summaryday,
			String tenantId, String uid, String unitname, String famc, boolean isbatch) {
		String rtn = "";
		// 初始化
		Double sc = Maths.abs(
				DateTimeUtils.diffSecond(DateTimeUtils.parseDate(fakssj), DateTimeUtils.parseDate(fajzsj))) / 3600.00;// 工作时长，小时
		this.p_gzscm.put(uid, sc);
		this.init(unitid, writeday, summaryday, shiftid, teamid, fakssj, fajzsj, faid, uid, isbatch);
		// 计算信息
		if (isbatch) {
			// 批次仅获取仪表数据，后续计算在外面
			this.batchInfo(uid, unitid, unitname, sbsj, xbsj, fakssj, fajzsj, faid, famc, shiftid, shiftname, teamid,
					teamname, writeday, summaryday);
			// 仪表
			this.instrumentProgramData(unitid, uid, fakssj, fajzsj, faid, writeday, tenantId, shiftid, teamid);
			// 保存批次计算内容
			this.saveProgramData(uid);
			// 去除暂存内容
			this.removeTempData(uid);
		} else {
			// 批次计算后续的交接班数据
			this.calcData(unitid, shiftid, shiftname, teamid, teamname, sbsj, xbsj, writeday, summaryday, faid, famc,
					tenantId, uid);
		}
		return rtn;
	}

	private String calcData(String unitid, String shiftid, String shiftname, String teamid, String teamname,
			String sbsj, String xbsj, String writeday, String summaryday, String faid, String famc, String tenantId,
			String uid) {
		String rtn = "";
		CalcTDSParam ctsp = this.p_ctspm.get(uid);
		if (ctsp == null) {
			ctsp = new CalcTDSParam("", writeday.substring(0, 7), sbsj, xbsj);
			this.p_ctspm.put(uid, ctsp);
		}
		// 计算信息
		CostTeamInfo info = this.calcInfo(uid, unitid, sbsj, xbsj, faid, shiftid, teamid, writeday, summaryday);
		// 初始化
		Double sc = Maths.abs(DateTimeUtils.diffSecond(DateTimeUtils.parseDate(sbsj), DateTimeUtils.parseDate(xbsj)))
				/ 3600.00;// 工作时长，小时
		this.p_gzscm.put(uid, sc);
		this.init(unitid, writeday, summaryday, shiftid, teamid, sbsj, xbsj, faid, uid, false);
		// 仪表
		this.instrumentData(unitid, uid, sbsj, xbsj, faid, writeday, tenantId, ctsp, shiftid, teamid);
		// 计算项目量
		this.itemData(unitid, uid, teamid, shiftid, sbsj, xbsj, writeday, faid, ctsp);
		// 计算核算指标
		this.indicatorData(unitid, uid, teamid, shiftid, sbsj, xbsj, writeday, faid, ctsp);
		this.saveData(uid, info);
		// 去除暂存内容
		this.removeTempData(uid);
		// 对象日报
		TeamReportInputDto rbinfo = new TeamReportInputDto();
		rbinfo.setUnitId(unitid);
		rbinfo.setWriteDay(summaryday);
		rbinfo.setSummaryDay(summaryday);
		rbinfo.setOrgId("");
		this.checkDayData(rbinfo);// 整理日报，去除无效数据
		// 计算周报
		HashMap<String, String> tjsd = gettjsd.getReportPeriod(unitid, summaryday, "3");
		if (tjsd != null) {
			// 周报
			String yf = tjsd.get("yf");
			String zs = tjsd.get("zs");
			// 对象周报
			TeamReportInputDto zbinfo = new TeamReportInputDto();
			zbinfo.setWriteDay(yf + "-" + zs);
			zbinfo.setUnitId(unitid);
			zbinfo.setBegintime(tjsd.get("ksrq"));
			zbinfo.setEndtime(tjsd.get("jzrq"));
			zbinfo.setOrgId("");
			this.checkWeekData(zbinfo);// 整理周报，去除无效数据
		}
		return rtn;
	}

	@Override
	public String calcDayReport(String unitid, String unitname, String summaryday, String faid, String famc,
			String tenantId, String uid) {
		String rtn = "";
		CalcTDSParam ctsp = this.p_ctspm.get(uid);
		if (ctsp == null) {
			ctsp = new CalcTDSParam("", summaryday.substring(0, 7), summaryday, summaryday);
			this.p_ctspm.put(uid, ctsp);
		}
		// 计算信息
		CostSummaryInfo info = this.calcInfo(uid, unitid, faid, summaryday);
		// 初始化
		Double sc = 24.0;// 工作时长，小时
		this.p_gzscm.put(uid, sc);
		this.init(unitid, summaryday, summaryday, null, faid, null, null, faid, uid, false);
		// 计算项目量
		this.dayItemData(unitid, uid, summaryday, faid, ctsp);
		// 计算核算指标
		this.dayIndicatorData(unitid, uid, summaryday, faid, ctsp);
		this.saveDayData(uid, info);
		// 去除暂存内容
		this.removeTempData(uid);
		// 计算周报
		HashMap<String, String> tjsd = gettjsd.getReportPeriod(unitid, summaryday, "3");
		if (tjsd != null) {
			String yf = tjsd.get("yf");
			String zs = tjsd.get("zs");
			// 对象周报
			TeamReportInputDto zbinfo = new TeamReportInputDto();
			zbinfo.setWriteDay(yf + "-" + zs);
			zbinfo.setUnitId(unitid);
			zbinfo.setBegintime(tjsd.get("ksrq"));
			zbinfo.setEndtime(tjsd.get("jzrq"));
			zbinfo.setOrgId("");
			this.checkWeekData(zbinfo);
		}
		return rtn;
	}

	private void removeTempData(String uid) {
		this.p_gzscm.remove(uid);
		this.p_ybm.remove(uid);
		this.p_itemjh.remove(uid);
		this.p_im.remove(uid);
		this.p_flxm.remove(uid);
		this.p_csm.remove(uid);
		this.p_gm.remove(uid);
		this.p_infom.remove(uid);
		this.p_ybdm.remove(uid);
		this.p_xmdm.remove(uid);
		this.p_csdm.remove(uid);
		this.p_alrm.remove(uid);
		this.p_subitem.remove(uid);
		this.p_valm.remove(uid);
		this.p_ctspm.remove(uid);
		this.p_dutyinfo.remove(uid);
	}

	/**
	 * @category 初始化数据：读取设置
	 * @param unitid
	 * @param rq
	 * @param faid
	 * @param uid
	 */
	private void init(String unitid, String tbrq, String tjrq, String shiftid, String teamid, String kssj, String jzsj,
			String faid, String uid, boolean isbatch) {
		Double gzsc;
		String key, pid;
		String erpcode, wzdm, key1, key2;
		if (this.p_gzscm.containsKey(uid)) {
			gzsc = this.p_gzscm.get(uid);
		} else {
			gzsc = 0.0;
		}
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		if (isbatch) {
			pid = uid;
		} else {
			if (this.p_infom.containsKey(uid)) {
				pid = this.p_infom.get(uid);
			} else {
				return;
			}
		}

		// 得到方案下的使用项目
		ConcurrentHashMap<String, ProgramLibraryCostItem> qywzm = new ConcurrentHashMap<String, ProgramLibraryCostItem>();// 启用的物资
		ConcurrentHashMap<String, ProgramLibraryCostItem> qyzbm = new ConcurrentHashMap<String, ProgramLibraryCostItem>();// 启用的指标
		CostItemInfoVo infos = this.unitItemInfoService.getUnitData(unitid, tbrq, faid);
		List<ProgramLibraryCostItem> qyxml = infos.getPItemList();
		if (qyxml != null) {
			Integer dtype;
			int count = qyxml.size();
			for (int i = 0; count > i; i++) {
				ProgramLibraryCostItem x = qyxml.get(i);
				dtype = x.getDataType();
				if (dtype == null || dtype == 0) {
					// 项目
					key = x.getItemId();
					if (StringUtils.isNotEmpty(key)) {
						qywzm.put(key, x);
					}
				} else {
					// 指标
					key = x.getName();
					if (StringUtils.isNotEmpty(key)) {
						qyzbm.put(key, x);
					}
				}
			}
		}
		// 分类信息
		ConcurrentHashMap<String, String> flm = new ConcurrentHashMap<String, String>();
		List<Costclass> fll = infos.getClassList();
		if (fll != null) {
			String fllx;
			int count = fll.size();
			for (int i = 0; count > i; i++) {
				Costclass cy = fll.get(i);
				fllx = cy.getCctype();
				if (fllx == null) {
					fllx = "";
				}
				key = cy.getId();
				if (StringUtils.isNotEmpty(key)) {
					flm.put(key, fllx);
				}
			}
		}
		// 项目仪表
		ConcurrentHashMap<String, Costinstrument> ym = new ConcurrentHashMap<String, Costinstrument>();
		ConcurrentHashMap<String, List<Costinstrument>> xmybm = new ConcurrentHashMap<String, List<Costinstrument>>();
		List<Costinstrument> ybids = infos.getInstrumentList();
		if (ybids != null) {
			int count = ybids.size();
			for (int i = 0; count > i; i++) {
				Costinstrument x = ybids.get(i);
				key = x.getId();
				wzdm = x.getPid();
				if (wzdm == null) {
					continue;// 无意义的成本仪表
				}
				if (StringUtils.isNotEmpty(key)) {
					ym.put(key, x);
				}
				// 添加仪表数据使用
				if (xmybm.containsKey(wzdm)) {
					xmybm.get(wzdm).add(x);
				} else {
					List<Costinstrument> yy = new ArrayList<Costinstrument>();
					yy.add(x);
					xmybm.put(wzdm, yy);
				}
			}
		}
		this.p_ybm.put(uid, ym);
		// 成本项目信息
		List<Object> ybdl = new ArrayList<Object>();
		List<Object> xmdl = new ArrayList<Object>();
		ConcurrentHashMap<String, Double> itemjh = new ConcurrentHashMap<String, Double>();
		HashMap<String, Costitem> im = new HashMap<String, Costitem>();
		ConcurrentHashMap<String, String> bm = new ConcurrentHashMap<String, String>();
		ConcurrentHashMap<String, List<String>> flxm = new ConcurrentHashMap<String, List<String>>();
		List<Costitem> xmids = infos.getItemList();
		if (xmids != null) {
			Double dj, jhz;
			Integer lrfs;
			String flid, fllx;
			HashMap<String, Double> jgm = gcii.getItemPrice("priceConfig", tbrq);
			HashMap<String, Double> jhm = gcii.getItemPrice("costConfig", tbrq);
			int count = xmids.size();
			for (int i = 0; count > i; i++) {
				Costitem x = xmids.get(i);
				// 物料提供
				lrfs = x.getMaterialSupply();
				if (lrfs != null && lrfs == 1) {
					// 班组录入的项目才添加到批次数据
					wzdm = x.getId();
					erpcode = x.getErpcode();
					dj = x.getItemprice();
					flid = x.getPid();
					if (flid == null) {
						flid = "";
					}
					// 得到分类类型下的项目
					if (flm.containsKey(flid)) {
						fllx = flm.get(flid);
						if (flxm.containsKey(fllx)) {
							flxm.get(fllx).add(wzdm);
						} else {
							List<String> ml = new ArrayList<String>();
							ml.add(wzdm);
							flxm.put(fllx, ml);
						}
					}
					// 计划值
					if (jhm != null && jhm.containsKey(wzdm)) {
						jhz = jhm.get(wzdm);
						if (jhz == null) {
							jhz = 0.0;
						}
						itemjh.put(wzdm, jhz);
					}
					// 从价格维护
					if (jgm != null) {
						if (jgm.containsKey(erpcode)) {
							dj = jgm.get(erpcode);
						}
					}
					if (dj != null) {
						x.setItemprice(dj);
					}
					if (qywzm.size() > 0) {
						// 使用了方案时仅添加方案下的项目
						if (qywzm.containsKey(wzdm)) {
							ProgramLibraryCostItem faxm = qywzm.get(wzdm);
							if (faxm.getTmUsed() != null && faxm.getTmUsed() == 0) {
								bm.put(wzdm, "1");
							} else {
								x.setBaseConsumption(faxm.getBaseConsumption());
							}
						}
					}
					im.put(wzdm, x);
					// 开始添加仪表和项目数据
					if (bm.containsKey(wzdm)) {
						continue;// 不用的项目不添加
					} else {
						if ("0".equals(teamid)) {
							// 装置日报
							CostSummaryItemData xmd = new CostSummaryItemData();
							xmd.setId(TMUID.getUID());
							xmd.setUnitId(unitid);
							xmd.setProgramId(faid);
							xmd.setPid(pid);
							xmd.setItemId(wzdm);
							xmd.setWriteDay(tbrq);
							xmd.setTeamId("0");
							xmd.setReportType("DayReport");
							xmd.setItemPrice(x.getItemprice());
							xmd.setWorkingHour(24.0);
							xmd.setBaseUnitConsumption(x.getBaseConsumption());
							xmdl.add(xmd);
						} else {
							// 交接班：添加项目下仪表
							if (xmybm.containsKey(wzdm)) {
								List<Costinstrument> ybl = xmybm.get(wzdm);
								if (ybl != null) {
									int countx = ybl.size();
									if (countx > 0) {
										// 添加项目
										if (isbatch) {
											CostBatchItemData xmd = new CostBatchItemData();
											xmd.setId(TMUID.getUID());
											xmd.setItemId(wzdm);
											xmd.setPid(pid);
											xmd.setBeginTime(kssj);
											xmd.setEndTime(jzsj);
											xmd.setProgramId(faid);
											xmd.setUnitId(unitid);
											xmd.setWorkingHour(gzsc);
											xmd.setWriteDay(tbrq);
											xmdl.add(xmd);
										} else {
											CostTeamItemData xmd = new CostTeamItemData();
											xmd.setId(TMUID.getUID());
											xmd.setUnitId(unitid);
											xmd.setProgramId(faid);
											xmd.setPid(pid);
											xmd.setItemId(wzdm);
											xmd.setWriteDay(tbrq);
											xmd.setSummaryDay(tjrq);
											xmd.setShiftId(shiftid);
											xmd.setTeamId(teamid);
											xmd.setBeginTime(kssj);
											xmd.setEndTime(jzsj);
											xmd.setItemPrice(x.getItemprice());
											xmd.setWorkingHour(gzsc);
											xmd.setBaseUnitConsumption(x.getBaseConsumption());
											xmdl.add(xmd);
										}
										// 添加仪表
										for (int h = 0; countx > h; h++) {
											Costinstrument yb = ybl.get(h);
											if (isbatch) {
												CostBatchInstrumentData ybd = new CostBatchInstrumentData();
												ybd.setId(TMUID.getUID());
												ybd.setUnitId(unitid);
												ybd.setPid(pid);
												ybd.setInstrumentId(yb.getId());
												ybd.setEndTime(jzsj);
												ybd.setProgramId(faid);
												ybdl.add(ybd);
											} else {
												CostTeamInstrumentData ybd = new CostTeamInstrumentData();
												ybd.setId(TMUID.getUID());
												ybd.setUnitId(unitid);
												ybd.setPid(pid);
												ybd.setInstrumentId(yb.getId());
												ybd.setProgramId(faid);
												ybd.setShiftId(shiftid);
												ybd.setTeamId(teamid);
												ybd.setWriteDay(tbrq);
												ybd.setShiftBeginTime(kssj);
												ybd.setShiftEndTime(jzsj);
												ybdl.add(ybd);
											}
										}
									}
								}
							} else {
								// 添加项目
								if (isbatch) {
									CostBatchItemData xmd = new CostBatchItemData();
									xmd.setId(TMUID.getUID());
									xmd.setItemId(wzdm);
									xmd.setPid(pid);
									xmd.setBeginTime(kssj);
									xmd.setEndTime(jzsj);
									xmd.setProgramId(faid);
									xmd.setUnitId(unitid);
									xmd.setWorkingHour(gzsc);
									xmd.setWriteDay(tbrq);
									xmdl.add(xmd);
								} else {
									CostTeamItemData xmd = new CostTeamItemData();
									xmd.setId(TMUID.getUID());
									xmd.setUnitId(unitid);
									xmd.setProgramId(faid);
									xmd.setPid(pid);
									xmd.setItemId(wzdm);
									xmd.setWriteDay(tbrq);
									xmd.setSummaryDay(tjrq);
									xmd.setShiftId(shiftid);
									xmd.setTeamId(teamid);
									xmd.setBeginTime(kssj);
									xmd.setEndTime(jzsj);
									xmd.setItemPrice(x.getItemprice());
									xmd.setWorkingHour(gzsc);
									xmd.setBaseUnitConsumption(x.getBaseConsumption());
									xmdl.add(xmd);
								}
							}
						}
					}
				}
			}
		}
		this.p_itemjh.put(uid, itemjh);
		this.p_im.put(uid, im);
		this.p_flxm.put(uid, flxm);
		this.p_ybdm.put(uid, ybdl);
		this.p_xmdm.put(uid, xmdl);

		// 核算指标
		List<Object> csdl = new ArrayList<Object>();
		ConcurrentHashMap<String, Costindicator> csm = new ConcurrentHashMap<String, Costindicator>();
		List<Costindicator> csl = infos.getIndicatorList();
		if (csl != null) {
			int count = csl.size();
			for (int i = 0; count > i; i++) {
				Costindicator x = csl.get(i);
				key1 = x.getId();
				key2 = x.getCpname();
				if (qyzbm.size() > 0) {
					// 使用了方案时仅添加方案下的项目
					if (qyzbm.containsKey(key2)) {
						ProgramLibraryCostItem faxm = qyzbm.get(key2);
						if (faxm.getTmUsed() != null && faxm.getTmUsed() == 0) {
							continue;// 方案下不使用指标不添加
						} else {
							x.setStandardval(faxm.getBaseConsumption());
						}
					}
				}
				if (StringUtils.isNotEmpty(key1)) {
					csm.put(key1, x);
					if ("0".equals(teamid)) {
						// 日报
						CostSummaryParamData b = new CostSummaryParamData();
						b.setId(TMUID.getUID());
						b.setUnitId(unitid);
						b.setPid(pid);
						b.setParamId(key1);
						b.setProgramId(faid);
						b.setWriteDay(tbrq);
						b.setReportType("DayReport");
						b.setTeamId("0");
						b.setWorkingHour(24.0);
						b.setBaseVal(x.getStandardval());
						csdl.add(b);
					} else {
						// 交接班
						if (isbatch) {
							CostBatchParamData b = new CostBatchParamData();
							b.setId(TMUID.getUID());
							b.setUnitId(unitid);
							b.setPid(pid);
							b.setParamId(key1);
							b.setBeginTime(kssj);
							b.setEndTime(jzsj);
							b.setProgramId(faid);
							b.setWriteDay(tbrq);
							b.setWorkingHour(gzsc);
							b.setBaseVal(x.getStandardval());
							csdl.add(b);
						} else {
							CostTeamParamData b = new CostTeamParamData();
							b.setId(TMUID.getUID());
							b.setUnitId(unitid);
							b.setPid(pid);
							b.setParamId(key1);
							b.setBeginTime(kssj);
							b.setEndTime(jzsj);
							b.setTeamId(teamid);
							b.setShiftId(shiftid);
							b.setProgramId(faid);
							b.setWriteDay(tbrq);
							b.setWorkingHour(gzsc);
							b.setBaseVal(x.getStandardval());
							;
							csdl.add(b);
						}
					}
				}
			}
		}
		this.p_csm.put(uid, csm);
		this.p_csdm.put(uid, csdl);
		// 公式
		String gslx;
		ConcurrentHashMap<String, CostItemFormula> gm = new ConcurrentHashMap<String, CostItemFormula>();
		List<CostItemFormula> gsl = infos.getFormulaList();
		if (gsl != null) {
			int count = gsl.size();
			for (int i = 0; count > i; i++) {
				CostItemFormula x = gsl.get(i);
				key1 = x.getPid();
				if (StringUtils.isEmpty(key1)) {
					continue;
				}
				gslx = x.getFType();
				if (StringUtils.isEmpty(gslx)) {
					continue;
				}
				key2 = (new StringBuffer(key1)).append(".").append(gslx).toString();
				gm.put(key2, x);
			}
		}
		this.p_gm.put(uid, gm);
	}

	/**
	 * @category 批次的计算信息
	 * @return
	 */
	private void batchInfo(String uid, String unitid, String unitname, String sbsj, String xbsj, String kssj,
			String jzsj, String faid, String famc, String shiftid, String shiftname, String teamid, String teamname,
			String tbrq, String tjrq) {
		if (faid == null && "".equals(faid)) {
			faid = "0";
		}
		if (teamid == null && "".equals(teamid)) {
			teamid = "0";
		}
		if (shiftid == null && "".equals(shiftid)) {
			shiftid = "0";
		}
		CostBatchOnDuty b = new CostBatchOnDuty();
		b.setId(uid);
		b.setBatchNo(uid);
		b.setBeginTime(kssj);
		b.setEndTime(jzsj);
		b.setIsEnd(1);
		b.setPid(uid);
		b.setProgramId(faid);
		b.setProgramName(famc);
		b.setShiftBegintime(sbsj);
		b.setShiftEndtime(xbsj);
		b.setShiftId(shiftid);
		b.setShiftName(shiftname);
		b.setTeamId(teamid);
		b.setTeamName(teamname);
		b.setSummaryDay(tjrq);
		b.setUnitId(unitid);
		b.setUnitName(unitname);
		b.setWriteDay(tbrq);
		this.p_dutyinfo.put(uid, b);
	}

	/**
	 * @category 交接班计算信息
	 * @return
	 */
	private CostTeamInfo calcInfo(String uid, String unitid, String kssj, String jzsj, String faid, String shiftid,
			String teamid, String tbrq, String tjrq) {
		// 旧的备注内容
		if (faid == null && "".equals(faid)) {
			faid = "0";
		}
		if (teamid == null && "".equals(teamid)) {
			teamid = "0";
		}
		if (shiftid == null && "".equals(shiftid)) {
			shiftid = "0";
		}
		Where where = Where.create();
		where.eq(CostTeamInfo::getWriteDay, tbrq);
		where.eq(CostTeamInfo::getUnitId, unitid);
		where.eq(CostTeamInfo::getProgramId, faid);
		where.eq(CostTeamInfo::getShiftId, shiftid);
		where.eq(CostTeamInfo::getTeamId, teamid);
		CostTeamInfo zbxx = this.entityService.queryObject(CostTeamInfo.class, where, null);
		String oremark = "", oid;
		if (zbxx != null) {
			oremark = zbxx.getRemark();
			oid = zbxx.getId();
		} else {
			oid = uid;
		}
		this.p_infom.put(uid, oid);
		CostTeamInfo b = new CostTeamInfo();
		b.setId(oid);
		b.setUnitId(unitid);
		b.setWriteDay(tbrq);
		b.setSummaryDay(tjrq);
		b.setProgramId(faid);
		b.setShiftId(shiftid);
		b.setTeamId(teamid);
		b.setBeginTime(kssj);
		b.setEndTime(jzsj);
		b.setRemark(oremark);
		return b;
	}

	/**
	 */
	private CostSummaryInfo calcInfo(String uid, String unitid, String faid, String tbrq) {
		// 旧的备注内容
		if (faid == null && "".equals(faid)) {
			faid = "0";
		}
		Where where = Where.create();
		where.eq(CostSummaryInfo::getReportNo, tbrq);
		where.eq(CostSummaryInfo::getUnitId, unitid);
		where.eq(CostSummaryInfo::getProgramId, faid);
		CostSummaryInfo zbxx = this.entityService.queryObject(CostSummaryInfo.class, where, null);
		String oremark = "", oid;
		if (zbxx != null) {
			oremark = zbxx.getRemark();
			oid = zbxx.getId();
		} else {
			oid = uid;
		}
		this.p_infom.put(uid, oid);
		CostSummaryInfo b = new CostSummaryInfo();
		b.setId(oid);
		b.setUnitId(unitid);
		b.setReportNo(tbrq);
		b.setProgramId(faid);
		b.setRemark(oremark);
		return b;
	}

	/**
	 * @category 方案下的核算仪表数据
	 */
	private void instrumentProgramData(String unitid, String uid, String sbsj, String xbsj, String faid, String tbrq,
			String tenantId, String shiftid, String teamid) {
		if (this.p_ybdm.containsKey(uid)) {
			@SuppressWarnings("unchecked")
			List<CostBatchInstrumentData> ybl = (List<CostBatchInstrumentData>) this.p_ybdm.get(uid);
			if (ybl != null) {
				// 有仪表数据时才继续
				int count = ybl.size();
				if (count > 0) {
					// 获取前后表数
					Double qbs, hbs;
					String ybid;
					FetchRealTimeDataDTO frdt = new FetchRealTimeDataDTO();
					frdt.setUnitId(unitid);
					frdt.setKssj(sbsj);
					frdt.setJzsj(xbsj);
					frdt.setProgramId(faid);
					frdt.setTbrq(tbrq);
					frdt.setShiftId(shiftid);
					frdt.setTENANT_ID(tenantId);
					HashMap<String, InstructmentDataVo> ybbsm = this.igsds.getProgramData(frdt);
					if (ybbsm != null) {
						for (int i = 0; count > i; i++) {
							CostBatchInstrumentData x = ybl.get(i);
							ybid = x.getInstrumentId();
							if (ybbsm.containsKey(ybid)) {
								InstructmentDataVo v = ybbsm.get(ybid);
								// 前表
								qbs = v.getPreviousReadOut();
								if (qbs == null) {
									qbs = 0.0;
								}
								x.setPreviousReadOut(qbs);
								x.setPreviousReadTime(v.getPreviousReadTime());
								// 后表
								hbs = v.getLastReadOut();
								if (hbs == null || hbs.compareTo(0.0) == 0) {
									// 没有后表数据，后表默认等于前表
									hbs = qbs;
								}
								x.setLastReadOut(hbs);
								x.setLastReadTime(v.getLastReadTime());
							}
						}
					}
				}
			}
		}
	}

	/**
	 * @category 普通交接班的核算仪表数据
	 */
	private void instrumentData(String unitid, String uid, String sbsj, String xbsj, String faid, String tbrq,
			String tenantId, CalcTDSParam ctsp, String shiftid, String teamid) {
		if (this.p_ybdm.containsKey(uid)) {
			ConcurrentHashMap<String, String> vm = this.p_valm.get(uid);
			if (vm == null) {
				vm = new ConcurrentHashMap<String, String>();
				this.p_valm.put(uid, vm);
			}
			@SuppressWarnings("unchecked")
			List<CostTeamInstrumentData> ybl = (List<CostTeamInstrumentData>) this.p_ybdm.get(uid);
			if (ybl != null) {
				// 有仪表数据时才继续
				int count = ybl.size();
				if (count > 0) {
					// 获取前后表数
					Double qbs, hbs, val;
					String ybid, key, keyx, sval;
					FetchRealTimeDataDTO frdt = new FetchRealTimeDataDTO();
					frdt.setUnitId(unitid);
					frdt.setKssj(sbsj);
					frdt.setJzsj(xbsj);
					frdt.setProgramId(faid);
					frdt.setShiftId(shiftid);
					frdt.setTbrq(tbrq);
					frdt.setTENANT_ID(tenantId);
					HashMap<String, InstructmentDataVo> ybbsm = this.igsds.getShiftData(frdt);
					if (ybbsm != null) {
						for (int i = 0; count > i; i++) {
							CostTeamInstrumentData x = ybl.get(i);
							ybid = x.getInstrumentId();
							key = (new StringBuffer(unitid).append(".").append(ybid)).toString();
							if (ybbsm.containsKey(ybid)) {
								InstructmentDataVo v = ybbsm.get(ybid);
								// 前表
								qbs = v.getPreviousReadOut();
								if (qbs == null) {
									qbs = 0.0;
								}
								x.setPreviousReadOut(qbs);
								keyx = (new StringBuffer(key).append(".qbs")).toString();
								vm.put(keyx, String.valueOf(qbs));
								x.setPreviousReadTime(v.getPreviousReadTime());
								// 后表
								hbs = v.getLastReadOut();
								if (hbs == null || hbs.compareTo(0.0) == 0) {
									// 没有后表数据，后表默认等于前表
									hbs = qbs;
								}
								x.setLastReadOut(hbs);
								keyx = (new StringBuffer(key).append(".hbs")).toString();
								vm.put(keyx, String.valueOf(hbs));
								x.setLastReadTime(v.getLastReadTime());
							} else {
								keyx = (new StringBuffer(key).append(".qbs")).toString();
								vm.put(keyx, "0");
								keyx = (new StringBuffer(key).append(".hbs")).toString();
								vm.put(keyx, "0");
							}
						}
					}
					// 计算仪表消耗
					ConcurrentHashMap<String, CostItemFormula> gsm = this.p_gm.get(uid);
					if (gsm == null) {
						gsm = new ConcurrentHashMap<String, CostItemFormula>();
						this.p_gm.put(uid, gsm);
					}
					for (int i = 0; count > i; i++) {
						CostTeamInstrumentData x = ybl.get(i);
						ybid = x.getInstrumentId();
						key = (new StringBuffer(ybid).append(".dbxh")).toString();
						val = this.calcformula(key, ctsp, "", ybid, unitid, teamid, shiftid, sbsj, xbsj, tbrq, faid,
								uid, gsm);
						x.setCalcVal(val);
						x.setWriteVal(val);
						sval = String.valueOf(val);
						keyx = (new StringBuffer(unitid).append(".").append(ybid).append(".dbxhl")).toString();
						vm.put(keyx, sval);
						keyx = (new StringBuffer(ybid).append(".dbxhl")).toString();
						vm.put(keyx, sval);
					}
				}
			}
		}
	}

	/**
	 * @category 核算项目数据
	 */
	private void itemData(String unitid, String uid, String teamid, String shiftid, String kssj, String jzsj,
			String tbrq, String faid, CalcTDSParam ctsp) {
		if (this.p_xmdm.containsKey(uid)) {
			ConcurrentHashMap<String, String> vm = this.p_valm.get(uid);
			if (vm == null) {
				vm = new ConcurrentHashMap<String, String>();
				this.p_valm.put(uid, vm);
			}
			@SuppressWarnings("unchecked")
			List<CostTeamItemData> xml = (List<CostTeamItemData>) this.p_xmdm.get(uid);
			if (xml != null) {
				int count = xml.size();
				if (count > 0) {
					Double val;
					String xmid, key, keyx, sval;
					// 暂存的公式
					ConcurrentHashMap<String, CostItemFormula> gsm = this.p_gm.get(uid);
					if (gsm == null) {
						gsm = new ConcurrentHashMap<String, CostItemFormula>();
						this.p_gm.put(uid, gsm);
					}
					// 先计算消耗耗量
					for (int i = 0; count > i; i++) {
						CostTeamItemData x = xml.get(i);
						xmid = x.getItemId();
						val = this.calcformula((new StringBuffer(xmid).append(".hxl")).toString(), ctsp, xmid, "",
								unitid, teamid, shiftid, kssj, jzsj, tbrq, faid, uid, gsm);
						x.setConsumption(val);
						x.setWriteConsumption(val);
						sval = String.valueOf(val);
						key = (new StringBuffer(unitid).append(".").append(xmid)).toString();
						keyx = (new StringBuffer(key).append(".xhl")).toString();
						vm.put(keyx, sval);
						keyx = (new StringBuffer(key).append(".cl")).toString();
						vm.put(keyx, sval);
						vm.put(xmid, sval);
					}
					// 计算项目其它公式
					for (int i = 0; count > i; i++) {
						CostTeamItemData x = xml.get(i);
						xmid = x.getItemId();
						// 考核消耗量
						val = this.calcformula((new StringBuffer(xmid).append(".khxhl")).toString(), ctsp, xmid, "",
								unitid, teamid, shiftid, kssj, jzsj, tbrq, faid, uid, gsm);
						x.setBaseConsumption(val);
						// 考核总成本
						val = this.calcformula((new StringBuffer(xmid).append(".khzcb")).toString(), ctsp, xmid, "",
								unitid, teamid, shiftid, kssj, jzsj, tbrq, faid, uid, gsm);
						x.setBaseCost(val);
						// 单耗
						val = this.calcformula((new StringBuffer(xmid).append(".dh")).toString(), ctsp, xmid, "",
								unitid, teamid, shiftid, kssj, jzsj, tbrq, faid, uid, gsm);
						x.setUnitConsumption(val);
						// 单位成本
						val = this.calcformula((new StringBuffer(xmid).append(".dwcb")).toString(), ctsp, xmid, "",
								unitid, teamid, shiftid, kssj, jzsj, tbrq, faid, uid, gsm);
						x.setUnitCost(val);
						// 总成本
						val = this.calcformula((new StringBuffer(xmid).append(".zcb")).toString(), ctsp, xmid, "",
								unitid, teamid, shiftid, kssj, jzsj, tbrq, faid, uid, gsm);
						x.setItemCost(val);
					}
				}
			}
		}
	}

	/**
	 * @category 日报核算项目数据
	 */
	private void dayItemData(String unitid, String uid, String tbrq, String faid, CalcTDSParam ctsp) {
		if (this.p_xmdm.containsKey(uid)) {
			ConcurrentHashMap<String, String> vm = this.p_valm.get(uid);
			if (vm == null) {
				vm = new ConcurrentHashMap<String, String>();
				this.p_valm.put(uid, vm);
			}
			@SuppressWarnings("unchecked")
			List<CostSummaryItemData> xml = (List<CostSummaryItemData>) this.p_xmdm.get(uid);
			if (xml != null) {
				int count = xml.size();
				if (count > 0) {
					Double val;
					String xmid, itemid, key, keyx, sval;
					// 暂存的公式
					ConcurrentHashMap<String, CostItemFormula> gsm = this.p_gm.get(uid);
					if (gsm == null) {
						gsm = new ConcurrentHashMap<String, CostItemFormula>();
						this.p_gm.put(uid, gsm);
					}
					HashMap<String, Costitem> im = this.p_im.get(uid);
					if (im == null) {
						im = new HashMap<String, Costitem>();
						this.p_im.put(uid, im);
					}
					ConcurrentHashMap<String, String> armm = this.p_alrm.get(uid);
					if (armm == null) {
						armm = new ConcurrentHashMap<String, String>();
						this.p_alrm.put(uid, armm);
					}
					ConcurrentHashMap<String, Double> subl = this.p_subitem.get(uid);
					if (subl == null) {
						subl = new ConcurrentHashMap<String, Double>();
						this.p_subitem.put(uid, subl);
					}
					if (!armm.containsKey(unitid + ".SUBUNITCONSUMPTION")) {
						// 未检索过数据，检索数据
						List<ItemConsumption> dd = gics.getSubUnitItemConsumption(unitid, tbrq, null, "0", faid, im);
						if (dd != null) {
							int xcount = dd.size();
							for (int i = 0; xcount > i; i++) {
								ItemConsumption d = dd.get(i);
								subl.put(d.getItemId(), d.getConsumption());
							}
						}
						armm.put(unitid + ".SUBUNITCONSUMPTION", "1");
					}
					for (int i = 0; count > i; i++) {
						CostSummaryItemData x = xml.get(i);
						xmid = x.getItemId();
						val = 0.0;
						if (im != null && im.containsKey(xmid)) {
							itemid = im.get(xmid).getItemid();
							if (subl.containsKey(itemid)) {
								val = subl.get(itemid);
							}
						}
						x.setConsumption(val);
						x.setWriteConsumption(val);
						sval = String.valueOf(val);
						key = (new StringBuffer(unitid).append(".").append(xmid)).toString();
						keyx = (new StringBuffer(key).append(".xhl")).toString();
						vm.put(keyx, sval);
						keyx = (new StringBuffer(key).append(".cl")).toString();
						vm.put(keyx, sval);
						vm.put(xmid, sval);
					}
					// 计算项目其它公式
					for (int i = 0; count > i; i++) {
						CostSummaryItemData x = xml.get(i);
						xmid = x.getItemId();
						// 考核消耗量
						val = this.calcformula((new StringBuffer(xmid).append(".khxhl")).toString(), ctsp, xmid, "",
								unitid, "0", null, null, null, tbrq, faid, uid, gsm);
						x.setBaseConsumption(val);
						// 考核总成本
						val = this.calcformula((new StringBuffer(xmid).append(".khzcb")).toString(), ctsp, xmid, "",
								unitid, "0", null, null, null, tbrq, faid, uid, gsm);
						x.setBaseCost(val);
						// 单耗
						val = this.calcformula((new StringBuffer(xmid).append(".dh")).toString(), ctsp, xmid, "",
								unitid, "0", null, null, null, tbrq, faid, uid, gsm);
						x.setUnitConsumption(val);
						// 单位成本
						val = this.calcformula((new StringBuffer(xmid).append(".dwcb")).toString(), ctsp, xmid, "",
								unitid, "0", null, null, null, tbrq, faid, uid, gsm);
						x.setUnitCost(val);
						// 总成本
						val = this.calcformula((new StringBuffer(xmid).append(".zcb")).toString(), ctsp, xmid, "",
								unitid, "0", null, null, null, tbrq, faid, uid, gsm);
						x.setItemCost(val);
					}
				}
			}
		}
	}

	/**
	 * @category 核算指标数据
	 */
	private void indicatorData(String unitid, String uid, String teamid, String shiftid, String kssj, String jzsj,
			String tbrq, String faid, CalcTDSParam ctsp) {
		if (this.p_csdm.containsKey(uid)) {
			@SuppressWarnings("unchecked")
			List<CostTeamParamData> csl = (List<CostTeamParamData>) this.p_csdm.get(uid);
			if (csl != null) {
				int count = csl.size();
				if (count > 0) {
					Double val;
					String csid;
					// 暂存的公式
					ConcurrentHashMap<String, CostItemFormula> gsm = this.p_gm.get(uid);
					if (gsm == null) {
						gsm = new ConcurrentHashMap<String, CostItemFormula>();
						this.p_gm.put(uid, gsm);
					}
					// 先计算消耗耗量
					for (int i = 0; count > i; i++) {
						CostTeamParamData x = csl.get(i);
						csid = x.getParamId();
						val = this.calcformula((new StringBuffer(csid).append(".param")).toString(), ctsp, "", "",
								unitid, teamid, shiftid, kssj, jzsj, tbrq, faid, uid, gsm);
						x.setCalcVal(val);
					}
				}
			}
		}
	}

	private void dayIndicatorData(String unitid, String uid, String tbrq, String faid, CalcTDSParam ctsp) {
		if (this.p_csdm.containsKey(uid)) {
			@SuppressWarnings("unchecked")
			List<CostSummaryParamData> csl = (List<CostSummaryParamData>) this.p_csdm.get(uid);
			if (csl != null) {
				int count = csl.size();
				if (count > 0) {
					Double val;
					String csid;
					// 暂存的公式
					ConcurrentHashMap<String, CostItemFormula> gsm = this.p_gm.get(uid);
					if (gsm == null) {
						gsm = new ConcurrentHashMap<String, CostItemFormula>();
						this.p_gm.put(uid, gsm);
					}
					// 先计算消耗耗量
					for (int i = 0; count > i; i++) {
						CostSummaryParamData x = csl.get(i);
						csid = x.getParamId();
						val = this.calcformula((new StringBuffer(csid).append(".param")).toString(), ctsp, "", "",
								unitid, "0", null, null, null, tbrq, faid, uid, gsm);
						x.setCalcVal(val);
					}
				}
			}
		}
	}

	/**
	 * @category 保存数据
	 */
	@SuppressWarnings("unchecked")
	private void saveData(String uid, CostTeamInfo info) {
		if (this.p_infom.containsKey(uid)) {
			String pid = this.p_infom.get(uid);
			if (this.iopsd.deleteTeamDayParamData(pid)) {
				if (this.iopsd.deleteTeamDayItemData(pid)) {
					if (this.iopsd.deleteTeamDayYbData(pid)) {
						if (this.iopsd.deleteTeamDayCalcInfo(pid)) {
							if (this.iopsd.insertTeamDayCalcInfo(info) == false) {
								log.error("", "新增交接班信息出现错误，信息：" + JSON.toJSONString(info));
							}
							if (this.p_ybdm.containsKey(uid)) {
								List<CostTeamInstrumentData> ybl = (List<CostTeamInstrumentData>) this.p_ybdm.get(uid);
								if (this.iopsd.insertTeamDayYbData(ybl) == false) {
									log.error("", "新增交接班仪表数据出现错误，信息：" + JSON.toJSONString(info));
								}
							}
							if (this.p_xmdm.containsKey(uid)) {
								List<CostTeamItemData> xml = (List<CostTeamItemData>) this.p_xmdm.get(uid);
								if (this.iopsd.insertTeamDayItemData(xml) == false) {
									log.error("", "新增交接班项目数据出现错误，信息：" + JSON.toJSONString(info));
								}
							}
							if (this.p_csdm.containsKey(uid)) {
								List<CostTeamParamData> csl = (List<CostTeamParamData>) this.p_csdm.get(uid);
								if (this.iopsd.insertTeamDayParamData(csl) == false) {
									log.error("", "新增交接班核算指标数据出现错误，信息：" + JSON.toJSONString(info));
								}
							}
						} else {
							log.error("", "删除旧的交接班信息出现错误，信息：" + JSON.toJSONString(info));
						}
					} else {
						log.error("", "删除旧的交接班仪表数据出现错误，信息：" + JSON.toJSONString(info));
					}
				} else {
					log.error("", "删除旧的交接班项目数据出现错误，信息：" + JSON.toJSONString(info));
				}
			} else {
				log.error("", "删除旧的交接班核算指标数据出现错误，信息：" + JSON.toJSONString(info));
			}
		}
	}

	/**
	 * @category 保存数据
	 */
	@SuppressWarnings("unchecked")
	private void saveDayData(String uid, CostSummaryInfo info) {
		if (this.p_infom.containsKey(uid)) {
			String pid = this.p_infom.get(uid);
			if (this.iopsd.deleteTeamDayParamData(pid)) {
				if (this.iopsd.deleteTeamDayItemData(pid)) {
					if (this.iopsd.deleteTeamDayCalcInfo(pid)) {
						if (this.iopsd.insertSummaryCalcInfo(info) == false) {
							log.error("", "新增交接班信息出现错误，信息：" + JSON.toJSONString(info));
						}
						if (this.p_xmdm.containsKey(uid)) {
							List<CostSummaryItemData> xml = (List<CostSummaryItemData>) this.p_xmdm.get(uid);
							if (this.iopsd.insertSummaryItemData(xml) == false) {
								log.error("", "新增交接班项目数据出现错误，信息：" + JSON.toJSONString(info));
							}
						}
						if (this.p_csdm.containsKey(uid)) {
							List<CostSummaryParamData> csl = (List<CostSummaryParamData>) this.p_csdm.get(uid);
							if (this.iopsd.insertSummaryParamData(csl) == false) {
								log.error("", "新增交接班核算指标数据出现错误，信息：" + JSON.toJSONString(info));
							}
						}
					} else {
						log.error("", "删除旧的交接班信息出现错误，信息：" + JSON.toJSONString(info));
					}
				} else {
					log.error("", "删除旧的交接班项目数据出现错误，信息：" + JSON.toJSONString(info));
				}
			} else {
				log.error("", "删除旧的交接班核算指标数据出现错误，信息：" + JSON.toJSONString(info));
			}
		}
	}

	/**
	 * @category 保存数据
	 */
	@SuppressWarnings("unchecked")
	private void saveProgramData(String uid) {
		CostBatchOnDuty info = this.p_dutyinfo.get(uid);
		if (info == null) {
			return;
		} else {
			if (this.iopsd.insertDutyInfo(info) == false) {
				log.error("", "新增交接班批次信息出现错误，信息：" + JSON.toJSONString(info));
				return;
			}
			if (this.p_ybdm.containsKey(uid)) {
				List<CostBatchInstrumentData> ybl = (List<CostBatchInstrumentData>) this.p_ybdm.get(uid);
				if (this.iopsd.insertInstrumentData(ybl) == false) {
					log.error("", "新增交接班pic仪表数据出现错误，信息：" + JSON.toJSONString(info));
				}
			}
		}
	}

	/**
	 * 获得匹配的变量
	 * 
	 * @param str
	 * @param rex
	 * @return
	 */
	private List<String> getBl(String str, String rex) {
		List<String> list = new ArrayList<String>();
		if (rex == null ? true : rex.trim().length() == 0)
			rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(str);
		while (m.find()) {
			list.add(m.group());
		}
		return list;
	}

	/**
	 * @category 替换公式参数
	 * @param sgs
	 * @param jscs
	 * @return
	 */

	private String calcFormula(String unitid, String sgs, String csid, String xmid, String ybid, String shiftid,
			String teamid, String kssj, String jzsj, String tbrq, String faid, CalcTDSParam ctsp, String uid) {
		if (StringUtils.isEmpty(sgs)) {
			return "0";
		}
		String calcResult = "0";
		String objid = unitid;
		if (csid != null && !"".equals(csid)) {
			// 根据参数指定的核算对象查找数据
			objid = csid;
		}

		Map<String, List<String>> resMap = AviatorUtils.getAllParams(sgs);
		// 所有变量列表（不包含数据源）
		List<String> varList = this.getBl(sgs, this._zbbl);
		sgs = sgs.replaceAll("[\\[\\]]", "");
		// 数据源列表（数据源别名，含$）
		List<String> tdsList = resMap.get("tds");
		// 数据源公式列表
		List<String> tdsFormulaList = resMap.get("tdsFormula");

		String dd = "0";
		Map<String, Object> valueMap = new HashMap<>();
		for (String var : varList) { // 获取变量值并赋值
			if ("$".equals(var.substring(0, 1)) || "if".equals(var) || "round".equals(var)
					|| this.pm.judgeDouble(var)) {
				// 数据源不解析
				continue;
			}
			dd = this.getValue(var, ctsp, teamid, objid, uid, shiftid, tbrq, kssj, jzsj, faid);
			valueMap.put(var.replaceAll("[\\[\\]]", ""), this.pm.convertDouble(dd, 0.0));
		}
		Map<String, Object> tdsValueMap = new HashMap<>();
		if (tdsList != null && tdsList.size() > 0) {
			TdsFormulaParamDTO p = new TdsFormulaParamDTO();
			p.setUnitId(objid);
			p.setUnitName("");
			p.setKssj(kssj);
			p.setJzsj(jzsj);
			p.setTbrq(tbrq);
			p.setShiftId(shiftid);
			p.setShiftName("");
			p.setTeamId(teamid);
			p.setTeamName("");
			p.setTENANT_ID("");
			p.setXmid(xmid);
			p.setXmmc("");
			p.setYbid(ybid);
			p.setYbmc("");
			ctsp.TDSRetrieve(tdsList, p);
			for (String param : tdsFormulaList) {
				Object tdsValue = null;
				try {
					tdsValue = ctsp.replaceDsFormula(param, p);
				} catch (Exception e) {
				}
				tdsValueMap.put(param, tdsValue);
			}
		}
		try {
			AviatorResult ar = AviatorUtils.execute(sgs, valueMap, tdsValueMap);
			calcResult = String.valueOf(ar.getResult());
			// 计算结果 String jg=ar.getExpression();
		} catch (Exception e) {
			calcResult = "0";
		}
		return calcResult;
	}

	/**
	 * @category 获取分类项目量
	 * @param wzdm
	 * @param reportinfo
	 * @param teamid
	 * @return
	 */
	private String getFlXml(String fllx, ConcurrentHashMap<String, List<String>> flxm, String teamid,
			ConcurrentHashMap<String, String> vm) {
		Double lj = 0.0;
		List<String> xxl = flxm.get(fllx);
		if (xxl != null) {
			for (String xx : xxl) {
				// 普通报表数据解析，直接用项目ID
				if (vm.containsKey(xx)) {
					lj = lj + pm.convertDouble(vm.get(xx), 0.0);
				}
			}
		}
		return String.valueOf(lj);
	}

	/**
	 * @category 获取分类的总成本
	 * @param fllx
	 * @param flxm
	 * @param teamid
	 * @param vm
	 * @param reportinfo
	 * @param ctsp
	 * @param unitid
	 * @return
	 */
	private String getFlZcb(String fllx, ConcurrentHashMap<String, List<String>> flxm, String teamid, String shiftid,
			String tbrq, String kssj, String jzsj, CalcTDSParam ctsp, String unitid, String uid, String faid) {
		Double lj = 0.0;
		String key, val;
		List<String> xxl = flxm.get(fllx);
		if (xxl != null) {
			ConcurrentHashMap<String, CostItemFormula> gsm = this.p_gm.get(uid);
			if (gsm == null) {
				gsm = new ConcurrentHashMap<String, CostItemFormula>();
				this.p_gm.put(uid, gsm);
			}
			for (String xx : xxl) {
				// 普通报表数据解析，直接用项目ID
				key = (new StringBuffer(xx).append(".zcb")).toString();
				if (gsm.containsKey(key)) {
					CostItemFormula gs = gsm.get(key);
					if (gs != null) {
						val = this.calcFormula(unitid, gs.getFormula(), unitid, "", "", shiftid, teamid, kssj, jzsj,
								tbrq, faid, ctsp, uid);
						lj = lj + pm.convertDouble(val, 0.0);
					}
				}
			}
		}
		return String.valueOf(lj);
	}

	/**
	 * @category 获取分类的综合能耗
	 * @param fllx
	 * @param flxm
	 * @param teamid
	 * @param vm
	 * @param reportinfo
	 * @param ctsp
	 * @param unitid
	 * @return
	 */
	private String getFlZhnh(String fllx, ConcurrentHashMap<String, List<String>> flxm, String teamid, String shiftid,
			String tbrq, String kssj, String jzsj, CalcTDSParam ctsp, String unitid, String uid, String faid) {
		Double lj = 0.0, dv, nv;
		String key;
		List<String> xxl = flxm.get(fllx);
		if (xxl != null) {
			ConcurrentHashMap<String, CostItemFormula> gsm = this.p_gm.get(uid);
			if (gsm == null) {
				gsm = new ConcurrentHashMap<String, CostItemFormula>();
				this.p_gm.put(uid, gsm);
			}
			HashMap<String, Costitem> im = this.p_im.get(uid);
			if (im == null) {
				im = new HashMap<String, Costitem>();
				this.p_im.put(uid, im);
			}
			for (String xx : xxl) {
				// 普通报表数据解析，直接用项目ID
				if (im.containsKey(xx)) {
					Costitem tr = im.get(xx);
					nv = tr.getEnergyfactor();// 能耗系数
					if (nv == null) {
						nv = 0.0;
					}
					key = (new StringBuffer(xx).append(".dh")).toString();
					if (gsm.containsKey(key)) {
						CostItemFormula gs = gsm.get(key);
						if (gs != null) {
							dv = this.pm.convertDouble(this.calcFormula(unitid, gs.getFormula(), unitid, "", "",
									shiftid, teamid, kssj, jzsj, tbrq, faid, ctsp, uid), 0.0);
							lj = lj + dv * nv;
						}
					}
				}
			}
		}
		return String.valueOf(lj);
	}

	private String getXmSubInfo(String wzdm, String unitid, String uid, String tbrq, String shiftid, String teamid,
			String faid) {
		Double dv = 0.0;
		HashMap<String, Costitem> im = this.p_im.get(uid);
		if (im == null) {
			im = new HashMap<String, Costitem>();
			this.p_im.put(uid, im);
		}
		ConcurrentHashMap<String, String> armm = this.p_alrm.get(uid);
		if (armm == null) {
			armm = new ConcurrentHashMap<String, String>();
			this.p_alrm.put(uid, armm);
		}
		ConcurrentHashMap<String, Double> subl = this.p_subitem.get(uid);
		if (subl == null) {
			subl = new ConcurrentHashMap<String, Double>();
			this.p_subitem.put(uid, subl);
		}
		if (!armm.containsKey(unitid + ".SUBUNITCONSUMPTION")) {
			// 未检索过数据，检索数据
			List<ItemConsumption> dd = gics.getSubUnitItemConsumption(unitid, tbrq, shiftid, teamid, faid, im);
			if (dd != null) {
				int count = dd.size();
				for (int i = 0; count > i; i++) {
					ItemConsumption d = dd.get(i);
					subl.put(d.getItemId(), d.getConsumption());
				}
			}
			armm.put(unitid + ".SUBUNITCONSUMPTION", "1");
		}
		String itemid;
		if (im != null && im.containsKey(wzdm)) {
			itemid = im.get(wzdm).getItemid();
			if (subl.containsKey(itemid)) {
				dv = subl.get(itemid);
			}
		}
		if (dv == null) {
			dv = 0.0;
		}
		return String.valueOf(dv);
	}

	/**
	 * @category 取核算项目值
	 * @param wzdm       项目代码
	 * @param qzlx       取值类型
	 * @param reportinfo 制表信息
	 * @param teamid     班组代码
	 * @param ctsp       数据源解析
	 * @param unitid     核算对象ID
	 * @return
	 */
	private String getCostingData(String wzdm, String qzlx, String teamid, CalcTDSParam ctsp, String unitid, String uid,
			String shiftid, String tbrq, String kssj, String jzsj, String faid) {
		Double val = 0.0;
		String rtn = "0";
		ConcurrentHashMap<String, String> vm = this.p_valm.get(uid);
		if (vm == null) {
			vm = new ConcurrentHashMap<String, String>();
			this.p_valm.put(uid, vm);
		}
		ConcurrentHashMap<String, Costinstrument> ym = this.p_ybm.get(uid);
		if (ym == null) {
			ym = new ConcurrentHashMap<String, Costinstrument>();
			this.p_ybm.put(uid, ym);
		}
		HashMap<String, Costitem> im = this.p_im.get(uid);
		if (im == null) {
			im = new HashMap<String, Costitem>();
			this.p_im.put(uid, im);
		}

		ConcurrentHashMap<String, Double> xmjh = this.p_itemjh.get(uid);
		if (xmjh == null) {
			xmjh = new ConcurrentHashMap<String, Double>();
			this.p_itemjh.put(uid, xmjh);
		}
		if ("xhl".equals(qzlx) || "cl".equals(qzlx)) {
			ConcurrentHashMap<String, List<String>> flxm = this.p_flxm.get(uid);
			if (flxm == null) {
				flxm = new ConcurrentHashMap<String, List<String>>();
				this.p_flxm.put(uid, flxm);
			}
			if (flxm.containsKey(wzdm)) {
				rtn = this.getFlXml(wzdm, flxm, teamid, vm);
			} else {
				if ("".equals(teamid)) {
					// 普通报表数据解析，直接用项目ID
					if (vm.containsKey(wzdm)) {
						rtn = vm.get(wzdm);
					}
				} else {
					// 班组月汇总取消耗量，完全从此处取
					String key = (new StringBuffer(wzdm).append(".").append(teamid).append(".xhl")).toString();
					if (vm.containsKey(key)) {
						rtn = vm.get(key);
					}
				}
			}
		} else if ("zcb".equals(qzlx)) {
			// 只能是分类的
			ConcurrentHashMap<String, List<String>> flxm = this.p_flxm.get(uid);
			if (flxm == null) {
				flxm = new ConcurrentHashMap<String, List<String>>();
				this.p_flxm.put(uid, flxm);
			}
			if (flxm.containsKey(wzdm)) {
				rtn = this.getFlZcb(wzdm, flxm, teamid, shiftid, tbrq, kssj, jzsj, ctsp, unitid, uid, faid);
			}
		} else if ("zhnh".equals(qzlx)) {
			// 只能是分类的
			ConcurrentHashMap<String, List<String>> flxm = this.p_flxm.get(uid);
			if (flxm == null) {
				flxm = new ConcurrentHashMap<String, List<String>>();
				this.p_flxm.put(uid, flxm);
			}
			if (flxm.containsKey(wzdm)) {
				rtn = this.getFlZhnh(wzdm, flxm, teamid, shiftid, tbrq, kssj, jzsj, ctsp, unitid, uid, faid);
			}
		} else if ("xjxhl".equals(qzlx)) {
			// 只能是下级核算对象的消耗量
			rtn = this.getXmSubInfo(wzdm, unitid, uid, tbrq, shiftid, teamid, faid);
		} else if ("dbxhl".equals(qzlx)) {
			if (vm.containsKey(wzdm)) {
				rtn = vm.get(wzdm);
			}
		} else {
			if ("ybxs".equals(qzlx) || "yblc".equals(qzlx)) {
				if (ym.containsKey(wzdm)) {
					Costinstrument tr = ym.get(wzdm);
					if ("ybxs".equals(qzlx)) {
						val = tr.getConversionfactor();
						if (val == null) {
							val = 1.0;
						}
						rtn = String.valueOf(val);
					} else if ("yblc".equals(qzlx)) {
						val = tr.getInstrumentRange();
						if (val == null) {
							val = 0.0;
						}
						rtn = String.valueOf(val);
					} else {
						rtn = "0";
					}
				}
			} else {
				// 成本项目的
				if (im.containsKey(wzdm)) {
					Costitem tr = im.get(wzdm);
					if ("nkhz".equals(qzlx)) {
						val = 0.0;
					} else if ("khzcb".equals(qzlx)) {
						if (xmjh.containsKey(wzdm)) {// 优先使用录入选择的价格（平均价格）
							val = xmjh.get(wzdm);
						} else {
							val = 0.0;
						}
					} else if ("dj".equals(qzlx) || "khdj".equals(qzlx) || "bzdj".equals(qzlx)) {
						val = tr.getItemprice();
					} else if ("cpsl".equals(qzlx) || "khdh".equals(qzlx)) {
						val = tr.getBaseConsumption();
					} else if ("hsxs".equals(qzlx)) {
						val = tr.getConversionfactor();
					} else if ("nhxs".equals(qzlx)) {
						val = tr.getEnergyfactor();
					} else if ("dkhz".equals(qzlx)) {
						val = tr.getApportionmentfactor();
					} else {
						val = 0.0;
					}
				}
				if (val == null) {
					rtn = "0";
				} else {
					rtn = String.valueOf(val);
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 取参数值
	 * @param var
	 * @return
	 */

	private String getValue(String var, CalcTDSParam ctsp, String teamid, String unitid, String uid, String shiftid,
			String tbrq, String kssj, String jzsj, String faid) {
		String key;
		String rtn = "0";
		if (this.pm.judgeDouble(var)) {
			rtn = var;// 常数，直接返回
		} else {
			ConcurrentHashMap<String, String> vm = this.p_valm.get(uid);
			if (vm == null) {
				vm = new ConcurrentHashMap<String, String>();
				this.p_valm.put(uid, vm);
			}
			ConcurrentHashMap<String, Costindicator> csm = this.p_csm.get(uid);
			if (csm == null) {
				csm = new ConcurrentHashMap<String, Costindicator>();
				this.p_csm.put(uid, csm);
			}
			ConcurrentHashMap<String, CostItemFormula> gm = this.p_gm.get(uid);
			if (gm == null) {
				gm = new ConcurrentHashMap<String, CostItemFormula>();
				this.p_gm.put(uid, gm);
			}
			if (vm.containsKey(var)) {
				rtn = vm.get(var);
			} else {
				String[] s = var.split("\\.");
				if (s.length == 1) {
					key = var.toLowerCase();
					// 内定参数、核算参数或内置函数（if,round等）
					if ("bzgzsj".equals(key) || "[工作时长]".equals(key)) {
						if (this.p_gzscm.containsKey(uid)) {
							rtn = String.valueOf(this.p_gzscm.get(uid));
						}
					} else if ("bzdbcs".equals(key)) {
						return "0";
					} else if ("mdays".equals(key)) {
						rtn = String.valueOf(this.pm.getMdays(tbrq));
					} else if ("ydays".equals(key)) {
						rtn = String.valueOf(this.pm.getYdays(tbrq));
					} else {
						// 未定义的直接返回
						if (csm.containsKey(var)) {
							key = new StringBuffer(var).append(".param").toString();
							if (gm.containsKey(key)) {
								// 核算参数
								CostItemFormula param = gm.get(key);
								rtn = this.calcFormula(unitid, param.getFormula(), unitid, "", "", shiftid, teamid,
										kssj, jzsj, tbrq, faid, ctsp, uid);
							}
						} else {
							return var;
						}
					}
				} else if (s.length == 2) {
					key = new StringBuffer(s[1]).append(".param").toString();
					if (gm.containsKey(key)) {
						// 核算参数
						CostItemFormula param = gm.get(key);
						rtn = this.calcFormula(unitid, param.getFormula(), unitid, s[0], "", shiftid, teamid, kssj,
								jzsj, tbrq, faid, ctsp, uid);
					}
				} else if (s.length == 3) {
					// 核算项目
					rtn = this.getCostingData(s[1], s[2], teamid, ctsp, unitid, uid, shiftid, tbrq, kssj, jzsj, faid);
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 查找并计算成本项目公式返回Double
	 * @param findKey
	 * @return
	 */
	private Double calcformula(String findKey, CalcTDSParam ctsp, String xmid, String ybid, String unitid,
			String teamid, String shiftid, String kssj, String jzsj, String tbrq, String faid, String uid,
			ConcurrentHashMap<String, CostItemFormula> gsm) {
		Double jsz = 0.0;
		if (gsm.containsKey(findKey)) {
			CostItemFormula fb = gsm.get(findKey);
			if (fb != null) {
				String gs = this.pm.determineFormula(fb.getFormula());
				String val = this.calcFormula(unitid, gs, unitid, xmid, ybid, shiftid, teamid, kssj, jzsj, tbrq, faid,
						ctsp, uid);
				jsz = this.pm.convertDouble(val, 0.0);
			}
		}
		return jsz;
	}

	@Override
	public String calcTask(String unitid, String shiftid, String teamid, String writeday) {
		String rtn = "";
		try {
			log.info("交接班计算任务开始", "计算任务10" + DateTimeUtils.getDTStr());
			log.info("", "计算任务1" + DateTimeUtils.getDTStr());
			List<CostTeamInfo> til = this.iopsd.getTeamInfo(unitid, shiftid, teamid, writeday);
			log.info("", "计算任务2" + DateTimeUtils.getDTStr());
			if (til != null) {
				String summaryday, kssj, jzsj;
				for (CostTeamInfo i : til) {
					// 对象日报
					summaryday = i.getSummaryDay();
					TeamReportInputDto rbinfo = new TeamReportInputDto();
					rbinfo.setUnitId(unitid);
					rbinfo.setWriteDay(summaryday);
					rbinfo.setSummaryDay(summaryday);
					rbinfo.setOrgId("");
					log.info("", "计算任务4" + DateTimeUtils.getDTStr());
					this.checkDayData(rbinfo);// 整理日报数据，去除无效数据
					log.info("", "计算任务5" + DateTimeUtils.getDTStr());
					// 计算周报
					HashMap<String, String> tjsd = gettjsd.getReportPeriod(unitid, summaryday, "3");
					if (tjsd != null) {
						// 班组周报
						log.info("", "计算任务6" + DateTimeUtils.getDTStr());
						String yf = tjsd.get("yf");
						String zs = tjsd.get("zs");
						kssj = tjsd.get("ksrq");
						jzsj = tjsd.get("jzrq");
						// 对象周报
						TeamReportInputDto zbinfo = new TeamReportInputDto();
						zbinfo.setWriteDay(yf + "-" + zs);
						zbinfo.setUnitId(unitid);
						zbinfo.setBegintime(kssj);
						zbinfo.setEndtime(jzsj);
						zbinfo.setOrgId("");
						this.checkWeekData(zbinfo);// 整理周报数据，去除无效数据
						log.info("", "计算任务9" + DateTimeUtils.getDTStr());
					}
				}
				log.info("交接班计算任务结束", "计算任务10" + DateTimeUtils.getDTStr());
			}
		} catch (Exception e) {
			rtn = "err";
		}
		return rtn;
	}

	@Override
	public String calcDayReport(String unitid, String writeday) {
		String rtn = "";
		try {
			List<CostTeamInfo> til = this.iopsd.getReportInfo(unitid, writeday);
			if (til != null) {
				HashMap<String, String> fam = new HashMap<String, String>();
				String summaryday;
				for (CostTeamInfo i : til) {
					// 对象日报
					summaryday = i.getSummaryDay();
					if (fam.containsKey(summaryday)) {
						continue;
					} else {
						fam.put(summaryday, "1");
					}
					TeamReportInputDto rbinfo = new TeamReportInputDto();
					rbinfo.setUnitId(unitid);
					rbinfo.setWriteDay(summaryday);
					rbinfo.setSummaryDay(summaryday);
					rbinfo.setOrgId("");
					this.checkDayData(rbinfo);// 整理日报数据，去除无效数据
				}
				fam = null;
			}
		} catch (Exception e) {
			rtn = "err";
		}
		return rtn;
	}

	@Override
	public String calcWeekReport(String unitid, String writeday) {
		String rtn = "";
		try {
			List<CostTeamInfo> til = this.iopsd.getReportInfo(unitid, writeday);
			if (til != null) {
				HashMap<String, String> fam = new HashMap<String, String>();
				String summaryday, kssj, jzsj, yzs;
				for (CostTeamInfo i : til) {
					// 对象日报
					summaryday = i.getSummaryDay();
					if (!fam.containsKey(summaryday)) {
						// 计算周报
						HashMap<String, String> tjsd = gettjsd.getReportPeriod(unitid, summaryday, "3");
						if (tjsd != null) {
							// 班组周报
							String yf = tjsd.get("yf");
							String zs = tjsd.get("zs");
							kssj = tjsd.get("ksrq");
							jzsj = tjsd.get("jzrq");
							yzs = (new StringBuffer(yf).append("-").append(zs)).toString();
							TeamReportInputDto info = new TeamReportInputDto();
							info.setWriteDay(yzs);
							info.setUnitId(unitid);
							info.setBegintime(kssj);
							info.setEndtime(jzsj);
							this.checkWeekData(info);// 整理周报数据，去除无效数据
						}
						fam.put(summaryday, summaryday);
					}
				}
				fam = null;
			}
		} catch (Exception e) {
			rtn = "err";
		}
		return rtn;
	}

	@Override
	public String batchCalc(String unitid, String ksrq, String jzrq, String refetch) {
		String rtn = "";
		try {
			MethodQueryDto qdto = new MethodQueryDto();
			qdto.setUnitid(unitid);// 核算对象ID
			qdto.setObjType("org");
			List<Costunitoperator> listCzjg = unitMeth.getCostunitoperatorList(qdto);
			List<String> listOrg = new ArrayList<String>();
			if (StringUtils.isNotEmpty(listCzjg)) {
				for (Costunitoperator temp : listCzjg) {
					listOrg.add(temp.getObjid());
				}
			}
			String nowDt = DateTimeUtils.getNowDateTimeStr();
			List<ShiftForeignVo> listShift = new ArrayList<ShiftForeignVo>();
			if (listOrg != null && listOrg.size() > 0) {
				String sbsj, xbsj, bzdm, bcdm, bcmc, bzmc, tbrq, tjrq, faid, famc;
				listShift = shift.getShiftDataByksrqjzrq(listOrg, ksrq, jzrq);
				if (listShift != null) {
					if ("1".equals(refetch)) {
						// 重新采集数据
						for (ShiftForeignVo yn : listShift) {
							sbsj = yn.getSbsj();
							if (DateTimeUtils.bjDate(DateTimeUtils.parseDate(nowDt),
									DateTimeUtils.parseDate(sbsj)) <= 0) {
								// 当前时间比批次结束时间晚，使用当前时间
								continue;
							}
							bzdm = yn.getOrgCode();
							bzmc = yn.getOrgName();
							bcdm = yn.getShiftClassCode();
							bcmc = yn.getShiftClassName();
							xbsj = yn.getXbsj();
							tbrq = yn.getTbrq();
							tjrq = yn.getTjsj();
							this.deleteBatchData(unitid, bcdm, bzdm, tbrq);// 先清除旧的批次数据
							LinkedHashMap<String, List<ProductScheduPlanStart>> faMap = this.runStateService
									.getUnitProgByksrqjzrq(unitid, sbsj, xbsj);
							if (faMap != null && faMap.size() > 0) {
								// 有方案切换的交接班
								List<ProductScheduPlanStart> fal = faMap.get(unitid);
								if (fal != null) {
									int facount = fal.size();
									// 每一次切换的数据
									for (int h = 0; facount > h; h++) {
										ProductScheduPlanStart y = fal.get(h);
										faid = y.getProgramid();
										if (faid != null) {
											// 计算方案数据
											famc = y.getProgramname();
											this.calcProgramData(unitid, bcdm, bcmc, bzdm, bzmc, sbsj, xbsj,
													y.getStartdatetime(), y.getEnddatetime(), faid, tbrq, tjrq, "0",
													TMUID.getUID(), "", famc, true);
										}
									}
									// 交接班计算
									TeamReportInputDto reportinfo = new TeamReportInputDto();
									reportinfo.setWriteDay(tbrq);
									reportinfo.setUnitId(unitid);
									reportinfo.setShiftId(bcdm);
									reportinfo.setTeamId(bzdm);
									reportinfo.setIsBatchCalc(true);
									this.ictpl.calcBatchProgramData(reportinfo);
								}
							} else {
								this.calcProgramData(unitid, bcdm, bcmc, bzdm, bzmc, sbsj, xbsj, sbsj, xbsj, "0", tbrq,
										tjrq, "0", TMUID.getUID(), "", "", true);
								// 交接班计算
								TeamReportInputDto reportinfo = new TeamReportInputDto();
								reportinfo.setWriteDay(tbrq);
								reportinfo.setUnitId(unitid);
								reportinfo.setShiftId(bcdm);
								reportinfo.setTeamId(bzdm);
								reportinfo.setIsBatchCalc(true);
								this.ictpl.calcBatchProgramData(reportinfo);
							}
						}
					} else {
						// 只是计算
						for (ShiftForeignVo yn : listShift) {
							sbsj = yn.getSbsj();
							if (DateTimeUtils.bjDate(DateTimeUtils.parseDate(nowDt),
									DateTimeUtils.parseDate(sbsj)) <= 0) {
								// 当前时间比批次结束时间晚，使用当前时间
								continue;
							}
							bzdm = yn.getOrgCode();
							bcdm = yn.getShiftClassCode();
							tbrq = yn.getTbrq();
							// 重新计算
							List<CostBatchOnDuty> dl = this.iopsd.getBatchOnDuty(unitid, bcdm, bzdm, tbrq);
							if (dl == null || dl.size() == 0) {
								// 当班未录入过，此时要采集数据
								bzmc = yn.getOrgName();
								bcmc = yn.getShiftClassName();
								xbsj = yn.getXbsj();
								tjrq = yn.getTjsj();
								LinkedHashMap<String, List<ProductScheduPlanStart>> faMap = this.runStateService
										.getUnitProgByksrqjzrq(unitid, sbsj, xbsj);
								if (faMap != null && faMap.size() > 0) {
									// 有方案切换的交接班
									List<ProductScheduPlanStart> fal = faMap.get(unitid);
									if (fal != null) {
										int facount = fal.size();
										// 每一次切换的数据
										for (int h = 0; facount > h; h++) {
											ProductScheduPlanStart y = fal.get(h);
											faid = y.getProgramid();
											if (faid != null) {
												// 计算方案数据
												famc = y.getProgramname();
												this.calcProgramData(unitid, bcdm, bcmc, bzdm, bzmc, sbsj, xbsj,
														y.getStartdatetime(), y.getEnddatetime(), faid, tbrq, tjrq, "0",
														TMUID.getUID(), "", famc, true);
											}
										}
										// 交接班计算
										TeamReportInputDto reportinfo = new TeamReportInputDto();
										reportinfo.setWriteDay(tbrq);
										reportinfo.setUnitId(unitid);
										reportinfo.setShiftId(bcdm);
										reportinfo.setTeamId(bzdm);
										reportinfo.setIsBatchCalc(true);
										this.ictpl.calcBatchProgramData(reportinfo);
									}
								} else {
									this.calcProgramData(unitid, bcdm, bcmc, bzdm, bzmc, sbsj, xbsj, sbsj, xbsj, "0",
											tbrq, tjrq, "0", TMUID.getUID(), "", "", true);
									// 交接班计算
									TeamReportInputDto reportinfo = new TeamReportInputDto();
									reportinfo.setWriteDay(tbrq);
									reportinfo.setUnitId(unitid);
									reportinfo.setShiftId(bcdm);
									reportinfo.setTeamId(bzdm);
									reportinfo.setIsBatchCalc(true);
									this.ictpl.calcBatchProgramData(reportinfo);
								}
							} else {
								// 交接班计算
								TeamReportInputDto reportinfo = new TeamReportInputDto();
								reportinfo.setWriteDay(tbrq);
								reportinfo.setUnitId(unitid);
								reportinfo.setShiftId(bcdm);
								reportinfo.setTeamId(bzdm);
								reportinfo.setIsBatchCalc(true);
								this.ictpl.calcBatchProgramData(reportinfo);
							}
						}
					}
				}
			}
		} catch (Exception e) {
			rtn = "err";
		}
		return rtn;
	}

	@Override
	public String reCalcShift(List<ProductUnitProgShift> shiftList) {
		String rtn = "";
		try {
			if (shiftList != null && shiftList.size() > 0) {
				String unitid, key, sbsj, bzdm, bcdm, tbrq, faid, bcmc, tjrq, zq, kssj, jzsj, msg;
				List<TeamReportInputDto> dbl = new ArrayList<TeamReportInputDto>();
				HashMap<String, TeamReportInputDto> rchm = new HashMap<String, TeamReportInputDto>();
				HashMap<String, TeamReportInputDto> zchm = new HashMap<String, TeamReportInputDto>();
				HashMap<String, HashMap<String, String>> zmm = new HashMap<String, HashMap<String, String>>();
				HashMap<String, List<ProductUnitProgShift>> dbfam = new HashMap<String, List<ProductUnitProgShift>>();
				String nowDt = DateTimeUtils.getNowDateTimeStr();
				msg = "工况切换后重新计算开始";
				log.info(msg, "统计时段1" + DateTimeUtils.getDTStr());
				// 第一次循环得到当班情况
				for (ProductUnitProgShift yn : shiftList) {
					sbsj = yn.getSbsj();
					if (DateTimeUtils.bjDate(DateTimeUtils.parseDate(nowDt), DateTimeUtils.parseDate(sbsj)) <= 0) {
						// 当前时间比批次结束时间晚，不计算
						continue;
					}
					unitid = yn.getUnitId();
					bzdm = yn.getOrgCode();
					bcdm = yn.getShiftClassCode();
					tbrq = yn.getTbrq();
					tjrq = yn.getTjrq();
					faid = yn.getProgramid();
					// 交接班
					key = (new StringBuffer(unitid).append(".").append(tbrq).append(".").append(bzdm).append(".")
							.append(bcdm)).toString();
					if (dbfam.containsKey(key)) {
						dbfam.get(key).add(yn);
					} else {
						TeamReportInputDto db = new TeamReportInputDto();
						db.setUnitId(unitid);
						db.setTeamId(bzdm);
						db.setShiftId(bcdm);
						db.setWriteDay(tbrq);
						db.setBatchNo(yn.getShiftClassName());
						db.setIsBatchCalc(true);
						dbl.add(db);
						List<ProductUnitProgShift> dd = new ArrayList<ProductUnitProgShift>();
						dd.add(yn);
						dbfam.put(key, dd);
					}
					// 日报
					key = (new StringBuffer(unitid).append(".").append(tjrq)).toString();
					if (!rchm.containsKey(key)) {
						TeamReportInputDto rbinfo = new TeamReportInputDto();
						rbinfo.setUnitId(unitid);
						rbinfo.setWriteDay(tjrq);
						rbinfo.setSummaryDay(tjrq);
						rbinfo.setOrgId("");
						rchm.put(key, rbinfo);
					}
					msg = "工况切换后重新计算";
					log.info(msg, "统计时段1" + DateTimeUtils.getDTStr());
					// 周报
					HashMap<String, String> tjsd = null;
					if (zmm.containsKey(tjrq)) {
						tjsd = zmm.get(tjrq);
					} else {
						tjsd = gettjsd.getReportPeriod(unitid, tjrq, "3");
						zmm.put(tjrq, tjsd);
					}
					log.info(msg, "统计时段2" + DateTimeUtils.getDTStr());
					if (tjsd != null) {
						String yf = tjsd.get("yf");
						String zs = tjsd.get("zs");
						kssj = tjsd.get("ksrq");
						jzsj = tjsd.get("jzrq");
						zq = yf + "-" + zs;
						key = (new StringBuffer(unitid).append(".").append(zq)).toString();
						if (!zchm.containsKey(key)) {
							TeamReportInputDto zbinfo = new TeamReportInputDto();
							zbinfo.setWriteDay(zq);
							zbinfo.setBnid(yf);
							zbinfo.setBatchNo(zs);
							zbinfo.setUnitId(unitid);
							zbinfo.setBegintime(kssj);
							zbinfo.setEndtime(jzsj);
							zchm.put(key, zbinfo);
						}
					}
				}
				// 第二次循环重新采集数据
				for (TeamReportInputDto yn : dbl) {
					unitid = yn.getUnitId();
					bzdm = yn.getTeamId();
					bcdm = yn.getShiftId();
					tbrq = yn.getWriteDay();
					bcmc = yn.getBatchNo();
					if (bcmc == null) {
						bcmc = "";
					}
					msg = "正在计算" + tbrq + bcmc + "的交接班";
					CostProgStartTimeDto dto = new CostProgStartTimeDto();
					dto.setUnitId(unitid);
					dto.setProgChangeFlagStr(msg);
					runStateService.setProrChangeRedisValue(dto);
					log.info(msg, "工况切换1" + DateTimeUtils.getDTStr());
					this.deleteBatchData(unitid, bcdm, bzdm, tbrq);// 先清除旧的批次数据
					log.info(msg, "工况切换2" + DateTimeUtils.getDTStr());
					key = (new StringBuffer(unitid).append(".").append(tbrq).append(".").append(bzdm).append(".")
							.append(bcdm)).toString();
					if (dbfam.containsKey(key)) {
						List<ProductUnitProgShift> dd = dbfam.get(key);
						if (dd != null) {
							for (ProductUnitProgShift yl : dd) {
								faid = yl.getProgramid();
								// 获取方案数据
								log.info(msg, "工况切换3" + DateTimeUtils.getDTStr());
								if (faid != null) {
									this.calcProgramData(unitid, bcdm, yl.getShiftClassName(), bzdm, "", yl.getSbsj(),
											yl.getXbsj(), yl.getStartTime(), yl.getEndTime(), faid, tbrq, yl.getTjrq(),
											"0", TMUID.getUID(), "", "", true);
								}
								log.info(msg, "工况切换4" + DateTimeUtils.getDTStr());
							}
						}
					}
					// 交接班计算
					log.info(msg, "工况切换5" + DateTimeUtils.getDTStr());
					this.ictpl.calcBatchProgramData(yn);
					log.info(msg, "工况切换6" + DateTimeUtils.getDTStr());
				}
				// 日报计算
				for (Entry<String, TeamReportInputDto> cyn : rchm.entrySet()) {
					TeamReportInputDto yn = cyn.getValue();
					msg = "正在计算" + yn.getWriteDay() + "的日报";
					CostProgStartTimeDto dto = new CostProgStartTimeDto();
					dto.setUnitId(yn.getUnitId());
					dto.setProgChangeFlagStr(msg);
					runStateService.setProrChangeRedisValue(dto);
					log.info(msg, "计算任务5" + DateTimeUtils.getDTStr());
					this.checkDayData(yn);// 整理日报数据，去除无效数据
					log.info(msg, "计算任务6" + DateTimeUtils.getDTStr());
				}
				// 周报计算
				for (Entry<String, TeamReportInputDto> cyn : zchm.entrySet()) {
					TeamReportInputDto yn = cyn.getValue();
					String yf = yn.getBnid();
					String zs = yn.getBatchNo();
					msg = "正在计算" + yf + "月，第" + zs + "周的周报";
					CostProgStartTimeDto dto = new CostProgStartTimeDto();
					dto.setUnitId(yn.getUnitId());
					dto.setProgChangeFlagStr(msg);
					runStateService.setProrChangeRedisValue(dto);
					log.info(msg, "计算任务9" + DateTimeUtils.getDTStr());
					this.checkWeekData(yn);// 整理周报数据，去除无效数据
					log.info(msg, "计算任务10" + DateTimeUtils.getDTStr());
				}
				msg = "工况切换后重新计算结束";
				log.info(msg, "统计时段2" + DateTimeUtils.getDTStr());
			}
		} catch (Exception e) {
			rtn = "err";
		}
		return rtn;
	}

	@Override
	public String checkDayData(TeamReportInputDto reportinfo) {
		String rtn = "";
		String unitid, tjrq, faid, id, msg;
		try {
			unitid = reportinfo.getUnitId();
			tjrq = reportinfo.getSummaryDay();
			msg = "正在计算" + tjrq + "的日报";
			log.info(msg, "日报计算开始" + DateTimeUtils.getDTStr());
			HashMap<String, String> mm = new HashMap<String, String>();
			// 得到统计日期内的当班数据
			List<CostTeamInfo> dbl = this.getTeamInfo(unitid, tjrq, tjrq);
			if (dbl != null) {
				for (CostTeamInfo yn : dbl) {
					faid = yn.getProgramId();
					mm.put(faid, "1");
				}
			}
			// 得到统计日期对应日报的数据
			List<CostSummaryInfo> rbl = this.getSummaryInfo(unitid, tjrq);
			if (rbl != null) {
				// 删除失效的日报
				for (CostSummaryInfo yn : rbl) {
					faid = yn.getProgramId();
					if (mm.containsKey(faid)) {
						// 有班组方案数据，重新计算
						continue;
					} else {
						// 班组没有方案数据时，要删除日报的对应数据
						id = yn.getId();
						if (id == null) {
							continue;
						}
						this.deleteSumDay(id);
					}
				}
			}
			for (Entry<String, String> m : mm.entrySet()) {
				faid = m.getKey();
				TeamReportInputDto rbinfo = new TeamReportInputDto();
				rbinfo.setUnitId(unitid);
				rbinfo.setProgramId(faid);
				rbinfo.setWriteDay(tjrq);
				rbinfo.setSummaryDay(tjrq);
				rbinfo.setOrgId("");
				log.info(msg, "日报计算1" + DateTimeUtils.getDTStr());
				this.ictpl.calcDeviceDayReportAuto(rbinfo);
				log.info(msg, "日报计算2" + DateTimeUtils.getDTStr());
			}
			log.info(msg, "日报计算结束" + DateTimeUtils.getDTStr());
		} catch (Exception e) {
			rtn = "err";
		}
		return rtn;
	}

	@Override
	public String checkWeekData(TeamReportInputDto reportinfo) {
		String rtn = "";
		String unitid, ksrq, jzrq, zs, faid, id, teamid, key, msg;
		try {
			unitid = reportinfo.getUnitId();
			zs = reportinfo.getWriteDay();// 周数
			ksrq = reportinfo.getBegintime().substring(0, 10);
			jzrq = reportinfo.getEndtime().substring(0, 10);
			msg = "正在计算" + zs + "的周报";
			log.info(msg, "周报计算开始" + DateTimeUtils.getDTStr());
			// 得到统计日期段内的当班数据
			HashMap<String, TeamReportInputDto> mm = new HashMap<String, TeamReportInputDto>();
			List<CostTeamInfo> dbl = this.getTeamInfo(unitid, ksrq, jzrq);
			if (dbl != null) {
				for (CostTeamInfo yn : dbl) {
					teamid = yn.getTeamId();
					faid = yn.getProgramId();
					TeamReportInputDto zz = new TeamReportInputDto();
					zz.setProgramId(faid);
					zz.setTeamId("0");
					mm.put(faid, zz);// 判断装置的
					key = (new StringBuffer(faid).append(".").append(teamid)).toString();
					TeamReportInputDto bz = new TeamReportInputDto();
					bz.setProgramId(faid);
					bz.setTeamId(teamid);
					mm.put(key, bz);// 判断班组的
				}
			}
			// 得到周数对应周报的数据
			List<CostSummaryInfo> rbl = this.getSummaryInfo(unitid, zs);
			if (rbl != null) {
				for (CostSummaryInfo yn : rbl) {
					teamid = yn.getTeamId();
					faid = yn.getProgramId();
					if (teamid == null || "".equals(teamid) || "0".equals(teamid)) {
						// 这种是装置的
						if (mm.containsKey(faid)) {
							continue;// 有班组方案数据，不处理
						} else {
							// 班组没有方案数据时，要删除日报的对应数据
							id = yn.getId();
							if (id == null) {
								continue;
							}
							this.deleteSumDay(id);
						}
					} else {
						// 班组的
						key = (new StringBuffer(faid).append(".").append(teamid)).toString();
						if (mm.containsKey(key)) {
							continue;// 有班组方案数据，不处理
						} else {
							// 班组没有方案数据时，要删除日报的对应数据
							id = yn.getId();
							if (id == null) {
								continue;
							}
							this.deleteSumDay(id);
						}
					}
				}
			}
			for (Entry<String, TeamReportInputDto> m : mm.entrySet()) {
				TeamReportInputDto ii = m.getValue();
				teamid = ii.getTeamId();
				faid = ii.getProgramId();
				TeamReportInputDto zbinfo = new TeamReportInputDto();
				zbinfo.setWriteDay(zs);
				zbinfo.setUnitId(unitid);
				zbinfo.setProgramId(faid);
				zbinfo.setTeamId(teamid);
				zbinfo.setBegintime(ksrq);
				zbinfo.setEndtime(jzrq);
				zbinfo.setOrgId("");
				log.info(msg, "周报计算1" + DateTimeUtils.getDTStr());
				this.ictpl.calcWeekReportAuto(zbinfo);
				log.info(msg, "周报计算2" + DateTimeUtils.getDTStr());
			}
			log.info(msg, "周报计算结束" + DateTimeUtils.getDTStr());
		} catch (Exception e) {
			rtn = "err";
		}
		return rtn;
	}

	private List<CostTeamInfo> getTeamInfo(String unitid, String ksrq, String jzrq) {
		Where where = Where.create();
		where.eq(CostTeamInfo::getUnitId, unitid);
		where.ge(CostTeamInfo::getSummaryDay, ksrq);
		where.le(CostTeamInfo::getSummaryDay, jzrq);
		return entityService.queryList(CostTeamInfo.class, where, null);
	}

	private List<CostSummaryInfo> getSummaryInfo(String unitid, String reportno) {
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, unitid);
		where.eq(CostSummaryInfo::getReportNo, reportno);
		return entityService.queryList(CostSummaryInfo.class, where, null);
	}

	private void deleteSumDay(String id) {
		this.iopsd.deleteSummaryParamData(id);
		this.iopsd.deleteSummaryItemData(id);
		this.iopsd.deleteSummaryInfo(id);
	}
}
