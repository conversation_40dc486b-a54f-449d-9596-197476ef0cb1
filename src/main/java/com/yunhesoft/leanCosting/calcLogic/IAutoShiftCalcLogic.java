package com.yunhesoft.leanCosting.calcLogic;

import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ProductUnitProgShift;

public interface IAutoShiftCalcLogic {

	/**
	 * @category 删除班次内已有的批次数据
	 * @param unitid   核算对象ID
	 * @param shiftid  班次ID
	 * @param teamid   班组ID
	 * @param writeday 填表日期
	 * @return
	 */
	public void deleteBatchData(String unitid, String shiftid, String teamid, String writeday);

	/**
	 * @category 计算班次内一个方案的交接班数据
	 * @param unitid     核算对象ID
	 * @param shiftid    班次ID
	 * @param shiftname  班次名称
	 * @param teamid     班组ID
	 * @param teamname   班组名称
	 * @param sbsj       上班时间
	 * @param xbsj       下班时间
	 * @param fakssj     方案的开始时间
	 * @param fajzsj     方案的截止时间
	 * @param faid       方案ID
	 * @param writeday   填表日期
	 * @param summaryday 汇总日期
	 * @param uid        计算暂存数据使用的uid
	 * @param unitname   核算对象名称
	 * @param famc       方案名称
	 * @param isbatch    批次计算：true 批次计算；false 方案的交接班计算
	 * @return
	 */
	public String calcProgramData(String unitid, String shiftid, String shiftname, String teamid, String teamname,
			String sbsj, String xbsj, String fakssj, String fajzsj, String faid, String writeday, String summaryday,
			String tenantId, String uid, String unitname, String famc, boolean isbatch);

	/**
	 * @category 计算日报
	 * @param unitid
	 * @param unitname
	 * @param summaryday
	 * @param faid
	 * @param famc
	 * @param tenantId
	 * @param uid
	 * @return
	 */
	public String calcDayReport(String unitid, String unitname, String summaryday, String faid, String famc,
			String tenantId, String uid);

	/**
	 * @category 交接班计算任务
	 * @param unitid
	 * @param shiftid
	 * @param teamid
	 * @param writeday
	 * @return
	 */
	public String calcTask(String unitid, String shiftid, String teamid, String writeday);

	/**
	 * @category 批量计算的日报计算
	 * @param unitid
	 * @param writeday
	 * @return
	 */
	public String calcDayReport(String unitid, String writeday);

	/**
	 * @category 批量计算的周报计算
	 * @param unitid
	 * @param writeday
	 * @return
	 */
	public String calcWeekReport(String unitid, String writeday);

	/**
	 * @category 批量计算
	 * @param unitid  核算对象
	 * @param ksrq    开始日期
	 * @param jzrq    截止日期
	 * @param refetch 重新获取实时数据：1 获取；0 不获取
	 * @return
	 */
	public String batchCalc(String unitid, String ksrq, String jzrq, String refetch);

	/**
	 * @category 切换方案后重新计算交接班
	 * @param tenantId  租户ID
	 * @param shiftList 切换记录
	 * @return
	 */
	public String reCalcShift(List<ProductUnitProgShift> shiftList);
	
	/**
	 * @category 检查日报数据：主要去除无效的方案数据
	 * @param reportinfo
	 * @return
	 */
	public String checkDayData(TeamReportInputDto reportinfo);

	/**
	 * @category 检查周报数据：主要去除无效的方案数据
	 * @param reportinfo
	 * @return
	 */
	public String checkWeekData(TeamReportInputDto reportinfo);

}
