package com.yunhesoft.leanCosting.costReport.controller;


import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbInternalDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostBbCodeNameVo;
import com.yunhesoft.leanCosting.costReport.service.CostBgcsszbService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@RestController
@Api(tags = "核算报表格式设置")
@RequestMapping("/CostBgcsszb")
public class CosBgcsszbController {
	@Autowired
	public HttpServletRequest request;
	@Autowired
	private CostBgcsszbService  costBgService;
	
    @ApiOperation("查询报表格式")
    @RequestMapping(value = "/getCostBgcsszbList", method = RequestMethod.POST)
    public Res<?> getCostBgcsszbList(@RequestBody CostBgcsszbDto param) {
        Res<List<CostBgcsszb>> res = new Res<List<CostBgcsszb>>();
        res.setResult(costBgService.getCostBgcsszbList(param));
        return res;
    }

    @ApiOperation("保存报表格式")
    @RequestMapping(value = "/saveCostBgcsszbdata", method = RequestMethod.POST)
    public Res<?> saveCostBgcsszbdata(@RequestBody CostBgcsszbDto param) {
        return Res.OK(costBgService.saveCostBgcsszbdata(param));
    }
    
    @ApiOperation("获取报表编码和名称")
    @RequestMapping(value = "/getBbNameCode", method = RequestMethod.POST)
    public Res<?> getBbNameCode() {
    	Res<List<CostBbCodeNameVo>> res = new Res<List<CostBbCodeNameVo>>();
        res.setResult(costBgService.getBbNameCode());
        return res;
    }
    
    @ApiOperation("保存内表头数据（包括启用和停用标识）")
    @RequestMapping(value = "/saveCostBgcsszbInternal", method = RequestMethod.POST)
    public Res<?> saveCostBgcsszbInternal(@RequestBody CostBgcsszbInternalDto param) {
        return Res.OK(costBgService.saveCostBgcsszbInternal(param));
    }
    
    @ApiOperation("获取内表头数据（包括启用和停用标识），按项目编码获取，给设置用）")
    @RequestMapping(value = "/getCostBgcsszbInternal", method = RequestMethod.POST)
    public Res<?> getCostBgcsszbInternal(@RequestBody CostBgcsszbInternalDto param) {
    	Res<CostBgcsszbInternalDto> res = new Res<CostBgcsszbInternalDto>();
    	res.setResult(costBgService.getCostBgcsszbInternal(param));
        return res;
    }
    
}
