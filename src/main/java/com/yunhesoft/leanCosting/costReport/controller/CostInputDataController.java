package com.yunhesoft.leanCosting.costReport.controller;

import java.util.LinkedHashMap;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBatchOnDutyDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostDataInputDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostUnitItemCountDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchOnDuty;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostDataInputVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostInputQueryStringVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostInputQueryVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostItemForWriteVo;
import com.yunhesoft.leanCosting.costReport.service.CostDataInputService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@RestController
@Api(tags = "核算数据录入接口")
@RequestMapping("/costInputData")
public class CostInputDataController {
	@Autowired
	public HttpServletRequest request;
	@Autowired
	private CostDataInputService costService;
	
	@Autowired
	public HttpServletResponse response;

//	@ApiOperation("查询核算录入数据")
//	@RequestMapping(value = "/getCostDataInput", method = RequestMethod.POST)
//	public Res<?> getPpiJobItemList(@RequestBody CostDataInputDto param) {
//		Res<CostDataInputDto> res = new Res<CostDataInputDto>();
//		res.setResult(costService.getCostDataInput(param));
//		return res;
//	}

	@ApiOperation("保存核算录入数据")
	@RequestMapping(value = "/saveCostInputData", method = RequestMethod.POST)
	public Res<?> saveCostInputData(@RequestBody CostDataInputDto param) {
		return Res.OK(costService.saveCostInputData(param));
	}

	@ApiOperation("核算数据录入点击获取数据按钮")
	@RequestMapping(value = "/getInputDataByBtn", method = RequestMethod.POST)
	public Res<?> getInputDataByBtn(@RequestBody CostDataInputDto param) {
		return Res.OK(costService.getInputDataByBtn(param));
	}

	@ApiOperation("按批次查询核算录入数据")
	@RequestMapping(value = "/CostInputDataQuery", method = RequestMethod.POST)
	public Res<?> CostInputDataQuery(@RequestParam @ApiParam(value = "批次录入数据编码") String mainId) {
		Res<List<CostInputQueryStringVo>> res = new Res<List<CostInputQueryStringVo>>();
		res.setResult(costService.CostInputDataQuery(mainId));
		return res;
	}
	
	@ApiOperation("按核算对象和填写日期获取批次数据")
	@RequestMapping(value = "/getCostBatchInfo", method = RequestMethod.POST)
	public Res<?> getCostBatchInfo(@RequestParam @ApiParam(value = "填写日期") String writeDay, @RequestParam @ApiParam(value = "核算对象编码") String unitId) {
		Res<List<CostBatchInfo>> res = new Res<List<CostBatchInfo>>();
		res.setResult(costService.getCostBatchInfo(writeDay, unitId));
		return res;
	}
	
	@ApiOperation("获取交接班主数据的信息")
	@RequestMapping(value = "/getMainInputData", method = RequestMethod.POST)
	public Res<?> getMainInputData(@RequestParam @ApiParam(value = "数据编码") String mainId) {
		Res<CostBatchOnDuty> res = new Res<CostBatchOnDuty>();
		res.setResult(costService.getMainInputData(mainId));
		return res;
	}
	
	/**
	 * 导出指标数据
	 * 
	 * @category 导出核算批次查询数据
	 * <AUTHOR> @param 
	 * @return
	 */
	@ApiOperation(value = "导出核算批次查询数据", notes = "导出核算批次查询数据")
	@RequestMapping(value = "/costBatchInfoToExcel", method = { RequestMethod.POST })
	public void costBatchInfoToExcel(@RequestBody CostDataInputDto dto) {
		costService.costBatchInfoToExcel(dto, response);
	}
	
	@ApiOperation("获取自定义添加项目下拉框数据")
	@RequestMapping(value = "/getAddShowTypeItem", method = RequestMethod.POST)
	public Res<?> getAddShowTypeItem(@RequestParam @ApiParam(value = "数据编码") String mainId, @RequestParam @ApiParam(value = "核算对象编码") String unitId,
			@RequestParam @ApiParam(value = "填写日期") String writeDay,
			@RequestParam @ApiParam(value = "方案编码") String programId) {
		Res<List<CostItemForWriteVo>> res = new Res<List<CostItemForWriteVo>>();
		res.setResult(costService.getAddShowTypeItem(mainId, unitId, writeDay, programId));
		return res;
	}
	
	@ApiOperation("获取添加自定义 项目下的仪表，返回录入的格式")
	@RequestMapping(value = "/getAddShowTypeItemInstrucment", method = RequestMethod.POST)
	public Res<?> getAddShowTypeItemInstrucment(@RequestParam @ApiParam(value = "项目编码") String itemId, @RequestParam @ApiParam(value = "数据编码") String mainId,
			@RequestParam @ApiParam(value = "核算对象编码") String unitId, @RequestParam @ApiParam(value = "填写日期") String writeDay,
			@RequestParam @ApiParam(value = "方案编码") String programId) {
		Res<List<CostDataInputVo>> res = new Res<List<CostDataInputVo>>();
		res.setResult(costService.getAddShowTypeItemInstrucment(itemId, mainId, unitId, writeDay, programId));
		return res;
	}
	
	@ApiOperation("获取核算项目及录入的数据（判断是否保存过主数据，未保存需要保存）")
	@RequestMapping(value = "/getCostDataInputNew", method = RequestMethod.POST)
	public Res<?> getCostDataInputNew(@RequestBody CostBatchOnDutyDto param) {
		Res<CostDataInputDto> res = new Res<CostDataInputDto>();
		res.setResult(costService.getCostDataInputNew(param));
		return res;
	}
	
	@ApiOperation("更新交接录入主数据CostBatchOnDuty")
	@RequestMapping(value = "/udpateCostBatchOnDuty", method = RequestMethod.POST)
	public Res<?> udpateCostBatchOnDuty(@RequestBody CostBatchOnDutyDto param) {
		return Res.OK(costService.udpateCostBatchOnDuty(param));
	}
	
	@ApiOperation("是否显示获取数据按钮 ")
	@RequestMapping(value = "/isShowGetDataBtn", method = RequestMethod.POST)
	public Res<?> isShowGetDataBtn(@RequestParam @ApiParam(value = "核算对象编码") String unitId) {
		return Res.OK(costService.isShowGetDataBtn(unitId));
	}
	
	@ApiOperation("获取一个核算对象是否有录入的项目")
	@RequestMapping(value = "/getUnitItemCount", method = RequestMethod.POST)
	public Res<?> getUnitItemCount(@RequestBody CostUnitItemCountDto dto) {
		Res<JSONObject> res = new Res<JSONObject>();
		res.setResult(costService.getUnitItemCount(dto));
		return res;
	}
	
	@ApiOperation("批量保存核算录入数据")
	@RequestMapping(value = "/saveCostInputDataList", method = RequestMethod.POST)
	public Res<?> saveCostInputDataList(@RequestBody CostDataInputDto param) {
		return Res.OK(costService.saveCostInputDataList(param));
	}
	
	@ApiOperation("根据方案切换来获取核算项目及录入的数据（判断是否保存过主数据，未保存需要保存）")
	@RequestMapping(value = "/getCostDataInputNewList", method = RequestMethod.POST)
	public Res<?> getCostDataInputNewList(@RequestBody CostBatchOnDutyDto param) {
		Res<List<CostDataInputDto>> res = new Res<List<CostDataInputDto>>();
		res.setResult(costService.getCostDataInputNewList(param));
		return res;
	}
	
}
