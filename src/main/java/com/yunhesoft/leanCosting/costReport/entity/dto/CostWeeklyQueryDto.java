package com.yunhesoft.leanCosting.costReport.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 核算报表查询条件
 * <AUTHOR>
 *
 */
@Setter
@Getter
@ApiModel(value = "查询条件", description = "查询条件")
public class CostWeeklyQueryDto {

	
	@ApiModelProperty(value="核算单元")
	private String unitId; 
	
	@ApiModelProperty(value="汇总内容",example = "方案ID")
	private String contentId; 
	
	@ApiModelProperty(value="汇总方式",example = "操作机构ID")
	private String methodId;
	
	@ApiModelProperty(value="查询月份")
	private String yf;
	
	@ApiModelProperty(value="周期")
	private String zs;
	
	@ApiModelProperty(value="截止日期")
	private String jzrq;
	
	@ApiModelProperty(value="报表类型",example = "1:周报，2：月报")
	private String reportType;
	
}
