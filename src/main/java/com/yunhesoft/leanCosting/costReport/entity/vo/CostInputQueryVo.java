package com.yunhesoft.leanCosting.costReport.entity.vo;

import com.yunhesoft.system.kernel.utils.excel.ExcelExt;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostInputQueryVo {

	@ApiModelProperty(value = "分类ID", example = "tmuid1")
	private String classId;

	@ApiModelProperty(value = "分类名称", example = "分类")
	private String className;

	@ApiModelProperty(value = "项目ID", example = "tmuid2")
	private String itemId;

	@ApiModelProperty(value = "项目名称", example = "项目")
	@Excel(name = "项目名称", width = 20, orderNum = "1")
	@ExcelExt(align = "left") // 导出列居左对齐
	private String itemName;

	@ApiModelProperty(value = "计量单位", example = "吨")
	@Excel(name = "计量单位", width = 20, orderNum = "2")
	@ExcelExt(align = "left") // 导出列居左对齐
	private String itemUnit;

	@ApiModelProperty(value = "单价", example = "1200.00")
	@Excel(name = "单位", width = 20, orderNum = "3")
	@ExcelExt(align = "left") // 导出列居左对齐
	private String itemPrice;

	@ApiModelProperty(value = "单耗", example = "1200.00")
	@Excel(name = "单耗", width = 20, orderNum = "7")
	@ExcelExt(align = "right") // 导出列居左对齐
	private String dh;

	@ApiModelProperty(value = "仪表ID", example = "tmuid3")
	private String instrumentId;

	@ApiModelProperty(value = "仪表名称", example = "仪表1")
	private String instrumentName;

	@ApiModelProperty(value = "批次当班信息的ID")
	private String pid;

	@ApiModelProperty(value = "核算单元ID")
	private String unitId;

	@ApiModelProperty(value = "方案ID")
	private String programId;

	@ApiModelProperty(value = "前表数")
	private String previousReadOut;

	@ApiModelProperty(value = "后表数")
	private String lastReadOut;
	
	@ApiModelProperty(value = "单表消耗")
	private String dbxh;
	
	@ApiModelProperty(value = "消耗量")
	@Excel(name = "消耗量", width = 20, orderNum = "6")
	@ExcelExt(align = "right") // 导出列居左对齐
	private String xhl;
	
	
	@ApiModelProperty(value = "单位成本")
	@Excel(name = "单位成本", width = 20, orderNum = "8")
	@ExcelExt(align = "right") // 导出列居左对齐
	private String dwcb;
	
	@ApiModelProperty(value = "总成本")
	@Excel(name = "总成本", width = 20, orderNum = "9")
	@ExcelExt(align = "right") // 导出列居左对齐
	private String zcb;
	
	@ApiModelProperty(value = "单耗差值")
	@Excel(name = "单耗差值", width = 20, orderNum = "10")
	@ExcelExt(align = "right") // 导出列居左对齐
	private String dhcz;
	
	@ApiModelProperty(value = "单位成本差值")
	@Excel(name = "单位成本差值", width = 20, orderNum = "11")
	@ExcelExt(align = "right") // 导出列居左对齐
	private String dwcbcz;
	
	@ApiModelProperty(value = "能耗")
	@Excel(name = "能耗", width = 20, orderNum = "12")
	@ExcelExt(align = "right") // 导出列居左对齐
	private String nh;
	
	@ApiModelProperty(value = "单位成本定额")
	@Excel(name = "单位成本定额", width = 20, orderNum = "4")
	@ExcelExt(align = "right") // 导出列居左对齐
	private String dwcbde;
	
	@ApiModelProperty(value = "单耗定额")
	@Excel(name = "单耗定额", width = 20, orderNum = "5")
	@ExcelExt(align = "right") // 导出列居左对齐
	private String dhde;
	
	@ApiModelProperty(value = "数据类型（1：分类，2：项目，3：仪表）")
	private Integer dataType;
	
	@ApiModelProperty(value = "项目合并行数")
	private Integer itemHbCount;
	
	@ApiModelProperty(value = "填写日期")
	private String writeDay;
	
	@ApiModelProperty(value = "跨行")
	private Integer rowspan;
	
	@ApiModelProperty(value = "跨列")
	private Integer colspan;
	
}
