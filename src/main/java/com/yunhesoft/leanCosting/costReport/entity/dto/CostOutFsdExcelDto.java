package com.yunhesoft.leanCosting.costReport.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostOutFsdExcelDto extends CostOutExcelDto {

	@ApiModelProperty(position = 1, value = "查询类型 交接班:jjb 装置日报:zzrb 装置月报:zzyb 班组月汇总:bzhz 分时段汇总:sdhz")
	private String queryType;

	@ApiModelProperty(position = 2, value = "方案类型 汇总固定为0  方案则为对应的方案id")
	private String projectId = "0";

	@ApiModelProperty(position = 3, value = "月份或日期")
	private String time;

	@ApiModelProperty(position = 4, value = "装置代码")
	private String orgCode;

	@ApiModelProperty(position = 5, value = "交接班保存简要分析时需传入班组代码,分时段报表时需传入下拉选中的报表类别")
	private String bzdm;

	@ApiModelProperty(position = 6, value = "交接班报表需传入班次代码")
	private String bcdm;

	@ApiModelProperty(position = 7, value = "分时段报表时需传入下拉选中的报表编号")
	private String bbbh;

	@ApiModelProperty(position = 8, value = "保存简要分析时需传入简要分析")
	private String reportText;
	
	
	private String starTime;
	
	private String endTime;
	
}
