package com.yunhesoft.leanCosting.costReport.entity.dto;


import java.util.List;

//import app.common.dto.BaseQueryDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据检索条件
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "查询", description = "查询")
public class ComboDto {//extends BaseQueryDto 

    /** 键值 */
    @ApiModelProperty(value = "key")
    private String key;
    
    /** 显示值 */
    @ApiModelProperty(value = "value")
    private String value;
    
    /** 说明 */
    @ApiModelProperty(value = "desc")
    private String desc;
    
    /** 标题 */
    @ApiModelProperty(value = "title")
    private String title;
    
    /** 属性1 */
    @ApiModelProperty(value = "attr1")
    private String attr1;
    
    /** 属性2 */
    @ApiModelProperty(value = "attr2")
    private String attr2;
    
    /** 属性3 */
    @ApiModelProperty(value = "attr3")
    private String attr3;
    
    /** 属性4 */
    @ApiModelProperty(value = "attr4")
    private String attr4;
    
    /** 属性5 */
    @ApiModelProperty(value = "attr5")
    private String attr5;
    
}
