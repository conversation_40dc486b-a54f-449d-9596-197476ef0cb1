package com.yunhesoft.leanCosting.costReport.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "查询报表数据")
public class InstructmentDataVo {

	@ApiModelProperty(value = "仪表ID")
	private String ybid;

	@ApiModelProperty(value = "录入序号")
	private String xh;

	@ApiModelProperty(value = "前表数")
	private Double previousReadOut;

	@ApiModelProperty(value = "前表数采集时间")
	private String previousReadTime;

	@ApiModelProperty(value = "后表数")
	private Double lastReadOut;

	@ApiModelProperty(value = "采集数据时间")
	private String lastReadTime;

	@ApiModelProperty(value = "单表消耗计算值")
	private String calcVal;

	@ApiModelProperty(value = "实时数据库类型： 0、没有实时数据；1、有实时数据")
	private Integer hasHbs;

}
