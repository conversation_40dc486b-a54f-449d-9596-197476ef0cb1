package com.yunhesoft.leanCosting.costReport.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 核算报表查询条件
 * <AUTHOR>
 *
 */
@Setter
@Getter
@ApiModel(value = "查询条件", description = "查询条件")
public class CostReportQueryDto {

	
	@ApiModelProperty(value="核算单元")
	private String unitId; 
	
	@ApiModelProperty(value="报表类型",example = "1:周报，2:交接班")
	private String reportType;
	
	@ApiModelProperty(value="报表类型",example = "周报,小数位数读取格式内外格式 0：内部；1 ：外部")
	private Integer typeDate;
	
	//周报
	@ApiModelProperty(value="方案ID",example = "方案ID")
	private String contentId;
	
	@ApiModelProperty(value = "班次代码")
	private String shiftId;
	
	@ApiModelProperty(value="汇总方式",example = "汇总方式")
	private String methodId;
	
	@ApiModelProperty(value="查询月份")
	private String yf;
	
	@ApiModelProperty(value="周期")
	private String zs;
	//周报-END
	
	//交接班
	@ApiModelProperty(value="交接班主表ID")
	private String pid;
	
	@ApiModelProperty(value="截止日期")
	private String jzrq;
	
	@ApiModelProperty(value="班次(班组)")
	private String bc;
	//交接班-END
	
	@ApiModelProperty(value="类型")
	private Integer contentType;
	
	@ApiModelProperty(value="文件名称")
	private String excelFileName;
	
	@ApiModelProperty(value="查询模式")
	private boolean queryKey;
	
	@ApiModelProperty(value="简要分析")
	private String analysisContent;
	
	@ApiModelProperty(value="日期格式")
	private String reportNo;
}
