package com.yunhesoft.leanCosting.costReport.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TeamProgramDto {

	@ApiModelProperty(value = "核算对象ID")
	private String unitId;

	@ApiModelProperty(value = "填表日期")
	private String writeDay;

	@ApiModelProperty(value = "统计日期")
	private String summaryDay;

	@ApiModelProperty(value = "当班班组ID")
	private String teamId;

	@ApiModelProperty(value = "班次ID")
	private String shiftId;

	@ApiModelProperty(value = "方案ID")
	private String programId;

	@ApiModelProperty(value = "工作时长")
	private Double gzsc;

	@ApiModelProperty(value = "有仪表数据")
	private Boolean noybbs;

	@ApiModelProperty(value = "上班时间")
	private String shiftBeginTime;

	@ApiModelProperty(value = "下班时间")
	private String shiftEndTime;

}
