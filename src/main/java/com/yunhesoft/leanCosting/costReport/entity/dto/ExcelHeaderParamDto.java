package com.yunhesoft.leanCosting.costReport.entity.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 核算报表查询接口参数
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExcelHeaderParamDto {
    
    @ApiModelProperty(position=1, value = "表头别名")
    private String alias;
    
    @ApiModelProperty(position=2, value = "表头名称（显示文字）")
    private String header;
    
    @ApiModelProperty(position=3, value = "列宽")
    private int colWidth = 300;
    
    @ApiModelProperty(position=4, value = "列宽", notes="left：:左对齐， center：居中， right：右对齐")
    private String colAlign = "center";
    
    @ApiModelProperty(position=5, value = "格式化")
    private String format;
    
    @ApiModelProperty(position=6, value = "下级表头", notes="该属性为下级表头，用以构建表头层级")
    private List<ExcelHeaderParamDto> children;
    
}
