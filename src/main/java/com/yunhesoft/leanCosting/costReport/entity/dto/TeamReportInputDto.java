package com.yunhesoft.leanCosting.costReport.entity.dto;

import java.util.HashMap;
import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchParamData;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramLibraryCostItem;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TeamReportInputDto {

	@ApiModelProperty(value = "核算对象ID")
	private String unitId;

	@ApiModelProperty(value = "核算对象名称")
	private String unitMc;

	@ApiModelProperty(value = "填表日期")
	private String writeDay;

	@ApiModelProperty(value = "统计日期")
	private String summaryDay;

	@ApiModelProperty(value = "上班时间")
	private String shiftBegintime;

	@ApiModelProperty(value = "下班时间")
	private String shiftEndtime;

	@ApiModelProperty(value = "开始时间")
	private String begintime;

	@ApiModelProperty(value = "结束时间")
	private String endtime;

	@ApiModelProperty(value = "当班班组ID")
	private String teamId;

	@ApiModelProperty(value = "班次ID")
	private String shiftId;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "旧的批号")
	private String delbn;

	@ApiModelProperty(value = "方案ID")
	private String programId;

	@ApiModelProperty(value = "机构ID")
	private String orgId;

	@ApiModelProperty(value = "批次ID")
	private String bnid;

	@ApiModelProperty(value = "反馈的ID")
	private String jgid;

	@ApiModelProperty(value = "批次开始时间")
	private String pckssj;

	@ApiModelProperty(value = "批次结束时间")
	private String pcjzsj;

	@ApiModelProperty(value = "工作时长")
	private Double gzsc;

	@ApiModelProperty(value = "制表信息的id")
	private String pid;

	@ApiModelProperty(value = "计划返馈中的产品id")
	private String cpid;

	@ApiModelProperty(value = "给计划返馈返回的的产品量")
	private Double cpVal;
	
	@ApiModelProperty(value = "批量计算时不生成计算任务")
	private Boolean isBatchCalc;

	@ApiModelProperty(value = "用户录入的仪表数据，用于单表消耗计算")
	private List<CostBatchInstrumentData> ybbs;

	@ApiModelProperty(value = "批次的项目数据")
	private List<CostBatchItemData> pcxml;

	@ApiModelProperty(value = "批次的指标数据")
	private List<CostBatchParamData> pccsl;

	@ApiModelProperty(value = "核算对象MAP：key 核算对象ID，value 核算对象名称")
	private HashMap<String, String> unitm;

	@ApiModelProperty(value = "核算对象父子关系MAP：key 核算对象PID，value 核算对象ID")
	private HashMap<String, List<String>> unitPSm;

	@ApiModelProperty(value = "核算对象父子关系MAP：key 核算对象ID，value 核算对象的PID")
	private HashMap<String, String> unitPm;

	@ApiModelProperty(value = "成本项目分类类型下的项目")
	private HashMap<String, List<String>> itemfl;

	@ApiModelProperty(value = "成本项目MAP：key 项目ID，value 项目实体类")
	private HashMap<String, Costitem> itemm;

	@ApiModelProperty(value = "不用项目MAP：key 项目ID，value 1")
	private HashMap<String, String> byxm;

	@ApiModelProperty(value = "方案的项目MAP：key 项目的主数据ID，value  方案项目的实体类")
	private HashMap<String, ProgramLibraryCostItem> fawzm;

	@ApiModelProperty(value = "方案的指标MAP：key 指标名称，value  方案指标的实体类")
	private HashMap<String, ProgramLibraryCostItem> fazbm;

	@ApiModelProperty(value = "成本仪表MAP：key 仪表ID，value 仪表实体类")
	private HashMap<String, Costinstrument> ybm;

	@ApiModelProperty(value = "核算指标MAP：key 指标ID，value 指标实体类")
	private HashMap<String, Costindicator> hszbm;

	@ApiModelProperty(value = "公式MAP：key ID+公式类型，value 公式实体类")
	private HashMap<String, CostItemFormula> gsm;

	@ApiModelProperty(value = "参数与值MAP：key 公式参数，value 值")
	private HashMap<String, String> valm;

	@ApiModelProperty(value = "批次仪表MAP：key 仪表ID，value 批次仪表实体类")
	private HashMap<String, CostBatchInstrumentData> pcybm;

	@ApiModelProperty(value = "根据交接班录入仪表选择得到的项目价格：key 项目ID，value 价格")
	private HashMap<String, Double> xmjg;

	@ApiModelProperty(value = "根据费用维护得到项目计划值：key 项目ID，value 价格")
	private HashMap<String, Double> xmjh;

	@ApiModelProperty(value = "参数与值MAP：key 核算对象ID+类型，value 1")
	private HashMap<String, String> alrm;

	@ApiModelProperty(value = "子核算对象的项目消耗量MAP：key 核算项目的itemid，value 消耗量合计")
	private HashMap<String, Double> subiteml;

}
