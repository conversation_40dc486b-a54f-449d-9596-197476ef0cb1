package com.yunhesoft.leanCosting.costReport.entity.vo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class TableSpanVo {

	private int rowIndex;// 行下标
	private int columnIndex;// 列下标
	private int rowspan;// 行占据单元格总数量
	private int colspan;// 列占据单元格总数量

	/**
	 * 
	 * @param rowIndex    // 行下标
	 * @param columnIndex // 列下标
	 * @param rowspan     // 行占据单元格总数量
	 * @param colspan     // 列占据单元格总数量
	 */
	public TableSpanVo(int rowIndex, int columnIndex, int rowspan, int colspan) {
		this.rowIndex = rowIndex;
		this.columnIndex = columnIndex;
		this.rowspan = rowspan;
		this.colspan = colspan;
	}

}
