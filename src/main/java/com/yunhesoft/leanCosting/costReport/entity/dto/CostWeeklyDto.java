package com.yunhesoft.leanCosting.costReport.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "查询", description = "查询")
public class CostWeeklyDto {

	
	@ApiModelProperty(value="核算对象ID")
	private String unitid; 
	
	@ApiModelProperty(value="查询月份")
	private String yf;
	
	@ApiModelProperty(value = "方案ID")
	private String contentId;
	
	@ApiModelProperty(value="报表类型",example = "1:周报，2：月报")
	private String reportType;
	
	@ApiModelProperty(value="周期")
	private String zs;
}
