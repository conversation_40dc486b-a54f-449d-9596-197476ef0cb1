package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算——汇总表信息")
@Getter
@Setter
@Entity
@Table(name = "COSTSUMMAYINFO")
public class CostSummaryInfo extends BaseEntity {

	private static final long serialVersionUID = 6660997718289297284L;

	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;

	@ApiModelProperty(value = "报表编号")
	@Column(name = "REPORTNO", length = 20)
	private String reportNo;

	@ApiModelProperty(value = "方案ID")
	@Column(name = "PROGRAMID", length = 100)
	private String programId;

	@ApiModelProperty(value = "班组ID")
	@Column(name = "TEAMID", length = 100)
	private String teamId;

	@ApiModelProperty(value = "开始时间")
	@Column(name = "BEGINTIME", length = 20)
	private String beginTime;

	@ApiModelProperty(value = "结束时间")
	@Column(name = "ENDTIME", length = 20)
	private String endTime;

	@ApiModelProperty(value = "备注")
	@Column(name = "REMARK", length = 4000)
	private String remark;

	@ApiModelProperty(value = "制表信息")
	@Column(name = "REPORTMEMO", length = 1000)
	private String reportMemo;

	@ApiModelProperty(value = "制表人")
	@Column(name = "REPORTPERSON", length = 200)
	private String reportPerson;

	@ApiModelProperty(value = "制表日期")
	@Column(name = "REPORTDAY", length = 10)
	private String reportDay;
	
	@ApiModelProperty(value = "制表月份")
	@Column(name = "REPORTYF", length = 10)
	private String reportYf;

}
