package com.yunhesoft.leanCosting.costReport.entity.dto;

import java.util.HashMap;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 核算报表查询接口参数
 * 
 * <AUTHOR>
 *
 */
@Data
public class CostOutBzyhzdExcelDto extends CostOutExcelDto{

	@ApiModelProperty(position = 1, value = "核算单元")
	private String unitId;

	@ApiModelProperty(position = 2, value = "方案类型 汇总固定为0  方案则为对应的方案id")
	private String projectId = "0";

	@ApiModelProperty(position = 3, value = "月份")
	private String yf;

	@ApiModelProperty(position = 3, value = "日期")
	private String rq;

	@ApiModelProperty(position = 4, value = "开始时间")
	private String kssj;

	@ApiModelProperty(position = 5, value = "截止时间")
	private String jzsj;

	@ApiModelProperty(position = 5, value = "是否是从新获取 1 是  0  否")
	private String isCxhq;

	@ApiModelProperty(position = 6, value = "报表查询的小数位数")
	private HashMap<String, Integer> colxsw;

}
