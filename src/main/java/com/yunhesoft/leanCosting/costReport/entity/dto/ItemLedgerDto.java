package com.yunhesoft.leanCosting.costReport.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "物料台账查询传入类", description = "物料台账查询传入类")
public class ItemLedgerDto {

    @ApiModelProperty(value = "核算对象id")
    private String unitId;

    @ApiModelProperty(value = "班组id")
    private String teamId;

    @ApiModelProperty(value = "开始日期")
    private String startDate;

    @ApiModelProperty(value = "截止日期")
    private String endDate;

    @ApiModelProperty(value = "项目分类id")
    private String classId;

}
