package com.yunhesoft.leanCosting.costReport.entity.dto;


import lombok.Getter;
import lombok.Setter;

import java.util.List;

import com.yunhesoft.core.common.dto.BaseQueryDto;


/**
 *	核算报表方法--查询类
 */
@Setter
@Getter
public class CostReportMethodQueryDto extends BaseQueryDto {
	

	private String id; //数据id
	
	private List<String> idList; //数据id列表
	
	private String pId; //父id
	
	private List<String> pIdList; //父id列表
	
	private String unitId; //核算对象id
	
	private String writeDay; //填写日期
	
	private String teamId; //班组id
	
	private String shiftId; //班次id
	
	
}
