package com.yunhesoft.leanCosting.costReport.entity.vo;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "查询日报数据")
@Data
public class DayReportVo {
	@ApiModelProperty(value = "id")
    private String id;
	
	@ApiModelProperty(value = "班")
    private String teamId;
	
	@ApiModelProperty(value = "项目")
    private String itemId;
	
    @ApiModelProperty(value = "消耗量")
    private Double xhl;
    
    @ApiModelProperty(value = "核定量")
    private Double hdl;
    
    @ApiModelProperty(value = "单价")
    private Double dj;
    
    @ApiModelProperty(value = "工作时间")
    private Double sc;
    
    @ApiModelProperty(value = "修改人")
    private String updataBy;
    
    @ApiModelProperty(value = "修改时间")
    private Date updataTime;
}
