package com.yunhesoft.leanCosting.costReport.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 标题
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "标题", description = "标题")
public class DayReportTitleDto {//extends BaseQueryDto 

	/** 标题 */
	@ApiModelProperty("title")
	private String title;
	
	/** 核算单元 */
	@ApiModelProperty("hsdy")
	private String hsdy;
	
	/** 时间 */
	@ApiModelProperty("time")
	private String time;
	
}
