package com.yunhesoft.leanCosting.costReport.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AutoCreateShiftTaskDto {

	@ApiModelProperty(value = "租户ID")
	private String TENANT_ID;

	@ApiModelProperty(value = "核算对象ID，多个核算对象用逗号分隔")
	private String unitList;

	@ApiModelProperty(value = "如何使用传入核算对象，0 只计算传入的核算对象；1 不计算传入的核算对象")
	private String useList;

	@ApiModelProperty(value = "相对班次：-1 上班，其余都按当班")
	private String DIFShift;
	
	@ApiModelProperty(value = "计算给定时间的交接班")
	private String gdsj;

}
