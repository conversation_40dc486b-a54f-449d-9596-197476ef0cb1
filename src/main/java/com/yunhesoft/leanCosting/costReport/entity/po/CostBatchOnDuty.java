package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算——批次当班信息")
@Getter
@Setter
@Entity
@Table(name = "COSTBATCHONDUTY")
public class CostBatchOnDuty extends BaseEntity {

	private static final long serialVersionUID = 2806863600278356273L;

	@ApiModelProperty(value = "父ID")
	@Column(name = "PID", length = 100)
	private String pid;

	@ApiModelProperty(value = "班组ID")
	@Column(name = "TEAMID", length = 100)
	private String teamId;

	@ApiModelProperty(value = "班次ID")
	@Column(name = "SHIFTID", length = 100)
	private String shiftId;

	@ApiModelProperty(value = "开始时间")
	@Column(name = "BEGINTIME", length = 20)
	private String beginTime;

	@ApiModelProperty(value = "结束时间")
	@Column(name = "ENDTIME", length = 20)
	private String endTime;

	@ApiModelProperty(value = "填写日期")
	@Column(name = "WRITEDAY", length = 10)
	private String writeDay;

	@ApiModelProperty(value = "统计日期")
	@Column(name = "SUMMARYDAY", length = 10)
	private String summaryDay;

	@ApiModelProperty(value = "批号")
	@Column(name = "BATCHNO", length = 100)
	private String batchNo;

	@ApiModelProperty(value = "核算对象ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;

	@ApiModelProperty(value = "方案ID")
	@Column(name = "PROGRAMID", length = 100)
	private String programId;

	@ApiModelProperty(value = "是否完成")
	@Column(name = "ISEND")
	private Integer isEnd;

	@ApiModelProperty(value = "班次开始时间")
	@Column(name = "SHIFTBEGINTIME", length = 20)
	private String shiftBegintime;

	@ApiModelProperty(value = "班次结束时间")
	@Column(name = "SHIFTENDTIME", length = 20)
	private String shiftEndtime;

	@ApiModelProperty(value = "班组名称")
	@Column(name = "TEAMNAME", length = 200)
	private String teamName;

	@ApiModelProperty(value = "班次名称")
	@Column(name = "SHIFTNAME", length = 200)
	private String shiftName;

	@ApiModelProperty(value = "核算对象名称")
	@Column(name = "UNITNAME", length = 200)
	private String unitName;

	@ApiModelProperty(value = "方案名称")
	@Column(name = "PROGRAMNAME", length = 200)
	private String programName;
	
	@ApiModelProperty(value = "表单ID")
	@Column(name = "FORMID", length = 100)
	private String formId;

	@ApiModelProperty(value = "反馈人id")
	@Column(name = "FEEDBACKUSERID", length = 100)
	private String feedbackUserId;
	@ApiModelProperty(value = "反馈人姓名")
	@Column(name = "FEEDBACKUSERNAME", length = 100)
	private String feedbackUserName;
	@ApiModelProperty(value = "反馈时间")
	@Column(name = "FEEDBACKTIME", length = 100)
	private String feedbackTime;
	
	@ApiModelProperty(value = "表单数据编码")
	@Column(name = "FORMDATAID", length = 100)
	private String formDataId;

}
