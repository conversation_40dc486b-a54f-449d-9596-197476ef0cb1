package com.yunhesoft.leanCosting.costReport.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostItemForWriteVo {

	@ApiModelProperty(value = "分类ID", example = "tmuid1")
	private String classId;

	@ApiModelProperty(value = "分类名称", example = "分类1")
	private String className;

	@ApiModelProperty(value = "项目ID", example = "tmuid2")
	private String itemId;

	@ApiModelProperty(value = "项目名称", example = "项目1")
	private String itemName;

	@ApiModelProperty(value = "计量单位", example = "吨")
	private String itemUnit;

	@ApiModelProperty(value = "单价", example = "1200.00")
	private Double itemPrice;

	@ApiModelProperty(value = "仪表ID", example = "tmuid3")
	private String instrumentId;

	@ApiModelProperty(value = "仪表名称", example = "仪表1")
	private String instrumentName;

	@ApiModelProperty(value = "物料提供者：0、车间；1、班组（默认）", example = "materialSupply")
	private Integer materialSupply;

	@ApiModelProperty(value = "反馈时，通过添加选择增加项目。0 默认显示项目，1 通过新增添加项目，默认是0")
	private Integer feedShowType;

	@ApiModelProperty(value = "价格来源", example = "0、价格维护；1、数据源；默认值：0")
	private Integer priceSource;

	@ApiModelProperty(value = "数据源别名")
	private String tdsAlias;

	@ApiModelProperty(value = "单耗", example = "1200.00")
	private Double BaseConsumption;

	@ApiModelProperty(value = "单位成本")
	private Double dwcb;

	@ApiModelProperty(value = "总成本")
	private Double zcb;

	@ApiModelProperty(value = "单耗差值")
	private Double dhcz;

	@ApiModelProperty(value = "单位成本差值")
	private Double dwcbcz;

	@ApiModelProperty(value = "录入时显示： 1、显示； 2、不显示")
	private Integer showInShiftWrite;

	@ApiModelProperty(value = "实时数采仪表： 1、是； 0、不是")
	private Integer hasRealTag;

	@ApiModelProperty(value = "费用科目： 1、是； 0、不是")
	private Integer isFeeItem;

	@ApiModelProperty(value = "报表查询显示： 1、是； 0、不是")
	private Integer isShowInQuery;

}
