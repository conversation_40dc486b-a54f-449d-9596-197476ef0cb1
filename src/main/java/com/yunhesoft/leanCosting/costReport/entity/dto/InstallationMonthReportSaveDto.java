package com.yunhesoft.leanCosting.costReport.entity.dto;

import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.vo.InstallationMonthReportVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
@ApiModel(value="装置月汇总表班保存DtO类",description="装置月汇总表班保存DtO类")
public class InstallationMonthReportSaveDto{
	@ApiModelProperty(value = "保存的月汇总数据用到的基础信息")
	private CalculateTimeDto saveParam;
	@ApiModelProperty(value = "要保存的月汇总数据")
	private List<InstallationMonthReportVo> dataList;
}
