package com.yunhesoft.leanCosting.costReport.entity.dto;

import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 核算报表查询接口参数
 * <AUTHOR>
 *
 */
@Data
public class BzybReportCxDto {
    
	@ApiModelProperty(position=1, value = "表头")
    private String title;
	
    @ApiModelProperty(position=1, value = "字段")
    private JSONArray column;
    
    @ApiModelProperty(position=2, value = "数据")
    private JSONArray data;
    
    @ApiModelProperty(position=5, value = "主记录")
    private CostSummaryInfo info;
}
