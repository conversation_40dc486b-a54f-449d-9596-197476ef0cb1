package com.yunhesoft.leanCosting.costReport.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计时段
  * @Description:
  * <AUTHOR>
  * @date 2022年7月22日
 */
@Data
@ApiModel(value="统计时段DtO类",description="统计时段DtO类")
public class CalculateTimeDto{
	@ApiModelProperty(value = "单元id")
	private String zzdm;
	@ApiModelProperty(value = "方案ID")
	private String projectId;
	@ApiModelProperty(value = "月份")
	private String yf;
	@ApiModelProperty(value = "开始日期")
	private String ksrq;
	@ApiModelProperty(value = "截止日期")
	private String jzrq;
	@ApiModelProperty(value = "填表日期")
	private String tbrq;
	@ApiModelProperty(value = "是否保留数据，true为保留")
	private String keepData;
	@ApiModelProperty(value = "操作人姓名")
	private String userName;
}