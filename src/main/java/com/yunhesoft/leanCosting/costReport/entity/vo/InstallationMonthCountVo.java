package com.yunhesoft.leanCosting.costReport.entity.vo;

import javax.persistence.Column;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 装置月汇总统计VO
  * @Description:
  * <AUTHOR>
  * @date 2022年7月22日
 */
@Data
@ApiModel(value="装置月汇总统计VO类",description="装置月汇总统计VO类")
public class InstallationMonthCountVo{
	
	@ApiModelProperty(value = "物资代码")
	private String wzdm;
	@ApiModelProperty(value = "消耗量")
	private double xhl=0;
	@ApiModelProperty(value = "数量")
	private int sl=0;
	@ApiModelProperty(value = "月消耗量")
	private double yxhl=0d;
	@ApiModelProperty(value = "月计量报表量")
	private double yhdhl=0d;
	@ApiModelProperty(value = "年消耗量")
	private double nxhl=0d;
	@ApiModelProperty(value = "年计量报表量")
	private double nhdhl;
	@ApiModelProperty(value = "更新人")
	private String lastmodifiedperson;
	@ApiModelProperty(value = "更新时间")
	private String lastmodifiedtime;
}