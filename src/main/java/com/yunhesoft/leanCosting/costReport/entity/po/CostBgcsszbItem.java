package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算报表设置")
@Getter
@Setter
@Entity
@Table(name = "COSTBGCSSZBITEM")
public class CostBgcsszbItem extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;
	
	@ApiModelProperty(value = "仪表ID")
	@Column(name = "INSTRUMENTID", length = 100)
	private String instrumentId;
	
	@ApiModelProperty(value = "数据类型（0：分类，1：仪表）")
	@Column(name = "DATATYPE")
	private Integer dataType;
	
	@ApiModelProperty(value = "是否显示（0：隐藏，1：显示）")
	@Column(name = "TMUSED")
	private Integer tmused;
	
}
