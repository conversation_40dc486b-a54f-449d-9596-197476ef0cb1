package com.yunhesoft.leanCosting.costReport.entity.dto;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据检索条件
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "查询条件", description = "查询条件")
public class DayReportParamDto {//extends BaseQueryDto 

	/** 日期 */
	@ApiModelProperty("rq")
	private String rq;
	
	@ApiModelProperty("方案ID")
	private String projectId;
	
	/** 装置代码 */
	@ApiModelProperty("zzdm")
	private String zzdm;
	
	/** 备注 */
	@ApiModelProperty("remark")
	private String remark;
	
	/** 日报数据 */
	@ApiModelProperty("data")
	private List<DayReportDto> data;
}
