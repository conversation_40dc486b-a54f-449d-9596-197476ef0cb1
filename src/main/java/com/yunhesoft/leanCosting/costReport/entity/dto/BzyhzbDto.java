package com.yunhesoft.leanCosting.costReport.entity.dto;


import java.util.List;

//import app.common.dto.BaseQueryDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据检索条件
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "查询", description = "查询")
public class BzyhzbDto {//extends BaseQueryDto 

    /** 分厂代码 */
    @ApiModelProperty(value = "fcdm")
    private String fcdm;
    
    /** 查询代码 */
    @ApiModelProperty(value = "csdm")
    private String csdm;
    
    /** 月份 */
    @ApiModelProperty(value = "yf")
    private String yf;
    
    /** 装置代码 */
    @ApiModelProperty(value = "zzdm")
    private String zzdm;
    
    /** 班组代码 */
    @ApiModelProperty(value = "bzdm")
    private String bzdm;
    
    /** 父id */
    @ApiModelProperty(value = "pid")
    private String pid;
    
    /** 月份列表 */
    @ApiModelProperty(value = "yfList")
    private List<String> yfList;
    
    /** 月份值 */
    @ApiModelProperty(value = "yfVal")
    private String yfVal;
    
    /** 制表日期（scsj） */
    @ApiModelProperty(value = "zbrq")
    private String zbrq;
    
    /** 开始日期 */
    @ApiModelProperty(value = "ksrq")
    private String ksrq;
    
    /** 开始时间 */
    @ApiModelProperty(value = "kssj")
    private String kssj;
    
    /** 结束日期 */
    @ApiModelProperty(value = "jzrq")
    private String jzrq;
    
    /** 结束时间 */
    @ApiModelProperty(value = "jzsj")
    private String jzsj;
    
    /** 制表人 */
    @ApiModelProperty(value = "zbr")
    private String zbr;
    
    /** 报表说明 */
    @ApiModelProperty(value = "sm")
    private String sm;
    
    /** 默认开始截至日期相差天数（作为参数可输入） */
    @ApiModelProperty(value = "diffday")
    private String diffday;
}
