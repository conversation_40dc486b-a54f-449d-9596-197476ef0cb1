package com.yunhesoft.leanCosting.costReport.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ItemConsumption {

	@ApiModelProperty(value = "项目ID", example = "tmuid1")
	private String itemId;

	@ApiModelProperty(value = "班组ID", example = "tmuid2")
	private String teamId;

	@ApiModelProperty(value = "班次ID", example = "tmuid3")
	private String shiftId;

	@ApiModelProperty(value = "项目单价", example = "12.00")
	private Double itemPrice;

	@ApiModelProperty(value = "项目量", example = "1200.0000")
	private Double consumption;

	@ApiModelProperty(value = "工作时长", example = "1200.0000")
	private Double workingHour;

}
