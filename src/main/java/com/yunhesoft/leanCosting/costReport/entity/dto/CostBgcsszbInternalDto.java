package com.yunhesoft.leanCosting.costReport.entity.dto;


import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszbInternal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostBgcsszbInternalDto {

	@ApiModelProperty(value = "核算单元ID")
	private String unitId;
	
	@ApiModelProperty(value = "表格名称（表1，表2，表3..。。）")
	private String tableName;
	
	@ApiModelProperty(value = "分类编码")
	private String classId;
	
	@ApiModelProperty(value = "是否使用")
	private Integer tmused;
	
	private List<CostBgcsszbInternal> list;
}
