package com.yunhesoft.leanCosting.costReport.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostBbCodeNameVo {

	/**
	 * bb00 批次核算数据查询,
	 * bb01 班组经济核算投入产出利润表,
	 * bb04 装置经济核算投入产出利润表(日),
	 * bb07 装置经济核算投入产出利润汇总表(月),
	 * bb08 经济核算投入产出利润汇总表(分时段)
	 * bb09 班组交接班数据录入
	 */
	@ApiModelProperty(value = "报表编码", example = "bb00")
	private String bbCode;// bb00,bb01,bb04,bb07,bb08, bb09

	@ApiModelProperty(value = "报表名称", example = "班组经济核算投入产出利润表")
	// 批次核算数据查询，班组经济核算投入产出利润表，装置经济核算投入产出利润表(日)，装置经济核算投入产出利润汇总表(月)，经济核算投入产出利润汇总表
	private String bbName;
}
