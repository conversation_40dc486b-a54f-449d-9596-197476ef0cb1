package com.yunhesoft.leanCosting.costReport.entity.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TeamRecordDataVo {

	@ApiModelProperty(value = "核算对象ID")
	private String unitId;

	@ApiModelProperty(value = "返回内容的ID")
	private String itemId;

	@ApiModelProperty(value = "返回内容的名称")
	private String itemName;
	
	@ApiModelProperty(value = "采集点位号")
	private String tagNumber;

	@ApiModelProperty(value = "返回内容的采集时间")
	private String fetchTime;

	@ApiModelProperty(value = "返回值")
	private String fetchValue;
	
	@ApiModelProperty(value = "选择项目值")
	private List<String> optionValue;
	
	@ApiModelProperty(value = "计量单位")
	private String itemUnit;
	
	@ApiModelProperty(value = "允许被修改。t 允许修改；f 不允许修改，默认是f")
	private String canModified;
	
	@ApiModelProperty(value = "自动获取。t 自动获取；f 手工填写，默认是f")
	private String autoGet;

}
