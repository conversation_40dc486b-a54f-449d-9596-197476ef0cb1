package com.yunhesoft.leanCosting.costReport.entity.dto;

import javax.persistence.Column;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostBatchOnDutyDto {
	
	@ApiModelProperty(value = "ID")
	private String id;
	
	@ApiModelProperty(value = "父ID")
	private String pid;

	@ApiModelProperty(value = "班组ID")
	private String teamId;

	@ApiModelProperty(value = "班次ID")
	private String shiftId;

	@ApiModelProperty(value = "开始时间")
	private String beginTime;

	@ApiModelProperty(value = "结束时间")
	private String endTime;

	@ApiModelProperty(value = "填写日期")
	private String writeDay;

	@ApiModelProperty(value = "统计日期")
	private String summaryDay;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "核算对象ID")
	private String unitId;

	@ApiModelProperty(value = "方案ID")
	private String programId;

	@ApiModelProperty(value = "是否完成")
	private Integer isEnd;

	@ApiModelProperty(value = "班次开始时间")
	private String shiftBegintime;

	@ApiModelProperty(value = "班次结束时间")
	@Column(name = "SHIFTENDTIME", length = 20)
	private String shiftEndtime;

	@ApiModelProperty(value = "班组名称")
	private String teamName;

	@ApiModelProperty(value = "班次名称")
	private String shiftName;

	@ApiModelProperty(value = "核算对象名称")
	private String unitName;

	@ApiModelProperty(value = "方案名称")
	private String programName;
	
	@ApiModelProperty(value = "表单ID")
	private String formId;

	@ApiModelProperty(value = "反馈人id")
	private String feedbackUserId;
	
	@ApiModelProperty(value = "反馈人姓名")
	private String feedbackUserName;
	
	@ApiModelProperty(value = "反馈时间")
	private String feedbackTime;
	
	@ApiModelProperty(value = "获取数据类型，0或null 查询数据，1：获取实时数据")
	private Integer cxType;
}
