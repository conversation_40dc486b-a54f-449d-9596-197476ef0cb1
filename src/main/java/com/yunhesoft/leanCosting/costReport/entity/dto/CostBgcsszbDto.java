package com.yunhesoft.leanCosting.costReport.entity.dto;

import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostBgcsszbDto {
	
	@ApiModelProperty(value = "bb00:批次核算数据查询")
	private String tableName; 
	
	@ApiModelProperty(value = "类型（标题，表头")
	private String tableType; 
	
	@ApiModelProperty(value = "核算对象编码")
	private String unitid; 
	
	@ApiModelProperty(value = "查询类型（0：查询，1：设置 ）")
	private Integer cxType; 
	
	@ApiModelProperty(value = "查询类型（0：全部，1：显示，2：隐藏  ）")
	private Integer tmused; 
	
	@ApiModelProperty(value = "详细信息")
	private List<CostBgcsszb> list;
	
	@ApiModelProperty(value = "表单编码")
	private String formId;
	
}
