package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算——交接班计算任务信息")
@Getter
@Setter
@Entity
@Table(name = "SHIFTCOSTCALCTASK")
public class ShiftCostCalcTask extends BaseEntity {

	private static final long serialVersionUID = -5546242779964865923L;

	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;

	@ApiModelProperty(value = "填写日期")
	@Column(name = "WRITEDAY", length = 10)
	private String writeDay;

	@ApiModelProperty(value = "方案ID")
	@Column(name = "PROGRAMID", length = 100)
	private String programId;

	@ApiModelProperty(value = "班组ID")
	@Column(name = "TEAMID", length = 100)
	private String teamId;

	@ApiModelProperty(value = "班次ID")
	@Column(name = "SHIFTID", length = 100)
	private String shiftId;

	@ApiModelProperty(value = "统计日期")
	@Column(name = "SUMMARYDAY", length = 10)
	private String summaryDay;

	@ApiModelProperty(value = "开始时间")
	@Column(name = "BEGINTIME", length = 20)
	private String beginTime;

	@ApiModelProperty(value = "结束时间")
	@Column(name = "ENDTIME", length = 20)
	private String endTime;

	@ApiModelProperty(value = "班次的上班时间")
	@Column(name = "SHIFTBEGINTIME", length = 20)
	private String shiftBeginTime;

	@ApiModelProperty(value = "班次的下班时间")
	@Column(name = "SHIFTENDTIME", length = 20)
	private String shiftEndTime;

	@ApiModelProperty(value = "备注")
	@Column(name = "REMARK", length = 4000)
	private String remark;

	@ApiModelProperty(value = "计算标识 0 未计算 1 正在计算 2 已计算")
	@Column(name = "CALCSTATUS")
	private Integer calcStatus;

	@ApiModelProperty(value = "计算的开始时间")
	@Column(name = "CALCBEGINTIME", length = 20)
	private String calcBeginTime;

	@ApiModelProperty(value = "计算的结束时间")
	@Column(name = "CALCENDTIME", length = 20)
	private String calcEndTime;

}
