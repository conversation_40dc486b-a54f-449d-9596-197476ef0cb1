package com.yunhesoft.leanCosting.costReport.entity.vo;


import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CostTeamInstrumentDataVo extends CostTeamInstrumentData {

	
	private static final long serialVersionUID = 1L;
	
	private Integer flag = 0; //-1为删除记录；其他为添加或修改记录（根据id判断）
	
	//方案名称（显示）
	private String programName;
	
	//方案名称
	private String programNameStr;
	
	//方案名称（数量）
	private Integer programNameCount;
	
	//分类id
	private String classId;

	//分类名称（显示）
	private String className;
	
	//分类名称
	private String classNameStr;
	
	//分类名称（数量）
	private Integer classNameCount;

	//项目id
	private String itemId;

	//项目名称（显示）
	private String itemName;

	//项目名称
	private String itemNameStr;

	//项目名称（数量）
	private Integer itemNameCount;

	//仪表名称
	private String instrumentName;
	
	//计量单位
	private String itemUnit;

}
