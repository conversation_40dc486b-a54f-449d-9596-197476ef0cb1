package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算——报表公式计算过程")
@Entity
@Setter
@Getter
@Table(name = "COSTREPORTCALCPROCESS")
public class CostReportCalcProcess extends BaseEntity {

	private static final long serialVersionUID = 2438887439058648206L;

	@ApiModelProperty(value = "报表ID")
	@Column(name = "PID", length = 100)
	private String pid;

	@ApiModelProperty(value = "项目的ID：可能是成本项目ID、仪表ID、核算指标ID")
	@Column(name = "ITEMID", length = 100)
	private String itemId;

	@ApiModelProperty(value = "公式类型")
	@Column(name = "FTYPE", length = 50)
	private String fType;

	@ApiModelProperty(value = "公式")
	@Column(name = "CALCPROCESS", length = 4000)
	private String calcProcess;

	@ApiModelProperty(value = "公式的中文形式")
	@Column(name = "FORMULA", length = 4000)
	private String formula;

}
