package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算——批次的项目数据")
@Getter
@Setter
@Entity
@Table(name = "COSTBATCHITEMDATA")
public class CostBatchItemData extends BaseEntity {

	private static final long serialVersionUID = -4015716247634642329L;

	@ApiModelProperty(value = "批次ID")
	@Column(name = "PID", length = 100)
	private String pid;

	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;

	@ApiModelProperty(value = "方案ID")
	@Column(name = "PROGRAMID", length = 100)
	private String programId;

	@ApiModelProperty(value = "项目ID")
	@Column(name = "ITEMID", length = 100)
	private String itemId;

	@ApiModelProperty(value = "单价")
	@Column(name = "ITEMPRICE")
	private Double itemprice;

	@ApiModelProperty(value = "标准单耗")
	@Column(name = "BASEUNITCONSUMPTION")
	private Double baseUnitConsumption;

	@ApiModelProperty(value = "标准消耗")
	@Column(name = "BASECONSUMPTION")
	private Double baseConsumption;

	@ApiModelProperty(value = "消耗量")
	@Column(name = "CONSUMPTION")
	private Double consumption;

	@ApiModelProperty(value = "消耗量调整")
	@Column(name = "WRTIECONSUMPTION")
	private Double writeConsumption;

	@ApiModelProperty(value = "单耗")
	@Column(name = "UNITCONSUMPTION")
	private Double unitConsumption;

	@ApiModelProperty(value = "成本")
	@Column(name = "ITEMCOST")
	private Double itemCost;

	@ApiModelProperty(value = "标准成本")
	@Column(name = "BASECOST")
	private Double baseCost;

	@ApiModelProperty(value = "单位成本")
	@Column(name = "UNITCOST")
	private Double unitCost;

	@ApiModelProperty(value = "工作时长")
	@Column(name = "WORKINGHOUR")
	private Double workingHour;

	@ApiModelProperty(value = "开始时间")
	@Column(name = "BEGINTIME", length = 20)
	private String beginTime;

	@ApiModelProperty(value = "结束时间")
	@Column(name = "ENDTIME", length = 20)
	private String endTime;

	@ApiModelProperty(value = "填写日期")
	@Column(name = "WRITEDAY", length = 10)
	private String writeDay;

}
