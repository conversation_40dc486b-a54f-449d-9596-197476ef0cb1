package com.yunhesoft.leanCosting.costReport.entity.vo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 核算报表表头类
 * <AUTHOR>
 *
 */
@Data
public class ReportQueryHeaderVo {
    
    @ApiModelProperty(value = "列名称")
    private String header;
    
    @ApiModelProperty(value = "列别名")
    private String alias;

    @ApiModelProperty(value = "列宽度")
    private Double width;

    @ApiModelProperty(value = "对齐方式")
    private String align;
    
    @ApiModelProperty(value = "控件类型")
    private String comType;
    
    @ApiModelProperty(value = "字体")
    private String font;
    
    @ApiModelProperty(value = "字体大小")
    private Double fontSize;
    
    @ApiModelProperty(value = "是否合计")
    private Boolean total;
    
    @ApiModelProperty(value = "合并相同行")
    private Boolean sparse;
    
    private String format;
    
    private int fsize;
    
    private int clx;
    
    private int colIndex;
    
    private List<ReportQueryHeaderVo> children;
    
}
