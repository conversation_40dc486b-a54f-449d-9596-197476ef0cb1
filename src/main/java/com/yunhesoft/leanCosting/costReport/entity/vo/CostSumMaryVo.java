package com.yunhesoft.leanCosting.costReport.entity.vo;

import javax.persistence.Column;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "查询报表数据")
public class CostSumMaryVo {

	
	@ApiModelProperty(value = "数据ID",example = "COSTSUMMARYITEMDATA.ID/COSTSUMMARYPARAMDATA.ID")
	private String id;
	
	@ApiModelProperty(value = "数据类型",example = "1:COSTSUMMARYITEMDATA(汇总表项目信息)；2:COSTSUMMARYPARAMDATA(汇总表核算指标)")
	private String type;
	
	@ApiModelProperty(value = "核算单元ID")
	private String unitId;
	
	@ApiModelProperty(value = "分类ID", example = "tmuid1")
	private String classId;

	@ApiModelProperty(value = "分类名称", example = "分类1")
	private String className;

	@ApiModelProperty(value = "项目ID", example = "tmuid2")
	private String itemId;

	@ApiModelProperty(value = "项目名称", example = "项目1")
	private String itemName;

	@ApiModelProperty(value = "计量单位", example = "吨")
	private String itemUnit;

	@ApiModelProperty(value = "单价", example = "1200.00")
	private String itemPrice;

	@ApiModelProperty(value = "标准单耗")
	private Double baseUnitConsumption;

	@ApiModelProperty(value = "标准消耗")
	private Double baseConsumption;
	
	@ApiModelProperty(value = "单耗")
	private Double unitConsumption;

	@ApiModelProperty(value = "成本")
	private Double itemCost;

	@ApiModelProperty(value = "标准成本")
	private Double baseCost;

	@ApiModelProperty(value = "单位成本")
	private Double unitCost;
	
	@ApiModelProperty(value = "仪表ID", example = "tmuid3")
	private String instrumentId;

	@ApiModelProperty(value = "仪表名称", example = "仪表1")
	private String instrumentName;
	
	@ApiModelProperty(value = "前表数")
	@Column(name = "PREVIOUSREADOUT")
	private Double previousReadOut;

	@ApiModelProperty(value = "后表数")
	private Double lastReadOut;
	
	@ApiModelProperty(value = "消耗量调整",example = "有【消耗量调整(COSTSUMMARYITEMDATA.writeConsumption)】显示调整，没有显示【消耗量(COSTSUMMARYITEMDATA.consumption)】")
	private String writeConsumption;
	
	@ApiModelProperty(value = "消耗量")
	private String xhl;

	@ApiModelProperty(value = "排序")
	private int tmSort;
	
	@ApiModelProperty(value = "单表消耗")
	private Double dbxh;
	
	@ApiModelProperty(value = "能耗")
	private Double nh;
}
