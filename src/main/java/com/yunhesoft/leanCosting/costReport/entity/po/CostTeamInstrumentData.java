package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算——交接班仪表数据")
@Getter
@Setter
@Entity
@Table(name = "COSTTEAMINSTRUMENTDATA")
public class CostTeamInstrumentData extends BaseEntity {

	private static final long serialVersionUID = -6699597015815472394L;

	@ApiModelProperty(value = "交接班ID")
	@Column(name = "PID", length = 100)
	private String pid;

	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;

	@ApiModelProperty(value = "填写日期")
	@Column(name = "WRITEDAY", length = 10)
	private String writeDay;

	@ApiModelProperty(value = "方案ID")
	@Column(name = "PROGRAMID", length = 100)
	private String programId;

	@ApiModelProperty(value = "班组ID")
	@Column(name = "TEAMID", length = 100)
	private String teamId;

	@ApiModelProperty(value = "班次ID")
	@Column(name = "SHIFTID", length = 100)
	private String shiftId;

	@ApiModelProperty(value = "仪表ID")
	@Column(name = "INSTRUMENTID", length = 100)
	private String instrumentId;

	@ApiModelProperty(value = "前表数")
	@Column(name = "PREVIOUSREADOUT")
	private Double previousReadOut;

	@ApiModelProperty(value = "前表读取时间")
	@Column(name = "PREVIOUSREADTIME", length = 50)
	private String previousReadTime;

	@ApiModelProperty(value = "后表数")
	@Column(name = "LASTREADOUT")
	private Double lastReadOut;

	@ApiModelProperty(value = "后表读取时间")
	@Column(name = "LASTREADTIME", length = 50)
	private String lastReadTime;

	@ApiModelProperty(value = "仪表计算量")
	@Column(name = "CALCVAL")
	private Double calcVal;

	@ApiModelProperty(value = "仪表调整量")
	@Column(name = "WRITEVAL")
	private Double writeVal;

	@ApiModelProperty(value = "上班时间")
	@Column(name = "SHIFTBEGINTIME", length = 20)
	private String shiftBeginTime;

	@ApiModelProperty(value = "下班时间")
	@Column(name = "SHIFTENDTIME", length = 20)
	private String shiftEndTime;

}
