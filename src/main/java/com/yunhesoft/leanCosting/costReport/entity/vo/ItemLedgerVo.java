package com.yunhesoft.leanCosting.costReport.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ItemLedgerVo {

	@ApiModelProperty(value = "分类ID")
	private String classId;

	@ApiModelProperty(value = "项目ID")
	private String itemId;

	@ApiModelProperty(value = "项目名称")
	private String itemName;

	@ApiModelProperty(value = "日期")
	private String rq;

	@ApiModelProperty(value = "当日量")
	private Double dayCount;

	@ApiModelProperty(value = "累加量")
	private Double sumCount;

}
