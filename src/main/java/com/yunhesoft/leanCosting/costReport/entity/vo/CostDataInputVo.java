package com.yunhesoft.leanCosting.costReport.entity.vo;


import javax.persistence.Column;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostDataInputVo {

	@ApiModelProperty(value = "分类ID", example = "tmuid1")
	private String classId;

	@ApiModelProperty(value = "分类名称", example = "分类1")
	private String className;

	@ApiModelProperty(value = "项目ID", example = "tmuid2")
	private String itemId;

	@ApiModelProperty(value = "项目名称", example = "项目1")
	private String itemName;

	@ApiModelProperty(value = "计量单位", example = "吨")
	private String itemUnit;

	@ApiModelProperty(value = "单耗", example = "1200.00")
	private Double BaseConsumption;

	@ApiModelProperty(value = "仪表ID", example = "tmuid3")
	private String instrumentId;

	@ApiModelProperty(value = "仪表名称", example = "仪表1")
	private String instrumentName;

	@ApiModelProperty(value = "批次当班信息的ID")
	private String pid;

	@ApiModelProperty(value = "核算单元ID")
	private String unitId;

	@ApiModelProperty(value = "方案ID")
	private String programId;

	@ApiModelProperty(value = "方案名称")
	private String programName;

	@ApiModelProperty(value = "前表数")
	private Double previousReadOut;

	@ApiModelProperty(value = "前表数读取时间")
	private String previousReadTime;

	@ApiModelProperty(value = "后表数")
	private Double lastReadOut;

	@ApiModelProperty(value = "后表数读取时间")
	private String lastReadTime;

	@ApiModelProperty(value = "仪表计算量")
	private Double calcVal;

	@ApiModelProperty(value = "仪表调整量")
	private Double writeVal;

	@ApiModelProperty(value = "当班的批次结束时间")
	private String endTime;

	@ApiModelProperty(value = "分类合并行数", example = "1")
	private int classCount;

	@ApiModelProperty(value = "项目合并行数", example = "1")
	private int itemCount;
	
	@ApiModelProperty(value = "项目价格")
	private String itemPrice;
	
	@ApiModelProperty(value = "新增项目的分组ID")
	private String addItemGroupId;

	@ApiModelProperty(value = "新增项目的描述")
	private String addItemRemark;
	
	@ApiModelProperty(value = "标识，-1：删除")
	private String rowFlag;
	
	@ApiModelProperty(value = "数据id")
	private String id;

	@ApiModelProperty(value = "方案合并行数", example = "1")
	private int programCount;
	
}
