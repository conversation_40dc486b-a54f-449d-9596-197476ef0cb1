package com.yunhesoft.leanCosting.costReport.entity.dto;

import java.util.List;


import com.yunhesoft.leanCosting.costReport.entity.vo.CostDataInputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostDataInputDto {

	@ApiModelProperty(value = "核算单元ID")
	private String unitId;
//
//	@ApiModelProperty(value = "方案ID")
//	private String programId;
//
//	@ApiModelProperty(value = "批次开始时间")
//	private String pcbeginTime;// 格式yyyy-mm-dd hh:mi:ss
//
//	@ApiModelProperty(value = "批次结束时间")
//	private String pcendTime;// 格式yyyy-mm-dd hh:mi:ss
	
	@ApiModelProperty(value = "核算录入数据的主ID")
	private String mainId; 
	
	@ApiModelProperty(value = "班组ID")
	private String teamId;

	@ApiModelProperty(value = "班次ID")
	private String shiftId;
//
//	@ApiModelProperty(value = "开始时间")
//	private String beginTime;
//
//	@ApiModelProperty(value = "结束时间")
//	private String endTime;
//
	@ApiModelProperty(value = "填写日期")
	private String writeDay;
//
//	@ApiModelProperty(value = "统计日期")
//	private String summaryDay;
	
	@ApiModelProperty(value = "核算项目及录入的数据")
	private List<CostDataInputVo> listInputData;
}
