package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算——批次的仪表表数")
@Getter
@Setter
@Entity
@Table(name = "COSTBATCHINSTRUMENTDATA")
public class CostBatchInstrumentData extends BaseEntity {

	private static final long serialVersionUID = 4852840988128906044L;

	@ApiModelProperty(value = "批次当班信息的ID")
	@Column(name = "PID", length = 100)
	private String pid;

	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;

	@ApiModelProperty(value = "方案ID")
	@Column(name = "PROGRAMID", length = 100)
	private String programId;

	@ApiModelProperty(value = "仪表ID")
	@Column(name = "INSTRUMENTID", length = 100)
	private String instrumentId;

	@ApiModelProperty(value = "前表数")
	@Column(name = "PREVIOUSREADOUT")
	private Double previousReadOut;

	@ApiModelProperty(value = "前表数读取时间")
	@Column(name = "PREVIOUSREADTIME", length = 50)
	private String previousReadTime;

	@ApiModelProperty(value = "后表数")
	@Column(name = "LASTREADOUT")
	private Double lastReadOut;

	@ApiModelProperty(value = "后表数读取时间")
	@Column(name = "LASTREADTIME", length = 50)
	private String lastReadTime;

	@ApiModelProperty(value = "仪表计算量")
	@Column(name = "CALCVAL")
	private Double calcVal;

	@ApiModelProperty(value = "仪表调整量")
	@Column(name = "WRITEVAL")
	private Double writeVal;

	@ApiModelProperty(value = "当班的批次结束时间")
	@Column(name = "ENDTIME", length = 50)
	private String endTime;

	@ApiModelProperty(value = "项目价格")
	@Column(name = "ITEMPRICE", length = 50)
	private String itemPrice;

	@ApiModelProperty(value = "新增项目的分组ID")
	@Column(name = "ADDITEMGROUPID", length = 100)
	private String addItemGroupId;

	@ApiModelProperty(value = "新增项目的描述")
	@Column(name = "ADDITEMREMARK", length = 2000)
	private String addItemRemark;

}
