package com.yunhesoft.leanCosting.costReport.entity.dto;

import com.yunhesoft.core.common.dto.BaseQueryDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "分时段报表查询", description = "分时段报表查询")
public class ReportFsdQueryDto extends BaseQueryDto {

    /** 核算单元 */
    @ApiModelProperty(value = "unitCode")
    private String unitCode;

    /** 开始时间 */
    @ApiModelProperty(value = "starTime")
    private String starTime;
    
    /** 截止时间 */
    @ApiModelProperty(value = "endTime")
    private String endTime;
}
