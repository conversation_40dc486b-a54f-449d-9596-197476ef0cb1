package com.yunhesoft.leanCosting.costReport.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CostAnalysisVo {

	@ApiModelProperty(value = "项目ID")
	private String itemid;

	@ApiModelProperty(value = "项目名称")
	private String itemName;

	@ApiModelProperty(value = "数据类型")
	private String dataType;

	@ApiModelProperty(value = "比较方式")
	private String bjType;

	@ApiModelProperty(value = "计量单位")
	private String itemUnit;

	@ApiModelProperty(value = "值")
	private Double val;

	@ApiModelProperty(value = "规定值")
	private Double stipulateVal;

}
