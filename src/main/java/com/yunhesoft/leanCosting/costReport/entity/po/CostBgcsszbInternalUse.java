package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算报表内表头是否启用")
@Getter
@Setter
@Entity
@Table(name = "COSTBGCSSZBINTERNALUSE")
public class CostBgcsszbInternalUse extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;
	
	@ApiModelProperty(value = "表格名称（表1，表2，表3..。。）")
	@Column(name = "TABLENAME", length = 100)
	private String tableName;
	
	@ApiModelProperty(value = "分类编码")
	@Column(name = "CLASSID", length = 100)
	private String classId;
	
	@ApiModelProperty(value = "是否使用")
	@Column(name = "TMUSED")
	private Integer tmused;

}
