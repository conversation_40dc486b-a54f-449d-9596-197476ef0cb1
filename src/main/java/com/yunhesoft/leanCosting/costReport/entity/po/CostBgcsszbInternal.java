package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算报表内表头设置")
@Getter
@Setter
@Entity
@Table(name = "COSTBGCSSZBINTERNAL")
public class CostBgcsszbInternal extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "表单ID")
	@Column(name = "FORMID", length = 100)
	private String formId;
	
	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;
	
	@ApiModelProperty(value = "表格名称（表1，表2，表3..。。）")
	@Column(name = "TABLENAME", length = 100)
	private String tableName;
	
	@ApiModelProperty(value = "分类编码")
	@Column(name = "CLASSID", length = 100)
	private String classId;
	
	@ApiModelProperty(value = "字段编码（itemname ）")
	@Column(name = "COLUMNCODE", length = 100)
	private String columnCode;
	
	@ApiModelProperty(value = "字段显示名称")
	@Column(name = "COLUMNSHOWNAME", length = 100)
	private String columnshowName;
	
	@ApiModelProperty(value = "排序")
	@Column(name = "TMSORT")
	private Integer tmsort;
	
	@ApiModelProperty(value = "内表头用于哪个分类 ")
	@Column(name = "OBJINS", length = 100)
	private String objins;
	
	@ApiModelProperty(value = "是否显示")
	@Column(name = "TMUSED")
	private Integer tmused;
	
	@ApiModelProperty(value = "是否合并")
	@Column(name = "SPARSE")
	private Integer sparse;
	
	@ApiModelProperty(value = "是否汇总")
	@Column(name = "ISTOTAL")
	private Integer isTotal;
	
	@ApiModelProperty(value = "对齐方式(0：左对齐，1：右对齐，2：居中对齐")
	@Column(name = "OBJALG")
	private Integer objalg;
	
	@ApiModelProperty(value = "小数位数")
	@Column(name = "OBJDIS")
	private Integer objdis;

	@ApiModelProperty(value = "字段宽度")
	@Column(name = "COLUMNWIDTH")
	private Integer columnwidth;

}
