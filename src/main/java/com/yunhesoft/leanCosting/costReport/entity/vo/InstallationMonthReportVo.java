package com.yunhesoft.leanCosting.costReport.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 装置月汇总数据VO
 * 
 * @Description:
 * <AUTHOR>
 * @date 2022年7月22日
 */
@Data
@ApiModel(value = "装置月汇总VO类", description = "装置月汇总VO类")
public class InstallationMonthReportVo {
	@ApiModelProperty(value = "id")
	private String id;
	@ApiModelProperty(value = "分类名称")
	private String flmc;
	@ApiModelProperty(value = "分类代码")
	private String fldm;
	@ApiModelProperty(value = "方案ID")
	private String pid;
	@ApiModelProperty(value = "月份")
	private String yf;
	@ApiModelProperty(value = "物资代码")
	private String wzdm;
	@ApiModelProperty(value = "物资名称")
	private String wzmc;
	@ApiModelProperty(value = "实际价格")
	private Double khdj;
	@ApiModelProperty(value = "月消耗量")
	private Double yxhl;
	@ApiModelProperty(value = "月计量报表量")
	private Double yhdhl;
	@ApiModelProperty(value = "年消耗量")
	private Double nxhl;
	@ApiModelProperty(value = "年计量报表量")
	private Double nhdhl;
	@ApiModelProperty(value = "修改人")
	private String updateUserName;
	@ApiModelProperty(value = "修改时间")
	private String updateDt;
	@ApiModelProperty(value = "是否为更新记录")
	private boolean rowChange = false;
	@ApiModelProperty(value = "月工作时长")
	private Double workingHour;

	/** 合并数量 */
	@ApiModelProperty(value = "rowspan")
	private int rowspan;

	/** 合并数量 */
	@ApiModelProperty(value = "colspan")
	private int colspan;
}