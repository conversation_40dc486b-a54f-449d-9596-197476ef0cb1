package com.yunhesoft.leanCosting.costReport.service;


import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportMethodQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;


/**
 *	核算报表相关方法服务接口
 * <AUTHOR>
 * @date 2023-10-17
 */
public interface ICostReportMethodService {

	
	/**
	 *	获取交接班信息数据
	 * @param queryDto
	 * @return
	 */
	public List<CostTeamInfo> getCostTeamInfoList(CostReportMethodQueryDto queryDto);
	
	/**
	 *	根据数据唯一id获取交接班信息
	 * @param id
	 * @return
	 */
	public CostTeamInfo getCostTeamInfoById(String id);
	
	
	/**
	 *	获取班组的仪表表数
	 * @param queryDto
	 * @return
	 */
	public List<CostTeamInstrumentData> getCostTeamInstrumentDataList(CostReportMethodQueryDto queryDto);
	
	
}
