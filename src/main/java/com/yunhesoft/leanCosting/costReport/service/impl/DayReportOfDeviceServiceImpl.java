package com.yunhesoft.leanCosting.costReport.service.impl;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.leanCosting.calcLogic.ICalcTeamProjectLogic;
import com.yunhesoft.leanCosting.costReport.entity.dto.DayReportDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.vo.CommonlyBean;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostItemForWriteVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.DayReportVo;
import com.yunhesoft.leanCosting.costReport.service.CostBgcsszbService;
import com.yunhesoft.leanCosting.costReport.service.DayReportOfDeviceService;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class DayReportOfDeviceServiceImpl implements DayReportOfDeviceService {

	@Autowired
	ICostService costSer;

	@Autowired
	EntityService entityService;

	@Autowired
	GetCostItemInfoService costItemSer;
	
	@Autowired
	CostBgcsszbService cbs;

	@Autowired
	ICalcTeamProjectLogic ictpLogic;

	/**
	 * 查询日报数据
	 */
	@Override
	public List<DayReportDto> getData(String unitCode, String rq) {
		List<DayReportDto> result = new ArrayList<DayReportDto>();
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, unitCode);
		where.eq(CostSummaryInfo::getReportNo, rq);
		where.eq(CostSummaryInfo::getProgramId, "0");
		List<CostSummaryInfo> infoList = entityService.queryList(CostSummaryInfo.class, where, null);
		if (infoList == null || infoList.size() == 0) {
			// 初始化计算日报数据
			TeamReportInputDto reportinfo = new TeamReportInputDto();
			reportinfo.setWriteDay(rq);
			reportinfo.setUnitId(unitCode);
			reportinfo.setProgramId("0");
			ictpLogic.calcDeviceDayReportAuto(reportinfo);
			infoList = entityService.queryList(CostSummaryInfo.class, where, null);
		}
		Map<String, DayReportVo> xhkMap = new LinkedHashMap<String, DayReportVo>();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		if (infoList != null && infoList.size() > 0) {
			// 得到已保存的数据
			Where where1 = Where.create();
			where1.eq(CostSummaryItemData::getPid, infoList.get(0).getId());
			List<CostSummaryItemData> rbData = entityService.queryList(CostSummaryItemData.class, where1, null);
			if (rbData != null) {
				DecimalFormat df = new DecimalFormat("#.###");
				for (CostSummaryItemData rb : rbData) {
					DayReportVo info = new DayReportVo();
					info.setId(rb.getId());
					info.setDj(rb.getItemPrice());
					String xhl = df.format(rb.getConsumption());
					info.setXhl(Double.parseDouble(xhl));
					info.setUpdataBy(rb.getUpdateBy());
					info.setUpdataTime(rb.getUpdateTime());
					xhkMap.put(rb.getItemId(), info);
				}
			}
		}
		List<CostItemForWriteVo> flList = costItemSer.sumWriteItemInfo(unitCode, rq, "0");
		Map<String, Integer> flMap = new LinkedHashMap<String, Integer>();
		List<DayReportDto> temper = new ArrayList<DayReportDto>();
		if (flList != null && flList.size() > 0) {
			Integer ms = 1;
			for (CostItemForWriteVo tt : flList) {
				DayReportDto info = new DayReportDto();
				info.setWzdm(tt.getItemId());
				info.setFldm(tt.getClassId());
				info.setFl(tt.getClassName());
				info.setXm(tt.getItemName());
				info.setDw(tt.getItemUnit());
				info.setDj(tt.getItemPrice());
				info.setEdit(false);
				info.setRowspan(0);
				info.setColspan(0);
				ms = tt.getMaterialSupply();
				if (ms == null) {
					ms = 1;// 默认班组
				}
				info.setMaterialSupply(ms);
				DayReportVo rbInfo = xhkMap.get(tt.getItemId());
				if (rbInfo == null) {
					info.setId(null);
					info.setXhl(0.0);
					info.setUpdataBy("");
					info.setUpdataTime("");
				} else {
					info.setId(rbInfo.getId());
					info.setDj(rbInfo.getDj());
					info.setXhl(rbInfo.getXhl());
					info.setUpdataBy(rbInfo.getUpdataBy());
					if (rbInfo.getUpdataTime() != null) {
						info.setUpdataTime(sdf.format(rbInfo.getUpdataTime()));
					}
				}
				if (flMap.containsKey(tt.getClassId())) {
					Integer count = flMap.get(tt.getClassId());
					flMap.put(tt.getClassId(), count + 1);
				} else {
					flMap.put(tt.getClassId(), 1);
				}
				temper.add(info);
			}
			if (flMap.size() > 0) {
				for (String key : flMap.keySet()) {
					if (temper != null && temper.size() > 0) {
						for (int i = 0; i < temper.size(); i++) {
							if (key.equals(temper.get(i).getFldm())) {
								result.add(temper.get(i));
							}
						}
					}
				}
			}
			if (result != null && result.size() > 0) {
				for (int i = 0; i < flList.size(); i++) {
					// 处理分类合并单元格信息
					for (int l = 0; l < result.size(); l++) {
						DayReportDto info = result.get(l);
						if (flList.get(i).getClassId().equals(info.getFldm())) {
							Integer rowspan = flMap.get(info.getFldm());
							info.setRowspan(rowspan);
							info.setColspan(1);
							break;
						}
					}
				}
			}
		}
		return result;
	}

	/**
	 * 保存日报数据
	 */
	@Override
	public String saveData(List<DayReportDto> list, String unitCode, String rq) {
		String result = "error";
		if (list != null & list.size() > 0) {
			CostSummaryInfo infoObj = this.getInfo(unitCode, rq,"0");//TODO:默认汇总
			if (infoObj != null) {
				String pid = infoObj.getId();
				Where where1 = Where.create();
				where1.eq(CostSummaryItemData::getPid, pid);
				List<CostSummaryItemData> rbData = entityService.queryList(CostSummaryItemData.class, where1, null);
				HashMap<String, CostSummaryItemData> ddm = new HashMap<String, CostSummaryItemData>();
				for (CostSummaryItemData x : rbData) {
					ddm.put(x.getItemId(), x);
				}
				String did;
				List<CostSummaryItemData> updList = new ArrayList<CostSummaryItemData>();
				List<CostSummaryItemData> addList = new ArrayList<CostSummaryItemData>();
				for (int i = 0; i < list.size(); i++) {
					DayReportDto dto = list.get(i);
					did = dto.getWzdm();
					if (did != null && !"".equals(did)) {
						if (ddm.containsKey(did)) {
							CostSummaryItemData d = ddm.get(did);
							d.setItemPrice(dto.getDj());
							d.setConsumption(dto.getXhl());
							updList.add(d);
						} else {
							CostSummaryItemData info = new CostSummaryItemData();
							info.setId(TMUID.getUID());
							info.setPid(pid);
							info.setUnitId(unitCode);
							info.setProgramId("0");
							info.setItemId(did);
							info.setConsumption(dto.getXhl());
							info.setItemPrice(dto.getDj());
							info.setWriteDay(rq);
							info.setReportType("DayReport");
							addList.add(info);
						}
					}
				}
				if (addList != null && addList.size() > 0) {
					entityService.insertBatch(addList);
					result = "success";
				}
				if (updList != null && updList.size() > 0) {
					entityService.rawUpdateByIdBatch(updList);
					result = "success";
				}
				TeamReportInputDto reportinfo = new TeamReportInputDto();
				reportinfo.setWriteDay(rq);
				reportinfo.setUnitId(unitCode);
				reportinfo.setProgramId("0");
				ictpLogic.calcDeviceDayReport(reportinfo);
			}
		}
		return result;
	}

	@Override
	public String getXhlJd() {
		String hljd = null;
		try {
			if (hljd == null) {
				hljd = "3";
			}
		} catch (Exception e) {
			hljd = "3";
		}
		return hljd;
	}

	/**
	 * 获取机构下拉
	 */
	@Override
	public List<CommonlyBean> getZzOperation() {
		List<CommonlyBean> result = new ArrayList<CommonlyBean>();
		SysUser user = SysUserHolder.getCurrentUser();
		if (user != null) {
			List<Costuint> list = costSer.getCostuintListByOrgId(user.getOrgId(), 2);
			if (list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					int productive = list.get(i).getProductive() == null? 0: list.get(i).getProductive();
					if(productive ==0) {
						CommonlyBean info = new CommonlyBean();
						info.setId(list.get(i).getId());
						info.setName(list.get(i).getName());
						result.add(info);
					}
				}
			}
		}
		return result;
	}

	/**
	 * 获取主记录
	 * 
	 * @param unitCode
	 * @param rq
	 * @return
	 */
	@Override
	public CostSummaryInfo getInfo(String unitCode, String rq,String projectId) {
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, unitCode);
		where.eq(CostSummaryInfo::getReportNo, rq);
		where.eq(CostSummaryInfo::getProgramId, projectId);
		List<CostSummaryInfo> infoList = entityService.queryList(CostSummaryInfo.class, where, null);
		if (infoList != null && infoList.size() > 0) {
			return infoList.get(0);
		} else {
			return null;
		}
	}

}
