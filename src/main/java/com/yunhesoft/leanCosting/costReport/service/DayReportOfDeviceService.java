package com.yunhesoft.leanCosting.costReport.service;

import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.dto.DayReportDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CommonlyBean;

public interface DayReportOfDeviceService {

	List<DayReportDto> getData(String zzdm, String rq);

	String saveData(List<DayReportDto> list, String zzdm, String rq);

	List<CommonlyBean> getZzOperation();

	String getXhlJd();

	CostSummaryInfo getInfo(String unitCode, String rq, String string);

}
