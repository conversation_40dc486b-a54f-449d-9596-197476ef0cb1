package com.yunhesoft.leanCosting.costReport.service;


import java.util.List;


import com.yunhesoft.leanCosting.costReport.entity.dto.ShiftInstrumentWriteQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ShiftInstrumentWriteSaveDto;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostTeamInstrumentDataVo;


/**
 *	交接班仪表填写相关服务接口
 * <AUTHOR>
 * @date 2023-10-17
 */
public interface IShiftInstrumentWriteService {

	/**
	 *	获取交接班仪表填写数据
	 * @param queryDto
	 * @return
	 */
	public List<CostTeamInstrumentDataVo> getShiftInstrumentWriteList(ShiftInstrumentWriteQueryDto queryDto);
	
	
	/**
	 *	保存交接班仪表填写数据
	 * @param saveDto
	 * @return
	 */
	public String saveShiftInstrumentWriteData(ShiftInstrumentWriteSaveDto saveDto);
	
	
}
