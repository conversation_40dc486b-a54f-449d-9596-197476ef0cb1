package com.yunhesoft.leanCosting.costReport.service.impl;


import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportMethodQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.service.ICostReportMethodService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 *	核算报表相关方法服务接口实现类
 * <AUTHOR>
 * @date 2023-10-17
 */
@Service
public class CostReportMethodServiceImpl implements ICostReportMethodService {

	@Autowired
	private EntityService entityService;
	
	
	/**
	 *	获取交接班信息数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<CostTeamInfo> getCostTeamInfoList(CostReportMethodQueryDto queryDto) {
		List<CostTeamInfo> result = new ArrayList<CostTeamInfo>();
		try {
			String id = ""; // 数据唯一id
			List<String> idList = null;
			String unitId = ""; //核算对象id
			String writeDay = ""; //填写日期
			String teamId = ""; //班组id
			String shiftId = ""; //班次id
			if (StringUtils.isNotNull(queryDto)) {
				id = queryDto.getId();
				idList = queryDto.getIdList();
				unitId = queryDto.getUnitId();
				writeDay = queryDto.getWriteDay();
				teamId = queryDto.getTeamId();
				shiftId = queryDto.getShiftId();
			}
			// 检索条件
			Where where = Where.create();
			if(StringUtils.isNotEmpty(id)) {
				where.eq(CostTeamInfo::getId, id);
			}
			if(StringUtils.isNotEmpty(idList)) {
				where.in(CostTeamInfo::getId, idList.toArray());
			}
			if(StringUtils.isNotEmpty(unitId)) {
				where.eq(CostTeamInfo::getUnitId, unitId);
			}
			if(StringUtils.isNotEmpty(writeDay)) {
				where.eq(CostTeamInfo::getWriteDay, writeDay);
			}
			if(StringUtils.isNotEmpty(teamId)) {
				where.eq(CostTeamInfo::getTeamId, teamId);
			}
			if(StringUtils.isNotEmpty(shiftId)) {
				where.eq(CostTeamInfo::getShiftId, shiftId);
			}
			// 排序
			Order order = Order.create();
			order.orderByDesc(CostTeamInfo::getUnitId);
			List<CostTeamInfo> list = entityService.queryData(CostTeamInfo.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	/**
	 *	根据数据唯一id获取交接班信息
	 * @param id
	 * @return
	 */
	@Override
	public CostTeamInfo getCostTeamInfoById(String id) {
		CostTeamInfo result = null;
		if(StringUtils.isNotEmpty(id)) {
			CostTeamInfo obj = entityService.queryObjectById(CostTeamInfo.class, id);
			if(obj!=null) {
				result = obj;
			}
		}
		return result;
	}
	
	
	/**
	 *	获取班组的仪表表数
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<CostTeamInstrumentData> getCostTeamInstrumentDataList(CostReportMethodQueryDto queryDto) {
		List<CostTeamInstrumentData> result = new ArrayList<CostTeamInstrumentData>();
		try {
			String pid = ""; // 父id
			List<String> pIdList = null; //父id列表
			String unitId = ""; //核算对象id
			String writeDay = ""; //填写日期
			String teamId = ""; //班组id
			String shiftId = ""; //班次id
			if (StringUtils.isNotNull(queryDto)) {
				pid = queryDto.getPId();
				pIdList = queryDto.getIdList();
				unitId = queryDto.getUnitId();
				writeDay = queryDto.getWriteDay();
				teamId = queryDto.getTeamId();
				shiftId = queryDto.getShiftId();
			}
			// 检索条件
			Where where = Where.create();
			if(StringUtils.isNotEmpty(pid)) {
				where.eq(CostTeamInstrumentData::getPid, pid);
			}
			if(StringUtils.isNotEmpty(pIdList)) {
				where.in(CostTeamInstrumentData::getPid, pIdList.toArray());
			}
			if(StringUtils.isNotEmpty(unitId)) {
				where.eq(CostTeamInstrumentData::getUnitId, unitId);
			}
			if(StringUtils.isNotEmpty(writeDay)) {
				where.eq(CostTeamInstrumentData::getWriteDay, writeDay);
			}
			if(StringUtils.isNotEmpty(teamId)) {
				where.eq(CostTeamInstrumentData::getTeamId, teamId);
			}
			if(StringUtils.isNotEmpty(shiftId)) {
				where.eq(CostTeamInstrumentData::getShiftId, shiftId);
			}
			// 排序
			Order order = Order.create();
			order.orderByDesc(CostTeamInstrumentData::getUnitId);
			List<CostTeamInstrumentData> list = entityService.queryData(CostTeamInstrumentData.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
}
