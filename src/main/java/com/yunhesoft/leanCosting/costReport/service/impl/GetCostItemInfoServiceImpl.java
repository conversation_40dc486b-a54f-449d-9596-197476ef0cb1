package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.calcLogic.FetchRealTimeDataVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostItemForWriteVo;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.priceManage.entity.po.ItemPriceChangeInfo;
import com.yunhesoft.leanCosting.priceManage.entity.po.ItemPriceDetail;
import com.yunhesoft.leanCosting.priceManage.service.IPriceManageMethodService;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramLibraryCostItem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemInfoVo;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;

@Service
public class GetCostItemInfoServiceImpl implements GetCostItemInfoService {

	@Autowired
	UnitItemInfoService uiis;

	@Autowired
	IPriceManageMethodService pmms;

	@Override
	public List<CostItemForWriteVo> shiftWriteItemInfo(String unitId, String selectDate, String programID) {
		List<CostItemForWriteVo> rtn = new ArrayList<CostItemForWriteVo>();
		CostItemInfoVo itemInfo = null;
		if (programID == null || "".equals(programID)) {
			programID = "0";
		}
		if ("0".equals(programID)) {
			// 没有使用方案
			itemInfo = uiis.getCostData(unitId, selectDate);
		} else {
			// 使用了方案
			itemInfo = uiis.getUnitData(unitId, selectDate, programID);
		}
		if (itemInfo != null) {
			HashMap<String, FetchRealTimeDataVo> pm = new HashMap<String, FetchRealTimeDataVo>();
			CostItemInfoVo fi = uiis.getFetchInfo(unitId, selectDate, "");
			if (fi != null) {
				pm = fi.getFiMap();
				if (pm == null) {
					pm = new HashMap<String, FetchRealTimeDataVo>();
				}
			}
			HashMap<String, Double> jgm = getItemPrice("priceConfig", selectDate);
			// 方案下配置的项目和核算指标
			HashMap<String, ProgramLibraryCostItem> pilm = new HashMap<String, ProgramLibraryCostItem>();
			List<ProgramLibraryCostItem> piteml = itemInfo.getPItemList();
			if (piteml != null) {
				int count = piteml.size();
				for (int i = 0; count > i; i++) {
					ProgramLibraryCostItem x = piteml.get(i);
					pilm.put(x.getItemId(), x);
				}
			}
			// 仪表
			HashMap<String, List<Costinstrument>> ybm = new HashMap<String, List<Costinstrument>>();
			List<Costinstrument> ybl = itemInfo.getInstrumentList();
			if (ybl != null) {
				String xmid;
				int count = ybl.size();
				for (int i = 0; count > i; i++) {
					Costinstrument x = ybl.get(i);
					xmid = x.getPid();
					if (ybm.containsKey(xmid)) {
						ybm.get(xmid).add(x);
					} else {
						List<Costinstrument> yb = new ArrayList<Costinstrument>();
						yb.add(x);
						ybm.put(xmid, yb);
					}
				}
			}
			// 项目
			List<Costitem> xml = itemInfo.getItemList();
			if (xml != null) {
				Double dh, dj;
				Integer lrc, yblrshow;
				String flid, xmid, ybid, erpcode;
				// 按照分类整理项目：项目的父分类ID
				HashMap<String, List<Costitem>> iteml = new HashMap<String, List<Costitem>>();
				int count = xml.size();
				for (int i = 0; count > i; i++) {
					Costitem x = xml.get(i);
					flid = x.getPid();
					lrc = x.getMaterialSupply();
					if (lrc == null) {
						lrc = 1;// 默认是班组录入
					}
					if (lrc == 1) {
						if (iteml.containsKey(flid)) {
							iteml.get(flid).add(x);
						} else {
							List<Costitem> ll = new ArrayList<Costitem>();
							ll.add(x);
							iteml.put(flid, ll);
						}
					}
				}
				List<Costclass> fll = itemInfo.getClassList();// 分类
				if (fll != null) {
					int countk1 = fll.size();
					for (int k1 = 0; countk1 > k1; k1++) {
						Costclass x = fll.get(k1);
						flid = x.getId();
						if (iteml.containsKey(flid)) {
							List<Costitem> mm = iteml.get(flid);
							if (mm != null && mm.size() > 0) {
								int countk2 = mm.size();
								for (int k2 = 0; countk2 > k2; k2++) {
									Costitem y = mm.get(k2);
									xmid = y.getId();// 项目的唯一ID
									erpcode = y.getErpcode();
									dh = y.getBaseConsumption();
									if (pilm.size() > 0) {
										// 使用的了方案
										if (pilm.containsKey(xmid)) {
											// 项目被方案使用
											ProgramLibraryCostItem faxm = pilm.get(xmid);
											if (faxm.getTmUsed() != null && faxm.getTmUsed() == 0) {
												// 使用了方案下，方案未使用项目
												continue;
											} else {
												dh = faxm.getBaseConsumption();
											}
										}
									}
									dj = y.getItemprice();
									if (jgm != null && jgm.size() > 0) {
										if (jgm.containsKey(erpcode)) {
											dj = jgm.get(erpcode);
										}
									}
									if (ybm.containsKey(xmid)) {
										List<Costinstrument> yb = ybm.get(xmid);
										if (yb != null) {
											int countk3 = yb.size();
											for (int k3 = 0; countk3 > k3; k3++) {
												Costinstrument z = yb.get(k3);
												ybid = z.getId();
												CostItemForWriteVo b = new CostItemForWriteVo();
												b.setClassId(flid);
												b.setClassName(x.getCcname());
												b.setItemId(xmid);
												b.setItemName(y.getItemname());
												b.setFeedShowType(y.getFeedShowType());
												b.setPriceSource(y.getPriceSource());
												b.setTdsAlias(y.getTdsAlias());
												b.setItemUnit(y.getItemunit());
												b.setItemPrice(dj);
												b.setBaseConsumption(dh);
												b.setInstrumentId(ybid);
												b.setInstrumentName(z.getName());
												yblrshow = z.getShowInShiftWrite();
												if (yblrshow == null) {
													yblrshow = 1;
												}
												b.setShowInShiftWrite(yblrshow);
												if (pm.containsKey(ybid)) {
													b.setHasRealTag(1);// 有实时仪表
												} else {
													b.setHasRealTag(0);// 没有实时仪表
												}
												rtn.add(b);
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
		return rtn;
	}

	@Override
	public List<CostItemForWriteVo> sumWriteItemInfo(String unitId, String selectDate, String programID) {
		List<CostItemForWriteVo> rtn = new ArrayList<CostItemForWriteVo>();
		CostItemInfoVo itemInfo = null;
		if (programID == null || "".equals(programID)) {
			programID = "0";
		}
		if ("0".equals(programID)) {
			// 没有使用方案
			itemInfo = uiis.getCostData(unitId, selectDate);
		} else {
			// 使用了方案
			itemInfo = uiis.getUnitData(unitId, selectDate, programID);
		}
		if (itemInfo != null) {
			HashMap<String, Double> jgm = getItemPrice("priceConfig", selectDate);
			HashMap<String, Double> pilm = new HashMap<String, Double>();
			List<ProgramLibraryCostItem> piteml = itemInfo.getPItemList();
			if (piteml != null) {
				for (ProgramLibraryCostItem x : piteml) {
					if (x.getTmUsed() == null || x.getTmUsed() == 1) {
						pilm.put(x.getItemId(), x.getBaseConsumption());
					}
				}
			}
			HashMap<String, List<Costitem>> iteml = new HashMap<String, List<Costitem>>();
			List<Costitem> xml = itemInfo.getItemList();// 项目
			if (xml != null) {
				Double dh, dj;
				String flid, xmid, erpcode;
				for (Costitem x : xml) {
					flid = x.getPid();// 项目的父分类ID
					if (iteml.containsKey(flid)) {
						iteml.get(flid).add(x);
					} else {
						List<Costitem> ll = new ArrayList<Costitem>();
						ll.add(x);
						iteml.put(flid, ll);
					}
				}
				List<Costclass> fll = itemInfo.getClassList();// 分类
				if (fll != null) {
					for (Costclass x : fll) {
						flid = x.getId();
						if (iteml.containsKey(flid)) {
							// 分类下有项目
							List<Costitem> mm = iteml.get(flid);
							if (mm != null && mm.size() > 0) {
								for (Costitem y : mm) {
									xmid = y.getId();// 项目的唯一ID
									erpcode = y.getErpcode();
									dh = y.getBaseConsumption();
									if (pilm.size() > 0) {
										// 使用的了方案
										if (pilm.containsKey(xmid)) {
											// 项目被方案使用
											dh = pilm.get(xmid);
										} else {
											// 使用了方案下，方案未使用项目
											continue;
										}
									}
									dj = y.getItemprice();
									if (jgm != null && jgm.size() > 0) {
										if (jgm.containsKey(erpcode)) {
											dj = jgm.get(erpcode);
										}
									}
									// 有分类
									CostItemForWriteVo b = new CostItemForWriteVo();
									b.setClassId(flid);
									b.setClassName(x.getCcname());
									b.setItemId(xmid);
									b.setItemName(y.getItemname());
									b.setFeedShowType(y.getFeedShowType());
									b.setPriceSource(y.getPriceSource());
									b.setTdsAlias(y.getTdsAlias());
									b.setItemUnit(y.getItemunit());
									b.setItemPrice(dj);
									b.setBaseConsumption(dh);
									b.setMaterialSupply(y.getMaterialSupply());
									rtn.add(b);
								}
							}
						}
					}
				}
			}
		}
		return rtn;
	}

	@Override
	public List<CostItemForWriteVo> reportQueryInfo(String unitId, String selectDate, String programID,
			String isShift) {
		List<CostItemForWriteVo> rtn = new ArrayList<CostItemForWriteVo>();
		CostItemInfoVo itemInfo = null;
		if (programID == null || "".equals(programID)) {
			programID = "0";
		}
		if ("0".equals(programID)) {
			// 没有使用方案
			itemInfo = uiis.getCostData(unitId, selectDate);
		} else {
			// 使用了方案
			itemInfo = uiis.getUnitData(unitId, selectDate, programID);
		}
		HashMap<String, Double> jgm = this.getItemPrice("priceConfig", selectDate);
		if (itemInfo != null) {
			// 方案项目
			HashMap<String, ProgramLibraryCostItem> pilm = new HashMap<String, ProgramLibraryCostItem>();
			HashMap<String, ProgramLibraryCostItem> zblm = new HashMap<String, ProgramLibraryCostItem>();
			List<ProgramLibraryCostItem> piteml = itemInfo.getPItemList();
			if (piteml != null) {
				Integer lx;
				for (ProgramLibraryCostItem x : piteml) {
					lx = x.getDataType();
					if (lx == null || lx == 0) {
						pilm.put(x.getItemId(), x);// 成本项目
					} else {
						zblm.put(x.getName(), x);// 核算指标
					}
				}
			}
			// 仪表
			HashMap<String, List<Costinstrument>> ybm = new HashMap<String, List<Costinstrument>>();
			if ("true".equals(isShift)) {
				List<Costinstrument> ybl = itemInfo.getInstrumentList();
				if (ybl != null) {
					String xmid;
					for (Costinstrument x : ybl) {
						xmid = x.getPid();
						if (ybm.containsKey(xmid)) {
							ybm.get(xmid).add(x);
						} else {
							List<Costinstrument> yb = new ArrayList<Costinstrument>();
							yb.add(x);
							ybm.put(xmid, yb);
						}
					}
				}
			}
			HashMap<String, List<Costitem>> iteml = new HashMap<String, List<Costitem>>();
			List<Costitem> xml = itemInfo.getItemList();// 项目
			if (xml != null) {
				Integer lrfs, xmxs, fyxm, bbxs;
				Double dh, dj;
				String flid, xmid, erpcode;
				for (Costitem x : xml) {
					flid = x.getPid();// 项目的父分类ID
					if (iteml.containsKey(flid)) {
						iteml.get(flid).add(x);
					} else {
						List<Costitem> ll = new ArrayList<Costitem>();
						ll.add(x);
						iteml.put(flid, ll);
					}
				}
				List<Costclass> fll = itemInfo.getClassList();// 分类
				if (fll != null) {
					for (Costclass x : fll) {
						flid = x.getId();
						if (iteml.containsKey(flid)) {
							// 分类下有项目
							List<Costitem> mm = iteml.get(flid);
							if (mm != null && mm.size() > 0) {
								// 有项目的分类默认添加一行
								CostItemForWriteVo bf = new CostItemForWriteVo();
								bf.setClassId(flid);
								bf.setClassName(x.getCcname());
								rtn.add(bf);
								for (Costitem y : mm) {
									xmid = y.getId();// 项目的唯一ID
									erpcode = y.getErpcode();
									dh = y.getBaseConsumption();
									if (pilm.size() > 0) {
										// 使用的了方案
										if (pilm.containsKey(xmid)) {
											// 项目被方案使用
											dh = pilm.get(xmid).getBaseConsumption();
										} else {
											// 使用了方案下，方案未使用项目
											continue;
										}
									}
									bbxs = y.getIsShowInQuery();// 报表查询时显示
									if (bbxs == null) {
										bbxs = 1;
									}
									lrfs = y.getMaterialSupply();// 物料提供者：0、车间；1、班组
									if (lrfs == null) {
										lrfs = 1;
									}
									if (lrfs == 0) {
										if (jgm.containsKey(erpcode)) {
											dj = jgm.get(erpcode);
										} else {
											dj = y.getItemprice();
										}
									} else {
										dj = y.getItemprice();
									}
									xmxs = y.getFeedShowType();// 反馈时，通过添加选择增加项目。0 默认显示项目，1 通过新增添加项目
									fyxm = y.getIsFeeItem();// 费用科目
									if (fyxm == null) {
										fyxm = 0;
									}
									if ("true".equals(isShift)) {
										if (ybm.containsKey(xmid)) {
											List<Costinstrument> yb = ybm.get(xmid);
											for (Costinstrument z : yb) {
												CostItemForWriteVo b = new CostItemForWriteVo();
												b.setClassId(flid);
												b.setClassName(x.getCcname());
												b.setItemId(xmid);
												b.setItemName(y.getItemname());
												b.setItemUnit(y.getItemunit());
												b.setItemPrice(dj);
												b.setBaseConsumption(dh);
												b.setInstrumentId(z.getId());
												b.setInstrumentName(z.getName());
												b.setFeedShowType(xmxs);
												b.setMaterialSupply(lrfs);
												b.setIsFeeItem(fyxm);
												b.setIsShowInQuery(bbxs);
												rtn.add(b);
											}
										}
									} else {
										// 有分类
										CostItemForWriteVo b = new CostItemForWriteVo();
										b.setClassId(flid);
										b.setClassName(x.getCcname());
										b.setItemId(xmid);
										b.setItemName(y.getItemname());
										b.setItemUnit(y.getItemunit());
										b.setItemPrice(dj);
										b.setBaseConsumption(dh);
										b.setMaterialSupply(lrfs);
										b.setFeedShowType(xmxs);
										b.setIsFeeItem(fyxm);
										b.setIsShowInQuery(bbxs);
										rtn.add(b);
									}
								}
							}
						}
					}
				}
			}
			// 核算指标
			List<Costindicator> zbl = itemInfo.getIndicatorList();
			if (zbl != null) {
				// 默认添加一行分类
				CostItemForWriteVo b = new CostItemForWriteVo();
				b.setClassId("核算指标");
				b.setClassName("核算指标");
				rtn.add(b);
				Double de;
				Integer xs;
				String zbn;
				for (Costindicator x : zbl) {
					zbn = x.getCpname();// 指标名称
					xs = x.getIsSelShow();// 查询时显示
					if (xs == null) {
						xs = 1;
					}
					if (xs == 1) {
						// 指标被设置成了显示
						de = x.getStandardval();
						if (zblm.size() > 0) {
							// 使用了方案
							if (!zblm.containsKey(zbn)) {
								// 方案未启用指标，不添加
								continue;
							} else {
								de = zblm.get(zbn).getBaseConsumption();
							}
						}
						if (de == null) {
							de = 0.0;
						}
						CostItemForWriteVo b1 = new CostItemForWriteVo();
						b1.setClassId("核算指标");
						b1.setClassName("核算指标");
						b1.setItemId(x.getId());
						b1.setItemName(zbn);
						b1.setItemUnit(x.getItemunit());
						b1.setBaseConsumption(de);
						rtn.add(b1);
					}
				}
			}
		}
		return rtn;
	}

	@Override
	public HashMap<String, Double> getItemPrice(String dtype, String queryDate) {
		HashMap<String, Double> rtn = new HashMap<String, Double>();
		ItemPriceChangeInfo ipci = pmms.getMaxVersionPriceChangeObj(dtype, queryDate, "<=");
		if (ipci != null) {
			String pid = ipci.getId();
			if (!StringUtils.isEmpty(pid)) {
				List<ItemPriceDetail> queryList = pmms.getItemPriceDetailListByRedis(pid);
				if (queryList != null) {
					Double dj;
					if ("priceConfig".equals(dtype)) {
						// 项目单价
						String cbbh, cpbh, erpcode;
						for (ItemPriceDetail x : queryList) {
							cbbh = x.getCostCode();// 成本编号
							cpbh = x.getErpcode();// 产品编号
							if (cbbh == null) {
								cbbh = "";
							}
							if (StringUtils.isEmpty(cpbh)) {
								continue;
							}
							if ("".equals(cbbh)) {
								erpcode = cpbh;// 没有成本编号，直接使用产品编号
							} else {
								erpcode = (new StringBuffer(cbbh).append("_").append(cpbh)).toString();
							}
							dj = x.getItemPrice();
							if (dj == null) {
								dj = 0.0;
							}
							rtn.put(erpcode, dj);
						}
					} else {
						// 费用计划
						String itemid;
						for (ItemPriceDetail x : queryList) {
							itemid = x.getItemId();
							if (itemid == null) {
								itemid = "";
							}
							dj = x.getItemPrice();
							if (dj == null) {
								dj = 0.0;
							}
							rtn.put(itemid, dj);
						}
					}
				}
			}
		}
		return rtn;
	}
}
