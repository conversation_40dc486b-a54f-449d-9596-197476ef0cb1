package com.yunhesoft.leanCosting.costReport.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ICostItemLedgerService {
    List<Costuint> getCostUnitComboList ();

    JSONArray getCostTeamComboList (String unitId);

    List<Costclass> getCostItemClassComboList (String unitId, String dt);

    List<Costitem> getCostItemList (String unitId, String dt, String classId);

    JSONObject getQueryTableJsonObj (String unitId, String teamId, String start, String end, String classId);

    void exportExcel (HttpServletResponse response, String unitId, String teamId, String start, String end, String classId);
}
