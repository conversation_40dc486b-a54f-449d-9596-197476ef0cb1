package com.yunhesoft.leanCosting.costReport.service;

import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.dto.TeamRecordDto;
import com.yunhesoft.leanCosting.costReport.entity.vo.TeamRecordDataVo;

public interface IGetTeamRecordService {

	/**
	 * @category 获取核算对象的采样点数据
	 * @param dto
	 * @return
	 */
	public List<TeamRecordDataVo> getSampleData(TeamRecordDto dto);

	/**
	 * @category 获取核算对象的核算数据
	 * @param dto
	 * @return
	 */
	public List<TeamRecordDataVo> getCostData(TeamRecordDto dto);

	/**
	 * @category 获取核算对象的核算数据
	 * @param dto
	 * @return
	 */
	public List<TeamRecordDataVo> getShiftData(TeamRecordDto dto);

	/**
	 * @category 获取核算对象的设备数据
	 * @param dto
	 * @return
	 */
	public List<TeamRecordDataVo> getDeviceData(TeamRecordDto dto);

}
