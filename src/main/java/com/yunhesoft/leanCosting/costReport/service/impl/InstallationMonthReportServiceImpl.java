package com.yunhesoft.leanCosting.costReport.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.calcLogic.ICalcTeamProjectLogic;
import com.yunhesoft.leanCosting.costReport.entity.dto.CalculateTimeDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostItemForWriteVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.DayReportVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.InstallationMonthReportVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.ItemConsumption;
import com.yunhesoft.leanCosting.costReport.entity.vo.ProjectDataVo;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.costReport.service.GetItemConsumptionService;
import com.yunhesoft.leanCosting.costReport.service.IInstallationMonthReportService;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;

import lombok.extern.log4j.Log4j2;

/**
 * 装置月汇总表数据录入服务实现类
 * 
 * @Description:
 * <AUTHOR>
 * @date 2022年07月22日
 */
@Service
@Log4j2
public class InstallationMonthReportServiceImpl implements IInstallationMonthReportService {

	@Autowired
	EntityService entityService;

	@Autowired
	GetCostItemInfoService costItemSer;

	@Autowired
	GetItemConsumptionService getItemSer;

	@Autowired
	UnitItemInfoService uiis;

	@Autowired
	IProgramService programSer;

	@Autowired
	ICalcTeamProjectLogic ictpLogic;

	/**
	 * 获取统计时段
	 * 
	 * @category <AUTHOR>
	 * @param zzdm      装置代码
	 * @param yf        月份
	 * @param projectId 方案id
	 * @return CalculateTimeDto
	 */
	@Override
	public CalculateTimeDto getCalculateTime(String unitCode, String yf, String projectId) {
		CalculateTimeDto result = new CalculateTimeDto();
		result.setZzdm(unitCode);
		result.setYf(yf);
		result.setProjectId(projectId);
		if (StringUtils.isNotEmpty(unitCode) && StringUtils.isNotEmpty(yf) && StringUtils.isNotEmpty(projectId)) {
			CostSummaryInfo bean = this.getProjectCalculateTime(unitCode, yf, projectId);
			if (bean == null) {// 默认值
				result.setKsrq(yf + "-01");
				result.setJzrq(DateTimeUtils.getMonthEnd(yf));
				result.setTbrq(DateTimeUtils.getDate());
				saveCalculateTime(result);// 没有数据就存一份
			} else {// 存储值
				result.setKsrq(bean.getBeginTime());
				result.setJzrq(bean.getEndTime());
				result.setTbrq(bean.getReportDay());
			}
		} else {
			result.setKsrq(yf + "-01");
			result.setJzrq(DateTimeUtils.getMonthEnd(yf));
			result.setTbrq(DateTimeUtils.getDate());
		}
		return result;
	}

	/**
	 * 获取info对象
	 * 
	 * @param unitCode
	 * @param yf
	 * @param projectId
	 * @return
	 */
	private CostSummaryInfo getProjectCalculateTime(String unitCode, String yf, String projectId) {
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, unitCode);
		where.eq(CostSummaryInfo::getReportNo, yf);
		where.eq(CostSummaryInfo::getProgramId, projectId);
		List<CostSummaryInfo> infoList = entityService.queryList(CostSummaryInfo.class, where, null);
		if (infoList != null && infoList.size() > 0) {
			return infoList.get(0);
		}
		return null;
	}

	/**
	 * 保存统计时段
	 * 
	 * @category <AUTHOR>
	 * @param calBean 统计时段信息bean
	 * @return boolean 成功true 失败false
	 */
	@Override
	public boolean saveCalculateTime(CalculateTimeDto calBean) {
		boolean result = false;
		if (calBean != null) {
			String zzdm = calBean.getZzdm();
			if (StringUtils.isNotEmpty(zzdm)) {
				boolean insert = false;
				CostSummaryInfo dataBean = this.getProjectCalculateTime(zzdm, calBean.getYf(), calBean.getProjectId());
				if (dataBean == null) {// 没有查找到数据
					insert = true;
				} else {
					dataBean.setBeginTime(calBean.getKsrq());
					dataBean.setEndTime(calBean.getJzrq());
					dataBean.setReportDay(calBean.getTbrq());
				}
				if (insert) {
					// 初始化计算数据
					TeamReportInputDto reportinfo = new TeamReportInputDto();
					reportinfo.setWriteDay(calBean.getYf());
					reportinfo.setUnitId(calBean.getZzdm());
					reportinfo.setProgramId(calBean.getProjectId());
					reportinfo.setBegintime(calBean.getKsrq());
					reportinfo.setEndtime(calBean.getJzrq());
					reportinfo.setSummaryDay(calBean.getTbrq());
					ictpLogic.calcDeviceMonthReportAuto(reportinfo);
				} else {
					entityService.rawUpdateById(dataBean);// 更新
				}
			}
		}
		return result;
	}

	/**
	 * 获取方案列表：当前是所有的方案列表
	 * 
	 * @category <AUTHOR>
	 * @param zzdm 装置代码
	 * @param ksrq 开始日期
	 * @param jzrq 截止日期
	 * @return
	 */
	@Override
	public List<ProjectDataVo> getProjectList(String unitCode) {
		List<ProjectDataVo> result = new ArrayList<ProjectDataVo>();
		ProjectDataVo baseBean = new ProjectDataVo();
		baseBean.setProjectId("0");
		baseBean.setProjectName("汇总");
		result.add(baseBean);
		if (unitCode != null && unitCode.length() != 0) {
			List<ProgramItem> pList = programSer.getProgramItemListByUnitid(unitCode);// 获取方案列表
			if (pList != null && pList.size() > 0) {
				for (ProgramItem temp : pList) {
					ProjectDataVo info = new ProjectDataVo();
					info.setProjectId(temp.getId());
					info.setProjectName(temp.getPiName());
					result.add(info);
				}
			}
		}
		return result;
	}

	/**
	 * 获取装置月报数据
	 * 
	 * @category <AUTHOR>
	 * @param dto  统计时段
	 * @param init 是否读取初始化值（仅清理时使用true，正常读取使用false）
	 * @return List<InstallationMonthReportVo>
	 */
	@Override
	public List<InstallationMonthReportVo> getDataList(CalculateTimeDto calBean, boolean init) {
		List<InstallationMonthReportVo> result = new ArrayList<InstallationMonthReportVo>();
		if (calBean != null) {
			String zzdm = calBean.getZzdm();
			if (StringUtils.isNotEmpty(zzdm)) {
				Where where = Where.create();
				where.eq(CostSummaryInfo::getUnitId, zzdm);
				where.eq(CostSummaryInfo::getReportNo, calBean.getYf());
				where.eq(CostSummaryInfo::getProgramId, calBean.getProjectId());
				List<CostSummaryInfo> infoList = entityService.queryList(CostSummaryInfo.class, where, null);
				Map<String, DayReportVo> xhkMap = new LinkedHashMap<String, DayReportVo>();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				if (infoList != null && infoList.size() > 0) {
					// 得到历史数据
					Where where1 = Where.create();
					where1.eq(CostSummaryItemData::getPid, infoList.get(0).getId());
					List<CostSummaryItemData> rbData = entityService.queryList(CostSummaryItemData.class, where1, null);
					if (rbData != null) {
						Double val = 0.0;
						for (CostSummaryItemData sj : rbData) {
							DayReportVo info = new DayReportVo();
							info.setId(sj.getId());
							val = sj.getItemPrice();
							if (val != null) {
								info.setDj(Maths.round(val, 2));
							}
							val = sj.getConsumption();
							if (val != null) {
								info.setXhl(Maths.round(val, 3));
							}
							val = sj.getWriteConsumption();
							if (val != null) {
								info.setHdl(Maths.round(val, 3));
							}
							info.setUpdataBy(sj.getUpdateBy());
							info.setUpdataTime(sj.getUpdateTime());
							info.setSc(sj.getWorkingHour());
							xhkMap.put(sj.getItemId(), info);
						}
					}
				}
				// 得到方案下（方案ID=0是汇总）的核算项目
				List<CostItemForWriteVo> flList = costItemSer.sumWriteItemInfo(zzdm, calBean.getJzrq(),
						calBean.getProjectId());
				Map<String, Integer> flMap = new LinkedHashMap<String, Integer>();
				List<InstallationMonthReportVo> temper = new ArrayList<InstallationMonthReportVo>();
				if (flList != null && flList.size() > 0) {
					for (int i = 0; i < flList.size(); i++) {
						InstallationMonthReportVo info = new InstallationMonthReportVo();
						info.setFlmc(flList.get(i).getClassName());
						info.setFldm(flList.get(i).getClassId());
						info.setPid(calBean.getProjectId());// 方案ID
						info.setYf(calBean.getYf());// 月份
						info.setWzdm(flList.get(i).getItemId());// 物资代码
						info.setWzmc(flList.get(i).getItemName());
						info.setKhdj(flList.get(i).getItemPrice());// 单价
						DayReportVo rbInfo = xhkMap.get(flList.get(i).getItemId());
						if (rbInfo == null) {
							info.setId(null);
							info.setYxhl(0.0);// 月消耗量
							info.setYhdhl(0.0);// 月计量报表量
							info.setUpdateDt("");// 更新时间
							info.setUpdateUserName("");// 更新人
						} else {
							info.setId(rbInfo.getId());
							info.setKhdj(rbInfo.getDj());
							info.setYxhl(rbInfo.getXhl());
							info.setYhdhl(rbInfo.getHdl());
							if (rbInfo.getUpdataTime() != null) {
								info.setWorkingHour(rbInfo.getSc());
								info.setUpdateUserName(rbInfo.getUpdataBy());
								info.setUpdateDt(sdf.format(rbInfo.getUpdataTime()));// 更新时间
							}
						}
						if (flMap.containsKey(flList.get(i).getClassId())) {
							Integer count = flMap.get(flList.get(i).getClassId());
							flMap.put(flList.get(i).getClassId(), count + 1);
						} else {
							flMap.put(flList.get(i).getClassId(), 1);
						}
						temper.add(info);
					}
					// 处理单元格合并
					if (flMap.size() > 0) {
						for (String key : flMap.keySet()) {
							if (temper != null && temper.size() > 0) {
								for (int i = 0; i < temper.size(); i++) {
									if (key.equals(temper.get(i).getFldm())) {
										result.add(temper.get(i));
									}
								}
							}
						}
					}
					if (result != null && result.size() > 0) {
						for (int i = 0; i < flList.size(); i++) {
							// 处理分类合并单元格信息
							for (int l = 0; l < result.size(); l++) {
								InstallationMonthReportVo info = result.get(l);
								if (flList.get(i).getClassId().equals(info.getFldm())) {
									Integer rowspan = flMap.get(info.getFldm());
									info.setRowspan(rowspan);
									info.setColspan(1);
									break;
								}
							}
						}
					}
				}
			}
		}
		return result;
	}

	/**
	 * 保存装置月报数据
	 * 
	 * @category <AUTHOR>
	 * @param tbrq     填表日期
	 * @param dataList 数据列表
	 * @return boolean 成功true 失败false
	 */
	@Override
	public String saveDataList(CalculateTimeDto calBean, List<InstallationMonthReportVo> dataList) {
		String result = "error";
		if (calBean != null && dataList != null) {
			String zzdm = calBean.getZzdm();
			String yf = calBean.getYf();
			String faid = calBean.getProjectId();
			if (StringUtils.isNotEmpty(zzdm) && StringUtils.isNotEmpty(yf) && StringUtils.isNotEmpty(faid)) {
				CostSummaryInfo infoObj = this.getProjectCalculateTime(zzdm, yf, faid);
				if (infoObj != null) {
					// 先得到已保存的数据
					String pid = infoObj.getId();
					Where where1 = Where.create();
					where1.eq(CostSummaryItemData::getPid, pid);
					List<CostSummaryItemData> rbData = entityService.queryList(CostSummaryItemData.class, where1, null);
					HashMap<String, CostSummaryItemData> ddm = new HashMap<String, CostSummaryItemData>();
					for (CostSummaryItemData x : rbData) {
						ddm.put(x.getItemId(), x);
					}
					// 准备回写的数据
					String did;
					List<CostSummaryItemData> updList = new ArrayList<CostSummaryItemData>();
					List<CostSummaryItemData> addList = new ArrayList<CostSummaryItemData>();
					for (int i = 0; i < dataList.size(); i++) {
						InstallationMonthReportVo dto = dataList.get(i);
						did = dto.getWzdm();
						if (StringUtils.isNotEmpty(did)) {
							// 项目代码不能是空，空的忽略
							if (ddm.containsKey(did)) {
								CostSummaryItemData d = ddm.get(did);
								d.setItemPrice(dto.getKhdj());
								d.setConsumption(dto.getYxhl());
								d.setWriteConsumption(dto.getYhdhl());
								d.setWorkingHour(dto.getWorkingHour());
								updList.add(d);
							} else {
								CostSummaryItemData info = new CostSummaryItemData();
								info.setId(TMUID.getUID());
								info.setPid(pid);
								info.setUnitId(zzdm);
								info.setProgramId(faid);
								info.setItemId(did);
								info.setItemPrice(dto.getKhdj());
								info.setConsumption(dto.getYxhl());
								info.setWriteConsumption(dto.getYhdhl());
								info.setWorkingHour(dto.getWorkingHour());
								info.setWriteDay(yf);
								addList.add(info);
							}
						}
					}
					if (addList != null && addList.size() > 0) {
						entityService.insertBatch(addList);
						result = "success";
					}
					if (updList != null && updList.size() > 0) {
						entityService.rawUpdateByIdBatch(updList);
						result = "success";
					}
					// 初始化计算数据
					TeamReportInputDto reportinfo = new TeamReportInputDto();
					reportinfo.setWriteDay(yf);
					reportinfo.setUnitId(zzdm);
					reportinfo.setProgramId(faid);
					reportinfo.setBegintime(calBean.getKsrq());
					reportinfo.setEndtime(calBean.getJzrq());
					reportinfo.setSummaryDay(calBean.getTbrq());
					ictpLogic.calcDeviceMonthReport(reportinfo);
				}
			}
		}
		return result;
	}

	/**
	 * 清除数据
	 * 
	 * @category <AUTHOR>
	 * @param dto 统计时段
	 * @return boolean 成功true 失败false
	 */
	@Override
	public List<InstallationMonthReportVo> clearData(CalculateTimeDto calBean) {
		List<InstallationMonthReportVo> result = new ArrayList<InstallationMonthReportVo>();
		if (calBean != null) {
			String zzdm = calBean.getZzdm();
			if (StringUtils.isNotEmpty(zzdm)) {
				Double dj;
				String erpcode;
				result = this.getDataList(calBean, true);// 重新获取项目
				if (result != null && result.size() > 0) {
					// 有数据才有后续操作
					String faid = calBean.getProjectId();
					if (StringUtils.isEmpty(faid)) {
						faid = "0";
					}
					String jzrq = calBean.getJzrq();
					// 根据统计时间点段获取项目量
					// 价格
					HashMap<String, Double> jgm = this.costItemSer.getItemPrice("priceConfig", jzrq);
					if (jgm == null) {
						jgm = new HashMap<String, Double>();
					}
					HashMap<String, Costitem> im = new HashMap<String, Costitem>();
					List<Costitem> itl = uiis.getItem(zzdm, jzrq);
					if (itl != null) {
						for (Costitem it : itl) {
							erpcode = it.getErpcode();
							dj = 0.0;
							if (erpcode != null) {
								if (jgm.containsKey(erpcode)) {
									dj = jgm.get(erpcode);
								}
							}
							it.setItemprice(dj);
							im.put(it.getId(), it);
						}
					}

					HashMap<String, ItemConsumption> xmm = new HashMap<String, ItemConsumption>();
					List<ItemConsumption> xmhl = this.getItemSer.getUnitWeekItemConsumption(zzdm, calBean.getKsrq(),
							jzrq, faid, "", im);
					if (xmhl != null) {
						for (ItemConsumption xm : xmhl) {
							xmm.put(xm.getItemId(), xm);
						}
					}
					Double val;
					if ("true".equals(calBean.getKeepData())) {// 保留数据
						String wzdm;
						Double yxhl, yhdhl, xhl;
						for (InstallationMonthReportVo temp : result) {
							wzdm = temp.getWzdm();
							// 检索到的数据有当前项目
							yxhl = temp.getYxhl();
							if (yxhl == null) {
								yxhl = 0.0;
							}
							yhdhl = temp.getYhdhl();
							if (yhdhl == null) {
								yhdhl = 0.0;
							}
							if (xmm.containsKey(wzdm)) {
								ItemConsumption yn = xmm.get(wzdm);
								xhl = yn.getConsumption();
								val = yn.getItemPrice();
								if (val != null) {
									temp.setKhdj(Maths.round(val, 2));
								} else {
									temp.setKhdj(0.0);
								}
								if (xhl != null) {
									temp.setYxhl(Maths.round(xhl, 3));
								} else {
									temp.setYxhl(0.0);
								}
								if (yhdhl.compareTo(yxhl) == 0) {
									// 没有修改过
									if (xhl != null) {
										temp.setYhdhl(Maths.round(xhl, 3));
									} else {
										temp.setYhdhl(0.0);
									}
								}
								temp.setWorkingHour(yn.getWorkingHour());
							} else {
								if (im.containsKey(wzdm)) {
									Costitem yn = im.get(wzdm);
									val = yn.getItemprice();
									if (val != null) {
										temp.setKhdj(Maths.round(val, 2));
									} else {
										temp.setKhdj(0.0);
									}
								} else {
									temp.setKhdj(0.0);
								}
								temp.setYxhl(0.0);
								if (yhdhl.compareTo(yxhl) == 0) {
									// 没有修改过
									temp.setYhdhl(0.0);
								}
								temp.setWorkingHour(0.0);
							}
						}
					} else {
						String wzdm;
						Double xhl;
						for (InstallationMonthReportVo temp : result) {
							wzdm = temp.getWzdm();
							if (xmm.containsKey(wzdm)) {
								// 检索到的数据有当前项目
								ItemConsumption yn = xmm.get(wzdm);
								val = yn.getItemPrice();
								if (val != null) {
									temp.setKhdj(Maths.round(val, 2));
								} else {
									temp.setKhdj(0.0);
								}
								xhl = yn.getConsumption();
								if (xhl != null) {
									temp.setYxhl(Maths.round(xhl, 3));
									temp.setYhdhl(Maths.round(xhl, 3));
								} else {
									temp.setYxhl(0.0);
									temp.setYhdhl(0.0);
								}
								temp.setWorkingHour(yn.getWorkingHour());
							} else {
								if (im.containsKey(wzdm)) {
									Costitem yn = im.get(wzdm);
									val = yn.getItemprice();
									if (val != null) {
										temp.setKhdj(Maths.round(val, 2));
									} else {
										temp.setKhdj(0.0);
									}
								} else {
									temp.setKhdj(0.0);
								}
								temp.setYxhl(0.0);
								temp.setYhdhl(0.0);
								temp.setWorkingHour(0.0);
							}
						}
					}
				}
			}
		}
		return result;
	}
}