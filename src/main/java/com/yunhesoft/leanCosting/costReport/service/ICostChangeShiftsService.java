package com.yunhesoft.leanCosting.costReport.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamParamData;
import com.yunhesoft.leanCosting.costReport.entity.vo.ComboVo;

public interface ICostChangeShiftsService {

	/**
	 * 查询核算对象最大日期
	 * 
	 * @param dto
	 * @return
	 */
	public String getMaxDate(CostReportQueryDto dto);

	/**
	 * 班次班组信息
	 * 
	 * @param dto
	 * @return
	 */
	public List<ComboVo> getBc(CostReportQueryDto dto);

	/**
	 * 获得方案
	 * 
	 * @param dto
	 * @return
	 */
	public List<ComboVo> getProgram(CostReportQueryDto dto);
	
	
	/**
	 * 表头设置数据
	 * @param dto
	 * @return
	 */
	public Map<String, CostBgcsszb> getCostBgcsszbMap(CostReportQueryDto dto);
	
	/**
	 *  查询汇总表主表信息(交接班)
	 *  
	 * @param dto
	 * @return
	 */
	List<CostTeamInfo> getCostTeamInfos(CostReportQueryDto dto);
	
	/**
	 * 查询汇总表项目信息(交接班)
	 * 
	 * @param dto
	 * @return
	 */
	public List<CostTeamItemData> getChangeShiftsCostTeamItemDatas(String pid);

	/**
	 * 查询核算指标数据(交接班)
	 * 
	 * @param dto
	 * @return
	 */
	List<CostTeamParamData> getChangeShiftsCostTeamParamDatas(String pid);

	/**
	 * 查询汇总表仪表信息(交接班)
	 * @param dto
	 * @return
	 */
	List<CostTeamInstrumentData> getChangeShiftsCostTeamInstrumentData(String pid);

}
