package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.leanCosting.calcLogic.FetchRealTimeDataDTO;
import com.yunhesoft.leanCosting.calcLogic.FetchRealTimeDataVo;
import com.yunhesoft.leanCosting.calcLogic.IFetchRealTimeData;
import com.yunhesoft.leanCosting.calcLogic.PublicMethods;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.vo.InstructmentDataVo;
import com.yunhesoft.leanCosting.costReport.service.IGetShiftDataService;
import com.yunhesoft.leanCosting.unitConf.entity.vo.ClassVo;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;

/**
 * 获取仪表数据
 * 
 * <AUTHOR>
 *
 */
@Service
public class GetShiftDataServiceImpl implements IGetShiftDataService {

	@Autowired
	EntityService entityService;
	@Autowired
	IFetchRealTimeData ifrtd;

	private PublicMethods pm = new PublicMethods();

	@Override
	public HashMap<String, InstructmentDataVo> getShiftData(FetchRealTimeDataDTO dto) {
		HashMap<String, InstructmentDataVo> rtn = new HashMap<String, InstructmentDataVo>();
		Double val;
		String unitid = dto.getUnitId();
		String sbsj = dto.getKssj();
		String ybid;
		// 获取上班表数
		String pxbsj = this.getLastShiftTime(unitid, sbsj);
		HashMap<String, ClassVo> sbbsm = this.getPreviousData(unitid, pxbsj);
		if (sbbsm != null) {
			for (Entry<String, ClassVo> x : sbbsm.entrySet()) {
				ybid = x.getKey();
				ClassVo vo = x.getValue();
				if (rtn.containsKey(ybid)) {
					InstructmentDataVo ybsj = rtn.get(ybid);
					ybsj.setPreviousReadOut(vo.getVal());
					ybsj.setPreviousReadTime(vo.getBeginTime());
				} else {
					InstructmentDataVo ybsj = new InstructmentDataVo();
					ybsj.setPreviousReadOut(vo.getVal());
					ybsj.setPreviousReadTime(vo.getBeginTime());
					ybsj.setHasHbs(0);
					rtn.put(ybid, ybsj);
				}
			}
		}
		// 获取后表数
		HashMap<String, List<FetchRealTimeDataVo>> xbbsm = this.ifrtd.getShiftDataWithMap(dto);
		if (xbbsm != null) {
			for (Entry<String, List<FetchRealTimeDataVo>> x : xbbsm.entrySet()) {
				ybid = x.getKey();
				List<FetchRealTimeDataVo> dl = x.getValue();
				if (dl != null) {
					for (FetchRealTimeDataVo y : dl) {
						val = this.pm.convertDouble(y.getValue(), 0.0);
						if (rtn.containsKey(ybid)) {
							InstructmentDataVo ybsj = rtn.get(ybid);
							ybsj.setLastReadOut(val);
							ybsj.setLastReadTime(y.getTime());
							ybsj.setHasHbs(1);
						} else {
							InstructmentDataVo ybsj = new InstructmentDataVo();
							ybsj.setLastReadOut(val);
							ybsj.setLastReadTime(y.getTime());
							ybsj.setHasHbs(1);
							rtn.put(ybid, ybsj);
						}
					}
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 获取最近的一次的下班时间，用于获取前表数
	 * @param unitid 核算对象ID
	 * @param faid   方案ID
	 * @param sbsj   上班时间
	 * @return
	 */
	private String getLastShiftTime(String unitid, String sbsj) {
		// 下班时间小于等于当班班次上班时间的最大下班时间
		String rtn = "";
		Where where = Where.create();
		where.eq(CostTeamInstrumentData::getUnitId, unitid);
		where.eq(CostTeamInstrumentData::getProgramId, "0");
		where.le(CostTeamInstrumentData::getShiftEndTime, sbsj);
		rtn = this.entityService.findMaxValue(CostTeamInstrumentData.class, CostTeamInstrumentData::getShiftEndTime,
				String.class, where);
		return rtn;
	}

	/**
	 * @category 获取上班表数
	 * @param unitid 核算对象ID
	 * @param xbsj   下班时间
	 * @return
	 */
	private HashMap<String, ClassVo> getPreviousData(String unitid, String xbsj) {
		HashMap<String, ClassVo> rtn = new HashMap<String, ClassVo>();
		Where where = Where.create();
		where.eq(CostTeamInstrumentData::getUnitId, unitid);
		where.eq(CostTeamInstrumentData::getProgramId, "0");
		where.eq(CostTeamInstrumentData::getShiftEndTime, xbsj);
		List<CostTeamInstrumentData> cisl = this.entityService.queryList(CostTeamInstrumentData.class, where, null);
		if (cisl != null) {
			Double val;
			String ybid, sj;
			int count = cisl.size();
			for (int i = 0; count > i; i++) {
				CostTeamInstrumentData x = cisl.get(i);
				ybid = x.getInstrumentId();
				if (ybid == null) {
					ybid = "";
				}
				sj = x.getLastReadTime();
				if (sj == null) {
					sj = "";
				}
				val = x.getLastReadOut();
				if (val == null) {
					val = 0.0;
				}
				ClassVo vo = new ClassVo();
				vo.setBeginTime(sj);
				vo.setVal(val);
				rtn.put(ybid, vo);
			}
		}
		return rtn;
	}

	@Override
	public HashMap<String, InstructmentDataVo> getProgramData(FetchRealTimeDataDTO dto) {
		HashMap<String, InstructmentDataVo> rtn = new HashMap<String, InstructmentDataVo>();
		Double val;
		String unitid = dto.getUnitId();
		String sbsj = dto.getKssj();
		String ybid;
		// 获取上班表数
		HashMap<String, ClassVo> sbbsm = this.getLastPreviousData(unitid, sbsj);
		if (sbbsm != null) {
			for (Entry<String, ClassVo> x : sbbsm.entrySet()) {
				ybid = x.getKey();
				ClassVo vo = x.getValue();
				if (rtn.containsKey(ybid)) {
					InstructmentDataVo ybsj = rtn.get(ybid);
					ybsj.setPreviousReadOut(vo.getVal());
					ybsj.setPreviousReadTime(vo.getBeginTime());
				} else {
					InstructmentDataVo ybsj = new InstructmentDataVo();
					ybsj.setPreviousReadOut(vo.getVal());
					ybsj.setPreviousReadTime(vo.getBeginTime());
					ybsj.setHasHbs(0);
					rtn.put(ybid, ybsj);
				}
			}
		}
		// 获取后表数
		HashMap<String, List<FetchRealTimeDataVo>> xbbsm = this.ifrtd.getShiftDataWithMap(dto);
		if (xbbsm != null) {
			for (Entry<String, List<FetchRealTimeDataVo>> x : xbbsm.entrySet()) {
				ybid = x.getKey();
				List<FetchRealTimeDataVo> dl = x.getValue();
				if (dl != null) {
					for (FetchRealTimeDataVo y : dl) {
						val = this.pm.convertDouble(y.getValue(), 0.0);
						if (rtn.containsKey(ybid)) {
							InstructmentDataVo ybsj = rtn.get(ybid);
							ybsj.setLastReadOut(val);
							ybsj.setLastReadTime(y.getTime());
							ybsj.setHasHbs(1);
						} else {
							InstructmentDataVo ybsj = new InstructmentDataVo();
							ybsj.setLastReadOut(val);
							ybsj.setLastReadTime(y.getTime());
							ybsj.setHasHbs(1);
							rtn.put(ybid, ybsj);
						}
					}
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 获取上班表数
	 * @param unitid 核算对象ID
	 * @param xbsj   下班时间
	 * @return
	 */
	private HashMap<String, ClassVo> getLastPreviousData(String unitid, String xbsj) {
		HashMap<String, ClassVo> rtn = new HashMap<String, ClassVo>();
		String sql = "select a.instrumentid,a.lastreadout,a.lastreadtime from costbatchinstrumentdata a inner join "
				+ "(select instrumentid,max(endtime) as et from costbatchinstrumentdata where unitid=? and endtime<=? group by instrumentid) b"
				+ " on a.instrumentid=b.instrumentid and a.endtime=b.et";
		List<Map<String, Object>> list = entityService.queryListMap(sql, unitid, xbsj);
		if (list != null) {
			Double dval;
			String ybid, sj;
			Object oval;
			int count = list.size();
			for (int i = 0; count > i; i++) {
				Map<String, Object> x = list.get(i);
				ybid = (String) x.get("instrumentid");
				if (ybid == null) {
					ybid = "";
				}
				oval=x.get("lastreadtime");
				if (oval==null) {
					sj="";
				}else {
					sj=oval.toString();
				}
				oval=x.get("lastreadout");
				if (oval==null) {
					dval = 0.0;
				}else {
					dval = Double.parseDouble(oval.toString());
				}
				ClassVo vo = new ClassVo();
				vo.setBeginTime(sj);
				vo.setVal(dval);
				rtn.put(ybid, vo);
			}
		}
		return rtn;
	}

}
