package com.yunhesoft.leanCosting.costReport.service;

import java.util.HashMap;

import com.yunhesoft.leanCosting.calcLogic.FetchRealTimeDataDTO;
import com.yunhesoft.leanCosting.costReport.entity.vo.InstructmentDataVo;

public interface IGetShiftDataService {

	/**
	 * @category 交接班获取前后表数
	 * @param dto 传入核算对象ID，上下班时间，班组和班次
	 * @return
	 */
	public HashMap<String, InstructmentDataVo> getShiftData(FetchRealTimeDataDTO dto);

	/**
	 * @category 按照方案时间进行数据采集
	 * @param dto
	 * @return
	 */
	public HashMap<String, InstructmentDataVo> getProgramData(FetchRealTimeDataDTO dto);

}
