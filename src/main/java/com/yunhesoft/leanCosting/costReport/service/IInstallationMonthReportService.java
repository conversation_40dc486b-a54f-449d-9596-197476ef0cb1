package com.yunhesoft.leanCosting.costReport.service;

import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.dto.CalculateTimeDto;
import com.yunhesoft.leanCosting.costReport.entity.vo.InstallationMonthReportVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.ProjectDataVo;

/**
 * 装置月汇总表录入服务类
 * 
 * @Description:
 * <AUTHOR>
 * @date 2022年07月22日
 */
public interface IInstallationMonthReportService {
	/**
	 * 获取统计时段
	 * @category 
	 * <AUTHOR> 
	 * @param zzdm 装置代码
	 * @param yf 月份
	 * @param projectId 方案id
	 * @return CalculateTimeDto
	 */
	public CalculateTimeDto getCalculateTime(String zzdm,String yf,String projectId);
	/**
	 * 保存统计时段
	 * @category 
	 * <AUTHOR> 
	 * @param calBean 统计时段信息bean
	 * @return boolean 成功true 失败false
	 */
    public boolean saveCalculateTime(CalculateTimeDto calBean);
    /**
        * 获取方案列表
     * @category 
     * <AUTHOR> 
     * @param zzdm 装置代码
     * @param ksrq 开始日期
     * @param jzrq 截止日期
     * @return
     */
	public List<ProjectDataVo> getProjectList(String zzdm);
	/**
	 * 获取装置月报数据
	 * @category 
	 * <AUTHOR> 
	 * @param dto 统计时段
	 * @param init 是否读取初始化值（仅清理时使用true，正常读取使用false）
	 * @return List<InstallationMonthReportVo>
	 */
	public List<InstallationMonthReportVo> getDataList(CalculateTimeDto calBean,boolean init);
	/**
	 * 保存装置月报数据
	 * @category 
	 * <AUTHOR> 
	 * @param tbrq 填表日期
	 * @param dataList 数据列表
	 * @return boolean 成功true 失败false
	 */
	public String saveDataList(CalculateTimeDto calBean,List<InstallationMonthReportVo> dataList);
	/**
	 * 清除数据
	 * @category 
	 * <AUTHOR> 
	 * @param dto 统计时段
	 * @return boolean 成功true 失败false
	 */
	List<InstallationMonthReportVo> clearData(CalculateTimeDto calBean);

}
