package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.costReport.entity.dto.BzyhzbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ComboDto;
import com.yunhesoft.leanCosting.costReport.service.BzyhzbService;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-6-13
 */
@Service
public class BzyhzbServiceImpl implements BzyhzbService {
	private Logger logger = LoggerFactory.getLogger(BzyhzbServiceImpl.class);
//    @Autowired
//    private EntityService entityService;

	/****
	 * 单个信息查询
	 *************************************************************************************/

	/****
	 * 列表信息查询（暂无用）
	 *************************************************************************************/
	/**
	 * @category 工作台历列表查询
	 * @param WorkCalendarInfo
	 * @param page
	 * @return
	 */
	public Res<BzyhzbDto> selectList(BzyhzbDto dto) {
		if (dto.getFcdm() == null || dto.getFcdm().length() < 6) {
			//return (Res<BzyhzbDto>) Res.ERROR("请重新登录或选择分厂进行操作！");
		}
//		EntityDao<BBzyhzb> dao = new EntityDao<BBzyhzb>(dto.getFcdm(), BBzyhzb.class);
//		String sql = " where id.csdm='填表日期' and id.zzdm='" + dto.getZzdm() + "' and id.yf like '"
//				+ dto.getYf().replace("-", "") + "%' and id.bzdm=" + dto.getBzdm() + " and id.pid='" + dto.getPid()
//				+ "'";
//		List<BBzyhzb> list = dao.findByWhereString(sql);
//		Res<BzyhzbDto> res = new Res<BzyhzbDto>();
//		if (list.size() > 0) {
//			List<String> ilist = new ArrayList<String>();
//			ilist.add("");// 加一个空，用于添加操作
//			for (BBzyhzb hzb : list) {
//				if (hzb.getId().getYf().endsWith("0000"))
//					continue;
//				ilist.add(hzb.getId().getYf());
//			}
//			dto.setYfList(ilist);
//
//			// 目前按加载月份列表后，不显示列表为准
////    		BBzyhzb obj = list.get(0);
//
//		} else {
//			List<String> ilist = new ArrayList<String>();
//			ilist.add("");// 加一个空，用于添加操作
//			dto.setYfList(ilist);
//		}
//
//		int diff = 7;
//		if (Coms.judgeLong(dto.getDiffday())) {
//			diff = Math.abs(Integer.parseInt(dto.getDiffday()));
//		}
//
//		dto.setYfVal("");
//		dto.setKsrq(Dates.formatDate(Dates.doDate(Dates.getNowDate(), -diff))); // 默认开始时间-7天
//		dto.setJzrq(Dates.getNowDateStr());
//		dto.setZbrq(Dates.getNowDateStr());
//		dto.setZbr(dto.getZbr());
//		dto.setSm("");
//		res.setResult(dto);
		return null;//res;

//    	Where where=Where.create();
//    	try {
//	    	if(ObjUtils.notEmpty(WorkCalendarInfo.getTmused())){
//	    		where.and("TMUSED = ?",WorkCalendarInfo.getTmused());
//	    	}
//	    	Order order = Order.create();
//			order.orderByAsc(PmContInfo::getTmsort);
//	        // 读取总记录数量
//			Res<List<WorkCalendarInfo>> res = new Res<List<WorkCalendarInfo>>();
//			res.setTotal(entityService.queryCount(WorkCalendarInfo.class, where));
//			// 读取记录结果
//			res.setResult(entityService.queryList(WorkCalendarInfo.class, where, order, page));
//			return res;
//		} catch (IllegalArgumentException e) {
//			logger.error("", e);
//			throw new RuntimeException(e);
//		}
	}

	/****
	 * 工作台历相关信息列表信息查询
	 *************************************************************************************/
	/**
	 * 工作台历列表查询
	 * 
	 * @param queryParam 检索条件
	 * @param page       分页信息
	 * @return
	 */
	@Override
	public Res<BzyhzbDto> queryCondition(BzyhzbDto dto) {
		if (dto.getFcdm() == null || dto.getFcdm().length() < 6) {
			//return (Res<BzyhzbDto>) Res.ERROR("请重新登录或选择分厂进行操作！");
		}
//		EntityDao<BBzyhzb> dao = new EntityDao<BBzyhzb>(dto.getFcdm(), BBzyhzb.class);
//		String sql = " where id.csdm='填表日期' and id.zzdm='" + dto.getZzdm() + "' and id.yf = '" + dto.getYfVal()
//				+ "' and id.yf!='' ";
//		List<BBzyhzb> list = dao.findByWhereString(sql);
//		Res<BzyhzbDto> res = new Res<BzyhzbDto>();
//
//		if (list.size() > 0) {
//			BBzyhzb obj = list.get(0);
//			String kssj = Dates.formatDateTime(obj.getQssj());
//			String jzsj = Dates.formatDateTime(obj.getJzsj());
//			dto.setKsrq(kssj.substring(0, 10)); // 默认开始时间-7天
//			dto.setJzrq(jzsj.substring(0, 10));
//			dto.setKssj(kssj.substring(11, 19));
//			dto.setJzsj(jzsj.substring(11, 19));
//			dto.setZbrq(obj.getZbrq());
//			dto.setZbr(obj.getZbr());
//			dto.setSm(obj.getBhsm());
//			res.setResult(dto);
//		}

		// where 条件
//        Where where = Where.create();
//        where.eq(PmContInfo::getTmused, 1);
//        if (queryParam != null) {
//        	if (StringUtils.isNotEmpty(queryParam.getProName())) {
//                where.andIns(WorkCalendarInfo::getProjectId, queryParam.getProName());
//            }
//        	if (StringUtils.isNotEmpty(queryParam.getContractId())) {
//        		where.andIns(WorkCalendarInfo::getContId, queryParam.getContractId());
//        	}
//            if (StringUtils.isNotEmpty(queryParam.getTypeName())) {
//                where.like(WorkCalendarInfo::getContName, queryParam.getContName());
//            }
//        }
//        // 排序
//        Order order = Order.create();
//        order.orderByAsc(WorkCalendarInfo::getTmsort);
//        return entityService.queryData(WorkCalendarInfo.class, where, order, page);
		return null;//res;
	}

	/****
	 * 合同管理子信息列表信息查询
	 *************************************************************************************/

	/**
	 * 保存替换工作台历数据
	 */
	@Override
	public BzyhzbDto saveReplaceData(BzyhzbDto dto) {
		boolean bln = false;
		if (dto != null && dto.getFcdm() != null && dto.getFcdm().length() >= 6) { // StringUtils.isNotEmpty(param.getData()))
																					// {
//			Date nd = Dates.getNowDate();
//
//			BBzyhzb obj = null;
//			EntityDao<BBzyhzb> dao = new EntityDao<BBzyhzb>(dto.getFcdm(), BBzyhzb.class);
//			String sql = " where id.csdm='填表日期' and id.zzdm='" + dto.getZzdm() + "' and id.yf = '" + dto.getYfVal()
//					+ "' and id.yf!='' ";
//
//			List<BBzyhzb> templateList = dao.findByWhereString(sql);
//			// 需要看一下时间的内容是否 hh:mm
////            System.out.println("时间的内容:"+dto.getKssj());
//			Date qssj = Dates.parseDateTime(
//					dto.getKsrq() + " " + (StringUtils.isEmpty(dto.getKssj()) ? "00:00:00" : dto.getKssj()));
//			Date jzsj = Dates.parseDateTime(
//					dto.getJzrq() + " " + (StringUtils.isEmpty(dto.getJzsj()) ? "00:00:00" : dto.getJzsj()));
//
//			if (templateList.size() > 0) {
//				obj = templateList.get(0);
//				obj.setQssj(qssj);
//				obj.setJzsj(jzsj);
//				obj.setZbr(dto.getZbr());
//				obj.setZbrq(dto.getZbrq());
//				obj.setBhsm(dto.getSm());
//				bln = dao.update(obj);
//				CalcPeriodReport cpr = new CalcPeriodReport();
//				String rtn = cpr.calc(dto.getZzdm(), obj.getId().getYf(), obj.getId().getPid());
//			} else {
//				obj = new BBzyhzb();
//				obj.setQssj(qssj);
//				obj.setJzsj(jzsj);
//				obj.setZbr(dto.getZbr());
//				obj.setZbrq(dto.getZbrq());
//				obj.setBhsm(dto.getSm());
//
//				BBzyhzbId bid = new BBzyhzbId();
//				bid.setZzdm(dto.getZzdm());
//				bid.setBzdm(Integer.parseInt(dto.getBzdm()));
//				bid.setCsdm("填表日期");
//				bid.setPid(dto.getPid());
//				bid.setBcdm(0);
//				PRLogic pl = new PRLogic();
//				pl.setDbname(dto.getFcdm());
//				String newYf = pl.newBh(dto.getZzdm(), dto.getYf());// 插入时生成新的编号
//				bid.setYf(newYf);
//
//				obj.setId(bid);
//
//				bln = dao.insert(obj);
//
//				if (bln) {
//					CalcPeriodReport cpr = new CalcPeriodReport();
//					String rtn = cpr.calc(dto.getZzdm(), newYf, dto.getPid());
//					dto.setYfVal(newYf);
//					sql = " where id.csdm='填表日期' and id.zzdm='" + dto.getZzdm() + "' and id.yf like '"
//							+ dto.getYf().replace("-", "") + "%' and id.bzdm=" + dto.getBzdm() + " and id.pid='"
//							+ dto.getPid() + "'";
//					List<BBzyhzb> list = dao.findByWhereString(sql);
//					if (list.size() > 0) {
//						List<String> ilist = new ArrayList<String>();
//						ilist.add("");// 加一个空，用于添加操作
//						for (BBzyhzb hzb : list) {
//							if (hzb.getId().getYf().endsWith("0000"))
//								continue;
//							ilist.add(hzb.getId().getYf());
//						}
//						dto.setYfList(ilist);
//					}
//				}
//			}
//			templateList = null;
		}
		return dto;
	}

	@Override
	public Res<List<ComboDto>> queryClassList(BzyhzbDto dto) {
		Res<List<ComboDto>> res = new Res<List<ComboDto>>();
		List<ComboDto> list = new ArrayList<ComboDto>();
		ComboDto zzcom = new ComboDto();
		zzcom.setKey("0");
		zzcom.setValue("装置");
		list.add(zzcom);

//		EntityDao<BZz> zzdao = new EntityDao<BZz>(BZz.class);
//		List<BZz> zzlist = zzdao.findByWhereString(" where used=1 and zzdm='" + dto.getZzdm() + "'");
//		if (zzlist.size() > 0) {
//			BZz zz = zzlist.get(0);
//			String zzdm = StringUtils.isEmpty(zz.getLhzz()) ? zz.getZzdm() : zz.getLhzz();
//			EntityDao<BZzbzbm> bzdao = new EntityDao<BZzbzbm>(BZzbzbm.class);
//			List<BZzbzbm> bzlist = bzdao.findByWhereString(" where used=1 and id.zzdm='" + zzdm + "' order by px");
//			if (bzlist != null && bzlist.size() > 0) {
//				for (BZzbzbm bz : bzlist) {
//					ComboDto bzcom = new ComboDto();
//					bzcom.setKey(String.valueOf(bz.getId().getBzdm()));
//					bzcom.setValue(bz.getBzmc());
//					list.add(bzcom);
//				}
//			}
//		}
		res.setResult(list);
		return res;
	}

}
