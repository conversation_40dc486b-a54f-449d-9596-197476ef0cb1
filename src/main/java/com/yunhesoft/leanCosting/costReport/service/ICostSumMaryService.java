package com.yunhesoft.leanCosting.costReport.service;

import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostSumMaryVo;

public interface ICostSumMaryService {

	/**
	 * 查询核算报表
	 * 
	 * @param dto
	 * @return
	 */
	public List<CostSumMaryVo> getData(CostReportQueryDto dto);
	//周报

	/**
	 * 周报-核算项目保存
	 * 
	 * @param dto
	 * @return 保存失败返回失败原因
	 */
	public String saveData(List<CostSumMaryVo> dto);

	/**
	 * 周报周报-核算使用
	 * 
	 * @param dto
	 * @return
	 */
	List<CostSummaryItemData> getCostSummaryItemDatas(CostReportQueryDto dto);
	// 周报END
	//交接班

	
	// 交接班END
	
	List<CostSummaryInfo> getDataList(CostReportQueryDto dto);
	
	
	List<CostSummaryInfo> getInfoList(CostReportQueryDto dto);
}
