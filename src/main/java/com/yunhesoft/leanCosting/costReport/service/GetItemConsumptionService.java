package com.yunhesoft.leanCosting.costReport.service;

import java.util.HashMap;
import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.dto.TeamProgramDto;
import com.yunhesoft.leanCosting.costReport.entity.vo.ItemConsumption;
import com.yunhesoft.leanCosting.costReport.entity.vo.ItemLedgerVo;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;

public interface GetItemConsumptionService {

	/**
	 * @category 获取对象的日消耗量，装置日报使用
	 * @param unitId    核算对象ID
	 * @param queryDate 查询日期，使用统计日期
	 * @param programId 如果传入空字符串，认为不区分方案
	 * @return
	 */
	public List<ItemConsumption> getUnitDayItemConsumption(String unitId, String queryDate, String programId,
			HashMap<String, Costitem> xm);

	/**
	 * @category 从交接班按照给定周期获取对象的消耗量：分时段报表
	 * @param unitId    核算对象ID
	 * @param beginDate 开始时间：传入日期格式(yyyy-mm-dd)时使用填表日期；传入时间格式(yyyy-mm-dd
	 *                  hh:mi:ss)时使用上下班时间
	 * @param endDate   结束时间：传入日期格式(yyyy-mm-dd)时使用填表日期；传入时间格式(yyyy-mm-dd
	 *                  hh:mi:ss)时使用上下班时间
	 * @param programId 如果传入空字符串，认为不区分方案
	 * @param teamid    班组ID，不传代表查核算对象的
	 * @return
	 */
	public List<ItemConsumption> getUnitPeriodItemConsumption(String unitId, String beginDate, String endDate,
			String programId, String teamid, HashMap<String, Costitem> xm);

	/**
	 * @category 获取周报使用的消耗量：装置使用日报数据；班组使用交接班（统计日期）
	 * @param unitId    核算对象ID
	 * @param beginDate 开始日期
	 * @param endDate   结束日期
	 * @param programId 方案ID
	 * @param teamid    班组ID
	 * @return
	 */
	public List<ItemConsumption> getUnitWeekItemConsumption(String unitId, String beginDate, String endDate,
			String programId, String teamid, HashMap<String, Costitem> xm);

	/**
	 * @category 按照给定周期获取对象内班组的周消耗量
	 * @param unitId    核算对象ID
	 * @param beginDate 开始时间：传入日期格式(yyyy-mm-dd)时使用填表日期；传入时间格式(yyyy-mm-dd
	 *                  hh:mi:ss)时使用上下班时间
	 * @param endDate   结束时间：传入日期格式(yyyy-mm-dd)时使用填表日期；传入时间格式(yyyy-mm-dd
	 *                  hh:mi:ss)时使用上下班时间
	 * @param programId 如果传入空字符串，认为不区分方案
	 * @return
	 */
	public List<ItemConsumption> getTeamPeriodItemConsumption(String unitId, String beginDate, String endDate,
			String programId, HashMap<String, Costitem> xm);

	/**
	 * @category 得到班组一个班内使用的项目量
	 * @param unitId
	 * @param queryDate
	 * @param programId
	 * @param shiftId
	 * @param teamId
	 * @return
	 */
	public List<ItemConsumption> getTeamProgramItemConsumption(String unitId, String queryDate, String programId,
			String shiftId, String teamId, HashMap<String, Costitem> xm);

	/**
	 * @category 获取班组在一个班内的项目量
	 * @param unitId
	 * @param queryDate
	 * @param shiftId
	 * @param teamId
	 * @param xm
	 * @return
	 */
	public List<ItemConsumption> getTeamItemConsumption(String unitId, String queryDate, String shiftId, String teamId,
			HashMap<String, Costitem> xm);

	/**
	 * @category 获取下级核算对象的项目量
	 * @param unitId    核算对象ID
	 * @param queryDate 日期
	 * @param shiftId   不指定是日报
	 * @param teamId    不指定是日报
	 * @param xm
	 * @return
	 */
	public List<ItemConsumption> getSubUnitItemConsumption(String unitId, String queryDate, String shiftId,
			String teamId, String programId, HashMap<String, Costitem> xm);

	/**
	 * @category 获取物料台账的项目信息
	 * @param dto 查询数据使用的参数
	 * @return
	 */
	public HashMap<String, HashMap<String, ItemLedgerVo>> getItemLedgerData(TeamProgramDto dto);

	/**
	 * @category 获取装置月汇总的消耗量：年累计
	 * @param unitId     核算对象ID
	 * @param beginMonth 开始日期
	 * @param endMonth   结束日期
	 * @param programId  方案ID
	 * @return
	 */
	public List<ItemConsumption> getUnitYearItemConsumption(String unitId, String beginMonth, String endMonth,
			String programId, HashMap<String, Costitem> xm);

}
