package com.yunhesoft.leanCosting.costReport.service;

import java.util.List;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.costReport.entity.dto.BzyhzbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ComboDto;

/**
 * 工作台历Service接口
 * 
 * <AUTHOR>
 * @date 2022-6-13
 */
public interface BzyhzbService {
    
    /**
     * 查询报表月份列表
     * 
     * @param  dto 查询对象信息
     * @return 结果对象
     */
    public Res<BzyhzbDto> selectList(BzyhzbDto dto);

    /**
     * 报表对象查询
     * 
     * @param dto
     * @return 结果对象
     */
    public Res<BzyhzbDto> queryCondition(BzyhzbDto dto);

    /**
     * 保存数据
     */
    BzyhzbDto saveReplaceData(BzyhzbDto param);
    
    /**
     * 班组列表查询
     * 
     * @param dto
     * @return 结果对象
     */
    public Res<List<ComboDto>> queryClassList(BzyhzbDto dto);
}
