package com.yunhesoft.leanCosting.costReport.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamProgramDto;
import com.yunhesoft.leanCosting.costReport.entity.vo.ItemLedgerVo;
import com.yunhesoft.leanCosting.costReport.service.GetItemConsumptionService;
import com.yunhesoft.leanCosting.costReport.service.ICostItemLedgerService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostBindOrgDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CostItemLedgerServiceImpl implements ICostItemLedgerService {

    @Autowired
    private ISysOrgService orgSrv;

    @Autowired
    private IUnitMethodService unitSrv;

    @Autowired
    private ICostService costSrv;

    @Autowired
    private GetItemConsumptionService dataSrv;

    @Override
    public List<Costuint> getCostUnitComboList() {
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String orgId = currentUser == null ? null : currentUser.getOrgId();
        List<Costuint> list = costSrv.getCostuintListByOrgId(orgId, 2);
        List<Costuint> newlist = new ArrayList<Costuint>();
        for (Costuint costuint : list) {
        	Integer productive = costuint.getProductive() == null ? 0 : costuint.getProductive();
        	if(productive==0) {
        		newlist.add(costuint);
        	}
		}
        return newlist;
    }

    @Override
    public JSONArray getCostTeamComboList (String unitId) {
        CostBindOrgDto dto = new CostBindOrgDto();
        dto.setUnitid(unitId);
        dto.setObjType("org");
        List<Costunitoperator> list = costSrv.getCostunitoperatorList(dto);
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        JSONArray result = new JSONArray();
        List<String> orgIdList = new ArrayList<>();
        for (Costunitoperator item : list) {
            orgIdList.add(item.getObjid());
        }
        List<SysOrg> orgList = orgSrv.getOrgListById(orgIdList);
        if (StringUtils.isEmpty(orgList)) {
            return result;
        }
        Map<String, String> orgMap = orgList.stream().collect(Collectors.toMap(SysOrg::getId, v -> v.getOrgname()));
        for (Costunitoperator item : list) {
            String orgId = item.getObjid();
            String orgName = orgMap.get(orgId);
            if (StringUtils.isEmpty(orgName)) {
                continue;
            }
            JSONObject obj = new JSONObject();
            obj.put("id", orgId);
            obj.put("name", orgName);
            result.add(obj);
        }
        return result;
    }

    /**
     * 查询核算项目分类下拉list
     * @param unitId
     * @param dt
     * @return
     */
    @Override
    public List<Costclass> getCostItemClassComboList(String unitId, String dt) {
        String maxVersion = unitSrv.getMaxVersionCostunit(unitId, dt);
        MethodQueryDto queryDto = new MethodQueryDto();
        queryDto.setUnitid(unitId);
        queryDto.setMaxBegintime(maxVersion);
        return unitSrv.getCostclassList(queryDto);
    }

    @Override
    public List<Costitem> getCostItemList(String unitId, String dt, String classId) {
        return null;
    }

    @Override
    public JSONObject getQueryTableJsonObj(String unitId, String teamId, String start, String end, String classId) {
        MethodQueryDto queryDto = new MethodQueryDto();
        queryDto.setUnitid(unitId);
        queryDto.setMaxBegintime(unitSrv.getMaxVersionCostunit(unitId, end));
        List<Costclass> classList = this.getCostItemClassComboList(unitId, end);
        List<String> classIdList = new ArrayList<>();
        Map<String, Costclass> classMap = new HashMap<>();
        if (StringUtils.isNotEmpty(classList)) {
            for (Costclass item : classList) {
                classIdList.add(item.getId());
                classMap.put(item.getId(), item);
            }
        }
        classIdList = "0".equals(classId) ? classIdList : Collections.singletonList(classId);
        queryDto.setPidList(classIdList);
        List<Costitem> itemList = unitSrv.getCostitemList(queryDto);
        if (StringUtils.isEmpty(itemList)) {
            return null;
        }
        JSONObject result = new JSONObject();
        Map<String, List<Costitem>> itemGroup = new HashMap<>();
        for (Costitem item : itemList) {
            String pid = item.getPid();
            itemGroup.computeIfAbsent(pid, k -> new ArrayList<>()).add(item);
        }

        JSONArray colArray = new JSONArray();

        JSONObject datecol = new JSONObject();
        datecol.put("alias", "date");
        datecol.put("align", "center");
        datecol.put("width", "120");
        datecol.put("header", "日期");
        colArray.add(datecol);

        for (String clsId : classIdList) {
            Costclass costclass = classMap.get(clsId);
            JSONObject col = new JSONObject();
            col.put("alias", costclass.getId());
            col.put("header", costclass.getCcname());
            List<Costitem> costitems = itemGroup.get(clsId);
            if (StringUtils.isNotEmpty(costitems)) {
                JSONArray children = new JSONArray();
                //分类下有项目
                for (Costitem costitem : costitems) {
                    JSONObject child = new JSONObject();
                    String itemunit = costitem.getItemunit();
                    child.put("alias", costitem.getId());
                    child.put("align", "right");
                    child.put("header", costitem.getItemname()+(StringUtils.isNotEmpty(itemunit)?"("+itemunit+")":""));
                    children.add(child);
                }
                col.put("children", children);
            }
            colArray.add(col);
        }
        result.put("column", colArray);

        TeamProgramDto dto = new TeamProgramDto();
        dto.setUnitId(unitId);
        dto.setTeamId(teamId);
        dto.setProgramId("0");
        dto.setShiftBeginTime(start);
        dto.setShiftEndTime(end);
        dto.setShiftId("0");
        HashMap<String, HashMap<String, ItemLedgerVo>> itemDataList = dataSrv.getItemLedgerData(dto);

        int scale = 3;

        JSONArray data = new JSONArray();
        List<String> dateBetween = this.getDateBetween(start, end);
        if (StringUtils.isNotEmpty(dateBetween)) {
            for (String date : dateBetween) {
                JSONObject obj = new JSONObject();
                obj.put("date", date);
                HashMap<String, ItemLedgerVo> map = itemDataList == null ? null : itemDataList.get(date);
                if (StringUtils.isNotEmpty(map)) {
                    Set<String> strings = map.keySet();
                    for (String key : strings) {
                        ItemLedgerVo item = map.get(key);
                        Double dayCount = item == null ? null : item.getDayCount();
                        String v = dayCount == null ? "" : new BigDecimal(dayCount).setScale(scale, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                        obj.put(key, v);
                    }
                }
                data.add(obj);
            }


            JSONObject sumRow = new JSONObject();
            sumRow.put("date", "合计");
            HashMap<String, ItemLedgerVo> summap = itemDataList == null ? null : itemDataList.get("累加");
            if (StringUtils.isNotEmpty(summap)) {
                Set<String> strings = summap.keySet();
                for (String key : strings) {
                    ItemLedgerVo item = summap.get(key);
                    Double dayCount = item == null ? null : item.getDayCount();
                    String v = dayCount == null ? "" : new BigDecimal(dayCount).setScale(scale, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                    sumRow.put(key, v);
                }
            }
            data.add(sumRow);


            JSONObject avgRow = new JSONObject();
            avgRow.put("date", "平均");
            HashMap<String, ItemLedgerVo> avgmap = itemDataList == null ? null : itemDataList.get("平均");
            if (StringUtils.isNotEmpty(avgmap)) {
                Set<String> strings = avgmap.keySet();
                for (String key : strings) {
                    ItemLedgerVo item = avgmap.get(key);
                    Double dayCount = item == null ? null : item.getDayCount();
                    String v = dayCount == null ? "" : new BigDecimal(dayCount).setScale(scale, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                    avgRow.put(key, v);
                }
            }
            data.add(avgRow);

        }

        result.put("data", data);


        return result;
    }

    private List<String> getDateBetween(String start, String end) {
        int diff = DateTimeUtils.dayDiff(DateTimeUtils.parseDate(start), DateTimeUtils.parseDate(end));
        if (diff < 0) {
            return null;
        }
        List<String> result = new ArrayList<>();
        for (int i=0; i<=diff; i++) {
            Date date = DateTimeUtils.doDate(DateTimeUtils.parseDate(start), i);
            result.add(DateTimeUtils.formatDate(date, "yyyy-MM-dd"));
        }
        return result;

    }


    @Override
    public void exportExcel (HttpServletResponse response, String unitId, String teamId, String start, String end, String classId) {
        List<ExcelExportEntity> colList = new ArrayList<>();

        JSONObject jsonObj = this.getQueryTableJsonObj(unitId, teamId, start, end, classId);
        JSONArray column = jsonObj != null && jsonObj.containsKey("column") ? jsonObj.getJSONArray("column") : new JSONArray();
        for (int i=0; i<column.size(); i++) {
            JSONObject col = column.getJSONObject(i);

            JSONArray children = col.containsKey("children") ? col.getJSONArray("children") : null;
            if (StringUtils.isNotEmpty(children)) {
                for (int j=0; j<children.size(); j++) {
                    JSONObject child = children.getJSONObject(j);
                    ExcelExportEntity childCol = new ExcelExportEntity(child.getString("header"), child.getString("alias"));
                    childCol.setNeedMerge(true);
                    childCol.setWidth(20);
                    childCol.setGroupName(col.getString("header"));
                    colList.add(childCol);
                }
            } else {
                ExcelExportEntity colEntity = new ExcelExportEntity(col.getString("header"), col.getString("alias"));
                colEntity.setNeedMerge(true);
                colEntity.setWidth(20);
                colList.add(colEntity);
            }
        }

//        ExcelExportEntity en = new ExcelExportEntity("a", "b");
//        colList.add(en);

        JSONArray data = jsonObj != null && jsonObj.containsKey("data") ? jsonObj.getJSONArray("data") : new JSONArray();
//        List<Map> list = new ArrayList<>();
        ExportParams params = new ExportParams();
//        params.setTitle("test");
        if (StringUtils.isNotEmpty(colList)) {
            params.setAddIndex(true);
        }
        Workbook workbook = ExcelExport.exportExcel(params, colList, data, null);

        String fileName = new Date().getTime()+ ".xlsx";

        ExcelExport.downLoadExcel(fileName, response, workbook);
    }


}
