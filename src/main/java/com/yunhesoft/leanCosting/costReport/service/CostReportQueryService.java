package com.yunhesoft.leanCosting.costReport.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.leanCosting.costReport.entity.dto.BzybReportCxDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostOutBzyhzdExcelDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostOutFsdExcelDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostOutRbExcelDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.DayReportDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.DayReportTitleDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ReportBzyhzQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.SaveReportDataDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CommonlyBean;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tds.entity.vo.TdsTableColumn;

public interface CostReportQueryService {
    
	List<TdsTableColumn> getReportHeaderList(ReportQueryDto dto1);

	/**
	 * 保存分析内容
	 */
	String saveData(String remark, String unitCode, String rq, String projectId);

	JSONArray getDataList(ReportQueryDto dto);

	DayReportTitleDto getTitle(ReportQueryDto dto);

	boolean saveReportData(SaveReportDataDto param);

	List<CostSummaryInfo> queryReportList(String unitCode, String starTime, String endTime, Pagination<?> page);

	List<CommonlyBean> getLxOperation(String uintId);

	List<DayReportDto> getBzybData(ReportBzyhzQueryDto dto);

	List<CostSummaryInfo> getBzybInfo(ReportBzyhzQueryDto dto);

	String saveBzybData(List<DayReportDto> list, ReportBzyhzQueryDto dto);

	BzybReportCxDto getBzybReport(ReportBzyhzQueryDto dto);

	List<CommonlyBean> getTabTitel(ReportBzyhzQueryDto dto);

	CostSummaryInfo getTableInfo(ReportQueryDto dto);

	/**
	 * 获取表头
	 * @param dto1
	 * @return
	 */
	Map<String, CostBgcsszb> getReportHeaderMap(ReportQueryDto dto1);

}
