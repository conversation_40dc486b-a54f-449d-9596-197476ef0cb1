package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostReportAnalysis;
import com.yunhesoft.leanCosting.costReport.service.ICostReportAnalysisService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class CostReportAnalysisServiceImpl implements ICostReportAnalysisService {

	@Autowired
	private EntityService entityService;
	
	@Override
	public List<CostReportAnalysis> getData(CostReportQueryDto dto) {
		// 查询简要分析
		Where where = Where.create();
		if (dto != null) {
			if (!StringUtils.isEmpty(dto.getUnitId())) {
				where.eq(CostReportAnalysis::getUnitId, dto.getUnitId());
			}
			if (!StringUtils.isEmpty(dto.getReportType())) {// 报表类型
				where.eq(CostReportAnalysis::getReportType, dto.getReportType());
			}
			if (!StringUtils.isEmpty(dto.getJzrq())) {// 日期YYYY-MM-DD
				where.eq(CostReportAnalysis::getWriteDay, dto.getJzrq());
			}
			if (!StringUtils.isEmpty(dto.getContentId())) {// 方案ID
				where.eq(CostReportAnalysis::getProgramId, dto.getContentId());
			}
			if (!StringUtils.isEmpty(dto.getMethodId())) {// 班组ID
				where.eq(CostReportAnalysis::getTeamId, dto.getMethodId());
			}
			if (!StringUtils.isEmpty(dto.getShiftId())) {// 班组ID
				where.eq(CostReportAnalysis::getShiftId, dto.getShiftId());
			}
			if (dto.getContentType()!=null) {// 类型 0 手动，1自动
				where.eq(CostReportAnalysis::getContentType, dto.getContentType());
			}
		}
		Order order = Order.create();
		return entityService.queryList(CostReportAnalysis.class, where, order);
	}
	
}
