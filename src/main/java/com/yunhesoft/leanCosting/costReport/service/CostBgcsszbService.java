package com.yunhesoft.leanCosting.costReport.service;

import java.util.LinkedHashMap;
import java.util.List;

import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbInternalDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszbInternal;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszbTemplate;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostBbCodeNameVo;

public interface CostBgcsszbService {

	/**
	 * 获取报表格式设置数据
	 * @param tableName 表名，bb0:批次核算数据查询
	 * @param tableType 标题 ，表头
	 * @param unitid 核算对象编码
	 * @return
	 */
	List<CostBgcsszb> getCostBgcsszbList(CostBgcsszbDto dto);

	/**
	 * 保存数据
	 * @param dto
	 * @return
	 */
	boolean saveCostBgcsszbdata(CostBgcsszbDto dto);

	/**
	 * 报表编码和报表名称
	 * @return
	 */
	List<CostBbCodeNameVo> getBbNameCode();

	/**
	 * 获取内表头数据（包括启用和停用标识）
	 * @param dto
	 * @return
	 */
	CostBgcsszbInternalDto getCostBgcsszbInternal(CostBgcsszbInternalDto dto);

	/**
	 * 保存内表头数据（包括启用和停用标识）
	 * @param dto
	 * @return
	 */
	boolean saveCostBgcsszbInternal(CostBgcsszbInternalDto dto);

	/**
	 * 获取一个核算对象的内表头数据
	 * @param dto
	 * @return
	 */
	LinkedHashMap<String, LinkedHashMap<String, CostBgcsszbInternal>> getCostBgcsszbInternalMap(CostBgcsszbInternalDto dto);

	List<CostBgcsszbTemplate> getCsotBgcsTemplate(CostBgcsszbDto dto);

	/**
	 * 将报表格式放到redis中
	 * @param dto
	 * @return
	 */
	List<CostBgcsszb> getCostBgcsszbListRedis(CostBgcsszbDto dto);

}
