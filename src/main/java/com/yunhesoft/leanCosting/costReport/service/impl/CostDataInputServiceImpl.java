package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.calcLogic.FetchRealTimeDataDTO;
import com.yunhesoft.leanCosting.calcLogic.ICalcTeamProjectLogic;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBatchOnDutyDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbInternalDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostDataInputDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostUnitItemCountDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchOnDuty;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszbInternal;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostDataInputVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostInputQueryStringVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostInputQueryVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostItemForWriteVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.InstructmentDataVo;
import com.yunhesoft.leanCosting.costReport.service.CostBgcsszbService;
import com.yunhesoft.leanCosting.costReport.service.CostDataInputService;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.costReport.service.IGetShiftDataService;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlanStart;
import com.yunhesoft.leanCosting.productSchedu.service.IProductScheduPlanService;
import com.yunhesoft.leanCosting.productSchedu.service.IZzRunStateService;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemInfoVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class CostDataInputServiceImpl implements CostDataInputService {

	/**
	 * 获取核算项目及录入的数据
	 * 
	 * @return
	 */
	@Autowired
	private GetCostItemInfoService costItemServ;

	@Autowired
	private UnitItemInfoService unitItemSev;

	@Autowired
	private EntityService dao;

	@Autowired
	private CostBgcsszbService costBgcssz;

	@Autowired
	ICalcTeamProjectLogic ctpl;

	@Autowired
	IUnitMethodService unitMethoderv;
	
	@Autowired
	IProductScheduPlanService planServ;
	
	@Autowired
	IGetShiftDataService getCostDataServ;
	
	@Autowired
	IZzRunStateService zzRun;
	
	@Autowired
	IProgramService iProg;
	
	@Autowired
	ICostService costServ;


	/**
	 * 获取核算项目及录入的数据（判断是否保存过主数据，未保存需要保存）
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public CostDataInputDto getCostDataInputNew(CostBatchOnDutyDto dto) {
		log.info("","开始"+DateTimeUtils.getDTStr());
		CostDataInputDto result = new CostDataInputDto();
		SysUser user = SysUserHolder.getCurrentUser();
		List<CostDataInputVo> listInputEnd = new ArrayList<CostDataInputVo>();
		CostBatchOnDuty mainObj = addCostBatchOnDuty(dto);
		// 核算录入项目仪表数据
		List<CostBatchInstrumentData> listInputDataAdd = new ArrayList<CostBatchInstrumentData>();

		LinkedHashMap<String, List<CostBatchInstrumentData>> mapInput = new LinkedHashMap<String, List<CostBatchInstrumentData>>();
		Integer hashbs;
		String mainId = "";
		if (mainObj != null) {
			mainId = mainObj.getId();// 核算录入数据的主ID
			String unitId = mainObj.getUnitId();// 核算单元ID
			String programId = mainObj.getProgramId();// 方案ID
			String pid = mainObj.getPid();// 批次ID
			String writeDay = mainObj.getWriteDay();// 填写日期
			String programName = dto.getProgramName();
			String shiftBeginTime = mainObj.getShiftBegintime();
			String shiftEndTime = mainObj.getShiftEndtime();
			String begintime=mainObj.getBeginTime();
			String endtime=mainObj.getEndTime();
			String shiftid=mainObj.getShiftId();
			String nowDt = DateTimeUtils.getNowDateTimeStr();
			String lastrdt=endtime;
			if (DateTimeUtils.bjDate(DateTimeUtils.parseDate(nowDt),
					DateTimeUtils.parseDate(endtime))<=0) {
				//当前时间比批次结束时间晚，使用当前时间
				lastrdt=nowDt;
			}
			HashMap<String, InstructmentDataVo> mapPInput = new HashMap<String, InstructmentDataVo>();
			// 获取核算分类项目仪表数据
			log.info("","开始1"+DateTimeUtils.getDTStr());
			List<CostItemForWriteVo> listClassItemYb = costItemServ.shiftWriteItemInfo(unitId, writeDay, programId);
			log.info("","开始2"+DateTimeUtils.getDTStr());
			LinkedHashMap<String, Integer> mapClassCount = new LinkedHashMap<String, Integer>();
			LinkedHashMap<String, Integer> mapItemCount = new LinkedHashMap<String, Integer>();
			// 计算分类和项目合并
			Where whereInput = Where.create();
			whereInput.eq(CostBatchInstrumentData::getPid, mainId);
			Order orderInput = Order.create();
			orderInput.orderByAsc(CostBatchInstrumentData::getInstrumentId);
			orderInput.orderByAsc(CostBatchInstrumentData::getAddItemGroupId);
			List<CostBatchInstrumentData> listInput = new ArrayList<CostBatchInstrumentData>();
			listInput = dao.queryData(CostBatchInstrumentData.class, whereInput, null, null);
			if (StringUtils.isNotEmpty(listInput)) {
				for (CostBatchInstrumentData temp : listInput) {
					List<CostBatchInstrumentData> listTemp = new ArrayList<CostBatchInstrumentData>();
					if(mapInput!=null && mapInput.containsKey(temp.getInstrumentId())) {
						listTemp = mapInput.get(temp.getInstrumentId());
					}
					listTemp.add(temp);
					mapInput.put(temp.getInstrumentId(), listTemp);
				}
			}
			String userOrgUnittype = costServ.userOrgIsManageOrg(unitId, user.getOrgId(), user.getId(), user.getPostId());
			
			boolean isCollect = true;
			if("manage".equals(userOrgUnittype) ||"operate".equals(userOrgUnittype)) {
				// 操作机构，维护机构：当前时间必须是在当班范围内才允许采集数据
				int ksDiff = 0;
				ksDiff = DateTimeUtils.bjDate(DateTimeUtils.parseDate(nowDt),
						DateTimeUtils.parseDate(shiftBeginTime));
				
				int jzDiff = 0;
				jzDiff = DateTimeUtils.bjDate(DateTimeUtils.parseDate(nowDt),
						DateTimeUtils.parseDate(shiftEndTime));
				if (ksDiff >= 0 && jzDiff<=0) {// 当前时间在上班时间和下班时间范围内，允许采集数据
					isCollect = true;
				}else {
					Integer cg = dto.getCxType();
					if (cg == null) {
						cg = 0;
					}
					if (cg == 1 && "manage".equals(userOrgUnittype)) {
						isCollect = true;
					} else {
						isCollect = false;
					}
				}
			}else {// 管理机构不允许采集数据
				isCollect = false;
			}
			if(isCollect) {// 允许采集数据
				log.info("","开始3"+DateTimeUtils.getDTStr());
				FetchRealTimeDataDTO realtimeDto = new FetchRealTimeDataDTO();
				realtimeDto.setUnitId(dto.getUnitId());
				realtimeDto.setProgramId(dto.getProgramId());
				realtimeDto.setKssj(begintime);
				realtimeDto.setJzsj(endtime);
				realtimeDto.setTbrq(dto.getWriteDay());
				realtimeDto.setShiftId(shiftid);
				realtimeDto.setTENANT_ID("0");
				// 调用文玉林新写的方法获取上个时间的仪表表数
				mapPInput = getCostDataServ.getProgramData(realtimeDto);
				log.info("","开始4"+DateTimeUtils.getDTStr());
			}
			LinkedHashMap<String, CostItemForWriteVo> mapYb = new LinkedHashMap<String, CostItemForWriteVo>();
			if (StringUtils.isNotEmpty(listClassItemYb)) {// 循环项目仪表基础数据，这里把所有的仪表都保存了，用于后续取上班表数时用
				for (CostItemForWriteVo temp : listClassItemYb) {
					String instrumentId = temp.getInstrumentId();// 仪表编码
					if(instrumentId!=null) {
						mapYb.put(instrumentId, temp);
						//通过添加选择增加项目。0 默认显示项目，1 通过新增添加项目，默认是0
						int feedShowType = temp.getFeedShowType()==null?0:temp.getFeedShowType();
						if (mapInput != null && mapInput.containsKey(instrumentId)) {// 仪表录入过数据
							if(feedShowType==0) {
								List<CostBatchInstrumentData> listInputTemp = new ArrayList<CostBatchInstrumentData>();
								listInputTemp = mapInput.get(instrumentId);
								if (listInputTemp!=null) {
									for (CostBatchInstrumentData cp:listInputTemp) {
										if (mapPInput != null && mapPInput.containsKey(instrumentId)) {
											InstructmentDataVo idv=mapPInput.get(instrumentId);
											Double previousReadOut =idv.getPreviousReadOut();
											hashbs=idv.getHasHbs();
											if (hashbs==null) {
												hashbs=0;
											}
											cp.setPreviousReadOut(previousReadOut);
											cp.setPreviousReadTime(idv.getPreviousReadTime());
											Double lastReadOut = 0.0;
											String lastReadTime = lastrdt;
											if (temp.getHasRealTag()!=null && temp.getHasRealTag()==1) {
												if (hashbs==1) {
													lastReadOut = idv.getLastReadOut();
													lastReadTime = idv.getLastReadTime();
													if(lastReadOut==null || lastReadOut==0) {// 读取到的数据为null 或是0，交班表数默认为接班表数
														lastReadOut = previousReadOut;
													}
												} else {
													lastReadOut = cp.getLastReadOut();
												}
												cp.setLastReadOut(lastReadOut);
												cp.setLastReadTime(lastReadTime);
											}
										}
										cp.setId(TMUID.getUID());
										cp.setInstrumentId(instrumentId);
										cp.setPid(mainId);
										cp.setProgramId(programId);
										cp.setUnitId(unitId);
										cp.setEndTime(endtime);
										listInputDataAdd.add(cp);
									}
								}
							}
						} else {// 仪表未录入过数据
							//通过添加选择增加项目。0 默认显示项目，1 通过新增添加项目，默认是0
							if(feedShowType==0) {// 只有是默认的显示项目在没录入过数据的时候才显示
								CostBatchInstrumentData objInput = new CostBatchInstrumentData();
								if (mapPInput != null && mapPInput.containsKey(instrumentId)) {
									InstructmentDataVo idv=mapPInput.get(instrumentId);
									Double previousReadOut =idv.getPreviousReadOut();
									hashbs=idv.getHasHbs();
									if (hashbs==null) {
										hashbs=0;
									}
									objInput.setPreviousReadOut(previousReadOut);
									objInput.setPreviousReadTime(idv.getPreviousReadTime());
									Double lastReadOut = 0.0;
									String lastReadTime=lastrdt;
									if(temp.getHasRealTag()!=null && temp.getHasRealTag()==1) {
										if(hashbs==1) {
											lastReadOut = idv.getLastReadOut();
											lastReadTime = idv.getLastReadTime();
											if(lastReadOut==null || lastReadOut==0) {// 读取到的数据为null 或是0，交班表数默认为接班表数
												lastReadOut = previousReadOut;
											}
										} else {//没有读取到数据，交班表数默认为接班表数
											lastReadOut = previousReadOut;
										}
									}
									objInput.setLastReadOut(lastReadOut);
									objInput.setLastReadTime(lastReadTime);
								}
								objInput.setId(TMUID.getUID());
								objInput.setInstrumentId(instrumentId);
								objInput.setPid(mainId);
								objInput.setProgramId(programId);
								objInput.setUnitId(unitId);
								objInput.setEndTime(endtime);
								List<CostBatchInstrumentData> listInputTemp = new ArrayList<CostBatchInstrumentData>();
								listInputTemp.add(objInput);
								mapInput.put(instrumentId, listInputTemp);
								listInputDataAdd.add(objInput);
							}
						}
					}
				}
			}
			log.info("","开始5"+DateTimeUtils.getDTStr());
			if (StringUtils.isNotEmpty(listInputDataAdd) && isCollect) {// 新添加了仪表
				// 表单组件保存后调用的方法 计算单表消耗
				try {
					log.info("","开始511"+DateTimeUtils.getDTStr());
					TeamReportInputDto reportinfo = new TeamReportInputDto();
					reportinfo.setOrgId("");
					reportinfo.setUnitId(unitId);
					reportinfo.setProgramId(programId);
					reportinfo.setShiftId(shiftid);
					reportinfo.setWriteDay(writeDay);
					reportinfo.setBatchNo("");
					reportinfo.setYbbs(listInputDataAdd);
					reportinfo.setBegintime(begintime);
					reportinfo.setEndtime(endtime);
					reportinfo.setJgid(mainObj.getId());
					ctpl.calcYbbs(reportinfo);
					log.info("","开始512"+DateTimeUtils.getDTStr());
					Where where = Where.create();
					where.eq(CostBatchInstrumentData::getPid, mainId);
					log.info("","开始51"+DateTimeUtils.getDTStr());
					dao.delete(CostBatchInstrumentData.class,where);
					log.info("","开始52"+DateTimeUtils.getDTStr());
					dao.insertBatch(listInputDataAdd);
				} catch (Exception e) {
					log.error("", e);
				}
			}
			log.info("","开始6"+DateTimeUtils.getDTStr());
			/************************************************计算分类和项目的合并行 数 end */
			if (StringUtils.isNotEmpty(listClassItemYb)) {// 循环项目仪表基础数据
				for (CostItemForWriteVo temp : listClassItemYb) {
					String instrumentId = temp.getInstrumentId();// 仪表编码
					int showInShiftWrite = temp.getShowInShiftWrite()==null?1:temp.getShowInShiftWrite();
					if(instrumentId!=null && instrumentId.length()>0 && showInShiftWrite==1) {
						CostDataInputVo obj = new CostDataInputVo();
						/* ***********************************************计算分类和项目的合并行 数 end */
						obj.setBaseConsumption(temp.getBaseConsumption());
						obj.setClassId(temp.getClassId());
						obj.setClassName(temp.getClassName());
						obj.setInstrumentId(instrumentId);
						obj.setInstrumentName(temp.getInstrumentName());
						obj.setItemId(temp.getItemId());
						obj.setItemName(temp.getItemName());
						obj.setItemUnit(temp.getItemUnit());
						obj.setPid(pid);
						obj.setProgramId(programId);
						obj.setUnitId(unitId);
						if (mapInput != null && mapInput.containsKey(instrumentId)) {// 仪表录入过数据
							List<CostBatchInstrumentData> listInputTemp = mapInput.get(instrumentId);
							if(StringUtils.isNotEmpty(listInputTemp)) {
								String classId = mapYb.get(instrumentId).getClassId();
								int ClassCountYb = listInputTemp.size();
								if (mapClassCount != null && mapClassCount.containsKey(classId)) {
									int classCount = mapClassCount.get(classId) + ClassCountYb;
									mapClassCount.put(classId, classCount);
								} else {
									mapClassCount.put(classId, ClassCountYb);
								}
								String itemId = mapYb.get(instrumentId).getItemId();
								if (mapItemCount != null && mapItemCount.containsKey(itemId)) {
									int itemCount = mapItemCount.get(itemId) + ClassCountYb;
									mapItemCount.put(itemId, itemCount);
								} else {
									mapItemCount.put(itemId, ClassCountYb);
								}
								for(CostBatchInstrumentData tempInput : listInputTemp) {
									if (ObjUtils.notEmpty(tempInput)) {
										CostDataInputVo objEnd = new CostDataInputVo();
										objEnd = ObjUtils.copyTo(obj, CostDataInputVo.class);
										objEnd.setId(tempInput.getId());
										objEnd.setCalcVal(tempInput.getCalcVal());
										objEnd.setEndTime(tempInput.getEndTime());
										objEnd.setLastReadOut(tempInput.getLastReadOut());
										objEnd.setLastReadTime(tempInput.getLastReadTime());
										objEnd.setPreviousReadOut(tempInput.getPreviousReadOut());
										objEnd.setPreviousReadTime(tempInput.getPreviousReadTime());
										objEnd.setWriteVal(tempInput.getWriteVal());
										objEnd.setItemPrice(tempInput.getItemPrice());
										objEnd.setAddItemGroupId(tempInput.getAddItemGroupId());
										objEnd.setAddItemRemark(tempInput.getAddItemRemark());
										objEnd.setPid(tempInput.getPid());
										listInputEnd.add(objEnd);
//										iCount++;
									}
								}
							}
						}
					}
				}
			}
			if(StringUtils.isNotEmpty(listInputEnd)) {
				String classId = "";
				String classIdOld = "";
				String itemId = "";
				String itemIdOld = "";
				int i = 1;
				for(CostDataInputVo temp : listInputEnd) {
					temp.setProgramCount(listInputEnd.size());
					if(i==1) {
						temp.setProgramName(programName);
					}else {
						temp.setProgramName("");
					}
					i++;
					classId = temp.getClassId();
					if (classId.equals(classIdOld)) {
						temp.setClassCount(1);
						temp.setClassName("");
					} else {
						temp.setClassName(temp.getClassName());
						if (mapClassCount != null && mapClassCount.containsKey(classId)) {
							temp.setClassCount(mapClassCount.get(classId));
						} else {
							temp.setClassCount(1);
						}
						classIdOld = classId;
					}
					if (classIdOld.length() <= 0) {
						classIdOld = classId;
					}
					itemId = temp.getItemId();
					if (itemId.equals(itemIdOld)) {
						temp.setItemCount(1);
						temp.setItemName("");
					} else {
						temp.setItemName(temp.getItemName());
						if (mapItemCount != null && mapItemCount.containsKey(itemId)) {
							temp.setItemCount(mapItemCount.get(itemId));
						} else {
							temp.setItemCount(1);
						}
						itemIdOld = itemId;
					}
					if (itemIdOld.length() <= 0) {
						itemIdOld = itemId;
					}
				}
			}
		}
		result.setMainId(mainId);
		result.setListInputData(listInputEnd);
		return result;
	}

	/**
	 * 保存核算录入数据
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public boolean saveCostInputData(CostDataInputDto dto) {
		boolean result = true;
		List<CostBatchInstrumentData> listInputAdd = new ArrayList<CostBatchInstrumentData>();
		List<CostDataInputVo> listInputTemp = dto.getListInputData();
		if (StringUtils.isNotEmpty(listInputTemp)) {
			List<String> idl=new ArrayList<String>();
			HashMap<String,String> alm=new HashMap<String,String>();
			String ybid,pid,groupid,key;
			//得到传回来的数据，缺少不显示的数据
			for (CostDataInputVo temp : listInputTemp) {
				ybid = temp.getInstrumentId();
				pid=temp.getPid();
				if (!idl.contains(pid)) {
					idl.add(pid);
				}
				groupid=temp.getAddItemGroupId();
				if(ybid!=null) {
					key=(new StringBuffer(pid).append(ybid).append(groupid)).toString();
					alm.put(key, "1");
					CostBatchInstrumentData objNew = new CostBatchInstrumentData();
					objNew.setId(TMUID.getUID());
					objNew.setInstrumentId(ybid);
					objNew.setItemPrice(temp.getItemPrice());
					objNew.setAddItemGroupId(groupid);
					objNew.setAddItemRemark(temp.getAddItemRemark());
					objNew.setPid(pid);
					objNew.setEndTime(temp.getEndTime());
					objNew.setLastReadOut(temp.getLastReadOut());
					objNew.setLastReadTime(temp.getLastReadTime());
					objNew.setPreviousReadOut(temp.getPreviousReadOut());
					objNew.setPreviousReadTime(temp.getPreviousReadTime());
					objNew.setUnitId(temp.getUnitId());
					objNew.setProgramId(temp.getProgramId());
					listInputAdd.add(objNew);
				}
			}
			//获取已保存的数据，补上缺少的不显示数据
			Where where = Where.create();
			where.in(CostBatchInstrumentData::getPid, idl.toArray());
			List<CostBatchInstrumentData> ybbs=dao.queryList(CostBatchInstrumentData.class, where, null);
			if (ybbs!=null) {
				for (CostBatchInstrumentData yn:ybbs) {
					ybid = yn.getInstrumentId();
					pid=yn.getPid();
					groupid=yn.getAddItemGroupId();
					key=(new StringBuffer(pid).append(ybid).append(groupid)).toString();
					if (alm.containsKey(key)) {
						continue;
					} else {
						listInputAdd.add(yn);
					}
				}
			}
			int bs = dao.delete(CostBatchInstrumentData.class, where);
			if (bs < 0) {
				result = false;
			} else {
				if(StringUtils.isNotEmpty(listInputAdd)) {
					dao.insertBatch(listInputAdd);
					result = true;
				}
			}
			// 表单组件保存后调用的方法 计算交接班
			TeamReportInputDto reportinfo = new TeamReportInputDto();
			reportinfo.setUnitId(dto.getUnitId());
			reportinfo.setShiftId(dto.getShiftId());
			reportinfo.setTeamId(dto.getTeamId());
			reportinfo.setWriteDay(dto.getWriteDay());
			reportinfo.setIsBatchCalc(false);
			ctpl.calcBatchProgramData(reportinfo);
		}

		return result;
	}

	/**
	 * 获取上一个班次的数据
	 * 
	 * @param unitId    核算对象编码
	 * @param beginTime 日期+时间
	 * @return
	 */
	public LinkedHashMap<String, CostBatchInstrumentData> getPInputData(String unitId, String beginTime) {
		LinkedHashMap<String, CostBatchInstrumentData> result = new LinkedHashMap<String, CostBatchInstrumentData>();
		Where where = Where.create();
		where.eq(CostBatchOnDuty::getUnitId, unitId);
		where.le(CostBatchOnDuty::getEndTime, beginTime);
		Order order = Order.create();
		order.orderByDesc(CostBatchOnDuty::getEndTime);
		String endTime = dao.findMaxValue(CostBatchOnDuty.class, CostBatchOnDuty::getEndTime, String.class, where);
		if (endTime != null && endTime.length() > 0) {
			Where wheremain = Where.create();
			wheremain.eq(CostBatchOnDuty::getUnitId, unitId);
			wheremain.eq(CostBatchOnDuty::getEndTime, endTime);
			List<CostBatchOnDuty> list = new ArrayList<CostBatchOnDuty>();
			list = dao.queryData(CostBatchOnDuty.class, wheremain, null, null);
			if (StringUtils.isNotEmpty(list)) {
				String mainId = list.get(0).getId();
				Where whereInput = Where.create();
				whereInput.eq(CostBatchInstrumentData::getPid, mainId);
				List<CostBatchInstrumentData> listInput = new ArrayList<CostBatchInstrumentData>();
				listInput = dao.queryData(CostBatchInstrumentData.class, whereInput, null, null);
				if (StringUtils.isNotEmpty(listInput)) {
					for (CostBatchInstrumentData temp : listInput) {
						String addItemGroupId = temp.getAddItemGroupId()==null?"":temp.getAddItemGroupId();
						result.put(temp.getInstrumentId()+"_"+addItemGroupId, temp);
					}
				}
			}
		}
		return result;
	}

	/**
	 * 获取数据
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public boolean getInputDataByBtn(CostDataInputDto dto) {
		boolean result = false;

		List<CostBatchInstrumentData> listInputUp = new ArrayList<CostBatchInstrumentData>();

		String mainId = dto.getMainId();// 核算录入数据的主ID
		// 核算录入项目仪表数据

		CostBatchOnDuty mainObj = dao.queryObjectById(CostBatchOnDuty.class, mainId);

		LinkedHashMap<String, CostBatchInstrumentData> mapInput = new LinkedHashMap<String, CostBatchInstrumentData>();

		if (mainObj != null) {
			String unitId = mainObj.getUnitId();// 核算单元ID

			String programId = mainObj.getProgramId();// 方案ID

			String beginTime = mainObj.getBeginTime();// 批次开始时间

			String shiftId = mainObj.getShiftId();// 班次Id

			String teamId = mainObj.getTeamId();

//			String pid = mainObj.getPid();// 批次ID

			String writeDay = mainObj.getWriteDay();// 填写日期

			List<Costinstrument> listInst = new ArrayList<Costinstrument>();
			CostItemInfoVo costItem = new CostItemInfoVo();
			costItem = unitItemSev.getCostData(unitId, writeDay);
			if (ObjUtils.notEmpty(costItem)) {
				listInst = costItem.getInstrumentList();
			}
			LinkedHashMap<String, CostBatchInstrumentData> mapPInput = getPInputData(unitId, beginTime);
			// 获取核算项目数据
			List<CostItemForWriteVo> listItem = costItemServ.shiftWriteItemInfo(unitId, writeDay, programId);
			LinkedHashMap<String, CostItemForWriteVo> mapItem = new LinkedHashMap<String, CostItemForWriteVo>();
			if (StringUtils.isNotEmpty(listItem)) {
				for (CostItemForWriteVo temp : listItem) {
					mapItem.put(temp.getInstrumentId()+"_", temp);
				}
			}
			Where whereInput = Where.create();
			whereInput.eq(CostBatchInstrumentData::getPid, mainId);
			List<CostBatchInstrumentData> listInput = new ArrayList<CostBatchInstrumentData>();
			listInput = dao.queryData(CostBatchInstrumentData.class, whereInput, null, null);
			if (StringUtils.isNotEmpty(listInput)) {
				for (CostBatchInstrumentData temp : listInput) {
					mapInput.put(temp.getInstrumentId(), temp);
				}
			}
			if (StringUtils.isNotEmpty(listInst)) {// 循环项目仪表基础数据，这里把所有的仪表都保存了，用于后续取上班表数时用
				for (Costinstrument temp : listInst) {
					String instrumentId = temp.getId();// 仪表编码
					if (mapInput != null && mapInput.containsKey(instrumentId)) {// 仪表录入过数据
						CostBatchInstrumentData objInput = new CostBatchInstrumentData();
						objInput = mapInput.get(instrumentId);
						String addItemGroupId = objInput.getAddItemGroupId()==null?"":objInput.getAddItemGroupId();
						String key = instrumentId + "_" + addItemGroupId;
						if (mapPInput != null && mapPInput.containsKey(key)) {
							objInput.setPreviousReadOut(mapPInput.get(key).getLastReadOut());
							if (mapItem != null && mapItem.containsKey(key)) {

							} else {
								objInput.setLastReadOut(mapPInput.get(key).getLastReadOut());
							}
							listInputUp.add(objInput);
						}
					}
				}
			}

			if (StringUtils.isNotEmpty(listInputUp)) {// 新添加了仪表

				// 表单组件保存后调用的方法 计算单表消耗
				//
				TeamReportInputDto reportinfo = new TeamReportInputDto();
				reportinfo.setTeamId(teamId);
				reportinfo.setUnitId(unitId);
				reportinfo.setShiftId(shiftId);
				reportinfo.setWriteDay(writeDay);
				reportinfo.setIsBatchCalc(false);
				int row = dao.updateBatch(listInputUp);
				if (row > 0) {
					// 表单组件保存后调用的方法 计算交接班
					ctpl.calcBatchProgramData(reportinfo);
					result = true;
				}

			} else {
				result = true;
			}
		}
		return result;
	}

	/**
	 * 批次数据查询
	 * 
	 * @param mainId 批次数据id
	 * @return
	 */
	@Override
	public List<CostInputQueryStringVo> CostInputDataQuery(String mainId) {
		List<CostInputQueryStringVo> result = new ArrayList<CostInputQueryStringVo>();
		CostBatchInfo mainObj = new CostBatchInfo();
		mainObj = dao.queryObjectById(CostBatchInfo.class, mainId);
		if (ObjUtils.notEmpty(mainObj)) {
			String unitId = mainObj.getUnitId();// 核算单元ID

			String programId = mainObj.getProgramId();// 方案ID

			String writeDay = mainObj.getWriteDay();// 填写日期

			CostBgcsszbDto dtoBgcs = new CostBgcsszbDto();
			dtoBgcs.setCxType(0);
			dtoBgcs.setTableName("bb00");
			dtoBgcs.setTableType("表头");
			dtoBgcs.setTmused(1);
			dtoBgcs.setUnitid(unitId);
			List<CostBgcsszb> listTitle = costBgcssz.getCostBgcsszbList(dtoBgcs);
			LinkedHashMap<String, CostBgcsszb> mapTitle = new LinkedHashMap<String, CostBgcsszb>();
			if(StringUtils.isNotEmpty(listTitle)) {
				for(CostBgcsszb temp : listTitle) {
					mapTitle.put(temp.getColumnCode(), temp);
				}
			}

			// 仪表数据
			LinkedHashMap<String, CostBatchInstrumentData> mapInput = new LinkedHashMap<String, CostBatchInstrumentData>();
			Where whereInput = Where.create();
			whereInput.eq(CostBatchInstrumentData::getPid, mainId);
			List<CostBatchInstrumentData> listInput = new ArrayList<CostBatchInstrumentData>();
			listInput = dao.queryData(CostBatchInstrumentData.class, whereInput, null, null);
			if (StringUtils.isNotEmpty(listInput)) {
				for (CostBatchInstrumentData temp : listInput) {
					mapInput.put(temp.getInstrumentId(), temp);
				}
			}
			// 项目数据
			LinkedHashMap<String, CostBatchItemData> mapInputItem = new LinkedHashMap<String, CostBatchItemData>();

			Where whereInputItem = Where.create();
			whereInputItem.eq(CostBatchInstrumentData::getPid, mainId);
			List<CostBatchItemData> listInputItem = new ArrayList<CostBatchItemData>();
			listInputItem = dao.queryData(CostBatchItemData.class, whereInputItem, null, null);
			if (StringUtils.isNotEmpty(listInput)) {
				for (CostBatchItemData temp : listInputItem) {
					mapInputItem.put(temp.getItemId(), temp);
				}
			}
			// 获取核算项目数据
			List<CostItemForWriteVo> listItem = costItemServ.shiftWriteItemInfo(unitId, writeDay, programId);
			// 项目的最终数据key 分类编码 value map key 项目编码，value 仪表数据
			LinkedHashMap<String, LinkedHashMap<String, List<CostItemForWriteVo>>> mapEnd = new LinkedHashMap<String, LinkedHashMap<String, List<CostItemForWriteVo>>>();
			LinkedHashMap<String, CostItemForWriteVo> mapfl = new LinkedHashMap<String, CostItemForWriteVo>();// 分类
			LinkedHashMap<String, CostItemForWriteVo> mapItem = new LinkedHashMap<String, CostItemForWriteVo>();// 项目
			if (StringUtils.isNotEmpty(listItem)) {
				for (CostItemForWriteVo temp : listItem) {
					LinkedHashMap<String, List<CostItemForWriteVo>> mapTemp = new LinkedHashMap<String, List<CostItemForWriteVo>>();
					if (mapEnd != null && mapEnd.containsKey(temp.getClassId())) {// mapEnd 中已有此分类数据
						mapTemp = mapEnd.get(temp.getClassId());
						if (mapTemp != null && mapTemp.containsKey(temp.getItemId())) {// 子map 中已有此项目数据
							List<CostItemForWriteVo> listTemp = mapTemp.get(temp.getItemId());
							listTemp.add(temp);
							mapTemp.put(temp.getItemId(), listTemp);
							mapEnd.put(temp.getClassId(), mapTemp);
						} else {// 子map 中没有此项目数据
							List<CostItemForWriteVo> listTemp = new ArrayList<CostItemForWriteVo>();
							listTemp.add(temp);
							mapTemp.put(temp.getItemId(), listTemp);
							mapEnd.put(temp.getClassId(), mapTemp);
						}
					} else {// mapEnd 中没有此分类数据
						List<CostItemForWriteVo> listTemp = new ArrayList<CostItemForWriteVo>();
						listTemp.add(temp);
						mapTemp.put(temp.getItemId(), listTemp);
						mapEnd.put(temp.getClassId(), mapTemp);
					}
					mapfl.put(temp.getClassId(), temp);
					mapItem.put(temp.getItemId(), temp);
				}
			}
			CostBgcsszbInternalDto titleInternalDto = new CostBgcsszbInternalDto();
			titleInternalDto.setUnitId(unitId);
			titleInternalDto.setTableName("bb01");
			LinkedHashMap<String, LinkedHashMap<String, CostBgcsszbInternal>> mapInternalTitle = costBgcssz.getCostBgcsszbInternalMap(titleInternalDto);
			for (Entry<String, LinkedHashMap<String, List<CostItemForWriteVo>>> entry : mapEnd.entrySet()) {// 循环分类
				if (mapfl != null && mapfl.containsKey(entry.getKey())) {
					if(mapInternalTitle!=null && mapInternalTitle.containsKey(entry.getKey())) {
//						List<CostBgcsszbInternal> listTitleTemp = new ArrayList<CostBgcsszbInternal>();
//						listTitleTemp = mapInternalTitle.get(entry.getKey());
						LinkedHashMap<String, CostBgcsszbInternal> mapTitleTemp = new LinkedHashMap<String, CostBgcsszbInternal>();
						mapTitleTemp = mapInternalTitle.get(entry.getKey());
//						if(StringUtils.isNotEmpty(listTitleTemp)) {
//							for(CostBgcsszbInternal temp : listTitleTemp) {
//								mapTitleTemp.put(temp.getColumnCode(), temp);
//							}
//						}
						CostInputQueryStringVo objTitleData = new CostInputQueryStringVo();
						objTitleData.setClassId(entry.getKey());
						String showText = "";
						showText = getInternalTitle("itemName", mapTitleTemp);
						objTitleData.setClassName(showText);
						
						objTitleData.setDataType(4);
						showText = getInternalTitle("dbxh", mapTitleTemp);
						objTitleData.setDbxh(showText);

						showText = getInternalTitle("dh", mapTitleTemp);
						objTitleData.setDh(showText);

						showText = getInternalTitle("dhcz", mapTitleTemp);
						objTitleData.setDhcz(showText);

						showText = getInternalTitle("dhde", mapTitleTemp);
						objTitleData.setDhde(showText);

						showText = getInternalTitle("dwcb", mapTitleTemp);
						objTitleData.setDwcb(showText);

						showText = getInternalTitle("dwcbcz", mapTitleTemp);
						objTitleData.setDwcbcz(showText);

						showText = getInternalTitle("dwcbde", mapTitleTemp);
						objTitleData.setDwcbde(showText);

						objTitleData.setInstrumentId("");

						showText = getInternalTitle("instrumentName", mapTitleTemp);
						objTitleData.setInstrumentName(showText);
						
						objTitleData.setItemHbCount(1);
						
						objTitleData.setItemId("");

						showText = getInternalTitle("itemName", mapTitleTemp);
						objTitleData.setItemName(showText);

						showText = getInternalTitle("itemPrice", mapTitleTemp);
						objTitleData.setItemPrice(showText);

						showText = getInternalTitle("itemUnit", mapTitleTemp);
						objTitleData.setItemUnit(showText);

						showText = getInternalTitle("lastReadOut", mapTitleTemp);
						objTitleData.setLastReadOut(showText);

						showText = getInternalTitle("previousReadOut", mapTitleTemp);
						objTitleData.setPreviousReadOut(showText);
						
						objTitleData.setUnitId(unitId);
						
						objTitleData.setWriteDay(writeDay);

						showText = getInternalTitle("xhl", mapTitleTemp);
						objTitleData.setXhl(showText);

						showText = getInternalTitle("zcb", mapTitleTemp);
						objTitleData.setZcb(showText);
						
						showText = getInternalTitle("nh", mapTitleTemp);
						objTitleData.setNh(showText);
						
						result.add(objTitleData);
						
					}
					CostInputQueryStringVo objfl = new CostInputQueryStringVo();
					CostItemForWriteVo objWritefl = mapfl.get(entry.getKey());
					String classId = objWritefl.getClassId();
					String className = objWritefl.getClassName();
					objfl.setClassId(classId);
					objfl.setClassName(className);
					objfl.setItemName(className);
					objfl.setUnitId(unitId);
					objfl.setProgramId(programId);
					objfl.setWriteDay(writeDay);
					objfl.setPid(mainId);
					LinkedHashMap<String, List<CostItemForWriteVo>> mapItemTemp = entry.getValue();
					Double dwcbde = 0.0;// 单位成本定额
					Double dhde = 0.0;// 单定额
					Double xhl = 0.0;// 消耗量
					Double dh = 0.0;// 单耗
					Double dwcb = 0.0;// 单位成本
					Double zcb = 0.0;// 总成本
					Double dhcz = 0.0;// 单耗差值
					Double dwcbcz = 0.0;// 单位成本差值
					Double nh = 0.0;// 能耗
					for (Entry<String, List<CostItemForWriteVo>> entryItem : mapItemTemp.entrySet()) {// 获取分类下的项目，根据表头是否汇总
																										// 算出字段的合计
						String itemId = entryItem.getKey();
						if (mapInputItem != null && mapInputItem.containsKey(itemId)) {
							if (StringUtils.isNotEmpty(listTitle)) {
								for (CostBgcsszb title : listTitle) {
									if(title.getIsTotal()!=null && title.getIsTotal()==1) {
										if ("dwcbde".equals(title.getColumnCode())) {
											dwcbde = 0.0;
										}
										if ("dhde".equals(title.getColumnCode())) {
											dhde = dhde + mapInputItem.get(itemId).getBaseConsumption();// ==null?0:// 标准单耗
										}
										if ("xhl".equals(title.getColumnCode())) {
											xhl = xhl + mapInputItem.get(itemId).getConsumption();// ==null?0:mapInputItem.get(itemId).getConsumption();
										}
										if ("dh".equals(title.getColumnCode())) {
											dh = dh + mapInputItem.get(itemId).getUnitConsumption();// ==null?0:
										}
										if ("dwcb".equals(title.getColumnCode())) {
											dwcb = dwcb + mapInputItem.get(itemId).getUnitCost();// ==null?0:mapInputItem.get(itemId).getUnitCost();
										}
										if ("zcb".equals(title.getColumnCode())) {
											zcb = zcb + mapInputItem.get(itemId).getItemCost();// ==null?0:// 成本
										}
										if ("dhcz".equals(title.getColumnCode())) {
											dhcz = dh - dhde;// 实际单耗 - 标准单耗
										}
										if ("dwcbcz".equals(title.getColumnCode())) {
											dwcbcz = 0.0;
										}
										if ("nh".equals(title.getColumnCode())) {
											nh = 0.0;
										}
									}
								}
							}
						}
					}
					String strValue = "";
					strValue = getStrValue(mapTitle, "dwcbde", dwcbde);
					objfl.setDwcbde(strValue);
					strValue = getStrValue(mapTitle, "dhde", dhde);
					objfl.setDhde(strValue);
					strValue = getStrValue(mapTitle, "xhl", xhl);
					objfl.setXhl(strValue);
					strValue = getStrValue(mapTitle, "dh", dh);
					objfl.setDh(strValue);
					strValue = getStrValue(mapTitle, "dwcb", dwcb);
					objfl.setDwcb(strValue);
					strValue = getStrValue(mapTitle, "zcb", zcb);
					objfl.setZcb(strValue);
					strValue = getStrValue(mapTitle, "dhcz", dhcz);
					objfl.setDhcz(strValue);
					strValue = getStrValue(mapTitle, "dwcbcz", dwcbcz);
					objfl.setDwcbcz(strValue);
					strValue = getStrValue(mapTitle, "nh", nh);
					objfl.setNh(strValue);
					objfl.setDataType(1);
					objfl.setItemHbCount(1);
					result.add(objfl);
					for (Entry<String, List<CostItemForWriteVo>> entryItem : mapItemTemp.entrySet()) {// 循环项目数据
//						int i = 0;
						String itemId = entryItem.getKey();
						if (mapItem != null && mapItem.containsKey(itemId)) {// 存在此项目
							CostItemForWriteVo itemBean = mapItem.get(itemId);
							CostInputQueryStringVo objItem = new CostInputQueryStringVo();
							objItem.setClassId(classId);// 分类编码
							objItem.setClassName(className);// 分类名称
							objItem.setItemId(itemId);// 项目编码
							objItem.setItemName(itemBean.getItemName());// 项目名称
							objItem.setItemUnit(itemBean.getItemUnit());// 计量单位
							objItem.setUnitId(unitId);
							objItem.setProgramId(programId);
							objItem.setWriteDay(writeDay);
							objItem.setPid(mainId);
							objItem.setDataType(2);
							if (mapInputItem != null && mapInputItem.containsKey(itemId)) {// 项目有录入的数据
								CostBatchItemData itemInput = mapInputItem.get(itemId);
								strValue = getStrValue(mapTitle, "itemPrice", itemInput.getItemprice());
								objItem.setItemPrice(strValue);// 单价
								strValue = getStrValue(mapTitle, "dh", itemInput.getBaseConsumption());
								objItem.setDh(strValue);// 单耗
								dwcbde = 0.0;
								dhde = mapInputItem.get(itemId).getBaseConsumption();// ==null?0:// 标准单耗
								xhl = mapInputItem.get(itemId).getConsumption();// ==null?0:mapInputItem.get(itemId).getConsumption();
								dh = mapInputItem.get(itemId).getUnitConsumption();// ==null?0:
								dwcb = mapInputItem.get(itemId).getUnitCost();// ==null?0:mapInputItem.get(itemId).getUnitCost();
								zcb = mapInputItem.get(itemId).getItemCost();// ==null?0:// 成本
								dhcz = dh - dhde;// 实际单耗 - 标准单耗
								dwcbcz = 0.0;
								strValue = getStrValue(mapTitle, "dwcbde", dwcbde);
								objItem.setDwcbde(strValue);
								strValue = getStrValue(mapTitle, "dhde", dhde);
								objItem.setDhde(strValue);
								strValue = getStrValue(mapTitle, "xhl", xhl);
								objItem.setXhl(strValue);
								strValue = getStrValue(mapTitle, "dh", dh);
								objItem.setDh(strValue);
								strValue = getStrValue(mapTitle, "dwcb", dwcb);
								objItem.setDwcb(strValue);
								strValue = getStrValue(mapTitle, "zcb", zcb);
								objItem.setZcb(strValue);
								strValue = getStrValue(mapTitle, "dhcz", dhcz);
								objItem.setDhcz(strValue);
								strValue = getStrValue(mapTitle, "dwcbcz", dwcbcz);
								objItem.setDwcbcz(strValue);
								strValue = getStrValue(mapTitle, "nh", nh);
								objItem.setNh(strValue);
								objItem.setDataType(2);
							}
							List<CostItemForWriteVo> listYb = entryItem.getValue();// 获取项目下的仪表
							if (StringUtils.isNotEmpty(listYb)) {// 如果项目下有仪表
								int i = 0;
								objItem.setItemHbCount(listYb.size() + 1);// 设置项目合并的行数
								for (CostItemForWriteVo ybItem : listYb) {// 循环仪表数据
									String instrumentId = ybItem.getInstrumentId();
									Double lastReadOut = 0.0;// 后表数
									Double writeVal = 0.0;// 单表消耗
									Double previousReadOut = 0.0;// 前表数
									if (mapInput != null && mapInput.containsKey(instrumentId)) {// 仪表录入了数据
										lastReadOut = mapInput.get(instrumentId).getLastReadOut();
										writeVal = mapInput.get(instrumentId).getWriteVal();
										previousReadOut = mapInput.get(instrumentId).getPreviousReadOut();
									}
									String instrumentName = ybItem.getInstrumentName();// 仪表位号
									if (i == 0) {// 如果是第一块仪表，那么将仪表的数据放到项目的数据中（项目和第一块仪表的数据在同一行显示）
										objItem.setInstrumentName(instrumentName);
										strValue = getStrValue(mapTitle, "lastReadOut", lastReadOut);
										objItem.setLastReadOut(strValue);
										strValue = getStrValue(mapTitle, "previousReadOut", previousReadOut);
										objItem.setPreviousReadOut(strValue);
										strValue = getStrValue(mapTitle, "dbxh", writeVal);
										objItem.setDbxh(strValue);
										result.add(objItem);
									} else {// 如果不是第一块仪表，那么新添加一条数据存放仪表的数据
										CostInputQueryStringVo objYb = new CostInputQueryStringVo();
										objYb.setPid(mainId);
										objYb.setUnitId(unitId);
										objYb.setProgramId(programId);
										objYb.setWriteDay(writeDay);
										objYb.setInstrumentName(instrumentName);
										objYb.setClassId(classId);
//										objYb.setClassName(className);
										objYb.setItemId(itemId);
										strValue = getStrValue(mapTitle, "lastReadOut", lastReadOut);
										objYb.setLastReadOut(strValue);
										strValue = getStrValue(mapTitle, "previousReadOut", previousReadOut);
										objYb.setPreviousReadOut(strValue);
										strValue = getStrValue(mapTitle, "dbxh", writeVal);
										objYb.setDbxh(strValue);
										
//										objYb.setLastReadOut(lastReadOut);
//										objYb.setPreviousReadOut(previousReadOut);
//										objYb.setDbxh(writeVal);
										objYb.setDataType(3);
										objYb.setItemHbCount(1);
										
										result.add(objYb);
									}
									i++;
								}
							} else {// 项目下无仪表，合并行为1，直接将项目添加到返回的结果中
								objItem.setItemHbCount(1);
								result.add(objItem);
							}
						}
					}
				}
			}
//			获取核算指标数据
			Where whereParam = Where.create();
			whereParam.eq(CostBatchParamData::getPid, mainId);
			List<CostBatchParamData> listParam = new ArrayList<CostBatchParamData>();
			listParam = dao.queryData(CostBatchParamData.class, whereParam, null, null);
			LinkedHashMap<String, CostBatchParamData> mapParam = new LinkedHashMap<String, CostBatchParamData>();
			if (StringUtils.isNotEmpty(listParam)) {
				for (CostBatchParamData temp : listParam) {
					mapParam.put(temp.getParamId(), temp);
				}
			}
			// 获取核算指标基础数据
			CostItemInfoVo paramObj = unitItemSev.getCostData(unitId, writeDay);
			List<Costindicator> listParamSet = paramObj.getIndicatorList();
			if (StringUtils.isNotEmpty(listParamSet)) {
				CostInputQueryStringVo objParam1 = new CostInputQueryStringVo();
				objParam1.setPid(mainId);
				objParam1.setUnitId(unitId);
				objParam1.setProgramId(programId);
				objParam1.setWriteDay(writeDay);
				objParam1.setClassName("核算指标");
				objParam1.setItemName("核算指标");
				objParam1.setDataType(1);
				result.add(objParam1);
				for (Costindicator temp : listParamSet) {
					CostInputQueryStringVo objParam = new CostInputQueryStringVo();
					objParam.setPid(mainId);
					objParam.setUnitId(unitId);
					objParam.setProgramId(programId);
					objParam.setWriteDay(writeDay);
					objParam.setInstrumentName("");
					objParam.setClassId("");
					objParam.setItemId(temp.getId());
					objParam.setItemName(temp.getCpname());
					objParam.setDataType(2);
					if (mapParam != null && mapParam.containsKey(temp.getId())) {
						String strValue = getStrValue(mapTitle, "xhl", mapParam.get(temp.getId()).getCalcVal());
						objParam.setXhl(strValue);
						strValue = getStrValue(mapTitle, "dhde", mapParam.get(temp.getId()).getBaseVal());
						objParam.setDhde(strValue);// 单耗定额
					}
					objParam.setItemHbCount(1);
					result.add(objParam);
				}
			}
		}
		return result;
	}

	/**
	 * 获取批次数据
	 * 
	 * @param writeDay 填写日期
	 * @param unitId   核算对象编码
	 * @return
	 */
	@Override
	public List<CostBatchInfo> getCostBatchInfo(String writeDay, String unitId) {
		List<CostBatchInfo> result = new ArrayList<CostBatchInfo>();
		if (writeDay != null && writeDay.length() > 0 && unitId != null && unitId.length() > 0) {
			Where where = Where.create();
			where.eq(CostBatchInfo::getWriteDay, writeDay);
			where.eq(CostBatchInfo::getUnitId, unitId);
			Order order = Order.create();
			order.orderByAsc(CostBatchInfo::getBatchNo);
			result = dao.queryData(CostBatchInfo.class, where, order, null);
		}
		return result;
	}
	/**
	 * 批次查询的数据转出excel
	 * @param dto
	 */
	@Override
	public void costBatchInfoToExcel(CostDataInputDto dto, HttpServletResponse response) {
		if(dto.getMainId()!=null && dto.getMainId().length()>0) {
			CostBatchInfo mainObj = new CostBatchInfo();
			mainObj = dao.queryObjectById(CostBatchInfo.class, dto.getMainId());
			if (ObjUtils.notEmpty(mainObj)) {
				
				String unitId = mainObj.getUnitId();// 核算单元ID

//				String programId = mainObj.getProgramId();// 方案ID
//
//				String writeDay = mainObj.getWriteDay();// 填写日期

				CostBgcsszbDto dtoBgcs = new CostBgcsszbDto();
				dtoBgcs.setCxType(0);
				dtoBgcs.setTableName("bb01");
				dtoBgcs.setTableType("表头");
				dtoBgcs.setTmused(1);
				dtoBgcs.setUnitid(unitId);
				List<CostBgcsszb> listColumnTitle = costBgcssz.getCostBgcsszbList(dtoBgcs);
				
				CostBgcsszbDto dtoTitle = new CostBgcsszbDto();
				dtoTitle.setCxType(0);
				dtoTitle.setTableName("bb01");
				dtoTitle.setTableType("标题");
				dtoTitle.setTmused(1);
				dtoTitle.setUnitid(unitId);
				List<CostBgcsszb> listTitle = costBgcssz.getCostBgcsszbList(dtoBgcs);
				String fileName = "批次核算数据查询";
				if(StringUtils.isNotEmpty(listTitle)) {
					fileName = listTitle.get(0).getColumnshowName();
				}

				List<ExcelExportEntity> excelTitleList = new ArrayList<ExcelExportEntity>();
				List<Map<String, Object>> excelDataList = new ArrayList<Map<String,Object>>();
				LinkedHashMap<String, CostBgcsszb> colmMap = new LinkedHashMap<String, CostBgcsszb>();
				// 表头数据
				Map<String, Object> colmNameMap = new LinkedHashMap<String, Object>(); //列名称Map
				List<CostInputQueryVo> list = new ArrayList<CostInputQueryVo>(); CostInputDataQuery(dto.getMainId());
				if(StringUtils.isNotEmpty(list)) {
					if(StringUtils.isNotEmpty(listColumnTitle)) {
						int i = 0;
						for (CostBgcsszb bean : listColumnTitle) {
							if(bean.getTmused()==1) {
								String columnCode = bean.getColumnCode();
								String columnName = bean.getColumnshowName();
								ExcelExportEntity expEntity = getExpEntity(i, bean);
								excelTitleList.add(expEntity);
								colmNameMap.put(columnCode, columnName); //表头名称Map
								
								colmMap.put(columnCode,bean);
//								this.getCombDataMap(bean,combDataMap); //表头下拉框数据Map
								i++;
							}
						}
						if(StringUtils.isNotEmpty(colmMap)) {
							excelDataList.add(colmNameMap); //表头名称
							for (CostInputQueryVo dataVo : list) {
								Map<String, Object> convertMap = ObjUtils.copyTo(dataVo, Map.class);
								Map<String, Object> dataMap = this.getExportExcelDataMap(colmMap,convertMap);
								excelDataList.add(dataMap);
							}
						}
					}
//					int dataRowCount = excelDataList.size();
					String sheetName = fileName;
					ExportParams exportParams = new ExportParams(null, null, sheetName);
					exportParams.setAddIndex(false); //是否生成序号
					Workbook workbook = ExcelExport.getWorkbook(exportParams, excelTitleList, excelDataList, null);

					Sheet sheet = workbook.getSheetAt(0);
					// 冻结标题行
					sheet.createFreezePane(4, 2, 4, 2);
					// 隐藏编码行
					sheet.getRow(0).setZeroHeight(true);
					// 设置默认样式
					if(StringUtils.isNotEmpty(listColumnTitle)) {
						Map<String,HSSFCellStyle> styleMap = this.styleMapInit((HSSFWorkbook)workbook);
						HSSFCellStyle styleBody_C_default = styleMap.get("styleBody_C_default");
						for (int i = 0; i < listColumnTitle.size(); i++) {
							sheet.setDefaultColumnStyle(i, styleBody_C_default);
						}
					}
					// 设置单元格样式
//					this.setExcelCellStyle(workbook,sheet,colmMap,dataRowCount,list);
					// 设置默认行高
					sheet.setDefaultRowHeightInPoints(20);
					// 下载记录
					ExcelExport.downLoadExcel(sheetName, response, workbook);
				}
			}
		}
	}
	
	/**
	 * @Description: 转换对象
	 * @param index
	 * @param titleObj
	 * @return
	 */
	private static ExcelExportEntity getExpEntity(int index, CostBgcsszb titleObj) {
		String columnCode = titleObj.getColumnCode();
		ExcelExportEntity expEntity = new ExcelExportEntity(columnCode, columnCode);
//		expEntity.setWidth(getWidth(titleObj.getWidth()));
		expEntity.setOrderNum(index+1);
		expEntity.setNeedMerge(false);
		return expEntity;
	}
	
	/**
	 *	初始化Excel表格样式
	 * @param wb
	 * @return
	 */
	private Map<String,HSSFCellStyle> styleMapInit(HSSFWorkbook wb){
		Map<String,HSSFCellStyle> map = new LinkedHashMap<String, HSSFCellStyle>();
		
		//表头样式
		HSSFCellStyle styleBody_Header = wb.createCellStyle();
		styleBody_Header.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_Header.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
		styleBody_Header.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_Header.setBorderBottom(BorderStyle.THIN);
		styleBody_Header.setBorderLeft(BorderStyle.THIN);
		styleBody_Header.setBorderRight(BorderStyle.THIN);
		styleBody_Header.setBorderTop(BorderStyle.THIN);
		// 背景色
		styleBody_Header.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		styleBody_Header.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		// 生成表头字体
		Font font = wb.createFont();
		font.setFontName("宋体");
		font.setBold(true);
		// 生成表头字体
		styleBody_Header.setFont(font);
		//styleBody_Header.setLocked(true); //设置锁定
		map.put("styleBody_Header", styleBody_Header);
		
		//无背景颜色(中)
		HSSFCellStyle styleBody_C = wb.createCellStyle();
		styleBody_C.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_C.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
		styleBody_C.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_C.setBorderBottom(BorderStyle.THIN);
		styleBody_C.setBorderLeft(BorderStyle.THIN);
		styleBody_C.setBorderRight(BorderStyle.THIN);
		styleBody_C.setBorderTop(BorderStyle.THIN);
		map.put("styleBody_C", styleBody_C);
		
		//无背景颜色(左)
		HSSFCellStyle styleBody_L = wb.createCellStyle();
		styleBody_L.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_L.setAlignment(HorizontalAlignment.LEFT); // 设置水平对齐方式
		styleBody_L.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_L.setBorderBottom(BorderStyle.THIN);
		styleBody_L.setBorderLeft(BorderStyle.THIN);
		styleBody_L.setBorderRight(BorderStyle.THIN);
		styleBody_L.setBorderTop(BorderStyle.THIN);
		map.put("styleBody_L", styleBody_L);
				
		//无背景颜色(右)
		HSSFCellStyle styleBody_R = wb.createCellStyle();
		styleBody_R.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_R.setAlignment(HorizontalAlignment.RIGHT); // 设置水平对齐方式
		styleBody_R.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_R.setBorderBottom(BorderStyle.THIN);
		styleBody_R.setBorderLeft(BorderStyle.THIN);
		styleBody_R.setBorderRight(BorderStyle.THIN);
		styleBody_R.setBorderTop(BorderStyle.THIN);
		map.put("styleBody_R", styleBody_R);
		
		//默认样式(中)
		HSSFCellStyle styleBody_C_default = wb.createCellStyle();
		styleBody_C_default.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_C_default.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
		styleBody_C_default.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		map.put("styleBody_C_default", styleBody_C_default);
		
		//背景颜色(中)
		HSSFCellStyle styleBody_C_color = wb.createCellStyle();
		styleBody_C_color.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_C_color.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
		styleBody_C_color.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 背景颜色	
		styleBody_C_color.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		styleBody_C_color.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
		// 设置边框线为细实线
		styleBody_C_color.setBorderBottom(BorderStyle.THIN);
		styleBody_C_color.setBorderLeft(BorderStyle.THIN);
		styleBody_C_color.setBorderRight(BorderStyle.THIN);
		styleBody_C_color.setBorderTop(BorderStyle.THIN);
		map.put("styleBody_C_color", styleBody_C_color);
				
		//背景颜色(左)
		HSSFCellStyle styleBody_L_color = wb.createCellStyle();
		styleBody_L_color.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_L_color.setAlignment(HorizontalAlignment.LEFT); // 设置水平对齐方式
		styleBody_L_color.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 背景颜色	
		styleBody_L_color.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		styleBody_L_color.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
		// 设置边框线为细实线
		styleBody_L_color.setBorderBottom(BorderStyle.THIN);
		styleBody_L_color.setBorderLeft(BorderStyle.THIN);
		styleBody_L_color.setBorderRight(BorderStyle.THIN);
		styleBody_L_color.setBorderTop(BorderStyle.THIN);
		map.put("styleBody_L_color", styleBody_L_color);
				
		//背景颜色(右)
		HSSFCellStyle styleBody_R_color = wb.createCellStyle();
		styleBody_R_color.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_R_color.setAlignment(HorizontalAlignment.RIGHT); // 设置水平对齐方式
		styleBody_R_color.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 背景颜色	
		styleBody_R_color.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		styleBody_R_color.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
		// 设置边框线为细实线
		styleBody_R_color.setBorderBottom(BorderStyle.THIN);
		styleBody_R_color.setBorderLeft(BorderStyle.THIN);
		styleBody_R_color.setBorderRight(BorderStyle.THIN);
		styleBody_R_color.setBorderTop(BorderStyle.THIN);
		map.put("styleBody_R_color", styleBody_R_color);
		
		//背景（绿色）
		HSSFCellStyle styleBody_C_green = wb.createCellStyle();
		styleBody_C_green.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_C_green.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
		styleBody_C_green.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 背景颜色	
		styleBody_C_green.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		styleBody_C_green.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
		// 设置边框线为细实线
		styleBody_C_green.setBorderBottom(BorderStyle.THIN);
		styleBody_C_green.setBorderLeft(BorderStyle.THIN);
		styleBody_C_green.setBorderRight(BorderStyle.THIN);
		styleBody_C_green.setBorderTop(BorderStyle.THIN);
		// 设置字体
		HSSFFont font_green = wb.createFont();
		font_green.setColor(IndexedColors.GREEN.getIndex());
		styleBody_C_green.setFont(font_green);
		map.put("styleBody_C_green", styleBody_C_green);
		
		//背景（红色）
		HSSFCellStyle styleBody_C_red = wb.createCellStyle();
		styleBody_C_red.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
		styleBody_C_red.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 背景颜色	
		styleBody_C_red.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		styleBody_C_red.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
		// 设置边框线为细实线
		styleBody_C_red.setBorderBottom(BorderStyle.THIN);
		styleBody_C_red.setBorderLeft(BorderStyle.THIN);
		styleBody_C_red.setBorderRight(BorderStyle.THIN);
		styleBody_C_red.setBorderTop(BorderStyle.THIN);
		// 设置字体
		HSSFFont font_red = wb.createFont();
		font_red.setColor(IndexedColors.RED.getIndex());
		styleBody_C_red.setFont(font_red);
		map.put("styleBody_C_red", styleBody_C_red);
				
		return map;
	}
	
	/**
	 *	获取导出Excel数据Map
	 * @param codeVoMap
	 * @param convertMap
	 * @param combDataMap
	 */
	private Map<String, Object> getExportExcelDataMap(LinkedHashMap<String, CostBgcsszb> colmMap,Map<String, Object> convertMap){
		Map<String, Object> dataMap = new LinkedHashMap<String, Object>();
		for (Entry<String, CostBgcsszb> entry : colmMap.entrySet()) {
			String columnCode = entry.getKey();
			CostBgcsszb titleVo = entry.getValue();
			if (StringUtils.isNotEmpty(columnCode)&&StringUtils.isNotNull(titleVo)) {
				Object value = null;
				if (convertMap.containsKey(columnCode)) {
					value = convertMap.get(columnCode);
				}
				if (ObjUtils.notEmpty(value)) {
					value = value.toString().trim();
				} else {
					value = "";
				}
				dataMap.put(columnCode, value);
			}
		}
		return dataMap;
	}
	/**
	 * 删除
	 * @param mainId
	 * @return
	 */
	@Override
	public boolean deleteCostInputData(List<String> mainIdList) {
		boolean result = false;
		Where where = Where.create();
		where.in(CostBatchInstrumentData::getPid, mainIdList.toArray());
		
		List<CostBatchInstrumentData> list = dao.queryData(CostBatchInstrumentData.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			int row = dao.deleteByIdBatch(list);
			if(row>0) {
				result = true;
			}
		}else {
			result = true;
		}
		return result;
	}
	/**
	 * 获取交接班主数据的信息
	 * @param mainId
	 * @return
	 */
	@Override
	public CostBatchOnDuty getMainInputData(String mainId) {
	//	
	//	// 核算录入主数据
	//	List<CostBatchOnDuty> listInputMainAdd = new  ArrayList<CostBatchOnDuty>();
		CostBatchOnDuty result = dao.queryObjectById(CostBatchOnDuty.class, mainId);
		
		return result;
	}
	
	
	/**
	 * 获取自定义添加项目下拉框数据
	 * @param mainId 主数据编码 
	 * @param unitId 核算对象编码
	 * @param writeDay 填写日期
	 * @param programId 方案编码
	 * @return
	 */
	@Override
	public List<CostItemForWriteVo> getAddShowTypeItem(String mainId, String unitId, String writeDay, String programId) {

		List<CostItemForWriteVo> result = new ArrayList<CostItemForWriteVo>();
//		// 获取核算项目数据
		CostBatchOnDuty mainObj = dao.queryObjectById(CostBatchOnDuty.class, mainId);

		if (mainObj != null) {
			unitId = mainObj.getUnitId();// 核算单元ID

			programId = mainObj.getProgramId();// 方案ID

			writeDay = mainObj.getWriteDay();// 填写日期
		}
			List<CostItemForWriteVo> listItem = costItemServ.shiftWriteItemInfo(unitId, writeDay, programId);
			LinkedHashMap<String, CostItemForWriteVo> map = new LinkedHashMap<String, CostItemForWriteVo>();
			if(StringUtils.isNotEmpty(listItem)) {
				for(CostItemForWriteVo temp : listItem) {
					if(map!=null && map.containsKey(temp.getItemId())) {
						
					}else {
						// 通过添加选择增加项目。0 默认显示项目，1 通过新增添加项目，默认是0 获取
						if(temp.getFeedShowType()!=null && temp.getFeedShowType()==1) {
							map.put(temp.getItemId(), temp);
						}
					}
				}
			}
			if(StringUtils.isNotEmpty(map)) {
				for(Entry<String, CostItemForWriteVo> entry : map.entrySet()) {
					result.add(entry.getValue());
				}
			}
//		}
		return result;			
	}
	
	/**
	 * 获取添加自定义 项目下的仪表，返回录入的格式
	 * @param itemId 项目编码
	 * @param mainId 主数据编码 
	 * @param unitId 核算对象编码
	 * @param writeDay 填写日期
	 * @param programId 方案编码
	 * @return
	 */
	@Override
	public List<CostDataInputVo> getAddShowTypeItemInstrucment(String itemId, String mainId, String unitId, String writeDay, String programId) {
		List<CostDataInputVo> result = new ArrayList<CostDataInputVo>();
		CostBatchOnDuty mainObj = dao.queryObjectById(CostBatchOnDuty.class, mainId);
		if (mainObj != null) {
			unitId = mainObj.getUnitId();// 核算单元ID

			programId = mainObj.getProgramId()==null?"0":mainObj.getProgramId();// 方案ID

			writeDay = mainObj.getWriteDay();// 填写日期
		}
			// 获取核算项目数据
			List<CostItemForWriteVo> listItem = costItemServ.shiftWriteItemInfo(unitId, writeDay, programId);
			LinkedHashMap<String, List<CostItemForWriteVo>> map = new LinkedHashMap<String, List<CostItemForWriteVo>>();
			if(StringUtils.isNotEmpty(listItem)) {
				for(CostItemForWriteVo temp : listItem) {
					List<CostItemForWriteVo> listTemp = new ArrayList<CostItemForWriteVo>();
					if(map!=null && map.containsKey(temp.getItemId())) {
						listTemp = map.get(temp.getItemId());
						listTemp.add(temp);
					}else {
						listTemp.add(temp);
					}
					map.put(temp.getItemId(), listTemp);
				}
			}
			if(map!=null && map.containsKey(itemId)) {// 有此项目
				List<CostItemForWriteVo> listTemp = new ArrayList<CostItemForWriteVo>();
				listTemp = map.get(itemId);
				if(StringUtils.isNotEmpty(listTemp)) {
//					String groupId = TMUID.getUID();
					for(CostItemForWriteVo temp : listTemp) {
						String instrumentId = temp.getInstrumentId();
						if(instrumentId!=null) {
							CostDataInputVo obj = new CostDataInputVo();
							obj.setAddItemGroupId("");
							obj.setAddItemRemark("");
							obj.setClassCount(1);
							obj.setClassId(temp.getClassId());
							obj.setClassName(temp.getClassName());
							obj.setInstrumentId(temp.getInstrumentId());
							obj.setInstrumentName(temp.getInstrumentName());
							obj.setItemCount(1);
							obj.setItemUnit(temp.getItemUnit());
							obj.setItemPrice(null);
							obj.setItemId(temp.getItemId());
							obj.setItemName(temp.getItemName());
							obj.setPid(mainId);
							obj.setProgramId(programId);
							obj.setUnitId(unitId);
							result.add(obj);
						}
					}
				}
			}
//		}
		return result;			
	}
	
	/**
	 * 更新交接录入主数据CostBatchOnDuty
	 * @param dto
	 * @return
	 */
	@Override
	public boolean udpateCostBatchOnDuty(CostBatchOnDutyDto dto) {
		boolean result = false;
		CostBatchOnDuty obj = new CostBatchOnDuty();
		if(dto.getId()==null || dto.getId().length()<=0) {
		}else {
			obj = dao.queryObjectById(CostBatchOnDuty.class, dto.getId());
			if(obj!=null) {
				
				
				String shiftIdData = obj.getShiftId();
				String teamIdData = obj.getTeamId();
				String unitIdData = obj.getUnitId();
				String writeDayData = obj.getWriteDay();
				String batchNoData = obj.getBatchNo();
				String programIdData = obj.getProgramId()==null?obj.getProgramId():obj.getProgramId();
				String keyOld = shiftIdData+teamIdData+unitIdData+writeDayData+batchNoData+programIdData;
				
				
				TeamReportInputDto reportinfo = new TeamReportInputDto();
				String begintime = dto.getBeginTime()==null?dto.getShiftBegintime():dto.getBeginTime();
				String endtime = dto.getEndTime()==null?dto.getShiftEndtime():dto.getEndTime();
				String shiftBegintime = dto.getShiftBegintime();
				String shiftEndtime = dto.getShiftEndtime();
				String shiftId = dto.getShiftId();
				String shiftName = dto.getShiftName();
				String summaryDay = dto.getSummaryDay();
				String teamId = dto.getTeamId();
				String teamName = dto.getTeamName();
				String unitId = dto.getUnitId();
				String unitName = dto.getUnitName();
				String writeDay = dto.getWriteDay();
				String batchNo = dto.getBatchNo()==null || dto.getBatchNo().length()<=0?obj.getBatchNo():dto.getBatchNo();
				String batchNoOld = obj.getBatchNo();
				String programId = dto.getProgramId()==null?obj.getProgramId():dto.getProgramId();
				String programName = dto.getProgramName()==null?obj.getProgramName():dto.getProgramName();
				String key = shiftId+teamId+unitId+writeDay+batchNo+programId;
				if(programId!=null && programId.length()>0) {
					obj.setProgramId(programId);
				}
				if(programName!=null && programName.length()>0) {
					obj.setProgramName(programName);;
				}
				if(shiftBegintime!=null && shiftBegintime.length()>0) {
					obj.setShiftBegintime(shiftBegintime);
				}
				if(shiftId!=null && shiftId.length()>0) {
					obj.setShiftId(shiftId);
				}
				if(summaryDay!=null && summaryDay.length()>0) {
					obj.setSummaryDay(summaryDay);
				}
				if(writeDay!=null && writeDay.length()>0) {
					obj.setWriteDay(writeDay);
				}
				if(unitId!=null && unitId.length()>0) {
					obj.setUnitId(unitId);
				}
				if(shiftName!=null && shiftName.length()>0) {
					obj.setShiftName(shiftName);
				}
				if(teamId!=null && teamId.length()>0) {
					obj.setTeamId(teamId);
				}
				if(teamName!=null && teamName.length()>0) {
					obj.setTeamName(teamName);
				}
				if(unitName!=null && unitName.length()>0) {
					obj.setUnitName(unitName);
				}
				if(shiftEndtime!=null && shiftEndtime.length()>0) {
					obj.setEndTime(shiftEndtime);
				}
				if(batchNo!=null && batchNo.length()>0) {
					obj.setBatchNo(batchNo);
					if(batchNoOld!=null && !batchNoOld.equals(batchNo)) {
						reportinfo.setDelbn(batchNoOld);
					}
				}
				obj.setBeginTime(begintime);
				obj.setEndTime(endtime);
				int row = dao.rawUpdateById(obj);
				if(row>0) {
					if(key!=null && keyOld!=null && !key.equals(keyOld)) {
						shiftId = obj.getShiftId();
						teamId = obj.getTeamId();
						unitId = obj.getUnitId();
						unitName = obj.getUnitName();
						writeDay = obj.getWriteDay();
						reportinfo.setTeamId(teamId);
						reportinfo.setUnitId(unitId);
						reportinfo.setShiftId(shiftId);
						reportinfo.setWriteDay(writeDay);
						reportinfo.setIsBatchCalc(false);
					// 表单组件保存后调用的方法 计算交接班
						ctpl.calcBatchProgramData(reportinfo);
					}
					result = true;
				}
			}
		}
		return result;
	}
	
	private CostBatchOnDuty addCostBatchOnDuty(CostBatchOnDutyDto dto) {
		CostBatchOnDuty obj = new CostBatchOnDuty();
		String unitId = dto.getUnitId();
		String writeDay = dto.getWriteDay();
		String programId = dto.getProgramId()==null?"0":dto.getProgramId();
		String batchNo = dto.getBatchNo();
		String shiftId = dto.getShiftId();
		String shiftBegintime = dto.getShiftBegintime();
		String shiftEndtime = dto.getShiftEndtime();
		String programName = dto.getProgramName()==null?"":dto.getProgramName();
		String summaryDay = dto.getSummaryDay();
		String teamId = dto.getTeamId();
		String shiftName = dto.getShiftName();
		String teamName = dto.getTeamName();
		String unitName = dto.getUnitName();
		int isEnd = dto.getIsEnd()==null?1:dto.getIsEnd();

		String begintime = dto.getBeginTime()==null?dto.getShiftBegintime():dto.getBeginTime();
		String endtime = dto.getEndTime()==null?dto.getShiftEndtime():dto.getEndTime();
		if(unitId==null || unitId.length()<=0 || writeDay==null || writeDay.length()<=0 || shiftId==null || shiftId.length()<=0) {
			return null;
		}else {
			Where where = Where.create();
			where.eq(CostBatchOnDuty::getUnitId, unitId);
			where.eq(CostBatchOnDuty::getProgramId, programId);
			where.eq(CostBatchOnDuty::getWriteDay, writeDay);
			where.eq(CostBatchOnDuty::getShiftId, shiftId);
			where.eq(CostBatchOnDuty::getTeamId, teamId);
			where.eq(CostBatchOnDuty::getBeginTime, begintime);
			where.eq(CostBatchOnDuty::getEndTime, endtime);
			List<CostBatchOnDuty> list = dao.queryData(CostBatchOnDuty.class, where, null, null);
			if(StringUtils.isNotEmpty(list)) {
				boolean blnDataid = false;
				for(CostBatchOnDuty temp : list) {
					if(temp.getId().equals(dto.getId())) {
						obj = temp;
						blnDataid = true;
					}
				}
				if(blnDataid) {
					
				}else {
					obj = list.get(0);
				}
				dto.setBatchNo(obj.getBatchNo());
				dto.setBeginTime(begintime);
				dto.setEndTime(endtime);
				dto.setId(obj.getId());
				udpateCostBatchOnDuty(dto);
			} else {
				if(shiftBegintime==null || shiftBegintime.length()<=0 || shiftEndtime==null || shiftEndtime.length()<=0 || shiftId==null || 
						shiftId.length()<=0 || summaryDay==null || summaryDay.length()<=0 || teamId==null ||
						teamId.length()<=0 || unitId==null || unitId.length()<=0 || writeDay==null || writeDay.length()<=0) {
					
				}else {
					SysUser user = SysUserHolder.getCurrentUser();
					obj = new CostBatchOnDuty();
					batchNo = TMUID.getUID();//id，pid和batchNo的数据一致
					obj.setBatchNo(batchNo);
					obj.setBeginTime(begintime);
					obj.setEndTime(endtime);
					obj.setFeedbackTime(DateTimeUtils.getNowDateTimeStr());
					obj.setFeedbackUserId(user.getId());
					obj.setFeedbackUserName(user.getRealName());
					obj.setId(batchNo);
					obj.setIsEnd(isEnd);
					obj.setPid(batchNo);
					obj.setProgramId(programId);
					obj.setProgramName(programName);
					obj.setShiftBegintime(shiftBegintime);
					obj.setShiftEndtime(shiftEndtime);
					obj.setShiftId(shiftId);
					obj.setShiftName(shiftName);
					obj.setSummaryDay(summaryDay);
					obj.setTeamId(teamId);
					obj.setTeamName(teamName);
					obj.setUnitId(unitId);
					obj.setUnitName(unitName);
					obj.setWriteDay(writeDay);
					int row = dao.insert(obj);
					if(row>0) {
					}else {
						obj = null;
					}
				}
			}
		}
		return obj;
	}
	
	/**
	 * 是否显示获取数据按钮 
	 * @param unitId 核算对象编码
	 * @return
	 */
	@Override
	public boolean isShowGetDataBtn(String unitId) {
		boolean result = false;
		CostBgcsszbDto dto = new CostBgcsszbDto();
		dto.setUnitid(unitId);
		dto.setCxType(0);
		dto.setTableName("bb09");
		dto.setTableType("表头");
		List<CostBgcsszb> list = costBgcssz.getCostBgcsszbList(dto);
		boolean blnpreviousReadOut = false;//前表
		boolean blnlastReadOut = false;//后表
		if(list!=null && list.size()>0) {
			for(CostBgcsszb temp : list) {
				if(temp.getColumnCode().equals("previousReadOut") && temp.getTmused()==1) {
					blnpreviousReadOut = true;
				}
				if(temp.getColumnCode().equals("lastReadOut") && temp.getTmused()==1) {
					blnlastReadOut = true;
				}
			}
		}
		if(blnlastReadOut && blnpreviousReadOut) {
			result = true;
		}
		return result;
	}
	
	/**
	 * 获取一个核算对象是否有录入的项目
	 * @param unitIdStr
	 * @return
	 */
	@Override
	public JSONObject getUnitItemCount(CostUnitItemCountDto dto) {
		JSONObject result = new JSONObject();// 数据
		String unitIdList = "";
		String unitNameList = "";
		if(dto!=null && dto.getUnitIdStr()!=null && dto.getUnitIdStr().length()>0) {
			String unitIdStr = dto.getUnitIdStr();
			String unitIdNameStr = dto.getUnitNameStr();
			if(unitIdStr!=null && unitIdStr.length()>0) {
				LinkedHashMap<String, String> mapUnit = new LinkedHashMap<String, String>();
				String[] unitArr = unitIdStr.split(",");
				String[] unitNameArr = unitIdNameStr.split(",");
				List<String> listUnitId = new ArrayList<String>();
				for(int i=0;i<unitArr.length;i++) {
					listUnitId.add(unitArr[i]);
					if(unitNameArr.length -1<i) {
						mapUnit.put(unitArr[i], "参数中没有核算对象名称");
					}else {
						mapUnit.put(unitArr[i], unitNameArr[i]);
					}
				}
				LinkedHashMap<String, Integer> mapUnitItemCount = new LinkedHashMap<String, Integer>();
				mapUnitItemCount = unitMethoderv.getCostItemCountMapByUnitid(listUnitId);
				for(String temp : listUnitId) {
					if(mapUnitItemCount!=null && mapUnitItemCount.containsKey(temp)) {
						int count = mapUnitItemCount.get(temp)==null?0:mapUnitItemCount.get(temp);
						if(count>0) {
							if(unitIdList.length()>0) {
								unitIdList = unitIdList + "," + temp;
							}else {
								unitIdList = temp;
							}
							String unitName = "";
							if(mapUnit!=null && mapUnit.containsKey(temp)) {
								unitName = mapUnit.get(temp);
							}
							if(unitNameList.length()>0) {
								unitNameList = unitNameList + "," + unitName;
							}else {
								unitNameList = unitName;
							}
						}
					}
				}
			}
		}
		result.put("unitId", unitIdList);
		result.put("unitName", unitNameList);
		return result;
	}
	/**
	 * 格式化一个字段的小数位数
	 * @param mapTitle 表头信息
	 * @param columnCode 字段名
	 * @param value 字段值
	 * @return
	 */
	private String getStrValue(LinkedHashMap<String, CostBgcsszb> mapTitle, String columnCode, Double value) {
		String result = "";
		if(value==null) {
			
		}else {
			if(mapTitle!=null && mapTitle.containsKey(columnCode)) {
				int objdis = mapTitle.get(columnCode).getObjdis()==null?6:mapTitle.get(columnCode).getObjdis();
				result = String.valueOf(Maths.round(value, objdis));
			}else {
				result = String.valueOf(Maths.round(value, 6));
			}
		}
		return result;
	}
	
	/**
	 * 返回一个字段的显示值
	 * @param columnCode 字段名
	 * @param mapTitleTemp 表头信息
	 * @return
	 */
	private String getInternalTitle(String columnCode, LinkedHashMap<String, CostBgcsszbInternal> mapTitleTemp) {
		String result = "";
		if(mapTitleTemp!=null && mapTitleTemp.containsKey(columnCode)) {
			if(mapTitleTemp.get(columnCode).getTmused()!=null && mapTitleTemp.get(columnCode).getTmused()==1) {
				result = mapTitleTemp.get(columnCode).getColumnshowName()==null?"":mapTitleTemp.get(columnCode).getColumnshowName();
			}
		}
		return result;
	}
	/**
	 * 在方案切换中获取方案，如果没获取到给默认方案0
	 * @param dto
	 * @return
	 */
	@Override
	public List<CostDataInputDto> getCostDataInputNewList(CostBatchOnDutyDto dto){
		List<CostDataInputDto> result = new ArrayList<CostDataInputDto>();
		LinkedHashMap<String, List<ProductScheduPlanStart>> mapStart = zzRun.getUnitProgByksrqjzrq(dto.getUnitId(), dto.getShiftBegintime(), dto.getShiftEndtime());
		if(mapStart==null || mapStart.size()<=0) {
			dto.setProgramId("0");
			dto.setProgramName("默认方案");
			dto.setBeginTime(dto.getShiftBegintime());
			dto.setEndTime(dto.getShiftEndtime());
			CostDataInputDto obj = getCostDataInputNew(dto);
			if(obj!=null) {
				result.add(obj);
			}
		}else {
			Where whereMain = Where.create();
			whereMain.eq(CostBatchOnDuty::getUnitId, dto.getUnitId());
			whereMain.eq(CostBatchOnDuty::getTeamId, dto.getTeamId());
			whereMain.eq(CostBatchOnDuty::getShiftId, dto.getShiftId());
			whereMain.eq(CostBatchOnDuty::getWriteDay, dto.getWriteDay());
			List<CostBatchOnDuty> listMain = dao.queryData(CostBatchOnDuty.class, whereMain, null, null);
			LinkedHashMap<String, CostBatchOnDuty> mapMain = new LinkedHashMap<String, CostBatchOnDuty>();
			if(StringUtils.isNotEmpty(listMain)) {
				for(CostBatchOnDuty temp : listMain) {
					String key = temp.getProgramId()+temp.getBeginTime()+temp.getEndTime();
					mapMain.put(key, temp);
				}
			}
			List<ProgramItem> listProg = iProg.getProgramItemListByUnitid(dto.getUnitId());
			LinkedHashMap<String, ProgramItem> mapProg = new LinkedHashMap<String, ProgramItem>();
			if(StringUtils.isNotEmpty(listProg)) {
				for(ProgramItem tempProg : listProg) {
					mapProg.put(tempProg.getId(), tempProg);
				}
			}
			
			for(Entry<String, List<ProductScheduPlanStart>> entry : mapStart.entrySet()) {
				List<ProductScheduPlanStart> list = entry.getValue();
				for(ProductScheduPlanStart temp : list) {
					String key = temp.getProgramid()+temp.getStartdatetime()+temp.getEnddatetime();
					if(mapMain!=null && mapMain.containsKey(key)) {
						mapMain.remove(key);//与历史相同，移除避免后续被删除
					}
					dto.setProgramId(temp.getProgramid());
					if(mapProg!=null && mapProg.containsKey(temp.getProgramid())) {
						dto.setProgramName(mapProg.get(temp.getProgramid()).getPiName());
					}else {
						dto.setProgramName(temp.getProgramname());
					}
					dto.setBeginTime(temp.getStartdatetime());
					dto.setEndTime(temp.getEnddatetime());
					CostDataInputDto obj = getCostDataInputNew(dto);
					if(obj!=null) {
						result.add(obj);
					}
				}
			}
			//删除无用的历史
			if(mapMain!=null && mapMain.size()>0) {
				List<CostBatchOnDuty> listTemp = new ArrayList<CostBatchOnDuty>();
				List<String> listId = new ArrayList<String>();
				for(Entry<String, CostBatchOnDuty> entry : mapMain.entrySet()) {
					CostBatchOnDuty obj = entry.getValue();
					listTemp.add(obj);
					listId.add(obj.getId());
				}
				if(StringUtils.isNotEmpty(listTemp)) {
					Where whereChild = Where.create();
					whereChild.in(CostBatchInstrumentData::getPid, listId.toArray());
					if (dao.delete(CostBatchInstrumentData.class, whereChild)>=0) {
						dao.deleteByIdBatch(listTemp);
					}
				}
			}
		}
		if (result!=null && result.size()>0) {
			// 表单组件保存后调用的方法 计算交接班
			log.info("","开始7"+DateTimeUtils.getDTStr());
			TeamReportInputDto reportinfo = new TeamReportInputDto();
			reportinfo.setTeamId(dto.getTeamId());
			reportinfo.setUnitId(dto.getUnitId());
			reportinfo.setShiftId(dto.getShiftId());
			reportinfo.setWriteDay(dto.getWriteDay());
			reportinfo.setIsBatchCalc(false);
			ctpl.calcBatchProgramData(reportinfo);
			log.info("","开始8"+DateTimeUtils.getDTStr());
		}
		return result;
	}
	
	/**
	 * 批量保存方案的交接班数据
	 * @param dto
	 * @return
	 */
	@Override
	public boolean saveCostInputDataList(CostDataInputDto dto) {
		return saveCostInputData(dto);
	}
	
}
