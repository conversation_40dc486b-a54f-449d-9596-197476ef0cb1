package com.yunhesoft.system.third.controller;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.yaml.snakeyaml.constructor.BaseConstructor;

import com.yunhesoft.system.third.entity.po.ThirdApplicInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api("第三方app应用管理")
@RequestMapping("/system/third")
public class ThirdAppSettingController extends BaseConstructor {

	@ApiOperation(value = "获得第三方应用设置列表")
	@RequestMapping(value = "/get-applic-settings", method = { RequestMethod.GET })
	public List<ThirdApplicInfo> getApplicSettings() {

		return null;
	}

	@ApiOperation(value = "创建或更新第三方应用设置列表")
	@RequestMapping(value = "/save-applic-settings", method = { RequestMethod.POST })
	public List<ThirdApplicInfo> SaveApplicSettings() {

		return null;
	}

	@ApiOperation(value = "获得第三方应用程序的accessToken")
	@RequestMapping(value = "/get-applic-access-token", method = { RequestMethod.POST })
	public List<ThirdApplicInfo> GetApplicAccessToken() {

		return null;
	}
}
