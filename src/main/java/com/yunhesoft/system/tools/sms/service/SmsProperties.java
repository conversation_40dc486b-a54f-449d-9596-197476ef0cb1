package com.yunhesoft.system.tools.sms.service;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "tm4.sms")
//@PropertySource(value="classpath:sms.properties", encoding="UTF-8")
public class SmsProperties {

	/**
	 * 调试模式开关 1：开启（只打日志，不发消息） 0：关闭（系统正常发送消息）
	 */
	private String debug = "0";

	/**
	 * 短信服务提供商
	 */
	private String provider = "";

	/**
	 * 短信签名
	 */
	private String signName = "";

	/**
	 * 服务器地址编号
	 */
	private String regionId = "";

	/**
	 * 访问口令id（类似公钥）
	 */
	private String accessKeyId = "";

	/**
	 * 访问口令秘钥（类似私钥）
	 */
	private String accessKeySecret = "";

	/**
	 * 短信API产品名称（短信产品名固定，无需修改）
	 */
	private String product = "";

	/**
	 * 短信API产品域名（接口地址固定，无需修改）
	 */
	private String domain = "";

	/**
	 * 短信验证码模板
	 */
	private Map<String, String> smsTemplate = new HashMap<String, String>();

	/**
	 * 缓存中验证码的key
	 */
	private String verifyCodeKey = "";

	/**
	 * 缓存中验证码的过期时间（单位：秒）
	 */
	private Integer verifyCodeExpire = 180;
}
