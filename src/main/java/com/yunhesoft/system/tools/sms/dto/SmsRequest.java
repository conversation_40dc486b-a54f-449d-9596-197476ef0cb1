package com.yunhesoft.system.tools.sms.dto;

import java.io.Serializable;

import lombok.Data;

@Data
public class SmsRequest implements Serializable {
	/**
	 * 发送短信封装实体类。
	 * 
	 * <AUTHOR>
	 *
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 电话号。
	 */
	private String phoneNumbers;
	/**
	 * 签名。
	 */
	private String signName;
	/**
	 * 短信模板。
	 */
	private String templateCode;
	/**
	 * 模板中的变量替换JSON串。
	 */
	private String templateParam;
}
