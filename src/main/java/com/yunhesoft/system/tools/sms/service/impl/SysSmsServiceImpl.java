package com.yunhesoft.system.tools.sms.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.MD5Utils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.system.tools.sms.dto.SmsRequest;
import com.yunhesoft.system.tools.sms.dto.SmsResponse;
import com.yunhesoft.system.tools.sms.dto.SmsVerifyCodeDto;
import com.yunhesoft.system.tools.sms.dto.SysSmsRequest;
import com.yunhesoft.system.tools.sms.dto.VerifyCodeInfoDto;
import com.yunhesoft.system.tools.sms.service.ISmsSender;
import com.yunhesoft.system.tools.sms.service.ISysSmsService;
import com.yunhesoft.system.tools.sms.service.SmsProperties;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class SysSmsServiceImpl implements ISysSmsService {

	/**
	 * smsSender实例。
	 */
	@Autowired
	private ISmsSender iSmsSender;

	/**
	 * redis实例
	 */
	@Autowired
	private RedisUtil redis;

	@Autowired
	SmsProperties smsProperties;

	/**
	 * 获取验证码
	 */
	@Override
	public Res<?> getVerifyCode(SmsVerifyCodeDto smsVerifyCodeDto) {
		Res<String> res = new Res<>();
		// 生成短信验证码
		final String verifyCode = makeRandom();
		final SysSmsRequest smsRequest = new SysSmsRequest();
		String phoneNumber = this.formatPhoneNumber(smsVerifyCodeDto.getPhoneNumber());
		if (phoneNumber == null || phoneNumber.length() == 0) {
			res.ok("手机号码无效。号码：" + smsVerifyCodeDto.getPhoneNumber()).setSuccess(false);
			log.error("手机号码无效。号码：" + smsVerifyCodeDto.getPhoneNumber());
			return res;
		}
		String templateType = smsVerifyCodeDto.getTemplateType();
		// 必填:待发送手机号。支持以逗号分隔的形式进行批量调用，批量上限为1000个手机号码
		smsRequest.setPhoneNumbers(phoneNumber);
		// 必填:短信模板-可在短信控制台中找到
		smsRequest.setTemplateType(templateType);
		;
		// 可选:模板中的变量替换JSON串
		Map<String, Object> templateParam = new HashedMap<>();
		templateParam.put("code", verifyCode);
		smsRequest.setTemplateParam(templateParam);
		log.debug("验证码发送参数：" + JSON.toJSONString(smsRequest));
		// 调用阿里云短信接口，发送短信
		final SmsResponse smsResponse = this.sendShortMsg(smsRequest);
		if (smsResponse.getSuccess()) {
			redis.set(this.smsProperties.getVerifyCodeKey() + ":" + phoneNumber + ":" + templateType,
					MD5Utils.md5(phoneNumber + verifyCode + templateType), smsProperties.getVerifyCodeExpire());
			log.debug("验证码:" + verifyCode);
			res.ok().setSuccess(true);
		} else {
			res.ok(smsResponse.getMessage()).setSuccess(false);
		}
		return res;
	}

	/**
	 * 校验验证码
	 */
	@Override
	public Res<?> checkVerifyCode(VerifyCodeInfoDto verifyCodeInfoDto) {
		Res<String> res = new Res<>();
		String phoneNumber = verifyCodeInfoDto.getPhoneNumber();
		String verifyCode = verifyCodeInfoDto.getVerifyCode();
		String templateType = verifyCodeInfoDto.getTemplateType();
		// redis过期后value会变为null。key值继续存在，需要判断
		if (redis.get(this.smsProperties.getVerifyCodeKey() + ":" + phoneNumber + ":" + templateType) != null) {
			String cacheCode = redis
					.getString(this.smsProperties.getVerifyCodeKey() + ":" + phoneNumber + ":" + templateType);
			String realCode = MD5Utils.md5(phoneNumber + verifyCode + templateType);
			// 验证码错误，即返回错误信息
			if (!cacheCode.equals(realCode)) {
				res.ok("验证码错误！").setSuccess(false);
			} else {
				// 验证码正确,清空验证码
				redis.delete(this.smsProperties.getVerifyCodeKey() + ":" + phoneNumber + ":" + templateType);
				res.ok().setSuccess(true);
			}
		} else {
			res.ok("未获取验证码或验证码已过期，请重新获取验证码！").setSuccess(false);
		}
		return res;
	}

	/**
	 * 随机生成6位数字验证码。
	 * 
	 * @return 随机验证码
	 */
	private String makeRandom() {
		Random rand = new Random();
		final String sources = "0123456789"; // 设置随机数取值范围
		final StringBuffer codeNum = new StringBuffer();
		for (int j = 0; j < 6; j++) {
			codeNum.append(sources.charAt(rand.nextInt(9)) + "");
		}
		return codeNum.toString();
	}

	private SmsResponse sendShortMsg(SysSmsRequest sysSmsRequest) {
		final SmsRequest smsRequest = new SmsRequest();
		SmsResponse smsResponse = new SmsResponse();
		String phoneNumber = this.formatPhoneNumber(sysSmsRequest.getPhoneNumbers());
		if (phoneNumber == null || phoneNumber.length() == 0) {
			smsResponse.setSuccess(false);
			smsResponse.setCode("601");
			log.error("手机号码无效。号码：" + sysSmsRequest.getPhoneNumbers());
			smsResponse.setMessage("手机号码无效。号码：" + sysSmsRequest.getPhoneNumbers());

			return smsResponse;
		}
		// 必填:待发送手机号。支持以逗号分隔的形式进行批量调用，批量上限为1000个手机号码
		smsRequest.setPhoneNumbers(sysSmsRequest.getPhoneNumbers());
		// 必填:短信模板-可在短信控制台中找到
		smsRequest.setTemplateCode(smsProperties.getSmsTemplate().get(sysSmsRequest.getTemplateType()));
		// 可选:模板中的变量替换JSON串
		smsRequest.setTemplateParam(JSON.toJSONString(sysSmsRequest.getTemplateParam()));
		// 调用短信接口，发送短信
		smsResponse = iSmsSender.sendMsg(smsRequest);
		if (!smsResponse.getSuccess()) {
			log.error(smsResponse.getMessage());
		}
		return smsResponse;
	}

	@Override
	public SmsResponse sendMsg(SysSmsRequest sysSmsRequest) {
		return this.sendShortMsg(sysSmsRequest);
	}

	/**
	 * 格式化电话号码
	 * 
	 * @category 格式化电话号码
	 * @param phoneNumber
	 * @return String 格式化后的电话号码
	 */
	private String formatPhoneNumber(String phoneNumber) {
		String result = null;
		List<String> phonelist = new ArrayList<String>();
		if (phoneNumber != null && phoneNumber.length() != 0) {
			String baseRegex = "^[\\d]{11,}$";// 解析基础参数 11位或以上数字
			Pattern p = Pattern.compile(baseRegex);
			String[] phoneArr = phoneNumber.split(",");
			for (int i = 0; i < phoneArr.length; i++) {
				Matcher m = p.matcher(phoneArr[i]);
				if (m.find()) {// 查找到了,代表输入数据正确有效
					phonelist.add(phoneArr[i]);
				}
			}

		}
		if (phonelist != null && phonelist.size() > 0) {
			result = String.join(",", phonelist);
		}
		return result;
	}
}
