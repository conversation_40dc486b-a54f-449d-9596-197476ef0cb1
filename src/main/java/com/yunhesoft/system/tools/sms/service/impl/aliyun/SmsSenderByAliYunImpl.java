package com.yunhesoft.system.tools.sms.service.impl.aliyun;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.yunhesoft.system.tools.sms.dto.SmsRequest;
import com.yunhesoft.system.tools.sms.dto.SmsResponse;
import com.yunhesoft.system.tools.sms.service.ISmsSender;
import com.yunhesoft.system.tools.sms.service.SmsProperties;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
@ConditionalOnProperty(prefix = "tm4.sms", name = "provider", havingValue = "aliyun")
public class SmsSenderByAliYunImpl implements ISmsSender {

	@Autowired
	SmsProperties smsProperties;

	@Override
	public SmsResponse sendMsg(SmsRequest smsRequest) {
		// 必填:短信签名-可在短信控制台中找到
		smsRequest.setSignName(new String(smsProperties.getSignName()));
		SmsResponse smsResponse = new SmsResponse();
		SendSmsResponse sendSmsResponse = new SendSmsResponse();
		try {
			// 设置超时时间-可自行调整
			System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
			System.setProperty("sun.net.client.defaultReadTimeout", "10000");
			// 初始化ascClient,暂时不支持多region（请勿修改）
			final IClientProfile profile = DefaultProfile.getProfile(this.smsProperties.getRegionId(),
					this.smsProperties.getAccessKeyId(), smsProperties.getAccessKeySecret());
			DefaultProfile.addEndpoint(this.smsProperties.getRegionId(), this.smsProperties.getRegionId(),
					this.smsProperties.getProduct(), this.smsProperties.getDomain());
			final IAcsClient acsClient = new DefaultAcsClient(profile);
			// 发送消息相关参数传递给request
			SendSmsRequest request = new SendSmsRequest();
			request.setPhoneNumbers(smsRequest.getPhoneNumbers());
			request.setSignName(smsProperties.getSignName());
			request.setTemplateCode(smsRequest.getTemplateCode());
			request.setTemplateParam(smsRequest.getTemplateParam());
			// 使用post提交
			request.setMethod(MethodType.POST);
			sendSmsResponse = acsClient.getAcsResponse(request);
			log.info(String.format("手机号:%s,短信模板参数:%s", smsRequest.getPhoneNumbers(),smsRequest.getTemplateParam()));
			if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
				smsResponse.setCode(sendSmsResponse.getCode());
				smsResponse.setSuccess(true);
				// 请求成功
				log.info("短信发送成功");
			} else {
				// smsResponse.setMessage("短信发送失败，请一分钟以后重试");
				smsResponse.setCode(sendSmsResponse.getCode());
				smsResponse.setSuccess(false);
				smsResponse.setMessage(sendSmsResponse.getMessage());
				String str = String.format("短信发送未成功，返回值为：%s", sendSmsResponse.getMessage());
				log.info(str);
			}
		} catch (final Exception e1) {
			log.error("Exception() error", e1);
		}
		return smsResponse;
	}
}
