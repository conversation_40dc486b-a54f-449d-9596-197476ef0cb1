package com.yunhesoft.system.tools.sms.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.sms.dto.SmsVerifyCodeDto;
import com.yunhesoft.system.tools.sms.dto.VerifyCodeInfoDto;
import com.yunhesoft.system.tools.sms.service.ISysSmsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/sms")
@Api(tags = "短信服务")
@Log4j2
public class SysSmsController extends BaseRestController {
	@Autowired
	private ISysSmsService srv;

	/**
	 * 获取验证码。
	 * 
	 * @param req 结果集
	 * @return Res<?>
	 */
	@RequestMapping(value = "/getVerifyCode", method = RequestMethod.POST)
	@ApiOperation(value = "获取验证码")
	public Res<?> getVerifyCode(@RequestBody SmsVerifyCodeDto smsVerifyCodeDto) {
		Res<?> result = new Res<>();
		try {
			result = srv.getVerifyCode(smsVerifyCodeDto);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			result.fail(500, "操作失败");
		}
		return result;
	}

	/**
	 * 验证验证码。
	 * 
	 * @param verifyCodeInfoDto 验证码信息
	 * @return Res<?>
	 */
	@RequestMapping(value = "/checkVerifyCode", method = RequestMethod.POST)
	@ApiOperation(value = "验证验证码")
	public Res<?> checkVerifyCode(@RequestBody VerifyCodeInfoDto verifyCodeInfoDto) {
		Res<?> result = new Res<>();
		try {
			result = srv.checkVerifyCode(verifyCodeInfoDto);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			result.fail(500, "操作失败");
		}
		return result;
	}

}