package com.yunhesoft.system.tools.sms.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "获取手机号验证码", description = "获取验证码实体")
public class SmsVerifyCodeDto {

	@ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phoneNumber;

    @ApiModelProperty(value = "短信模板类型")
    @NotBlank(message = "短信模板类型不能为空")
    private String templateType;
}
