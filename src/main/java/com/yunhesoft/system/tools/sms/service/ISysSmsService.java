package com.yunhesoft.system.tools.sms.service;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.tools.sms.dto.SmsResponse;
import com.yunhesoft.system.tools.sms.dto.SmsVerifyCodeDto;
import com.yunhesoft.system.tools.sms.dto.SysSmsRequest;
import com.yunhesoft.system.tools.sms.dto.VerifyCodeInfoDto;

public interface ISysSmsService {

    Res<?> checkVerifyCode(VerifyCodeInfoDto verifyCodeInfoDto);

	/**
	 * 获取验证码
	 */
	Res<?> getVerifyCode(SmsVerifyCodeDto smsVerifyCodeDto);

	SmsResponse sendMsg(SysSmsRequest sysSmsRequest);
}
