package com.yunhesoft.system.tools.sms.service.impl;

import com.yunhesoft.system.tools.sms.dto.SmsRequest;
import com.yunhesoft.system.tools.sms.dto.SmsResponse;
import com.yunhesoft.system.tools.sms.service.ISmsSender;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Log4j2
@Component
@ConditionalOnProperty(prefix = "tm4.sms", name = "provider", havingValue = "none", matchIfMissing = true)
public class SmsSenderByNoneImpl implements ISmsSender {
    @Override
    public SmsResponse sendMsg(SmsRequest smsRequest) {
        SmsResponse res = new SmsResponse();
        res.setSuccess(false);
        res.setMessage("请确认短信接口配置是否正确");
        return res;
    }
}
