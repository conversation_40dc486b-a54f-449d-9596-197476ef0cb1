package com.yunhesoft.system.tools.sms.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "手机号验证码信息", description = "登录或注册时验证码信息")
public class VerifyCodeInfoDto {
	@ApiModelProperty(value = "手机号")
	@NotBlank(message = "手机号不能为空")
	private String phoneNumber;

	@ApiModelProperty(value = "验证码")
	@NotBlank(message = "验证码不能为空")
	private String verifyCode;

	@ApiModelProperty(value = "短信模板类型")
	@NotBlank(message = "短信模板类型不能为空")
	private String templateType;
}
