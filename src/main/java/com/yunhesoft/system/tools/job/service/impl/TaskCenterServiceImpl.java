package com.yunhesoft.system.tools.job.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.stereotype.Service;

import com.yunhesoft.system.tools.job.service.ITaskCenterService;

import lombok.extern.log4j.Log4j2;

/**
 * 调度中心实现类
 * 
 * <AUTHOR>
 *
 */
@Log4j2
@Service
public class TaskCenterServiceImpl implements ITaskCenterService {

	/**
	 * 执行调度任务
	 * 
	 * @param execContent 执行内容
	 * @param execParam   执行参数
	 * @return 错误信息
	 */
	@Override
	public String execTask(String execContent, String execParam) {
		String error = null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if (execContent == null || execContent.trim().length() == 0) {
			error = "请传入执行内容（execContent）！";
			log.error(error);
			return error;
		}

		if (execContent.endsWith("/run")) {
			execContent = execContent.substring(0, execContent.length() - 4);
		}
		if (execParam != null && execParam.endsWith("/run")) {
			execParam = execParam.substring(0, execParam.length() - 4);
		}
		String execInfo = "execContent:[" + execContent + "],execParam:[" + String.valueOf(execParam) + "]";
//		if (!execContent.startsWith("taskcenter.")) {
//			error = "禁止执行非调度中心应用，" + execInfo;
//			log.error(error);
//			return error;
//		}
		try {
			log.info("**tm-task,start:[" + sdf.format(new Date()) + "]," + execInfo);
			Class<?> cls = Class.forName(execContent);
			try {
				Object instance = cls.newInstance();
				try {
					Method method = cls.getMethod("exec", String.class);
					try {
						method.invoke(instance, execParam);
						log.info(
								"**tm-task,end:[" + sdf.format(new Date()) + "]," + execInfo + ",execResult:[success]");
					} catch (IllegalArgumentException e) {
						error = e.toString();
						log.error(execInfo + ",execResult:[fail]", e);
					} catch (InvocationTargetException e) {
						error = e.toString();
						log.error(execInfo + ",execResult:[fail]", e);
					} catch (Exception e) {
						error = e.toString();
						log.error(execInfo + ",execResult:[fail]", e);
					}
				} catch (SecurityException e) {
					error = e.toString();
					log.error(execInfo + ",execResult:[fail]", e);
				} catch (NoSuchMethodException e) {
					error = e.toString();
					log.error(execInfo + ",execResult:[fail]", e);
				}
			} catch (InstantiationException e) {
				error = e.toString();
				log.error(execInfo + ",类实例化异常！", e);
			} catch (IllegalAccessException e) {
				error = e.toString();
				log.error(execInfo + ",反射异常！", e);
			}
		} catch (Exception e) {
			error = e.toString();
			log.error(execInfo, e);
		}
		return error;
	}

}
