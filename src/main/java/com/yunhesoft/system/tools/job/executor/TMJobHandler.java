package com.yunhesoft.system.tools.job.executor;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yunhesoft.system.tools.job.service.ITaskCenterService;

/**
 * XXL-Job调度任务执行器
 * 
 * <AUTHOR>
 *
 * @since 2022.3.10
 */
@Component
public class TMJobHandler {

	private static Logger logger = LoggerFactory.getLogger(TMJobHandler.class);

	private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	@Autowired
	private ITaskCenterService iTaskServ;

	/**
	 * tm4任务执行通用类
	 */
	@XxlJob("tm4Job")
	public void tm4Job() throws Exception {
		String param = XxlJobHelper.getJobParam();// 执行参数
		// class:com.yunhesoft.system.tools.job.app.TMTestJob
		// param:{zzdm:"0050060101"}
		String error = "";
		if (param == null || param.trim().length() == 0) {
			error = "tm4Job执行失败,param invalid.";
			XxlJobHelper.log(error);
			logger.error(error);
			XxlJobHelper.handleFail(error);
			return;
		} else {
			String[] params = param.split("\n");
			String className = null;
			String execParam = null;
			for (String s : params) {
				if (s.startsWith("class:")) {// 执行类
					className = s.substring(s.indexOf("class:") + 6).trim();
				}
				if (s.startsWith("param:")) {// 执行参数
					execParam = s.substring(s.indexOf("param:") + 6).trim();
				}
			}
			if (className == null || className.length() == 0) {
				error = "tm4Job执行失败,className invalid.";
				XxlJobHelper.log(error);
				logger.error(error);
				XxlJobHelper.handleFail(error);
				return;
			} else {
				this.exceJob(className, execParam);
			}
		}
	}

	/**
	 * 测试任务
	 */
	@XxlJob("tm4Test")
	public void test() throws Exception {
		this.exceJob("com.yunhesoft.system.tools.job.app.TMTestJob", XxlJobHelper.getJobParam());
	}

	/**
	 * 执行任务类
	 * 
	 * @param className 类名 eg：taskcenter.app.Test
	 * @throws Exception
	 */
	private void exceJob(String className, String param) throws Exception {
		// String className = "taskcenter.app.Test";// 任务要执行的java类
		String execInfo = "className:[" + className + "],param:[" + String.valueOf(param) + "]";
		String info = iTaskServ.execTask(className, param);
		if (info == null) {
			info = "执行成功," + sdf.format(new Date()) + "," + execInfo;
			XxlJobHelper.log(info);
			logger.info(info);
			XxlJobHelper.handleSuccess(info);
		} else {
			info = "执行失败," + execInfo + "," + info;
			XxlJobHelper.log(info);
			logger.error(info);
			XxlJobHelper.handleFail(info);
		}
	}

	/**
	 * 跨平台Http任务 参数示例： "url: http://www.baidu.com\n" + "method: get\n" + "data:
	 * content\n";
	 */
	@XxlJob("tmHttpJobHandler")
	public void httpJobHandler() throws Exception {

		// param parse
		String error = "";
		String param = XxlJobHelper.getJobParam();
		if (param == null || param.trim().length() == 0) {
			error = "param[" + param + "] invalid.";
			XxlJobHelper.log(error);
			XxlJobHelper.handleFail(error);
			return;
		}

		String[] httpParams = param.split("\n");
		String url = null;
		String method = null;
		String data = null;
		for (String httpParam : httpParams) {
			if (httpParam.startsWith("url:")) {
				url = httpParam.substring(httpParam.indexOf("url:") + 4).trim();
			}
			if (httpParam.startsWith("method:")) {
				method = httpParam.substring(httpParam.indexOf("method:") + 7).trim().toUpperCase();
			}
			if (httpParam.startsWith("data:")) {
				data = httpParam.substring(httpParam.indexOf("data:") + 5).trim();
			}
		}

		// param valid
		if (url == null || url.trim().length() == 0) {
			error = "url[" + url + "] invalid.";
			XxlJobHelper.log(error);
			XxlJobHelper.handleFail(error);
			return;
		}
		if (method == null || !Arrays.asList("GET", "POST").contains(method)) {
			error = "method[" + method + "] invalid.";
			XxlJobHelper.log(error);
			XxlJobHelper.handleFail(error);
			return;
		}
		boolean isPostMethod = method.equals("POST");
		// request
		HttpURLConnection connection = null;
		BufferedReader bufferedReader = null;
		try {
			// connection
			URL realUrl = new URL(url);
			connection = (HttpURLConnection) realUrl.openConnection();
			// connection setting
			connection.setRequestMethod(method);
			connection.setDoOutput(isPostMethod);
			connection.setDoInput(true);
			connection.setUseCaches(false);
			connection.setReadTimeout(5 * 1000);
			connection.setConnectTimeout(3 * 1000);
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
			connection.setRequestProperty("Accept-Charset", "application/json;charset=UTF-8");

			// do connection
			connection.connect();

			// data
			if (isPostMethod && data != null && data.trim().length() > 0) {
				DataOutputStream dataOutputStream = new DataOutputStream(connection.getOutputStream());
				dataOutputStream.write(data.getBytes("UTF-8"));
				dataOutputStream.flush();
				dataOutputStream.close();
			}

			// valid StatusCode
			int statusCode = connection.getResponseCode();
			if (statusCode != 200) {
				throw new RuntimeException("Http Request StatusCode(" + statusCode + ") Invalid.");
			}

			// result
			bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
			StringBuilder result = new StringBuilder();
			String line;
			while ((line = bufferedReader.readLine()) != null) {
				result.append(line);
			}
			String responseMsg = result.toString();

			XxlJobHelper.log(responseMsg);

			return;
		} catch (Exception e) {
			XxlJobHelper.log(e);
			XxlJobHelper.handleFail(e.getMessage());
			return;
		} finally {
			try {
				if (bufferedReader != null) {
					bufferedReader.close();
				}
				if (connection != null) {
					connection.disconnect();
				}
			} catch (Exception e2) {
				XxlJobHelper.log(e2);
			}
		}

	}

	public void init() {
		logger.info("init");
	}

	public void destroy() {
		logger.info("destroy");
	}

}
