package com.yunhesoft.system.tools.job.app;

import java.text.SimpleDateFormat;
import java.util.Date;

import com.yunhesoft.system.tools.job.service.IJobApp;

/**
 * TM4测试调度执行类
 * 
 * <AUTHOR>
 *
 */
public class TMTestJob implements IJobApp {

	/**
	 * 调度执行内容
	 */
	@Override
	public void exec(String param) {
		// 这里调用执行内容
		// 如果执行出错，要抛出错误，方便执行器抓取问题
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		System.out.println(sdf.format(new Date()) + ",TMTestJob执行成功！,参数:" + String.valueOf(param));
	}

}