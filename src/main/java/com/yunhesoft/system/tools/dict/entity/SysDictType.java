package com.yunhesoft.system.tools.dict.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 字典类型表 sys_dict_type
 * 
 */
@Entity
@Table(name = "SYS_DICT_TYPE")
@Getter
@Setter
public class SysDictType extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 模块编码
	 */
	@Column(name = "MODULECODE", length = 50)
	private String moduleCode;

	/** 字典名称 */
	@Column(name = "DICT_NAME")
	private String dictName;

	/** 字典类型 */
	@Column(name = "DICT_TYPE")
	private String dictType;

	/** 状态（0正常 1停用） */
	@Column(name = "STATUS")
	private String status;

	@Column(name = "REMARK")
	private String remark;

	@NotBlank(message = "字典名称不能为空")
	@Size(min = 0, max = 100, message = "字典类型名称长度不能超过100个字符")
	public String getDictName() {
		return dictName;
	}

	public void setDictName(String dictName) {
		this.dictName = dictName;
	}

	@NotBlank(message = "字典类型不能为空")
	@Size(min = 0, max = 100, message = "字典类型类型长度不能超过100个字符")
	public String getDictType() {
		return dictType;
	}

	public void setDictType(String dictType) {
		this.dictType = dictType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
}
