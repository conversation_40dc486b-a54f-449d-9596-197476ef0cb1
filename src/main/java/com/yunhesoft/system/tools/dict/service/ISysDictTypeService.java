package com.yunhesoft.system.tools.dict.service;

import java.util.List;

import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.entity.SysDictType;

/**
 * 字典 业务层
 * 
 */
public interface ISysDictTypeService {
	/**
	 * 根据条件分页查询字典类型
	 * 
	 * @param dictType 字典类型信息
	 * @return 字典类型集合信息
	 */
	public List<SysDictType> selectDictTypeList(SysDictType dictType);

	/**
	 * 根据所有字典类型
	 * 
	 * @return 字典类型集合信息
	 */
	public List<SysDictType> selectDictTypeAll();

	/**
	 * 根据字典类型查询字典数据
	 * 
	 * @param dictType 字典类型
	 * @return 字典数据集合信息
	 */
	public List<SysDictData> selectDictDataByType(String dictType);

	/**
	 * 根据字典类型ID查询信息
	 * 
	 * @param dictId 字典类型ID
	 * @return 字典类型
	 */
	public SysDictType selectDictTypeById(String id);

	/**
	 * 根据字典类型查询信息
	 * 
	 * @param dictType 字典类型
	 * @return 字典类型
	 */
	public SysDictType selectDictTypeByType(String dictType);

	/**
	 * 批量删除字典信息
	 * 
	 * @param dictIds 需要删除的字典ID
	 * @return 结果
	 */
	public int deleteDictTypeById(String dictIds);

	/**
	 * 清空缓存数据
	 */
	public void clearCache();

	/**
	 * 新增保存字典类型信息
	 * 
	 * @param dictType 字典类型信息
	 * @return 结果
	 */
	public int insertDictType(SysDictType dictType);

	/**
	 * 修改保存字典类型信息
	 * 
	 * @param dictType 字典类型信息
	 * @return 结果
	 */
	public int updateDictType(SysDictType dictType);

	/**
	 * 校验字典类型称是否唯一
	 * 
	 * @param dictType 字典类型
	 * @return 结果
	 */
	public String checkDictTypeUnique(SysDictType dictType);

	/**
	 * 数据初始化
	 */
	public void init();

	int insertDictTypeDisableTenant(SysDictType dictType);
}
