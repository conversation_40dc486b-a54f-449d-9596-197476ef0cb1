package com.yunhesoft.system.tools.dict.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 字典数据表 sys_dict_data
 * 
 */
@Entity
@Table(name = "SYS_DICT_DATA")
@Getter
@Setter
public class SysDictData extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/** 字典排序 */
	@Column(name = "DICT_SORT")
	private Long dictSort;

	/** 字典标签 */
	@Column(name = "DICT_LABEL")
	private String dictLabel;

	/** 字典键值 */
	@Column(name = "DICT_VALUE")
	private String dictValue;

	/** 字典类型 */
	@Column(name = "DICT_TYPE")
	private String dictType;

	/** 样式属性（其他样式扩展） */
	@Column(name = "CSS_CLASS")
	private String cssClass;

	/** 表格字典样式 */
	@Column(name = "LIST_CLASS")
	private String listClass;

	/** 是否默认（Y是 N否） */
	@Column(name = "IS_DEFAULT")
	private String isDefault;

	/** 状态（0正常 1停用） */
	@Column(name = "STATUS")
	private String status;

	@Column(name = "REMARK")
	private String remark;



	@NotBlank(message = "字典标签不能为空")
	@Size(min = 0, max = 100, message = "字典标签长度不能超过100个字符")
	public String getDictLabel() {
		return dictLabel;
	}

	public void setDictLabel(String dictLabel) {
		this.dictLabel = dictLabel;
	}

	@NotBlank(message = "字典键值不能为空")
	@Size(min = 0, max = 100, message = "字典键值长度不能超过100个字符")
	public String getDictValue() {
		return dictValue;
	}

	public void setDictValue(String dictValue) {
		this.dictValue = dictValue;
	}

	@NotBlank(message = "字典类型不能为空")
	@Size(min = 0, max = 100, message = "字典类型长度不能超过100个字符")
	public String getDictType() {
		return dictType;
	}

	public void setDictType(String dictType) {
		this.dictType = dictType;
	}

	@Size(min = 0, max = 100, message = "样式属性长度不能超过100个字符")
	public String getCssClass() {
		return cssClass;
	}

	public void setCssClass(String cssClass) {
		this.cssClass = cssClass;
	}

	public String getListClass() {
		return listClass;
	}

	public void setListClass(String listClass) {
		this.listClass = listClass;
	}

	public boolean getDefault() {
		return "yes".equalsIgnoreCase(this.isDefault) ? true : false;
	}

	public String getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(String isDefault) {
		this.isDefault = isDefault;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
}
