package com.yunhesoft.system.tools.diy.service;


import com.yunhesoft.core.common.entity.BaseEntity;
import com.yunhesoft.system.tools.diy.entity.dto.PageAnalysisDrawDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageAnalysisDrawDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageAnalysisDrawTypeDto;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageAnalysisDraw;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageAnalysisDrawType;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageAnalysisDrawVo;

import java.util.List;

/**
 * @Description: 分析图
 * <AUTHOR>
 * @date 2022/10/22
 */
public interface IDiyPageAnalysisDrawService {
    /**
     * 查询分析图
     * <AUTHOR>
     * @return
     * @params
    */
    List<DiyPageAnalysisDraw> getAnalysisDraw(PageAnalysisDrawDto param);
    /**
     * 保存分析图
     * <AUTHOR>
     * @return
     * @params
    */
    Boolean saveAnalysisDraw(SavePageAnalysisDrawDto param);
    /**
     * 保存分析图类型
     * @param param
     * @return
     */
	Boolean saveAnalysisDrawType(SavePageAnalysisDrawTypeDto param);
	/**
     * 查询分析图类型
     * @param param
     * @return
     */
	List<DiyPageAnalysisDrawType> getAnalysisDrawType(String classId);


}
