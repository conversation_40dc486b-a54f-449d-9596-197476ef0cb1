package com.yunhesoft.system.tools.diy.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "自定义页面关系表")
@Data
@Entity
@Table(name = "DIY_PAGE_LIB_REL")
public class DiyPageLibRel extends BaseEntity {
    @ApiModelProperty(value = "对象id")
    @Column(name = "OBJID", length = 100)
    private String objId;

    @ApiModelProperty(value = "自定义页面id")
    @Column(name = "PAGEID", length = 2000)
    private String pageId;

    @ApiModelProperty(value = "类型 first机构首页 second岗位首页 third个人首页")
    @Column(name = "TYPE", length = 100)
    private String type;

    @ApiModelProperty(value = "是否引用")
    @Column(name = "ISREFS")
    private Integer isRefs;

    @ApiModelProperty(value = "引用pageId  type为zero pageId 非zero objId")
    @Column(name = "REFSPAGEID", length = 100)
    private String refsPageId;

    @ApiModelProperty(value = "引用page名称  type为zero pageId 非zero objId")
    @Column(name = "REFSPAGENAME", length = 100)
    private String refsPageName;

    @ApiModelProperty(value = "继承方式  0|| null 不继承  1 指定机构  2 上级继承")
    @Column(name = "EXTENDSTYPE")
    private  Integer extendsType;
}
