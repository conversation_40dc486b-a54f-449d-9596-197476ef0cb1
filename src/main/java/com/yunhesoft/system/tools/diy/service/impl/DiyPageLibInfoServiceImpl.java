package com.yunhesoft.system.tools.diy.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageInfoDto;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageLib;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageLibInfo;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageLibRel;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibInfoVo;
import com.yunhesoft.system.tools.diy.service.IDiyPageLibInfoService;
import com.yunhesoft.system.tools.diy.service.IDiyPageLibService;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/29 10:56
 * @description：自定义页面服务实现类
 * @modified By：
 */
@Service
public class DiyPageLibInfoServiceImpl implements IDiyPageLibInfoService {

    @Autowired
    private EntityService dao;
    @Autowired
    private IDiyPageLibService libService;
    @Autowired
    private IDiyPageLibInfoService infoService;

    /**
     * 通过pageId获取自定义页面配置数据
     *
     * @param pageId
     * @return
     */
    @Override
    public List<DiyPageLibInfoVo> getPageLibInfoByPageIdIncludeRouter(String pageId, String type) {
        JSONObject redirectPage = libService.redirectPage(pageId, type);
        Integer isRelation = 0;
        if (StringUtils.isNotEmpty(redirectPage)) {
            pageId = redirectPage.getString("pageId");
            type = redirectPage.getString("type");
            isRelation = 1;
        }
        List<DiyPageLibInfoVo> pageLibInfoByPageId = this.getPageLibInfoByPageId(pageId, type);
        if (StringUtils.isNotEmpty(pageLibInfoByPageId)) {
            for (DiyPageLibInfoVo diyPageLibInfoVo : pageLibInfoByPageId) {
                diyPageLibInfoVo.setIsRelation(isRelation);
            }
        }
        return pageLibInfoByPageId;
    }

    /**
     * 通过pageId获取自定义页面配置数据
     *
     * @param pageId
     * @return
     */
    @Override
    public List<DiyPageLibInfoVo> getPageLibInfoByPageId(String pageId, String type) {
        List<DiyPageLibInfoVo> list = new ArrayList<>();
        if (!"zero".equals(type)) {
            list = this.getObjPage(pageId, type, 1);
        } else {
            list = this.getPageLibInfoByPageId(pageId, 1, null, null);
        }
        if (ObjUtils.notEmpty(list)) {
            list.forEach(item -> {
                String commParams = item.getCommParams();
                if (ObjUtils.notEmpty(commParams)) {
                    item.setParams(ObjUtils.convertTo(JSONObject.parse(commParams), Map.class));
                }
            });
        }
        return list;
    }

    /*
     * 通过pageId和tmused获取自定义页面配置数据
     *
     * @param pageId 页面id
     *
     * @param tmUsed 是否获取启用的数据 null：全部获取（用于设置页面） 1：只获取启用数据（用于调用）
     *
     * @return
     */
    private List<DiyPageLibInfoVo> getPageLibInfoByPageId(String pageId, Integer tmUsed, String moduleCode,
                                                          String dataId) {
        List<DiyPageLibInfoVo> listVo = new ArrayList<>();
        Where where = Where.create();
        if (StringUtils.isNotEmpty(pageId) && pageId.contains(",")) {
            where.in(DiyPageLibInfo::getPageId, pageId.split(","));
        } else if (StringUtils.isNotEmpty(pageId)) {
            where.eq(DiyPageLibInfo::getPageId, pageId);
        }
        if (ObjUtils.notEmpty(tmUsed)) {
            where.eq(DiyPageLibInfo::getTmUsed, tmUsed);
        }
        if (StringUtils.isNotEmpty(moduleCode)) {
            where.eq(DiyPageLibInfo::getModuleCode, moduleCode);
        }
        if (StringUtils.isNotEmpty(dataId)) {
            where.eq(DiyPageLibInfo::getModuleCode, dataId);
        }
        Order order = Order.create();
        order.orderByAsc(DiyPageLibInfo::getTmSort);
        List<DiyPageLibInfo> list = dao.rawQueryListByWhere(DiyPageLibInfo.class, where, order);
        if (StringUtils.isNotEmpty(list)) {
            for (DiyPageLibInfo info : list) {
                DiyPageLibInfoVo bean = ObjUtils.copyTo(info, DiyPageLibInfoVo.class);
                listVo.add(bean);
            }
        }
        return listVo;
    }

    /**
     * 查询机构岗位人员页面
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private List<DiyPageLibInfoVo> getObjPage(String objcode, String type, Integer tmUsed) {
        String pageId = this.getPageIdByOrg(objcode, type);
        if (StringUtils.isEmpty(pageId)) {
            return null;
        }
        return this.getPageLibInfoByPageId(pageId, tmUsed, null, null);
    }

    /**
     * 通过机构查询pageId
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public String getPageIdByOrg(String objcode, String type) {
        // 查询关系表
        Where where = Where.create();
        where.eq(DiyPageLibRel::getObjId, objcode);
        DiyPageLibRel rel = dao.queryObject(DiyPageLibRel.class, where);
        if (ObjUtils.isEmpty(rel)) {
            return null;
        }
        type = rel.getType();
        // 查询lib表
        List<DiyPageLib> lib = null;
        if (rel.getIsRefs() == 1) {
            List<DiyPageLibRel> allLibrel = dao.queryList(DiyPageLibRel.class);
            rel = this.getPageByRefs(allLibrel, rel.getRefsPageId());
        }
        if (ObjUtils.isEmpty(rel) || StringUtils.isEmpty(rel.getPageId())) {
            return null;
        }
        Where where1 = Where.create();
        if (rel.getPageId().contains(",")) {
            where1.in(DiyPageLib::getPageId, rel.getPageId().split(","));
        } else {
            where1.eq(DiyPageLib::getPageId, rel.getPageId());
        }
        where1.eq(DiyPageLib::getUsed, 1);
        lib = dao.rawQueryListByWhere(DiyPageLib.class, where1);
        if (StringUtils.isEmpty(lib)) {
            return null;
        }
        String pageId = "";
        for (DiyPageLib diyPageLib : lib) {
            if (StringUtils.isNotEmpty(diyPageLib.getPageId())) {
                if (StringUtils.isNotEmpty(pageId)) {
                    pageId = pageId + "," + diyPageLib.getPageId();
                } else {
                    pageId = diyPageLib.getPageId();
                }
            }
        }
        if (StringUtils.isEmpty(pageId)) {
            return null;
        }
        return pageId;
    }

    /**
     * 查询页面
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private DiyPageLibRel getPageByRefs(List<DiyPageLibRel> allLibrel, String refsId) {
        if (StringUtils.isEmpty(allLibrel)) {
            return null;
        }
        for (DiyPageLibRel lib : allLibrel) {
            //引用id未是关系表对象id 则可能是自定义页面的id，此时数据未引用关系树叶子节点
            List<String> objList = allLibrel.stream().map(DiyPageLibRel::getObjId).collect(Collectors.toList());
            if (!objList.contains(refsId)) {
                lib.setPageId(lib.getRefsPageId());
                return lib;
            } else {
                if (StringUtils.isNotEmpty(lib.getObjId()) && lib.getObjId().equals(refsId)) {
                    if (lib.getIsRefs() == 1) {
                        return this.getPageByRefs(allLibrel, lib.getRefsPageId());
                    } else {
                        return lib;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 保存自定义页面配置数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public Boolean savePageInfo(SavePageInfoDto param) {
        Boolean bln = false;
        if (ObjUtils.notEmpty(param)) {
            if (StringUtils.isNotEmpty(param.getData())) {
                Where where = Where.create();
                Integer libMax = dao.findMaxValue(DiyPageLib.class, DiyPageLib::getTmSort, Integer.class, where);
                if (ObjUtils.isEmpty(libMax)) {
                    libMax = 0;
                }
                List<DiyPageLibInfo> insertList = new ArrayList<>();
                List<DiyPageLibInfo> updateList = new ArrayList<>();
                List<DiyPageLibInfo> deleteList = new ArrayList<>();
                List<DiyPageLibRel> insertRelList = new ArrayList<>();
                List<DiyPageLib> saveLibList = new ArrayList<>();
                for (DiyPageLibInfoVo infoVo : param.getData()) {
                    Integer rowFlag = infoVo.getRowFlag();
                    DiyPageLibInfo bean = ObjUtils.copyTo(infoVo, DiyPageLibInfo.class);
                    if (rowFlag == null || rowFlag == 0) {
                        // 增加
                        bean.setId(TMUID.getUID());
                        if (StringUtils.isNotEmpty(infoVo.getObjCode())) {
                            String pageId = TMUID.getUID();
                            // 维护库表
                            DiyPageLib lib = new DiyPageLib();
                            lib.setId(pageId);
                            lib.setPageId(pageId);
                            lib.setObjType(param.getType());
                            lib.setShowType(param.getShowType());
                            lib.setIsUsePersonMenu(param.getIsUsePersonMenu());
                            lib.setUsed(1);
                            lib.setTmSort(++libMax);
                            saveLibList.add(lib);
                            // 提供 机构岗位人员 等 对象编码
                            // 生成关系
                            DiyPageLibRel rel = new DiyPageLibRel();
                            rel.setPageId(pageId);
                            rel.setType(infoVo.getType());
                            rel.setObjId(infoVo.getObjCode());
                            rel.setType(param.getType());
                            rel.setRefsPageId(null);
                            rel.setIsRefs(0);
                            if (StringUtils.isNotEmpty(insertRelList)) {
                                for (int i = 0; i < insertRelList.size(); i++) {
                                    DiyPageLibRel relbean = insertRelList.get(i);
                                    if (relbean.getObjId().equals(infoVo.getObjCode())) {
                                        String pages = relbean.getPageId();
                                        relbean.setPageId(pages + "," + pageId);
                                    }
                                }
                            } else {
                                insertRelList.add(rel);
                            }
                            bean.setPageId(pageId);
                        }
                        // 自定义布局保存
                        insertList.add(bean);
                    } else if (rowFlag == 1) {
                        // 修改
                        updateList.add(bean);
                        String pageids = "";
                        if ("zero".equals(param.getType())) {
                            //此时对象id就是pageid
                            if (StringUtils.isEmpty(param.getObjId())) {
                                pageids = bean.getPageId();
                            } else {
                                pageids = param.getObjId();
                            }
                        } else {
                            if (StringUtils.isEmpty(param.getObjId())) {
                                pageids = bean.getPageId();
                            } else {
                                pageids = infoService.getPageIdByOrg(param.getObjId(), param.getType());
                            }
                        }
                        if (StringUtils.isNotEmpty(pageids)) {
                            libService.updateShowTypeByPageId(pageids, param.getShowType(), param.getShowWay(),
                                    param.getIsExportDataSource(), param.getIsUsePersonMenu(), param.getMenuSize());
                        }
                    } else {
                        // 删除
                        deleteList.add(bean);
                    }
                }
                if (StringUtils.isNotEmpty(insertList)) {
                    bln = this.addPageInfo(insertList);
                }
                if (StringUtils.isNotEmpty(updateList)) {
                    this.saveShowType(param.getShowType(), param.getObjId());
                    bln = this.updatePageInfo(updateList);
                }
                if (StringUtils.isNotEmpty(deleteList)) {
                    bln = this.deletePageInfo(deleteList);
                }
                // 维护库表
                if (StringUtils.isNotEmpty(saveLibList)) {
                    bln = dao.insertBatch(saveLibList) > 0;
                }
                // 保存关系
                if (StringUtils.isNotEmpty(insertRelList)) {
                    bln = this.addPageRelInfo(insertRelList);
                }
            }
        }
        return bln;
    }

    /**
     * 保存显示类型
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private void saveShowType(Integer showType, String objId) {
        Where w = Where.create();
        w.eq(DiyPageLibRel::getObjId, objId);
        List<DiyPageLibRel> rellist = dao.rawQueryListByWhere(DiyPageLibRel.class, w);
        if (StringUtils.isEmpty(rellist)) {
            return;
        }
        String[] pageIdList = {""};
        if (StringUtils.isNotEmpty(rellist.get(0).getPageId())) {
            if (rellist.get(0).getPageId().contains(",")) {
                pageIdList = rellist.get(0).getPageId().split(",");
            } else {
                pageIdList[0] = rellist.get(0).getPageId();
            }
        } else {
            return;
        }
        Where where = Where.create();
        where.eq(DiyPageLib::getUsed, 1);
        where.in(DiyPageLib::getPageId, pageIdList);
        List<DiyPageLib> list = dao.rawQueryListByWhere(DiyPageLib.class, where);
        for (DiyPageLib lib : list) {
            lib.setShowType(showType);
        }
        dao.rawUpdateByIdBatch(list);
    }

    /**
     * 删除配置信息
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private Boolean deletePageInfo(List<DiyPageLibInfo> deleteList) {
        int msg = 0;
        if (StringUtils.isNotEmpty(deleteList)) {
            for (DiyPageLibInfo page : deleteList) {
                page.setTmUsed(0);
            }
            msg = dao.updateByIdBatch(deleteList);
        }
        return msg > 0;
    }

    /**
     * 修改配置信息
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private Boolean updatePageInfo(List<DiyPageLibInfo> updateList) {
        int msg = 0;
        if (StringUtils.isNotEmpty(updateList)) {
            for (DiyPageLibInfo page : updateList) {
                page.setTmUsed(1);
            }
            msg = dao.updateByIdBatch(updateList);
        }
        return msg > 0;
    }

    /**
     * 添加配置信息
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private Boolean addPageInfo(List<DiyPageLibInfo> insertList) {
        int msg = 0;
        Where where = Where.create();
        Integer max = dao.findMaxValue(DiyPageLibInfo.class, DiyPageLibInfo::getTmSort, Integer.class, where);
        if (ObjUtils.isEmpty(max)) {
            max = 0;
        }
        if (StringUtils.isNotEmpty(insertList)) {
            for (DiyPageLibInfo page : insertList) {
                page.setTmUsed(1);
                page.setTmSort(++max);
            }
            msg = dao.insertBatch(insertList);
        }
        return msg > 0;
    }

    /**
     * 添加单个关系
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Boolean addPageRelInfo(DiyPageLibRel insertRel) {
        List<DiyPageLibRel> insertRelList = new ArrayList<>();
        insertRelList.add(insertRel);
        return this.addPageRelInfo(insertRelList);
    }

    /**
     * 批量添加关系
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private Boolean addPageRelInfo(List<DiyPageLibRel> insertRelList) {
        int msg = 0;
        Where where = Where.create();
        List<DiyPageLibRel> rels = dao.rawQueryListByWhere(DiyPageLibRel.class, where);
        List<DiyPageLibRel> insertList = new ArrayList<>();
        List<DiyPageLibRel> updateList = new ArrayList<>();
        for (DiyPageLibRel diyPageLibRel : insertRelList) {
            List<DiyPageLibRel> relList = rels.stream()
                    .filter(i -> StringUtils.isNotEmpty(i.getObjId()) && i.getObjId().equals(diyPageLibRel.getObjId()))
                    .collect(Collectors.toList());
            if (StringUtils.isEmpty(relList)) {
                insertList.add(diyPageLibRel);
            } else {
                DiyPageLibRel rel = relList.get(0);
                if (StringUtils.isNotEmpty(rel.getPageId())) {
                    // 避免重复添加
                    if (!rel.getPageId().contains(diyPageLibRel.getPageId())) {
                        rel.setPageId(rel.getPageId() + "," + diyPageLibRel.getPageId());
                    }
                } else {
                    rel.setPageId(diyPageLibRel.getPageId());
                }
                updateList.add(rel);
            }
        }
        if (StringUtils.isNotEmpty(insertList)) {
            for (DiyPageLibRel page : insertList) {
                page.setId(TMUID.getUID());
            }
            msg = dao.insertBatch(insertList);
        }
        if (StringUtils.isNotEmpty(updateList)) {
            msg = dao.updateByIdBatch(updateList);
        }
        return msg > 0;
    }

    /**
     * 复制 页面详细
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Boolean copyPage(String srcId, String newPageId) {
        Boolean msg = false;
        List<DiyPageLibInfo> insertList = new ArrayList<>();
        // 复制页面详细
        Where where = Where.create();
        Integer max = dao.findMaxValue(DiyPageLibInfo.class, DiyPageLibInfo::getTmSort, Integer.class, where);
        if (ObjUtils.isEmpty(max)) {
            max = 0;
        }
        Where where1 = Where.create();
        if (srcId.contains(",")) {
            where1.in(DiyPageLibInfo::getPageId, srcId.split(","));
        } else {
            where1.eq(DiyPageLibInfo::getPageId, srcId);
        }

        where1.eq(DiyPageLibInfo::getTmUsed, 1);
        Order order = Order.create();
        order.orderByAsc(DiyPageLibInfo::getTmSort);
        //源列表
        List<DiyPageLibInfo> libInfo = dao.rawQueryListByWhere(DiyPageLibInfo.class, where1, order);
        List<String> libId = libInfo.stream().map(i -> i.getId()).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(libId)) {
            // 查询自定义布局
            for (DiyPageLibInfo lib : libInfo) {
                String id = TMUID.getUID();
                DiyPageLibInfo copyLibInfo = ObjUtils.copyTo(lib, DiyPageLibInfo.class);
                copyLibInfo.setId(id);
                copyLibInfo.setPageId(newPageId);
                copyLibInfo.setTmUsed(1);
                copyLibInfo.setTmSort(++max);
                insertList.add(copyLibInfo);
            }
        }
        if (StringUtils.isNotEmpty(insertList)) {
            msg = this.addPageInfo(insertList);
        }
        return msg;
    }

}
