package com.yunhesoft.system.tools.diy.service;

import java.util.List;

import com.yunhesoft.system.tools.diy.entity.dto.SavePageInfoDto;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageLibRel;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibInfoVo;

/**
 * <AUTHOR>
 */
public interface IDiyPageLibInfoService {
    List<DiyPageLibInfoVo> getPageLibInfoByPageIdIncludeRouter(String pageId, String type);

    /**
     * 通过pageId获取自定义页面配置数据
     *
     * @param pageId
     * @return
     */
    List<DiyPageLibInfoVo> getPageLibInfoByPageId(String pageId,String type);

    String getPageIdByOrg(String objcode, String type);

    /**
     * 保存自定义页面配置
     *
     * @param
     * @return
     * <AUTHOR>
     */

    Boolean savePageInfo(SavePageInfoDto param);

    Boolean addPageRelInfo(DiyPageLibRel insertRel);

    Boolean copyPage(String srcId, String newPageId);
}
