package com.yunhesoft.system.tools.diy.entity.po;


import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Description: 分析图
 * <AUTHOR>
 * @date 2022/10/22
 */
@ApiModel(value = "分析图实体")
@Data
@Entity
@Table(name = "DIY_PAGE_ANALYSIS_DRAW")
public class DiyPageAnalysisDraw extends BaseEntity {
	
	@ApiModelProperty(value = "模块id")
    @Column(name = "MODULE_ID", length = 255)
    private String moduleId;
	
	@ApiModelProperty(value = "模块名称")
    @Column(name = "MODULE_NAME", length = 255)
    private String moduleName;
	
    @ApiModelProperty(value = "数据源")
    @Column(name = "TDSALIAS", length = 255)
    private String tdsAlias;
    
    @ApiModelProperty(value = "数据源名称")
    @Column(name = "TDSNAME", length = 255)
    private String tdsName;
    
    @ApiModelProperty(value = "分析图类型id")
    @Column(name = "TYPE_ID", length = 255)
    private String typeId;
    
    @ApiModelProperty(value = "分析图类型名称")
    @Column(name = "TYPE_NAME", length = 255)
    private String typeName;
    
    @ApiModelProperty(value = "分析图组id")
    @Column(name = "CLASSID", length = 255)
    private String classId;
    
    @ApiModelProperty(value = "分析图组名称")
    @Column(name = "CLASSNAME", length = 255)
    private String className;

    @ApiModelProperty(value = "x轴")
    @Column(name = "XAXIS", length = 255)
    private String xAxis;

    @ApiModelProperty(value = "Y轴")
    @Column(name = "YAXIS", length = 255)
    private String yAxis;

    @ApiModelProperty(value = "输入参数")
    @Column(name = "TDSINPARA", length = 2000)
    private String tdsInpara;

    @ApiModelProperty(value = "是否为多序列")
    @Column(name = "ISMULTISERIAL")
    private Integer isMultiSerial;
    
    @ApiModelProperty(value = "序列字段")
    @Column(name = "SERIES_FIELD", length = 500)
    private String seriesField;
    
    @ApiModelProperty(value = "上限值")
    @Column(name = "UPPER_LIMIT")
    private double upperLimit;
    
    @ApiModelProperty(value = "下限值")
    @Column(name = "LOWER_LIMIT")
    private double lowerLimit;

    @ApiModelProperty(value = "标题")
    @Column(name = "TITLE", length = 100)
    private String title;

    @ApiModelProperty(value = "更多参数")
    @Column(name = "MORE_PARAM", length = 2000)
    private String moreParam;
    
    @ApiModelProperty(value = "追溯脚本")
    @Column(name = "SCRIPT", length = 2000)
    private String script;
    
    /** 排序 */
	@Column(name = "TMSORT")
	private Integer tmsort;

	/** 是否启用 1：使用 0：禁用*/
	@Column(name = "TMUSED")
	private Integer tmused;
}
