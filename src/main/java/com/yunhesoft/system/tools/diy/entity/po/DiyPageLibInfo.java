package com.yunhesoft.system.tools.diy.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/29 10:17
 * @description：自定义页面配置库po类
 * @modified By：
 */
@Entity
@Table(name = "DIY_PAGE_LIB_INFO")
@Data
@ApiModel(description = "自定义页面配置库")
public class DiyPageLibInfo extends BaseEntity {

    /**页面ID*/
    @Column(name = "PAGEID",length = 100)
    @ApiModelProperty(value = "页面ID")
    private String pageId;

    /**标题*/
    @Column(name = "TITLE",length = 200)
    @ApiModelProperty(value = "标题")
    private String title;
    /**参数信息
     * {"tdsAlias":"ACT_BUSS_USER_T_FYXM_HZ","tdsInParaAlias":"XMLB=检维修费用","tdsPage":1,"tdsPageSize":100,"tdsShowQueryBar":true,"showAdd":false,"showDel":false,"showSave":false,"saveNullMsg":false,"height":550}
     */
    @Lob
    @Column(name = "COMMPARAMS")
    private String commParams;

    /**数据源别名*/
    @ApiModelProperty(value = "数据源别名")
    @Column(name = "TDSALIAS",length = 255)
    private String tdsAlias;
    
    /**数据源名称*/ 
    @ApiModelProperty(value = "数据源名称")
    @Column(name = "TDSNAME",length = 255)
    private String tdsName;
    
    /**栅格*/
    @ApiModelProperty(value = "栅格")
    @Column(name = "GIRD", length = 100)
    private String gird;
    
    /**组件名称 组件名称*/
    @ApiModelProperty(value = "标题")
    @Column(name = "COMMNAME",length = 255)
    private String commName;
    /**组件路径 组件路径*/
    @ApiModelProperty(value = "组件路径")
    @Column(name = "COMMPATH",length = 255)
    private String commPath;

    /**排序*/
    @Column(name = "TMSORT")
    @ApiModelProperty(value = "排序")
    private  Integer tmSort;

    /**是否启用*/
    @Column(name = "TMUSED")
    @ApiModelProperty(value = "是否启用",notes = "1:启用 0:禁用")
    private  Integer tmUsed;

    /**显隐控制*/
    @Column(name = "TMSHOW")
    @ApiModelProperty(value = "显隐控制",notes = "1:显示 0:隐藏")
    private  Integer tmShow;

    @Column(name = "STYLE")
    @ApiModelProperty(value = "页面样式类型 0选项卡 1手风琴 2栅格 3自定义")
    private  Integer style;

    @Column(name = "SHOWWAY")
    @ApiModelProperty(value = "检索条件显示类型 0常规 1顶部悬浮 2左侧悬浮 3右侧悬浮")
    private  Integer showWay;
    @Column(name = "ISEXPORTDATASOURCE")
    @ApiModelProperty(value = "是否可以导出数据源")
    private  Integer isExportDataSource;

    @Column(name = "CHARTPARAM",length = 3000)
    @ApiModelProperty(value = "分析图参数")
    private  String chartParam;

    @Column(name = "PAGEJSON",length = 4000)
    @ApiModelProperty(value = "布局设置")
    private  String pageJson;

    @Column(name = "MODULECODE",length = 3000)
    @ApiModelProperty(value = "模块编码")
    private  String moduleCode;

    @Column(name = "DATAID",length = 3000)
    @ApiModelProperty(value = "数据id")
    private  String dataId;
}
