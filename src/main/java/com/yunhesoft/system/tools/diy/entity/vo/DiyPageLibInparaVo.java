package com.yunhesoft.system.tools.diy.entity.vo;

import com.yunhesoft.system.tools.diy.entity.po.DiyPageLibInpara;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 自定义页面参数显示页面$
 * @date 2022/3/29
 */
@Data
public class DiyPageLibInparaVo extends DiyPageLibInpara {
    /**
     * 操作标识
     */
    private Integer rowFlag;
    private List<DiyPageLibInparaVo> children;
    private Object store;
}
