package com.yunhesoft.system.tools.diy.service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.system.tds.entity.vo.TdsInParaRelTreeVo;
import com.yunhesoft.system.tools.diy.entity.dto.PageInparaQueryChangeDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageLibInparaDto;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibInparaVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 自定义页面输入参数 接口$
 * @date 2022/3/29
 */
public interface IDiyPageLibInparaService {
    /**
     * 查询自定义页面输入参数
     * <AUTHOR>
     * @param
     * @return
     */
    List<DiyPageLibInparaVo> getPageLibInpara(String pageId,String type);

    Boolean updateDiyPageLibInpara(String pageId, String tdsAlias);

    /**
     * 保存自定义页面输入参数
     * <AUTHOR>
     * @param
     * @return
     */
    Boolean savePageLibInpara(SavePageLibInparaDto param);

    /**
     * 复制数据源  输入参数
     *
     * @param
     * @return
     * <AUTHOR>
     */
    Boolean copyDataSourceInpara(String alias, String pageId);

    Boolean syncInpara(String pageId, String type);

    List<TdsInParaRelTreeVo> getDiyInParaRelTreeData(String pageId, String type);

    List<TdsInParaRelTreeVo> saveDiyInParaRelTreeData(String pageId, String type,TdsInParaRelTreeVo node);

    JSONObject queryChange(PageInparaQueryChangeDto params);
}
