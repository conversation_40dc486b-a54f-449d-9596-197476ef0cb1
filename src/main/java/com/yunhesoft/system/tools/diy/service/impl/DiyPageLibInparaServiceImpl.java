package com.yunhesoft.system.tools.diy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.entity.po.TdsoutPara;
import com.yunhesoft.system.tds.entity.vo.TdsInParaRelTreeVo;
import com.yunhesoft.system.tds.entity.vo.TdsInParaRelVo;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.model.TInPara;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tools.diy.entity.dto.PageInparaQueryChangeDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageLibInparaDto;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageLibInpara;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibInfoVo;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibInparaVo;
import com.yunhesoft.system.tools.diy.service.IDiyPageLibInfoService;
import com.yunhesoft.system.tools.diy.service.IDiyPageLibInparaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 自定义页面输入参数实现类$
 * @date 2022/3/29
 */
@Service
public class DiyPageLibInparaServiceImpl implements IDiyPageLibInparaService {

    @Autowired
    private EntityService dao;
    @Autowired
    private IDataSourceService tdsServ; // 数据源服务
    @Autowired
    private IDiyPageLibInfoService diyPageLibSetService;

    @Override
    public List<DiyPageLibInparaVo> getPageLibInpara(String pageId, String type) {
        Where where = Where.create();
        where.eq(DiyPageLibInpara::getPageId, pageId);
        where.eq(DiyPageLibInpara::getUsed, 1);
        List<DiyPageLibInpara> list = dao.rawQueryListByWhere(DiyPageLibInpara.class, where);
        List<DiyPageLibInparaVo> listVo = new ArrayList<>();
        List<DiyPageLibInparaVo> finalListVo = listVo;
        if(StringUtils.isNotEmpty(list)) {
            list.forEach(i -> {
                DiyPageLibInparaVo bean = ObjUtils.copyTo(i, DiyPageLibInparaVo.class);
                finalListVo.add(bean);
            });
        }
        if (StringUtils.isEmpty(listVo) && StringUtils.isNotEmpty(type)) {
            //执行初始化
            listVo = this.initInpara(pageId, type);
        }
        //解析数据源公式
        if (StringUtils.isNotEmpty(listVo)) {
            for (DiyPageLibInparaVo diyPageLibInparaVo : listVo) {
                if (StringUtils.isNotEmpty(diyPageLibInparaVo.getDefaultKeyScript())) {
                    if (diyPageLibInparaVo.getDefaultKeyScript().contains("$")) {
                        //数据源表达式
                        LinkedHashMap<String, IDataSource> idsMap = new LinkedHashMap<>();
                        TDataSourceManager script = new TDataSourceManager();
                        String scripttext = diyPageLibInparaVo.getDefaultKeyScript();
                        String scripttext1 = diyPageLibInparaVo.getDefaultValueScript();
                        List<?> obj = (List<?>) script.getScriptValue(scripttext, null, idsMap);
                        List<?> obj1 = (List<?>) script.getScriptValue(scripttext1, null, idsMap);
                        if (StringUtils.isNotEmpty(obj1) && StringUtils.isNotEmpty(obj)) {
                            JSONArray array = new JSONArray();
                            for (int i = 0; i < obj.size(); i++) {
                                JSONObject object = new JSONObject();
                                object.put("key", String.valueOf(obj.get(i)));
                                object.put("value", String.valueOf(obj1.get(i)));
                                array.add(object);
                            }
                            diyPageLibInparaVo.setStore(array);
                        }
                    }
                }
            }
        }
        if(StringUtils.isNotEmpty(listVo)) {
            listVo = listVo.stream()
                    .filter(i-> i.getTmSort()!=null)
                    .sorted(Comparator.comparing(DiyPageLibInparaVo::getTmSort)).collect(Collectors.toList());
        }
        return listVo;
    }

    List<DiyPageLibInparaVo> initInpara(String pageId, String type) {
        List<DiyPageLibInparaVo> inparaVos = new ArrayList<>();
        List<DiyPageLibInfoVo> pageInfos = diyPageLibSetService.getPageLibInfoByPageIdIncludeRouter(pageId, type);
        if (StringUtils.isNotEmpty(pageInfos)) {
            //查看全部数据源的页面
            pageInfos = pageInfos.stream().filter(i -> StringUtils.isNotEmpty(i.getTdsAlias())).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(pageInfos)) {
                List<String> tdsAlias = pageInfos.stream().map(i -> i.getTdsAlias()).distinct().collect(Collectors.toList());
                String tdsAliasStr = StringUtils.join(tdsAlias, ",");
                List<TdsinPara> mutiTDSData = tdsServ.getTDSInParaMulti(tdsAliasStr);
                //通过构造treeset 达到去重目的 输入参数去重
                mutiTDSData = mutiTDSData.stream().distinct()
                        .collect(Collectors.collectingAndThen(
                                        Collectors.toCollection(
                                                () -> new TreeSet<>(Comparator.comparing(TdsinPara::getParaAlias))
                                        ), ArrayList::new
                                )
                        );
                if (mutiTDSData != null) {
                    List<DiyPageLibInpara> insertList = new ArrayList<>();
                    for (TdsinPara mutiTDSDatum : mutiTDSData) {
                        DiyPageLibInpara bean = ObjUtils.copyTo(mutiTDSDatum, DiyPageLibInpara.class);
                        bean.setPageId(pageId);
                        DiyPageLibInparaVo beanVo = ObjUtils.copyTo(bean, DiyPageLibInparaVo.class);
                        inparaVos.add(beanVo);
                        insertList.add(bean);
                    }
                    if (StringUtils.isNotEmpty(insertList)) {
                        this.addPageInpara(insertList);
                    }
                }
            }
        }
        return inparaVos;
    }

    /**
     * 数据源输入参数维护，  当数据源发生改变的时候输入参数可能会有变化
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Boolean updateDiyPageLibInpara(String pageId, String tdsAlias) {
        Boolean bln = false;
        if (StringUtils.isNotEmpty(tdsAlias)) {
            List<TdsinPara> mutiTDSData = tdsServ.getTDSInParaMulti(tdsAlias);
            if (StringUtils.isNotEmpty(mutiTDSData)) {
                List<DiyPageLibInparaVo> pageLibInpara = getPageLibInpara(pageId, null);
                if (StringUtils.isNotEmpty(pageLibInpara)) {
                    List<String> inparas = pageLibInpara.stream().map(i -> i.getParaAlias()).collect(Collectors.toList());
                    mutiTDSData = mutiTDSData.stream()
                            .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TdsinPara::getParaAlias))), ArrayList::new));
                    List<DiyPageLibInpara> insertList = new ArrayList<>();
                    for (TdsinPara mutiTDSDatum : mutiTDSData) {
                        if (!inparas.contains(mutiTDSDatum.getParaAlias())) {
                            DiyPageLibInpara bean = ObjUtils.copyTo(mutiTDSDatum, DiyPageLibInpara.class);
                            bean.setPageId(pageId);
                            insertList.add(bean);
                        }
                    }
                    if (StringUtils.isNotEmpty(insertList)) {
                        bln = this.addPageInpara(insertList);
                    }
                }
            }
        }
        return bln;
    }

    /**
     * 保存自定义页面输入参数
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public Boolean savePageLibInpara(SavePageLibInparaDto param) {
        Boolean bln = false;
        if (ObjUtils.notEmpty(param)) {
            if (StringUtils.isNotEmpty(param.getData())) {
                List<DiyPageLibInpara> insertList = new ArrayList<>();
                List<DiyPageLibInpara> updateList = new ArrayList<>();
                List<DiyPageLibInpara> deleteList = new ArrayList<>();
                for (DiyPageLibInparaVo inparaVo : param.getData()) {
                    Integer rowFlag = inparaVo.getRowFlag();
                    DiyPageLibInpara bean = ObjUtils.copyTo(inparaVo, DiyPageLibInpara.class);
                    if (rowFlag == null || rowFlag == 0) {
                        //增加
                        insertList.add(bean);
                    } else if (rowFlag == 1) {
                        //修改
                        updateList.add(bean);
                    } else {
                        //删除
                        deleteList.add(bean);
                    }
                }
                if (StringUtils.isNotEmpty(insertList)) {
                    bln = this.addPageInpara(insertList);
                }
                if (StringUtils.isNotEmpty(updateList)) {
                    bln = this.updatePageInpara(updateList);
                }
                if (StringUtils.isNotEmpty(deleteList)) {
                    bln = this.deletePageInpara(deleteList);
                }
            }
        }
        return bln;
    }

    /**
     * 删除
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private Boolean deletePageInpara(List<DiyPageLibInpara> deleteList) {
        int msg = 0;
        if (StringUtils.isNotEmpty(deleteList)) {
            for (DiyPageLibInpara page : deleteList) {
                page.setUsed(0);
            }
            msg = dao.updateByIdBatch(deleteList);
        }
        return msg > 0;
    }

    /**
     * 修改
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private Boolean updatePageInpara(List<DiyPageLibInpara> updateList) {
        int msg = 0;
        if (StringUtils.isNotEmpty(updateList)) {
            for (DiyPageLibInpara page : updateList) {
                page.setUsed(1);
            }
            msg = dao.updateByIdBatch(updateList);
        }
        return msg > 0;
    }

    /**
     * 添加
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private Boolean addPageInpara(List<DiyPageLibInpara> insertList) {
        int msg = 0;
        if (StringUtils.isNotEmpty(insertList)) {
            int i = dao.findMaxId(DiyPageLibInpara.class, DiyPageLibInpara::getTmSort).intValue();
            //数据库中存在
            Where where = Where.create();
            where.eq(DiyPageLibInpara::getUsed,1);
            where.eq(DiyPageLibInpara::getPageId,insertList.get(0).getPageId());
            List<DiyPageLibInpara> diyPageLibInparas = dao.queryList(DiyPageLibInpara.class, where);
            List<String> collect = diyPageLibInparas.stream().map(item -> item.getParaAlias()).collect(Collectors.toList());
            Iterator<DiyPageLibInpara> iterator = insertList.iterator();
            while (iterator.hasNext()){
//            for (DiyPageLibInpara page : insertList) {
                DiyPageLibInpara page = iterator.next();
                if(collect.contains(page.getParaAlias())){
                    //数据库中已经有了
                    iterator.remove();
                    continue;
                }
                page.setId(TMUID.getUID());
                page.setUsed(1);
                page.setTmSort(i++);
            }
            msg = dao.insertBatch(insertList);
        }
        return msg > 0;
    }

    /**
     * 复制数据源 输入参数
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public Boolean copyDataSourceInpara(String alias, String pageId) {
        Boolean bln = false;
        List<DiyPageLibInpara> list = new ArrayList<>();
        if (alias == null || pageId == null || "".equals(alias) || "".equals(pageId)) {
            return false;
        }
        Where where = Where.create();
        Order order = Order.create();
        // 获取绑定表数据
        where.eq(TdsinPara::getTdsalias, alias);
        order.orderByAsc(TdsinPara::getParaId);
        List<TdsinPara> listb = dao.queryList(TdsinPara.class, where, order);
        if (listb != null && listb.size() > 0) {
            for (TdsinPara tds : listb) {
                DiyPageLibInpara bean = new DiyPageLibInpara();
                bean = ObjUtils.copyTo(tds, DiyPageLibInpara.class);
                bean.setPageId(pageId);
                bean.setImportChoice(tds.getImportChoice());
                list.add(bean);
            }
            if (list != null && list.size() > 0) {
                bln = this.addPageInpara(list);
            }
        }
        return bln;
    }

    /**
     * 同步输入参数
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Boolean syncInpara(String pageId, String type) {
        //不删除旧数据
//        Where where = Where.create();
//        where.eq(DiyPageLibInpara::getPageId, pageId);
//        dao.rawDeleteByWhere(DiyPageLibInpara.class, where);
        List<DiyPageLibInparaVo> inparaVos = this.initInpara(pageId, type);
        if (StringUtils.isNotEmpty(inparaVos)) {
            return true;
        }
        return false;
    }

    /**
     * 获取关系树
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<TdsInParaRelTreeVo> getDiyInParaRelTreeData(String pageId, String type) {
        //没查到关系数据 显示全部参数的树形
        List<TdsInParaRelTreeVo> res = new ArrayList<>();
        List<DiyPageLibInparaVo> pageLibInpara = this.getPageLibInpara(pageId, type);
        List<TdsInParaRelTreeVo> inparaList = new ArrayList<>();
        //只查下拉框
        pageLibInpara = pageLibInpara.stream()
                .filter(i -> "combo".equals(i.getComponentType())).collect(Collectors.toList());
        for (DiyPageLibInparaVo diyPageLibInparaVo : pageLibInpara) {
            TdsInParaRelTreeVo node = new TdsInParaRelTreeVo();
            node.setPid(diyPageLibInparaVo.getPid());
            node.setId(diyPageLibInparaVo.getId());
            node.setLabel(diyPageLibInparaVo.getParaName());
            node.setComType(diyPageLibInparaVo.getComponentType());
            inparaList.add(node);
        }
        for (TdsInParaRelTreeVo diyPageLibInparaVo : inparaList) {
            if (StringUtils.isEmpty(diyPageLibInparaVo.getPid())) {
                //根节点
                TdsInParaRelTreeVo tree = this.wiredTree(inparaList, diyPageLibInparaVo);
                res.add(tree);
            }
        }
        return res;
    }

    /**
     * 装配树形
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private TdsInParaRelTreeVo wiredTree(List<TdsInParaRelTreeVo> pageLibInpara, TdsInParaRelTreeVo node) {
        List<DiyPageLibInparaVo> list = new ArrayList<>();
        //子节点
        List<TdsInParaRelTreeVo> children = pageLibInpara.stream()
                .filter(i -> StringUtils.isNotEmpty(i.getPid()) && i.getPid().equals(node.getId()))
                .collect(Collectors.toList());
        node.setChildren(children);
        for (TdsInParaRelTreeVo diyPageLibInparaVo : children) {
            this.wiredTree(pageLibInpara, diyPageLibInparaVo);
        }
        return node;
    }

    /**
     * 保存关系树
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<TdsInParaRelTreeVo> saveDiyInParaRelTreeData(String pageId, String type, TdsInParaRelTreeVo node) {
        Where where = Where.create();
        where.eq(DiyPageLibInpara::getId, node.getId());
        List<DiyPageLibInpara> inparaVos = dao.queryList(DiyPageLibInpara.class, where);
        int flag = 0;
        if (StringUtils.isNotEmpty(inparaVos)) {
            DiyPageLibInpara inpara = inparaVos.get(0);
            inpara.setPid(node.getPid());
            flag = dao.updateById(inpara);
        }
        if (flag > 0) {
            //查询关系
            return this.getDiyInParaRelTreeData(pageId, type);
        }
        return null;
    }

    /**
     * 检索条件切换联动功能
     *
     * @param params
     * @return
     */
    @Override
    public JSONObject queryChange(PageInparaQueryChangeDto params) {
        String pageId = params.getPageId();
        Where where = Where.create();
        where.eq(DiyPageLibInpara::getPageId, pageId);
        where.eq(DiyPageLibInpara::getUsed, 1);
        List<DiyPageLibInpara> allList = dao.rawQueryListByWhere(DiyPageLibInpara.class, where);
        JSONObject jobj = new JSONObject();
        LinkedHashMap<String, TdsInParaRelVo> relMap = this.getInParaRelVoMap(pageId);
        LinkedHashMap<String, IDataSource> idsMap = new LinkedHashMap<>();
        TDataSourceManager script = new TDataSourceManager();
        if (StringUtils.isNotEmpty(relMap)) {
            JSONArray queryData = JSONArray.parseArray(params.getQueryData());
            //获取修改的输入参数对象
            List<DiyPageLibInpara> list = allList.stream().filter(i -> i.getParaAlias().equals(params.getParaAlias())).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(list) && queryData != null) {
                String changeAlias = params.getParaAlias();
                Map<String, Object> valueMap = new HashMap<String, Object>();
                // 初始化参数值
                for (int i = 0; i < queryData.size(); i++) {
                    JSONObject obj = queryData.getJSONObject(i);
                    String name = obj.getString("name");
                    String val = obj.getString("value");
                    valueMap.put(name, val);
                }
                valueMap.put(changeAlias, params.getValue());
                for (String alias : relMap.keySet()) {
                    TdsInParaRelVo vo = relMap.get(alias);
                    if (vo.getId().equals(changeAlias)) {
                        continue;
                    }
                    if (vo.getPidList().contains(changeAlias)) {
                        // 被切换对象
                        DiyPageLibInpara ip = allList.stream().filter(i -> i.getParaAlias().equals(alias)).collect(Collectors.toList()).get(0);
                        if (ip != null) {
                            //计算新的后代联动默认值
                            //获取数据源别名
                            String scripttext = ip.getDefaultKeyScript();
                            String scripttext2 = ip.getDefaultValueScript();
                            Pattern pattern = Pattern.compile("\\$(.*?)\\.");
                            Pattern pattern1 = Pattern.compile("\\.getColValues\\(\\\"(.*?)\\\"\\)");
                            Matcher matcher = pattern.matcher(scripttext);
                            String var = "";
                            String paraAlias = "";
                            String paraName = "";
                            while (matcher.find()) {
                                var = matcher.group(1);
                                if (StringUtils.isNotEmpty(var)) {
                                    break;
                                }
                            }
                            Matcher matcher1 = pattern1.matcher(scripttext);
                            while (matcher1.find()) {
                                paraAlias = matcher1.group(1);
                                if (StringUtils.isNotEmpty(paraAlias)) {
                                    break;
                                }
                            }
                            Matcher matcher2 = pattern1.matcher(scripttext2);
                            while (matcher2.find()) {
                                paraName = matcher2.group(1);
                                if (StringUtils.isNotEmpty(paraName)) {
                                    break;
                                }
                            }

                            //获取数据源输入参数
                            TdsQueryDto param = new TdsQueryDto();
                            param.setTdsAlias(var);
                            HashMap<String, Object> tdsQuery = tdsServ.getTDSQuery(param);
                            List<TdsoutPara> tdsOutPara = tdsServ.getTDSOutPara(var);
                            Map<String,String> outMap = new HashMap<>();
                            for (TdsoutPara tdsoutPara : tdsOutPara) {
                                outMap.put(tdsoutPara.getParaName(),tdsoutPara.getParaAlias());
                            }
                            JSONArray inpara = new JSONArray();
                            if (ObjUtils.notEmpty(tdsQuery)) {
                                //所有的输入参数
                                ArrayList o = (ArrayList) tdsQuery.get("data");
                                //根据查询的输入参数拼装数据源查询条件
                                for (int i = 0; i < o.size(); i++) {
                                    Map<String, Object> p = (Map<String, Object>) o.get(i);
                                    if (params.getParaAlias().equals(p.get("name"))) {
                                        JSONObject object = new JSONObject();
                                        object.put("name", p.get("name"));
                                        object.put("value", params.getValue());
                                        inpara.add(object);
                                    }
                                }
                            }
                            if (inpara == null) {
                                continue;
                            }
                            param.setInParaAlias(inpara.toString());
                            JSONArray tdsData = tdsServ.getTDSData(param);
                            if (ObjUtils.notEmpty(tdsData)) {
                                JSONObject td = (JSONObject) tdsData.get(0);
                                //设置默认值 以及下拉框内容
                                JSONObject vobj = new JSONObject();
                                JSONArray data = td.getJSONArray("data");
                                if (data != null && data.size()>0) {
                                    JSONObject dataObject = (JSONObject) data.get(0);
                                    vobj.put("value", dataObject == null ? "" : dataObject.getString(paraAlias));
                                    JSONArray store = new JSONArray();
                                    for (Object datum : data) {
                                        JSONObject parse = JSON.parseObject(datum.toString());
                                        JSONObject item = new JSONObject();
                                        item.put("key",parse.getString(paraAlias));
                                        String s = outMap.get(paraName);
                                        if(StringUtils.isEmpty(s)){
                                            s = item.getString("key");
                                        }
                                        item.put("value",parse.getString(s));
                                        store.add(item);
                                    }
                                    vobj.put("datas", store);
                                    jobj.put(ip.getParaAlias(), vobj);
                                }else{
                                    //无内容
                                    vobj.put("value", "");
                                    JSONArray store = new JSONArray();
                                    vobj.put("datas", store);
                                    jobj.put(ip.getParaAlias(), vobj);
                                }
                            }
                        }
                    }
                }
            }
        }
        return jobj;
    }

    /**
     * 获取联动关系
     *
     * @return
     * <AUTHOR>
     * @params
     */

    public LinkedHashMap<String, TdsInParaRelVo> getInParaRelVoMap(String pageId) {
        LinkedHashMap<String, TdsInParaRelVo> map = new LinkedHashMap<>();
        //获取联动数据
        Where where = Where.create();
        where.eq(DiyPageLibInpara::getPageId, pageId);
        List<DiyPageLibInpara> inparaVos = dao.queryList(DiyPageLibInpara.class, where);
        List<TdsInParaRelVo> voList = new ArrayList<TdsInParaRelVo>();
        int i = 1;
        for (DiyPageLibInpara inparaVo : inparaVos) {
            TdsInParaRelVo node = new TdsInParaRelVo();
            node.setPid(inparaVo.getPid());
            node.setId(inparaVo.getParaAlias());
            //排序
            node.setSort(i++);
            //父节点 和父节点数量
            List<String> parentNodes = getParentNodes(inparaVo, inparaVos);
            node.setPidList(parentNodes);
            node.setLevel(parentNodes.size() + 1);
            voList.add(node);
        }
        // 按照序号排序
        voList = voList.stream().sorted(Comparator.comparing(TdsInParaRelVo::getSort)).collect(Collectors.toList());
        for (TdsInParaRelVo e : voList) {
            map.put(e.getId(), e);
        }
        return map;
    }

    /**
     * 获取父节点
     *
     * @return
     * <AUTHOR>
     * @params
     */
    List<String> getParentNodes(DiyPageLibInpara node, List<DiyPageLibInpara> inparaVos) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(node.getPid())) {
            //有父节点
            DiyPageLibInpara pnode = inparaVos.stream()
                    .filter(i -> i.getId().equals(node.getPid())).collect(Collectors.toList()).get(0);
            list.add(pnode.getParaAlias());
            this.getParentNodes(pnode, inparaVos);
        }
        return list;
    }

}

