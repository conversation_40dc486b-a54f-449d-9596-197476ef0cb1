package com.yunhesoft.system.tools.diy.service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.org.entity.vo.SysOrgTreeData;
import com.yunhesoft.system.tds.entity.dto.TdsExportDto;
import com.yunhesoft.system.tools.diy.entity.dto.PageParamDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageDto;
import com.yunhesoft.system.tools.diy.entity.dto.TemplateParamDto;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibInfoVo;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 自定义页面配置$
 * @date 2022/3/17
 */
public interface IDiyPageLibService {

    /**
     * 查询页面   带分页
     *
     * @param
     * @return
     * <AUTHOR>
     */
    List<DiyPageLibVo> getPage(PageParamDto paramDto, Pagination<?> page);

    /**
     * 保存页面配置
     *
     * @param
     * @return
     * <AUTHOR>
     */
    Boolean savePages(SavePageDto param);

    void operTemplate(TemplateParamDto param);

    JSONObject redirectPage(String reqPageId,String type);

    Boolean deleteRelationByReq(String reqId);

    List<DiyPageLibInfoVo> extendsPageById(String srcOrgId, String targetOrgId, String type, Integer extendType);

    List<DiyPageLibInfoVo> extendsPageBySuper(SysOrgTreeData param,String type,Integer extendType);

    List<DiyPageLibVo> getTemPlateByType(String type);

    void updateShowTypeByPageId(String pageId, Integer showType, Integer showWay, Integer isExportDataSource, Integer isUsePersonMenu,String menuSize);

    JSONObject getShowTypeByOrg(String objId, String type);

    JSONObject getExtendsByOrg(String objId, String type);

    Integer getOrignType(String objId, String type);

    JSONObject getCurrentUserPages();

    void exportTds(@RequestBody List<TdsExportDto> params);
}
