package com.yunhesoft.system.tools.diy.service.impl;


import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.diy.entity.dto.PageAnalysisDrawDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageAnalysisDrawDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageAnalysisDrawTypeDto;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageAnalysisDraw;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageAnalysisDrawType;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageAnalysisDrawTypeVo;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageAnalysisDrawVo;
import com.yunhesoft.system.tools.diy.service.IDiyPageAnalysisDrawService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 分析图实现类
 * @date 2022/10/22
 */
@Service
public class DiyPageAnalysisDrawServicImpl implements IDiyPageAnalysisDrawService {

    @Autowired
    private EntityService dao;

    /**
     * 查询分析图
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<DiyPageAnalysisDraw> getAnalysisDraw(PageAnalysisDrawDto param) {
        Where where = Where.create();
        if(StringUtils.isNotEmpty(param.getId())) {
            where.eq(DiyPageAnalysisDraw::getId, param.getId());
        }else {
        	where.eq(DiyPageAnalysisDraw::getTmused, 1);
        }
        return dao.rawQueryListByWhere(DiyPageAnalysisDraw.class, where);
    }

    /**
     * 增加
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private Boolean addAnalysisDraw(List<DiyPageAnalysisDraw> insertList) {
        int msg = 0;
        msg = dao.insertBatch(insertList);
        return msg > 0;
    }

    /**
     * 删除
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private Boolean delAnalysisDraw(List<DiyPageAnalysisDraw> deleteList) {
        int msg = 0;
        msg = dao.deleteByIdBatch(deleteList);
        return msg > 0;
    }

    /**
     * 修改
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private Boolean updateAnalysisDraw(List<DiyPageAnalysisDraw> updateList) {
        int msg = 0;
        msg = dao.updateByIdBatch(updateList);
        return msg > 0;
    }

    /**
     * 保存分析图
     *
     * @param param
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Boolean saveAnalysisDraw(SavePageAnalysisDrawDto param) {
        Boolean bln = false;
        if (ObjUtils.notEmpty(param)) {
            if (StringUtils.isNotEmpty(param.getData())) {
                List<DiyPageAnalysisDraw> insertList = new ArrayList<>();
                List<DiyPageAnalysisDraw> updateList = new ArrayList<>();
                List<DiyPageAnalysisDraw> deleteList = new ArrayList<>();
                for (int i = 0; i < param.getData().size(); i++) {
                    DiyPageAnalysisDrawVo beanVo = param.getData().get(i);
                    DiyPageAnalysisDraw bean = ObjUtils.copyTo(beanVo, DiyPageAnalysisDraw.class);
                    Integer rowFlag = beanVo.getRowFlag();
                    if (rowFlag == null || rowFlag == 0) {
                        //增加
                        bean.setId(TMUID.getUID());
                        insertList.add(bean);
                    } else if (rowFlag == 1) {
                        //修改
                        updateList.add(bean);
                    } else {
                        // 删除
                        deleteList.add(bean);
                    }
                }
                if (StringUtils.isNotEmpty(insertList)) {
                    bln = this.addAnalysisDraw(insertList);
                }
                if (StringUtils.isNotEmpty(updateList)) {
                    bln = this.updateAnalysisDraw(updateList);
                }
                if (StringUtils.isNotEmpty(deleteList)) {
                    bln = this.delAnalysisDraw(deleteList);
                }
            }
        }
        return false;
    }
    
    /**
     * 查询分析图分类
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<DiyPageAnalysisDrawType> getAnalysisDrawType(String classId) {
        Where where = Where.create();
        where.eq(DiyPageAnalysisDraw::getTmused, 1);
        if(StringUtils.isNotEmpty(classId)&&!"all".equals(classId)) {
            where.eq(DiyPageAnalysisDrawType::getClassId, classId);
        }
        return dao.rawQueryListByWhere(DiyPageAnalysisDrawType.class, where);
    }
    
    /**
     * 保存分析图
     *
     * @param param
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Boolean saveAnalysisDrawType(SavePageAnalysisDrawTypeDto param) {
        Boolean bln = false;
        if (ObjUtils.notEmpty(param)) {
            if (StringUtils.isNotEmpty(param.getData())) {
                List<DiyPageAnalysisDrawType> insertList = new ArrayList<>();
                List<DiyPageAnalysisDrawType> updateList = new ArrayList<>();
                List<DiyPageAnalysisDrawType> deleteList = new ArrayList<>();
                
                List<DiyPageAnalysisDraw> insert2List = new ArrayList<>();
                for (int i = 0; i < param.getData().size(); i++) {
                    DiyPageAnalysisDrawTypeVo beanVo = param.getData().get(i);
                    DiyPageAnalysisDrawType bean = ObjUtils.copyTo(beanVo, DiyPageAnalysisDrawType.class);
                    Integer rowFlag = beanVo.getRowFlag();
                    if (rowFlag == null || rowFlag == 0) {
                        //增加
                        bean.setId(TMUID.getUID());
                        
                        DiyPageAnalysisDraw info = new DiyPageAnalysisDraw();
                        info.setId(TMUID.getUID());
                        info.setTmused(2);
                        info.setTitle(bean.getName()+"-示例");
                        info.setTypeId(bean.getId());
                        info.setTypeName(bean.getName());
                        info.setClassId(bean.getClassId());
                        info.setClassName(bean.getClassName());
                        info.setCreateBy(bean.getCreateBy());
                        info.setCreateTime(bean.getCreateTime());
                        insert2List.add(info);
                        
                        bean.setSlId(info.getId());
                        insertList.add(bean);
                    } else if (rowFlag == 1) {
                        //修改
                        updateList.add(bean);
                    } else {
                        // 删除
                        deleteList.add(bean);
                    }
                }
                if (StringUtils.isNotEmpty(insertList)) {
                    bln = dao.insertBatch(insertList)>0;
                    if(bln) {
                    	dao.insertBatch(insert2List);
                    }
                }
                if (StringUtils.isNotEmpty(updateList)) {
                    bln = dao.updateByIdBatch(updateList)>0;
                }
                if (StringUtils.isNotEmpty(deleteList)) {
                    bln = dao.deleteByIdBatch(deleteList)>0;
                }
            }
        }
        return false;
    }
}
