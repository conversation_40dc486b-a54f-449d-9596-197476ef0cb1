package com.yunhesoft.system.tools.diy.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Description: 自定义页面输入参数$
 * @date 2022/3/29
 */
@ApiModel(value = "自定义页面输入参数$")
@Entity
@Data
@Table(name = "DIY_PAGE_LIB_INPARA")
public class DiyPageLibInpara extends BaseEntity {

    @ApiModelProperty(value = "用于构建关系树形")
    @Column(name = "PID", length=100)
    private String pid;
    @ApiModelProperty(value = "页面id")
    @Column(name = "PAGEID", length=100)
    private String pageId;
    // 输入参数别名
    @Column(name = "PARAALIAS", length=50)
    private String paraAlias;
    // 显示顺序
    @Column(name = "PARAID")
    private Integer paraId;
    // 参数名称
    @Column(name = "PARANAME", length=50)
    private String paraName;
    // 数据类型（TM4目前未使用，默认值：tdsString）
    @Column(name = "DATATYPE", length=50)
    private String dataType;
    // 下拉框填充key值脚本
    @Column(name = "DEFAULTKEYSCRIPT", length=1000)
    private String defaultKeyScript;
    // 下拉框添加显示值脚本
    @Column(name = "DEFAULTVALUESCRIPT", length=1000)
    private String defaultValueScript;
    // 组件类型
    @Column(name = "COMPONENTTYPE", length=50)
    private String componentType;
    // 是否显示 1：显示；0：隐藏
    @Column(name = "DISPLAY")
    private Integer display;

    @Column(name = "USED")
    private Integer used;

    // 备注
    @Column(name = "MEMO")
    private String memo;
    // 下拉框是否支持模糊检索（TM4未使用）
    @Column(name = "ISCANQUERY")
    private Integer iscanquery;
    // 导入选项，常规列1，关键列2，不导入3（TM4未使用）
    @Column(name = "IMPORTCHOICE")
    private Integer importChoice;
    // 输入是否代入录入项 for 数据源编辑
    @Column(name = "INSERTEDIT")
    private Integer insertEdit;
    // 控件显示宽度
    @Column(name = "WIDTH")
    private Integer width;
    // 初始化默认值脚本（TM4预留，未使用）
    @Column(name = "INITVALUESCRIPT", length=1000)
    private String initValueScript;
    // 组件配置
    @Column(name = "COMPARAMS", length = 255)
    private String comParams;
    @Column(name = "TMSORT", length = 255)
    private Integer tmSort;
}
