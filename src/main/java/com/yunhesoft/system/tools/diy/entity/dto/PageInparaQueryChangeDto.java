package com.yunhesoft.system.tools.diy.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 自定义页面下拉改变参数
 * <AUTHOR>
 * @date 2023/3/21
 */
@Data
public class PageInparaQueryChangeDto {
    /** 数据源别名 */
    private String pageId;

    /** 切换的项目 */
    private String paraAlias;

    /** 切换值 */
    private String value;

    /** 检索条件值 */
    private String queryData;
}
