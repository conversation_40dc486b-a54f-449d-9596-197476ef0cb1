package com.yunhesoft.system.tools.diy.controller;


import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.diy.entity.dto.PageAnalysisDrawDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageAnalysisDrawDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageAnalysisDrawTypeDto;
import com.yunhesoft.system.tools.diy.service.IDiyPageAnalysisDrawService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 分析图控制
 * <AUTHOR>
 * @date 2022/10/22
 */
@RestController
@RequestMapping("/diyPageAnalysis")
@Api(tags = "分析图管理")
public class DiyPageAnalysisDrawController extends BaseRestController {

    @Autowired
    private IDiyPageAnalysisDrawService drawService;

    @ApiOperation("查询分析图")
    @RequestMapping(value = "/getAnalysisDraw", method = RequestMethod.POST)
    public Res<?>getAnalysisDraw(@RequestBody PageAnalysisDrawDto param) {
        return Res.OK(drawService.getAnalysisDraw(param));
    }
    @ApiOperation("保存分析图")
    @RequestMapping(value = "/saveAnalysisDraw", method = RequestMethod.POST)
    public Res<?>saveAnalysisDraw(@RequestBody SavePageAnalysisDrawDto param) {
        return Res.OK(drawService.saveAnalysisDraw(param));
    }
    @ApiOperation("查询分析图分类")
    @RequestMapping(value = "/getAnalysisDrawType", method = RequestMethod.POST)
    public Res<?>getAnalysisDrawType(@RequestParam("classId") String classId) {
        return Res.OK(drawService.getAnalysisDrawType(classId));
    }
    @ApiOperation("保存分析图分类")
    @RequestMapping(value = "/saveAnalysisDrawType", method = RequestMethod.POST)
    public Res<?>saveAnalysisDraw(@RequestBody SavePageAnalysisDrawTypeDto param) {
        return Res.OK(drawService.saveAnalysisDrawType(param));
    }
}
