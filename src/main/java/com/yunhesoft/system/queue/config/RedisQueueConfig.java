package com.yunhesoft.system.queue.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.yunhesoft.system.queue.redis.impl.RedisQueueConsumerContainer;

import lombok.extern.slf4j.Slf4j;

/**
 * @category redis队列初始化配置信息
 * <AUTHOR>
 *
 */
@Slf4j
@Configuration
public class RedisQueueConfig {

	// 初始化完毕后调取 init
	@Bean(initMethod = "init", destroyMethod = "destroy")
	public RedisQueueConsumerContainer redisQueueConsumerContainer() {
		log.info("redis队列开始加载");
		RedisQueueConsumerContainer redisQueueConsumerContainer = new RedisQueueConsumerContainer();
		return redisQueueConsumerContainer;
	}
}