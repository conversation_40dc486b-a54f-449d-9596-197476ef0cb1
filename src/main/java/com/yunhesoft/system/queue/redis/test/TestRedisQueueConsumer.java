package com.yunhesoft.system.queue.redis.test;

import com.yunhesoft.system.queue.redis.RedisQueueConsumer;

import lombok.Data;

@Data
public class TestRedisQueueConsumer implements RedisQueueConsumer {
	private String queueName;

	@Override
	public String getQueueName() {
		return queueName;
	}

	@Override
	public void handleMessage(Object message) {
		System.out.println(message);

	}

	@Override
	public void error(String error) {
		System.out.println("error");
	}

}
