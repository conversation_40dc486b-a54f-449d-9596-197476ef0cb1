package com.yunhesoft.qualityIndex.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@ApiModel(value = "岗位工作清单活动质量指标设置")
@Entity
@Data
@Table(name = "QUALITY_INDEX",indexes = {
        @Index(name = "QUALITY_INDEX_OBJECT_ID_IDX", columnList = "OBJECT_ID")
})
public class QualityIndex extends BaseEntity {
    //活动配置ID 活动属性表ID
    @ApiModelProperty(value = "对象id")
    @Column(name = "OBJECT_ID", length = 255)
    private String objectId;
    @ApiModelProperty(value = "对象标识  用于指定是什么模块的质量指标")
    @Column(name = "OBJECT_FLAG", length = 255)
    private String objectFlag;
    //指标名称
    @ApiModelProperty(value = "指标名称")
    @Column(name = "INDEX_NAME", length = 255)
    private String indexName;
    //指标描述
    @ApiModelProperty(value = "指标描述")
    @Column(name = "INDEX_DESC", length = 255)
    private String indexDesc;
    //专业
    @ApiModelProperty(value = "专业")
    @Column(name = "SPEC_CODE", length = 255)
    private String specCode;
    //数据来源公式
    @ApiModelProperty(value = "数据来源公式")
    @Column(name = "DATA_SOURCE_FORMULA", length = 255)
    private String dataSourceFormula;
    @ApiModelProperty(value = "完成条件")
    @Column(name = "FINISH_FORMULA", length = 255)
    private String finishFormula;
    @ApiModelProperty(value = "是否为关键指标")
    @Column(name = "IS_IMPORTANT", length = 255)
    private Integer isImportant;
    @ApiModelProperty(value = "记分类型")
    @Column(name = "ADD_SCORE_TYPE")
    private Integer addScoreType;
    //可用标识 1=可用，0=无效
    @ApiModelProperty(value = "可用标识 1=可用，0=无效")
    @Column(name = "TMUSED")
    private Integer tmused;

    @ApiModelProperty(value = "完成条件值")
    @Column(name = "formula_data" , length = 500)
    private String formulaData;
}

