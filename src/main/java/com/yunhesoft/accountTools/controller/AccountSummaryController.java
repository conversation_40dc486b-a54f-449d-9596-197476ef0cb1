package com.yunhesoft.accountTools.controller;


import com.yunhesoft.accountTools.entity.dto.AccountParam;
import com.yunhesoft.accountTools.entity.dto.AccountSummaryDto;
import com.yunhesoft.accountTools.service.IAccountSummaryService;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "台账异常相关函数")
@RestController
@RequestMapping("/accountSummary")
public class AccountSummaryController extends BaseRestController {
    @Autowired
    private IAccountSummaryService accountSummaryService;

    @ApiOperation(value = "获取台账已录入记录数")
    @RequestMapping(value = "/getAccountRecordCount", method = { RequestMethod.POST })
    public Res<?> getAccountRecordCount(@RequestBody AccountSummaryDto dto) {
        return Res.OK(accountSummaryService.getAccountRecordCount(dto));
    }
}
