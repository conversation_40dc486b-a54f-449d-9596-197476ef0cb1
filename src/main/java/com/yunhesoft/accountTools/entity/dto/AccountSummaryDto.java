package com.yunhesoft.accountTools.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("台账统计查询参数")
@Data
public class AccountSummaryDto {
    @ApiModelProperty("表单ID")
    private String formIds;
    @ApiModelProperty("班次ID")
    private String shiftId;
    @ApiModelProperty("上班时间")
    private String sbsj;
    @ApiModelProperty("当班机构")
    private String orgcode;
}
