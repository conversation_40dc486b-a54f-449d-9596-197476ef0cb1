package com.yunhesoft.accountTools.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("台账汇总查询数据")
@Data
public class AccountSummaryVo {
    @ApiModelProperty("表单模版ID")
    private String formId;
    //@ApiModelProperty("台账模型ID")
    //private String ledgerModuleId;
    //@ApiModelProperty("台账名称")
    //private String ledgerName;
    @ApiModelProperty("已保存的记录数")
    private Integer savedReordCnt;
}
