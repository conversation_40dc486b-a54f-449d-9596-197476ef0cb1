package com.yunhesoft.accountTools.service.impl;

import com.yunhesoft.accountTools.entity.dto.AccountSummaryDto;
import com.yunhesoft.accountTools.entity.po.DigitalLedger;
import com.yunhesoft.accountTools.entity.vo.AccountSummaryVo;
import com.yunhesoft.accountTools.service.IAccountSummaryService;
import com.yunhesoft.system.kernel.service.EntityService;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Log4j2
public class AccountSummaryServiceImpl implements IAccountSummaryService {
    @Autowired
    private EntityService entityService;

    /**
     * 获取台账已录入记录数
     * @param dto
     * @return
     */
    @Override
    public List<AccountSummaryVo> getAccountRecordCount(AccountSummaryDto dto) {
        List<AccountSummaryVo> rst = new ArrayList<>();

        String sbsj = dto.getSbsj();
        String orgcode = dto.getOrgcode();
        String shiftId = dto.getShiftId();
        String formIds = dto.getFormIds();
        if (StringUtils.isEmpty(formIds)) {
            log.error("表单ID不能为空");
            return rst;
        }

        List<Object> params = new ArrayList<>();
        //通过表单模版ID查询台账表格数据（台账模型ID、名称...）
        String sqlStr = "select form_id formid,ledger_name ledgername,ledger_module_id ledgermoduleid from DIGITAL_LEDGER " +
                        "where ";

        //表单过滤
        String[] formIdArray = formIds.split(",");
        StringBuilder inClause = new StringBuilder();
        inClause.append("form_id in (");
        for (int i = 0; i < formIdArray.length; i++) {
            String formId = formIdArray[i];
            if (i > 0) {
                inClause.append(",");
            }
            inClause.append("?");
            // 添加参数
            params.add(formId);
        }
        inClause.append(") and ");
        sqlStr += inClause.toString();

        sqlStr += "tmused=1 " +
                "order by formid,tmsort ";
        SqlRowSet ledgerSqlRowSet = entityService.rawQuery(sqlStr, params.toArray());
        if (ledgerSqlRowSet == null || !ledgerSqlRowSet.first()) { //表单台账配置数据为空
            return rst;
        }

        List<DigitalLedger> ledgerList = new ArrayList<>();

        while (true) {
            DigitalLedger digitalLedger = new DigitalLedger();
            digitalLedger.setFormId(ledgerSqlRowSet.getString("formid"));
            digitalLedger.setLedgerModuleId(ledgerSqlRowSet.getString("ledgermoduleid"));
            digitalLedger.setLedgerName(ledgerSqlRowSet.getString("ledgername"));
            ledgerList.add(digitalLedger);

            if (!ledgerSqlRowSet.next()) {
                break;
            }
        }

        List<String> ledgerModuleIdList = ledgerList.stream().map(digitalLedger -> digitalLedger.getLedgerModuleId()).distinct().collect(Collectors.toList());
        if (ObjectUtils.isEmpty(ledgerModuleIdList)){
            return rst;
        }
        //表单→台账模型Map
        Map<String, List<DigitalLedger>> formLedgerMap = new LinkedHashMap<>();
        for (DigitalLedger digitalLedger : ledgerList) {
            String ledgerModuleId = digitalLedger.getLedgerModuleId();
            String formId = digitalLedger.getFormId();
            String ledgerName = digitalLedger.getLedgerName();

            List<DigitalLedger> digitalLedgers = formLedgerMap.get(formId);
            if (digitalLedgers == null) {
                digitalLedgers = new ArrayList<>();
                formLedgerMap.put(formId, digitalLedgers);
            }
            digitalLedgers.add(digitalLedger);
        }

        params.clear();
        //通过台账模型ID（核算对象ID）、其它查询条件查询台账已录入记录数
        sqlStr = "select acctobj_id, count(*) rec_cnt " +
                "from ACCTOBJ_INPUT where TMUSED=1 ";

        inClause = new StringBuilder();
        inClause.append("and ACCTOBJ_ID in (");
        for (int i = 0; i < ledgerModuleIdList.size(); i++) {
            String ledgerModuleId = ledgerModuleIdList.get(i);
            if (i > 0) {
                inClause.append(",");
            }
            inClause.append("?");
            // 添加参数
            params.add(ledgerModuleId);
        }
        inClause.append(") ");
        sqlStr += inClause.toString();

        sqlStr += "and bcdm=? ";
        params.add(shiftId);
        sqlStr += "and sbsj=? ";
        params.add(sbsj);
        sqlStr += "and team_id=? ";
        params.add(orgcode);
        sqlStr += "group by ACCTOBJ_ID ";

        SqlRowSet accountSqlRowSet = entityService.rawQuery(sqlStr, params.toArray());
        if (accountSqlRowSet == null || !accountSqlRowSet.first()) { //台账数据为空
            //返回0
            for (int i = 0; i < formIdArray.length; i++) {
                String formId = formIdArray[i];
                AccountSummaryVo accountSummaryVo = new AccountSummaryVo();
                accountSummaryVo.setFormId(formId);
                List<DigitalLedger> digitalLedgers = formLedgerMap.get(formId);
                if (!ObjectUtils.isEmpty(digitalLedgers)) {
                    accountSummaryVo.setSavedReordCnt(0);
                }
                rst.add(accountSummaryVo);
            }
            return rst;
        }
        List<LedgerModuleRecordCount> ledgerModuleRecordCountList = new ArrayList<>();

        while (true) {
            LedgerModuleRecordCount ledgerModuleRecordCount = new LedgerModuleRecordCount();
            ledgerModuleRecordCount.setLedgerModuleId(accountSqlRowSet.getString("acctobj_id"));
            ledgerModuleRecordCount.setRecordCount(accountSqlRowSet.getInt("rec_cnt"));
            ledgerModuleRecordCountList.add(ledgerModuleRecordCount);

            if (!accountSqlRowSet.next()) {
                break;
            }
        }
        Map<String, Integer> ledgerRecordCountMap = ledgerModuleRecordCountList.stream().collect(Collectors.toMap(LedgerModuleRecordCount::getLedgerModuleId, LedgerModuleRecordCount::getRecordCount));

        //汇总统计
        for (int i = 0; i < formIdArray.length; i++) {
            String formId = formIdArray[i];
            AccountSummaryVo accountSummaryVo = new AccountSummaryVo();
            accountSummaryVo.setFormId(formId);
            List<DigitalLedger> digitalLedgers = formLedgerMap.get(formId);
            if (!ObjectUtils.isEmpty(digitalLedgers)) {
                Integer totalCount = digitalLedgers.stream().map(digitalLedger -> {
                    String ledgerModuleId = digitalLedger.getLedgerModuleId();
                    Integer cnt = ledgerRecordCountMap.get(ledgerModuleId);
                    if (cnt == null) cnt = 0;
                    return cnt;
                }).reduce(0, Integer::sum);
                accountSummaryVo.setSavedReordCnt(totalCount);
            }
            rst.add(accountSummaryVo);
        }

        return rst;
    }

    @Data
    class LedgerModuleRecordCount {
        String ledgerModuleId;
        Integer recordCount;
    }

}
