package com.yunhesoft.accountTools.service;

import com.yunhesoft.accountTools.entity.dto.AccountSummaryDto;
import com.yunhesoft.accountTools.entity.vo.AccountSummaryVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface IAccountSummaryService {
    /**
     * 获取台账已录入记录数
     * @param dto
     * @return
     */
    List<AccountSummaryVo> getAccountRecordCount(AccountSummaryDto dto);
}
