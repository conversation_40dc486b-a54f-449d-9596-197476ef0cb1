package com.yunhesoft.joblist.workInstruction.controller;


import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataFeedbackQueryDto;
import com.yunhesoft.joblist.workInstruction.service.IWIDataQueryService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/joblist/wi/query")
@Api(tags = "工作指令查询接口")
public class WIDataQueryController extends BaseRestController {

    @Autowired
    private IWIDataQueryService srv;

    @ApiOperation("查询机构指令列表")
    @RequestMapping(value = "/queryOrgDataList", method = {RequestMethod.POST})
    public Res<?> queryOrgDataList(@RequestBody WIDataFeedbackQueryDto dto) {
//        Pagination<?> page = null;
//        if (dto.getPageSize() > 0) {
//            page = Pagination.create(dto.getPageNum(), dto.getPageSize());
//        }
        Res res = Res.OK(srv.queryOrgDataList(dto));
//        if (page != null) {
//            res.setTotal(page.getTotal());
//        }
        return res;
    }

    @ApiOperation("查询我所在的车间或部门")
    @RequestMapping(value = "/queryMyWorkshop", method = {RequestMethod.GET})
    public Res<?> queryMyWorkshop() {
//        Pagination<?> page = null;
//        if (dto.getPageSize() > 0) {
//            page = Pagination.create(dto.getPageNum(), dto.getPageSize());
//        }
        Res res = Res.OK(srv.queryMyWorkshop());
//        if (page != null) {
//            res.setTotal(page.getTotal());
//        }
        return res;
    }



}
