package com.yunhesoft.joblist.workInstruction.service.impl;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataFeedbackQueryDto;
import com.yunhesoft.joblist.workInstruction.entity.po.WIData;
import com.yunhesoft.joblist.workInstruction.entity.po.WIDataOrgInfo;
import com.yunhesoft.joblist.workInstruction.entity.po.WIDataOrgOpCardInfo;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIFeedbackItemVo;
import com.yunhesoft.joblist.workInstruction.service.IWIDataQueryService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class WIDataQueryServiceImpl implements IWIDataQueryService {

    @Autowired
    private EntityService entityService;

    @Autowired
    private ISysOrgService orgService;

    /**
     * 查询机构指令列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<WIFeedbackItemVo> queryOrgDataList(WIDataFeedbackQueryDto dto) {
        if (dto == null) {
            return null;
        }
        Where where = Where.create();
        where.eq(WIData::getTmused, 1);
        where.eq(WIData::getCategory, WIDataUtils.WI_DATA_CATEGORY_WI);
        where.eq(WIData::getInAcceptOrg, dto.getInAcceptOrg());
        where.ge(WIData::getDataStatus, 3);
        String startDt = dto.getStartDt();
        String endDt = dto.getEndDt();
        String dataName = dto.getDataName();
        String orgCode = dto.getOrgCode();
        if (StringUtils.isNoneEmpty(startDt, endDt)) {
            // start>=st && end<=ed   start<=st && end>=st   start>=st && start<=end   end>=st && end<=ed
            // start betweend st and ed  and end is null
            where.and();
            where.lb();

            where.between(WIData::getStartDt, startDt+" 00:00:00", endDt+" 23:59:59");

            where.or().between(WIData::getEndDt, startDt+" 00:00:00", endDt+" 23:59:59");

            where.or();

            where.lb();

            where.lt(WIData::getStartDt, startDt+" 00:00:00");
            where.and();
            where.lb();
            where.gt(WIData::getEndDt, endDt+" 23:59:59");
            where.or().isEmpty(WIData::getEndDt);
            where.or().isNull(WIData::getEndDt);
            where.rb();

            where.rb();

            where.rb();
        }
        Order order = Order.create();
        order.orderByAsc(WIData::getCreateTime);
        List<WIData> list = entityService.queryData(WIData.class, where, order, null);
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        List<WIData> dataResult = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        for (WIData data : list) {
            if (StringUtils.isNotEmpty(dataName)) {
                if (!data.getDataName().contains(dataName)) {
                    continue;
                }
            }
            dataResult.add(data);
            idList.add(data.getId());
        }

        if (StringUtils.isEmpty(dataResult)) {
            return null;
        }
        Where orgWhere = Where.create();
        orgWhere.in(WIDataOrgInfo::getDataId, idList.toArray());
//        orgWhere.eq(WIDataOrgInfo::getFeedbackComplete, 1);
        if (StringUtils.isNotEmpty(orgCode)) {
            orgWhere.eq(WIDataOrgInfo::getAccOrgCode, orgCode);
        }
//        if (dto.getFeedbackStatus() != 0) {
//            orgWhere.eq(WIDataOrgInfo::getFeedbackStatus, dto.getFeedbackStatus());
//        }
        List<WIDataOrgInfo> orgList = entityService.queryData(WIDataOrgInfo.class, orgWhere, Order.create(), null);
        if (StringUtils.isEmpty(orgList)) {
            return null;
        }

        Map<String, List<WIDataOrgInfo>> group = orgList.stream().collect(Collectors.groupingBy(WIDataOrgInfo::getDataId));

        //查找指令关联的操作卡生成的实例id
        Where cardWhere = Where.create();
        //List<WIDataOrgOpCardInfo> cardInfoList=entityService.queryData(WIDataOrgOpCardInfo.class,cardWhere, Order.create(), null);
        //Map<String, List<WIDataOrgOpCardInfo>> cardInfoMap = cardInfoList.stream().collect(Collectors.groupingBy(WIDataOrgOpCardInfo::getDataId));
        List<WIFeedbackItemVo> result = new ArrayList<>();
        for (WIData data : dataResult) {
            List<WIDataOrgInfo> dataOrgInfoList = group.get(data.getId());
            //根据指令id获取绑定该指令上的操作卡
            //List<WIDataOrgOpCardInfo> cardList= cardInfoMap.get(data.getId());
            if (StringUtils.isEmpty(dataOrgInfoList)) {
                continue;
            }
            for (WIDataOrgInfo orgInfo : dataOrgInfoList) {

                WIFeedbackItemVo vo = new WIFeedbackItemVo();
                vo.setOrgInfoId(orgInfo.getId());
                vo.setDataId(data.getId());
                vo.setDataName(data.getDataName());
                vo.setDataContent(data.getDataContent());
                vo.setAcceptOrgCode(orgInfo.getAccOrgCode());
                vo.setAcceptOrgName(orgInfo.getAccOrgName());
                vo.setSubmitDt(data.getSubmitDt());
                vo.setSubmitUser(data.getSubmitUserName());
                vo.setAcceptUser(orgInfo.getAccUserName());
                vo.setAcceptDt(orgInfo.getAccDt());
                vo.setCompleteDt(orgInfo.getCompleteDt());
                boolean inputFeedback = false;
                if (StringUtils.isNotEmpty(orgInfo.getCardId())) {
                    //绑定了操作卡
                    WIDataOrgOpCardInfo cardInfo = new WIDataOrgOpCardInfo();
                    cardInfo.setCardId(orgInfo.getCardId());
                    cardInfo.setCardName(orgInfo.getCardName());
                    cardInfo.setCardInstanceId(orgInfo.getCardInstanceId());
                    cardInfo.setCardExecType(orgInfo.getCardExecType());
                    cardInfo.setCatalogAlias(orgInfo.getCatalogAlias());
                    vo.setCardInfoList(new ArrayList<>(Collections.singletonList(cardInfo)));
                    if (orgInfo.getFixedStatus() != null && orgInfo.getFixedStatus() == 1) {
                        inputFeedback = true;
                    }
                } else {
                    inputFeedback = true;
                }

                //显示反馈信息
                vo.setInputFeedback(inputFeedback);

                result.add(vo);
            }
        }

        //result按照submitDt倒叙
        if (StringUtils.isNotEmpty(result)) {
            result.sort(Comparator.comparing(WIFeedbackItemVo::getSubmitDt).reversed());
        }
        return result;
    }

    @Override
    public SysOrg queryMyWorkshop() {

        SysUser user = SysUserUtil.getCurrentUser();
        String userOrgId = Optional.ofNullable(user).map(SysUser::getOrgId).orElse(null);
        if (StringUtils.isEmpty(userOrgId)) {
            return null;
        }
        SysOrg org = orgService.getParentOrgByOrgType(userOrgId, ISysOrgService.OrgWorkshopOrDepartment);
        return org;
    }


}
