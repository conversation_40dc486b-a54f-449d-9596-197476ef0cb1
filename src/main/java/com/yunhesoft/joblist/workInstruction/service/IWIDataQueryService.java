package com.yunhesoft.joblist.workInstruction.service;


import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataFeedbackQueryDto;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIFeedbackItemVo;
import com.yunhesoft.system.org.entity.po.SysOrg;

import java.util.List;


public interface IWIDataQueryService {

	/**
	 * 查询机构指令列表
	 * @param dto
	 * @return
	 */
	List<WIFeedbackItemVo> queryOrgDataList(WIDataFeedbackQueryDto dto);


	SysOrg queryMyWorkshop();

}
