package com.yunhesoft.joblist.workInstruction.entity.vo;

import com.yunhesoft.joblist.workInstruction.entity.po.WIDataFile;
import com.yunhesoft.joblist.workInstruction.entity.po.WIDataOrgOpCardInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.util.List;

@ApiModel(value = "流程设置")
@Getter
@Setter
public class WIFeedbackItemVo {

	@ApiModelProperty(value = "指令id")
	private String dataId;

	@ApiModelProperty(value = "指令接收单位")
	private String acceptOrgCode;
	@ApiModelProperty(value = "指令接收单位")
	private String acceptOrgName;

	@ApiModelProperty(value = "指令名称")
	private String dataName;

	@ApiModelProperty(value = "指令内容")
	private String dataContent;

	@ApiModelProperty(value = "下达时间")
	private String submitDt;

	@ApiModelProperty(value = "安排人")
	private String submitUser;

	@ApiModelProperty(value = "承办人")
	private String acceptUser;

	@ApiModelProperty(value = "承办时间")
	private String acceptDt;

	@ApiModelProperty(value = "完成情况（反馈内容）")
	private String feedbackContent;

	@ApiModelProperty(value = "完成情况（反馈内容）")
	private String feedbackText;

	@ApiModelProperty(value = "完成时间")
	private String completeDt;

	@ApiModelProperty(value = "反馈状态")
	private int feedbackStatus;

	@ApiModelProperty(value = "机构信息表id")
	private String orgInfoId;

	@ApiModelProperty(value = "反馈附件列表")
	private List<WIDataFile> fileList;

	@ApiModelProperty(value = "汇总状态")
	private int summaryStatus;

	@ApiModelProperty(value = "绑定的操作卡")
	private List<WIDataOrgOpCardInfo> cardInfoList;

	@ApiModelProperty(value = "有填报反馈")
	private boolean inputFeedback = false;
}
