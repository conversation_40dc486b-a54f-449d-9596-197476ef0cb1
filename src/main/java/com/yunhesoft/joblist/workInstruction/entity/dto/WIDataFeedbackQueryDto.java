package com.yunhesoft.joblist.workInstruction.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;

@ApiModel(value = "反馈")
@Getter
@Setter
public class WIDataFeedbackQueryDto {

	@ApiModelProperty(value = "模式")
	private String mode;

	@ApiModelProperty(value = "开始时间")
	private String startDt;

	@ApiModelProperty(value = "截止时间")
	private String endDt;

	@ApiModelProperty(value = "机构")
	private String orgCode;

	@ApiModelProperty(value = "状态 2已完成 1进行中 -1未完成")
	private int feedbackStatus;

	@ApiModelProperty(value = "生产指令")
	private String dataName;

	@ApiModelProperty(value = "执行机构内部")
	private int inAcceptOrg;
}
