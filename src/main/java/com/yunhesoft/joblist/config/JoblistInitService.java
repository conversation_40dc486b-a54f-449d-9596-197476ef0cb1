package com.yunhesoft.joblist.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.joblist.constant.JoblistConstant;
import com.yunhesoft.joblist.service.IJoblistMethodService;
import com.yunhesoft.system.menu.service.ISysMenuLibInitService;
import com.yunhesoft.system.tools.classExec.entry.po.MtmFormulaTree;
import com.yunhesoft.system.tools.classExec.service.impl.SysClassExecServiceImpl;

@Component
public class JoblistInitService implements ApplicationRunner {
    @Autowired
    private ISysMenuLibInitService serv; // 菜单库
    
    @Autowired
	private IJoblistMethodService joblistMethodService; //工作清单方法服务

    @Autowired
    private SysClassExecServiceImpl sysClassExecService;

    @Value("${spring.application.name:TM4-PAAS-JOBLIST}")
    private String serviceName;

    /**
     * 判断是否初始化
     *
     * @return
     */
    private boolean isInit() {
        return true;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (this.isInit()) {
            this.init();
        }
    }

    /**
     * 模块相关数据初始化
     */
    private void init() {
        initJoblistModuleAndMenu();
        initMtmInterfaceData();
    }

    /**
     * 初始化模块和菜单（包含按钮权限等）
     */
    private void initJoblistModuleAndMenu() {
        String moduleCode = JoblistConstant.MODULE_CODE; // 模块编码
        /**
         ** 注意必须顺序初始化数据 1.模块->2.分类->3.菜单->4.按钮 （无按钮权限可以忽略）
         */
        // 1、初始化系统模块
        serv.initModule("智能化岗位工作清单", moduleCode);/** 模块编码：微服务的编码，这里的模块以一个微服务为单位 **/
        // 2、初始化模块下的分类
        serv.initClass("岗位工作清单", moduleCode + "Setting");/** 分类编码该模块下不要重复 **/
        // 3、初始化菜单
        //serv.initMenuLib("模版设置", "/bzjs/noteTemplate/index");
        serv.initMenuLib("标准工作库维护", "/joblist/standardJobLibConfig/standardJoblibConfigMain");
        serv.initButton("班长操作权限", JoblistConstant.OPS_CLASS_MONITOR);
        serv.initButton("高级管理权限", JoblistConstant.OPS_CLASS_MANAGE);
        serv.initButton("添加工作权限", JoblistConstant.ADD_JOB_PERMISSION);
        serv.initMenuLib("工作台历统计", "/joblist/workCalendar/workCalendarCensus");
        serv.initMenuLib("工作积分调整", "/joblist/workCalendar/workScoreAdjust");
        serv.initMenuLib("工作台历录入", "/joblist/workCalendar/workCalendarInputNew");
        serv.initMenuLib("生成工作台历", "/joblist/workCalendar/workCalendarGenerate");
        serv.initMenuLib("岗位工作录入情况统计", "/joblist/summary/inputCompletionStatus");
        serv.initButton("高级管理权限", JoblistConstant.SUMMARY_MONITOR_PERMISSION);

        // **** 初始化频次 ******
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('A', 'administrator', NULL, NULL, '2024-06-27 10:33:57.8260000', 'administrator', '2024-06-27 10:34:31.5210000', '{\"interval\":\"1\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"1\"},{\"ftype\":\"isrelative\",\"selected\":\"0\",\"rdata\":[{\"lx\":\"sbq\",\"selected\":\"0\",\"time\":\"0\",\"unit\":\"min\"},{\"lx\":\"sbh\",\"selected\":\"0\",\"time\":\"0\",\"unit\":\"min\"},{\"lx\":\"xbq\",\"selected\":\"0\",\"time\":\"0\",\"unit\":\"min\"},{\"lx\":\"xbh\",\"selected\":\"0\",\"time\":\"0\",\"unit\":\"min\"}]},{\"ftype\":\"isafter\",\"selected\":\"0\",\"atime\":\"0\",\"aunit\":\"min\",\"etime\":\"0\",\"eunit\":\"min\"}]}', 0, '每班一次', 'system', '每班', 1, 1, 1)",
                "初始化作业活动的频次：每班一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('B', 'administrator', NULL, NULL, '2024-06-27 10:33:57.8260000', 'administrator', '2024-06-27 10:34:31.5210000', '{\"interval\":\"1\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"2\"},{\"ftype\":\"isrelative\",\"selected\":\"0\",\"rdata\":[{\"lx\":\"sbq\",\"selected\":\"1\",\"time\":\"30\",\"unit\":\"min\"},{\"lx\":\"sbh\",\"selected\":\"1\",\"time\":\"30\",\"unit\":\"min\"},{\"lx\":\"xbq\",\"selected\":\"1\",\"time\":\"30\",\"unit\":\"min\"},{\"lx\":\"xbh\",\"selected\":\"0\",\"time\":\"15\",\"unit\":\"min\"}]},{\"ftype\":\"isafter\",\"selected\":\"0\",\"atime\":\"0\",\"aunit\":\"min\",\"etime\":\"0\",\"eunit\":\"min\"}]}', 0, '每班2次', 'system', '每班2次', 2, 1, 1)",
                "初始化作业活动的频次：每班2次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('C', 'administrator', NULL, NULL, '2024-06-27 10:33:57.8260000', 'administrator', '2024-06-27 10:34:31.5210000', '{\"interval\":\"1\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"0\",\"count\":\"2\"},{\"ftype\":\"isrelative\",\"selected\":\"0\",\"rdata\":[{\"lx\":\"sbq\",\"selected\":\"1\",\"time\":\"15\",\"unit\":\"min\"},{\"lx\":\"sbh\",\"selected\":\"0\",\"time\":\"0\",\"unit\":\"min\"},{\"lx\":\"xbq\",\"selected\":\"1\",\"time\":\"15\",\"unit\":\"min\"},{\"lx\":\"xbh\",\"selected\":\"0\",\"time\":\"15\",\"unit\":\"min\"}]},{\"ftype\":\"isafter\",\"selected\":\"1\",\"atime\":\"30\",\"aunit\":\"min\",\"etime\":\"60\",\"eunit\":\"min\"}]}', 0, '上班后30分钟开始每小时一次', 'system', '每小时', 3, 1, 1)",
                "初始化作业活动的频次：上班后30分钟开始每小时一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('D', 'administrator', NULL, NULL, '2024-06-27 10:33:57.8260000', 'administrator', '2024-06-27 10:34:31.5210000', '{\"interval\":\"1\",\"iseveryteam\":\"0\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"1\"}]}', 0, '每天一次', 'system', '每天', 5, 1, 2)",
                "初始化作业活动的频次：每天一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('E', 'administrator', NULL, NULL, '2024-06-27 10:33:57.8260000', 'administrator', '2024-06-27 10:34:31.5210000', '{\"interval\":\"1\",\"iseveryteam\":\"0\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"1\"}]}', 0, '每周一次', 'system', '每周', 7, 1, 3)",
                "初始化作业活动的频次：每周一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('F', 'administrator', NULL, NULL, '2024-06-27 10:33:57.8260000', 'administrator', '2024-06-27 10:34:31.5210000', '{\"interval\":\"1\",\"iseveryteam\":\"0\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"1\"}]}', 0, '每月一次', 'system', '每月', 8, 1, 4)",
                "初始化作业活动的频次：每月一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('G', 'administrator', NULL, NULL, '2024-06-27 10:33:57.8260000', 'administrator', '2024-06-27 10:34:31.5210000', '{\"interval\":\"1\",\"iseveryteam\":\"0\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"1\"}]}', 0, '每季一次', 'system', '每季', 10, 1, 5)",
                "初始化作业活动的频次：每季一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('H', 'administrator', NULL, NULL, '2024-06-27 10:33:57.8260000', 'administrator', '2024-06-27 10:34:31.5210000', '{\"interval\":\"1\",\"iseveryteam\":\"0\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"1\"}]}', 0, '每年一次', 'system', '每年', 11, 1, 6)",
                "初始化作业活动的频次：每年一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('I', 'administrator', NULL, NULL, '2024-06-26 15:44:34.2470000', 'administrator', '2024-06-27 10:24:33.6090000', '0 0 0,2,4,6,8,10,12,14,16,18,20,22 * * ? ', 1, 'cron表达式', 'system', '每2小时执行一次', 13, 1, NULL)",
                "初始化作业活动的频次：每2小时执行一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('J', 'administrator', NULL, NULL, '2024-06-26 15:44:34.2470000', 'administrator', '2024-06-27 10:24:33.6090000', '0 0 18 ? * 6', 1, 'cron表达式', 'system', '每周五18点执行一次', 14, 1, NULL)",
                "初始化作业活动的频次：每周五18点执行一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('K', 'administrator', NULL, NULL, '2024-06-27 09:51:11.6120000', 'administrator', '2024-06-27 13:25:44.6170000', '0 0 12 ? * 3,5 ', 1, 'cron表达式', 'system', '每个周二和周四的12点执行一次', 15, 1, NULL)",
                "初始化作业活动的频次：每个周二和周四的12点执行一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('L', 'administrator', NULL, NULL, '2024-06-26 15:44:06.1260000', 'administrator', '2024-06-28 09:03:13.9990000', '{\"interval\":\"2\",\"iseveryteam\":\"0\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"1\"}]}', 0, '每2天一次', 'system', '每2天', 6, 1, 2)",
                "初始化作业活动的频次：每2天一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('M', 'administrator', NULL, NULL, '2024-07-04 08:43:44.3850000', NULL, NULL, '{\"interval\":\"1\",\"iseveryteam\":\"0\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"2\"}]}', 0, '每月2次', 'system', '每月2次', 9, 1, 4)",
                "初始化作业活动的频次：每月2次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('N', 'administrator', NULL, NULL, '2024-06-26 15:44:34.2470000', 'administrator', '2024-06-27 10:24:33.6090000', '0 0 20 * * ?', 1, '每天20点执行一次', 'system', '每个夜班', 4, 1, NULL)",
                "初始化作业活动的频次：每天20点执行一次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('O', 'administrator', NULL, NULL, '2024-06-26 15:44:34.2470000', 'administrator', '2024-06-27 10:24:33.6090000', '{\"interval\":\"1\",\"iseveryteam\":\"0\",\"frequency\":[{\"ftype\":\"iscount\",\"selected\":\"1\",\"count\":\"2\"}]}', 0, '测试', 'system', '每年2次', 12, 1, 6)",
                "初始化作业活动的频次：每年2次", "文玉林", "2024-11-26");
        serv.initSqlScript("INSERT INTO CYCLE_SCHEME (ID, CREATE_BY, CREATE_BY_ORG, CREATE_BY_POST, CREATE_TIME, UPDATE_BY, UPDATE_TIME, CYCLESTR, ISCRON, MEMO, ORGID, SNAME, TMSORT, TMUSED, BINDCYCLE) VALUES ('P', 'administrator', NULL, NULL, '2024-06-26 15:44:34.2470000', 'administrator', '2024-06-27 10:24:33.6090000', '0 0 10 ? * 2', 1, '每周一10点执行一次', 'system', '每周一', 16, 1, NULL)",
                "初始化作业活动的频次：每周一10点执行一次", "文玉林", "2024-11-26");
        //系统参数
        serv.initConfig("操作卡固化执行次数", "opercardFixed", "0", "0为不进行固化");


        // 2、初始化模块下的分类
        serv.initClass("工作指令",  "workInstruction"); /** 分类编码该模块下不要重复 **/
        serv.initMenuLib("流程设置",  "/joblist/workInstruction/config/instructionConfig"); /** 分类编码该模块下不要重复 **/
//        serv.initMenuLib("流程变量设置",  "/joblist/workInstruction/config/objVarsConfig"); /** 分类编码该模块下不要重复 **/
        serv.initMenuLib("汇总反馈",  "/joblist/workInstruction/base/workInstructionFeedbackSubmit"); /** 分类编码该模块下不要重复 **/
        serv.initButton("提交",  "submit"); /** 分类编码该模块下不要重复 **/
        serv.initMenuLib("工作指令", "/joblist/workInstruction/config/workInstructionIndex", null, null, "3", "工作指令", "/pages/workInstruction/workInstructionIndex", null, "compose");

        serv.initMenuLib("内部指令查询",  "/joblist/workInstruction/base/workInstructionQueryIn"); /** 分类编码该模块下不要重复 **/
        serv.initButton("查询全部机构",  "queryAll"); /** 分类编码该模块下不要重复 **/

        // **** 执行初始化操作******
        serv.runInit();

        //工作清单数据字典初始化
        joblistMethodService.initDictInfoByJoblist();
    }

    /**
     * 初始化目标传导接口数据
     */
    private void initMtmInterfaceData() {
        initMtmInterfaceFormula();
        initMtmInterfaceMicroservice();
    }

    /**
     * 初始化目标传导接口公式
     */
    private void initMtmInterfaceFormula() {
        List<MtmFormulaTree> treeNodes = new ArrayList<>();

        String moduleCode = JoblistConstant.MODULE_CODE; // 模块编码

        MtmFormulaTree newNode = new MtmFormulaTree();
        newNode.setId(TMUID.getUID());
        newNode.setTmUsed(1);
        newNode.setFormulaCode("obj.job.score");
        newNode.setFormulaName("当前责任对象.工作.得分");
        newNode.setFormulaDesc("获取当前责任对象的工作得分");
        newNode.setIsLeaf(1);
        newNode.setModuleCode(moduleCode);
        treeNodes.add(newNode);

        sysClassExecService.initMtmFormulaTree(treeNodes);
    }

    /**
     * 初始化目标传导接口微服务
     */
    private void initMtmInterfaceMicroservice(){
        String moduleCode = JoblistConstant.MODULE_CODE; // 模块编码
        String moduleName = JoblistConstant.MODULE_NAME; // 模块编码
        //MtmFormulaModelFactory.registerMicroservice(moduleCode, moduleName, serviceName, "/tm4main/leanCosting/costFormula/getFormulaTree", "/tm4main/leanCosting/costFormula/getFormulaValue");
    }
}
