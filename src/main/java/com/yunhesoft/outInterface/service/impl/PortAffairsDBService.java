package com.yunhesoft.outInterface.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.outInterface.entity.po.PortAffairsOilDetail;
import com.yunhesoft.outInterface.entity.po.PortAffairsProductDetail;
import com.yunhesoft.outInterface.service.IPortAffairsDBService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class PortAffairsDBService implements IPortAffairsDBService {
	
	@Autowired
	private EntityService entityService;
	
	
	/**
	 * 获取产品码头进港数据
	 * @category 获取产品码头进港数据
	 * <AUTHOR> 
	 * @param startDt
	 * @param endDt
	 * @param tableCodeList
	 * @return
	 */
	@Override
	public List<PortAffairsProductDetail> getPortAffairsProduct(String startDt, String endDt,
			List<String> tableCodeList) {
		// TODO Auto-generated method stub
		List<PortAffairsProductDetail> result = new ArrayList<PortAffairsProductDetail>();
		if (StringUtils.isNotEmpty(startDt) && StringUtils.isNotEmpty(endDt) && StringUtils.isNotEmpty(tableCodeList)) {
			Where where = Where.create();
			where.in(PortAffairsProductDetail::getOperateType, tableCodeList.toArray());
			where.between(PortAffairsProductDetail::getOperateDate, startDt, endDt);
			Order order = Order.create();		
			order.orderByAsc(PortAffairsProductDetail::getOperateType);
			order.orderByAsc(PortAffairsProductDetail::getOperateDate);
			order.orderByAsc(PortAffairsProductDetail::getStartTime);
			List<PortAffairsProductDetail> queryList = entityService.queryList(PortAffairsProductDetail.class, where,order);
			if (StringUtils.isNotEmpty(queryList)) {
				result=queryList;
			}
		}
		return result;
	}
	/**
	 * 更新产品码头进港数据
	 * @category  更新产品码头进港数据
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	@Override
	public boolean updatePortAffairsProduct(List<PortAffairsProductDetail> dataList) {
		// TODO Auto-generated method stub
		boolean result = false;
		if (StringUtils.isNotEmpty(dataList)){
			int saveResult = entityService.updateBatch(dataList);
			if (saveResult > 0) {
				result = true;
			}
		}
		return result;
	}
	/**
	 * 获取原油码头进港数据
	 * @category 获取原油码头进港数据
	 * <AUTHOR> 
	 * @param startDt
	 * @param endDt
	 * @param tableCodeList
	 * @return
	 */
	@Override
	public List<PortAffairsOilDetail> getPortAffairsOil(String startDt, String endDt, List<String> tableCodeList) {
		// TODO Auto-generated method stub
		List<PortAffairsOilDetail> result = new ArrayList<PortAffairsOilDetail>();
		if (StringUtils.isNotEmpty(startDt) && StringUtils.isNotEmpty(endDt) && StringUtils.isNotEmpty(tableCodeList)) {
			Where where = Where.create();
			where.in(PortAffairsOilDetail::getOperateType, tableCodeList.toArray());
			where.between(PortAffairsOilDetail::getOperateDate, startDt, endDt);
			Order order = Order.create();		
			order.orderByAsc(PortAffairsOilDetail::getOperateType);
			order.orderByAsc(PortAffairsOilDetail::getOperateDate);
			order.orderByAsc(PortAffairsOilDetail::getStartTime);
			List<PortAffairsOilDetail> queryList = entityService.queryList(PortAffairsOilDetail.class, where,order);
			if (StringUtils.isNotEmpty(queryList)) {
				result=queryList;
			}
		}
		return result;
	}
	/**
	 * 更新原油码头进港数据
	 * @category  更新原油码头进港数据
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	@Override
	public boolean updatePortAffairsOil(List<PortAffairsOilDetail> dataList) {
		// TODO Auto-generated method stub
		boolean result = false;
		if (StringUtils.isNotEmpty(dataList)){
			int saveResult = entityService.updateBatch(dataList);
			if (saveResult > 0) {
				result = true;
			}
		}
		return result;
	}
}
