package com.yunhesoft.outInterface.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.outInterface.entity.po.PortAffairsOilDetail;
import com.yunhesoft.outInterface.entity.po.PortAffairsProductDetail;
import com.yunhesoft.outInterface.service.IPortAffairsDBService;
import com.yunhesoft.outInterface.service.IPortAffairsService;

@Service
public class PortAffairsService implements IPortAffairsService {
	
	@Autowired
	private IPortAffairsDBService dbService;
	/**
	 * 获取产品码头进港数据
	 * @category 获取产品码头进港数据
	 * <AUTHOR> 
	 * @param startDt
	 * @param endDt
	 * @param tableCodeList
	 * @return
	 */
	@Override
	public List<PortAffairsProductDetail> getPortAffairsProduct(String startDt, String endDt,
			List<String> tableCodeList) {
		// TODO Auto-generated method stub
		return dbService.getPortAffairsProduct(startDt, endDt, tableCodeList);
	}
	/**
	 * 更新产品码头进港数据
	 * @category  更新产品码头进港数据
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	@Override
	public boolean updatePortAffairsProduct(List<PortAffairsProductDetail> dataList) {
		// TODO Auto-generated method stub
		return dbService.updatePortAffairsProduct(dataList);
	}
	/**
	 * 获取原油码头进港数据
	 * @category 获取原油码头进港数据
	 * <AUTHOR> 
	 * @param startDt
	 * @param endDt
	 * @param tableCodeList
	 * @return
	 */
	@Override
	public List<PortAffairsOilDetail> getPortAffairsOil(String startDt, String endDt, List<String> tableCodeList) {
		// TODO Auto-generated method stub
		return dbService.getPortAffairsOil(startDt, endDt, tableCodeList);
	}
	/**
	 * 更新原油码头进港数据
	 * @category  更新原油码头进港数据
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	@Override
	public boolean updatePortAffairsOil(List<PortAffairsOilDetail> dataList) {
		// TODO Auto-generated method stub
		return dbService.updatePortAffairsOil(dataList);
	}

}
