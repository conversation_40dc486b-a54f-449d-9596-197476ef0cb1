package com.yunhesoft.outInterface.entity.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 外部数据接口检索条件
 * 
 * @Description:
 * <AUTHOR>
 * @date 2021年11月17日
 */
@Data
@ApiModel(value="外部数据接口查询DTO类",description="外部数据接口查询DTO类")
public class OutInterfaceDto {
//	// 战略主题id
//	@ApiModelProperty(value = "战略主题id")
//	private String themesId;
//	
//	// 版本号（月份）
//	@ApiModelProperty(value = "版本号（月份）")
//	private String versionNumber;
//
//	// 任矩阵编码
//	@ApiModelProperty(value = "父责任矩阵编码")
//	private String matrixId;
//
//	// 是否预警  0无预警数据 1预警数据 2全部数据
//	@ApiModelProperty(value = "是否预警 0无预警数据 1预警数据 2全部数据")
//	private Integer isAlarm;
}
