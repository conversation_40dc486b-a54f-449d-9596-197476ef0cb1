package com.yunhesoft.outInterface.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 产品码头进港信息明细表
 * 
 * @Description:
 * <AUTHOR>
 * @date 2024年03月24日
 */
@Entity
@Table(name = "PORTAFFAIRS_PRODUCTDETAIL")
@Getter
@Setter
public class PortAffairsProductDetail extends BaseEntity {

	/** 
	 * serialVersionUID:TODO
	 */ 
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "主记录ID")
	@Column(name = "PID", length = 500)
	private String pId;
	
	@ApiModelProperty(value = "船名")
	@Column(name = "SHIPNAME", length = 500)
	private String shipName;

	@ApiModelProperty(value = "船型")
	@Column(name = "SHIPTYPE", length = 200)
	private String shipType;
	
	@ApiModelProperty(value = "机构代码")
	@Column(name = "ORGCODE", length = 50)
	private String orgCode;
	
	@ApiModelProperty(value = "机构名称")
	@Column(name = "ORGNAME", length = 200)
	private String orgName;
	
	@ApiModelProperty(value = "开始时间")
	@Column(name = "STARTTIME", length = 50)
	private String startTime;
	
	@ApiModelProperty(value = "截止时间")
	@Column(name = "ENDTIME", length = 50)
	private String endTime;
	
	@ApiModelProperty(value = "操作类型编码")
	@Column(name = "OPERATETYPE", length = 200)
	private String operateType;
	
	@ApiModelProperty(value = "操作类型名称")
	@Column(name = "OPERATETYPENAME", length = 500)
	private String operateTypeName;
	
	@ApiModelProperty(value = "操作日期")
	@Column(name = "OPERATEDATE", length = 50)
	private String operateDate;
	
	@ApiModelProperty(value = "操作小时数")
	@Column(name = "OPERATEHOUR")
	private Double operateHour;

	@ApiModelProperty(value = "得分")
	@Column(name = "SCORE")
	private Double score;
}
