package com.yunhesoft.outInterface.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 码头竞赛船信息表
 * 
 * @Description:
 * <AUTHOR>
 * @date 2024年03月24日
 */
@Entity
@Table(name = "PORTAFFAIRS_SHIP")
@Getter
@Setter
public class PortAffairsShip extends BaseEntity {

	/** 
	 * serialVersionUID:TODO
	 */ 
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "船名")
	@Column(name = "SHIPNAME", length = 500)
	private String shipName;

	@ApiModelProperty(value = "船型")
	@Column(name = "SHIPTYPE", length = 200)
	private String shipType;
	
}
