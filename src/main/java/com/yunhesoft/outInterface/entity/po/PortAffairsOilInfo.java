package com.yunhesoft.outInterface.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 原油码头进港信息表
 * 
 * @Description:
 * <AUTHOR>
 * @date 2024年03月24日
 */
@Entity
@Table(name = "PORTAFFAIRS_OILINFO")
@Getter
@Setter
public class PortAffairsOilInfo extends BaseEntity {

	/** 
	 * serialVersionUID:TODO
	 */ 
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "船名")
	@Column(name = "SHIPNAME", length = 500)
	private String shipName;

	@ApiModelProperty(value = "船型")
	@Column(name = "SHIPTYPE", length = 200)
	private String shipType;

	@ApiModelProperty(value = "码头")
	@Column(name = "WHARF", length = 200)
	private String wharf;
	
	@ApiModelProperty(value = "泊位")
	@Column(name = "BERTH", length = 200)
	private String berth;
	
	@ApiModelProperty(value = "机构代码")
	@Column(name = "ORGCODE", length = 50)
	private String orgCode;
	
	@ApiModelProperty(value = "机构名称")
	@Column(name = "ORGNAME", length = 200)
	private String orgName;
	
	@ApiModelProperty(value = "部门代码")
	@Column(name = "DEPARTMENTCODE", length = 50)
	private String departmentCode;
	
	@ApiModelProperty(value = "部门名称")
	@Column(name = "DEPARTMENTNAME", length = 200)
	private String departmentName;
	
	@ApiModelProperty(value = "系缆时间")
	@Column(name = "TIETIME", length = 50)
	private String tieTime;//mooringlineTime
	
	@ApiModelProperty(value = "解缆时间") 
	@Column(name = "UNTIETIME", length = 50) 
	private String untieTime;//unwindingTime
	
	@ApiModelProperty(value = "进港日期")
	@Column(name = "ARRIVALDATE", length = 50)
	private String arrivalDate;
	
	@ApiModelProperty(value = "通知进港时间")
	@Column(name = "NOTIFI_ENTRY_TIME", length = 50)
	private String notifiEntryTime;
	
	@ApiModelProperty(value = "系缆开始时间")
	@Column(name = "MOORINGLINE_TIME", length = 50)
	private String mooringlineTime;
	
	@ApiModelProperty(value = "实际靠泊时间（系缆完毕时间）")
	@Column(name = "ACTUAL_BERTH_TIME", length = 50)
	private String actualBerthTime;
	
	@ApiModelProperty(value = "储运开泵时间")
	@Column(name = "PUMPING_TIME", length = 50)
	private String pumpingTime;
	
	@ApiModelProperty(value = "船方见油时间")
	@Column(name = "THESHIP_OIL_TIME", length = 50)
	private String theshipOilTime;
	
	@ApiModelProperty(value = "装卸货开泵时间")
	@Column(name = "UNLOADPUMPINGSTR_TIME", length = 50)
	private String unloadpumpingstrTime;
	
	@ApiModelProperty(value = "装卸货停泵时间")
	@Column(name = "UNLOADPUMPINGSTOP_TIME", length = 50)
	private String unloadpumpingstopTime;
	
	@ApiModelProperty(value = "解缆开始时间")
	@Column(name = "UNWINDING_TIME", length = 50)
	private String unwindingTime;
	
	@ApiModelProperty(value = "实际离泊时间(解缆完毕时间)")
	@Column(name = "ACTUAL_UNBERTHING_TIME", length = 50)
	private String actualUnberthingTime;

	@ApiModelProperty(value = "接臂开始时间")
	@Column(name = "EXTEN_STRARM_TIME", length = 50)
	private String extenStrarmTime;

	@ApiModelProperty(value = "接臂完毕时间")
	@Column(name = "EXTEN_END_TIME", length = 50)
	private String extenEndTime;
	//下面的字段对方接口没有
	@ApiModelProperty(value = "拆臂开始时间")
	@Column(name = "DETACH_STRARM_TIME", length = 50)
	private String detachStrarmTime;

	@ApiModelProperty(value = "拆臂完毕时间")
	@Column(name = "DETACH_END_TIME", length = 50)
	private String detachEndTime;
	
	@ApiModelProperty(value = "联检完成时间")
	@Column(name = "UNION_CHECK_TIME", length = 50)
	private String unionCheckTime;

	@ApiModelProperty(value = "计量开始时间")
	@Column(name = "MEASUREMENT_START_TIME", length = 50)
	private String measurementStartTime;
	
	@ApiModelProperty(value = "计量完毕时间")
	@Column(name = "MEASUREMENT_END_TIME", length = 50)
	private String measurementEndTime;
	
	@ApiModelProperty(value = "起泵时间")
	@Column(name = "RISE_PUMPING_TIME", length = 50)
	private String risePumpingTime;
	
	@ApiModelProperty(value = "卸船前检查时间")
	@Column(name = "UNLOAD_CHECK_TIME", length = 50)
	private String unloadCheckTime;
	
	@ApiModelProperty(value = "备缆时间")
	@Column(name = "PREPARE_ROPE_TIME", length = 50)
	private String prepareRopeTime;
	
//	靠泊用时：通知进港时间 - 系缆完毕时间（目前没有）
//	接管开泵：储运开泵时间-船方见油时间
//	装卸货：装卸货开泵时间-装卸货停泵时间
//	拆管离泊：装卸货停泵时间 - 解缆完毕时间（目前没有）
//	
//	系缆时间：系缆开始时间 - 系缆完毕时间（目前没有）
//	接臂时间：接臂开始时间 - 接臂完毕时间（目前没有）
//	吹扫拆臂时间：吹扫拆臂开始时间（目前没有） - 吹扫拆臂时间截止时间（目前没有）
//	联检和计量之间衔接时间：联检完成时间（目前没有） -计量开始时间（目前只有计量时间，但不能确定是开始时间还是结束时间）
//	计量完成和起泵之间时间：-计量完成时间（目前只有计量时间，但不能确定是开始时间还是结束时间）- 起泵时间（目前没有）
//	工作量计算：
//	卸船前检查：有卸船前检查完成时间，则班组工作次数+1
//	备缆：有备缆时间，则班组工作次数+1
//	靠泊系缆：有系缆开始时间 ，则班组工作次数+1
//	接输油臂：有接臂开始时间 ，则班组工作次数+1
//	拆臂：有吹扫拆臂开始时间 ，则班组工作次数+1
//	解缆：有解缆开始时间 ，则班组工作次数+1
	
}
